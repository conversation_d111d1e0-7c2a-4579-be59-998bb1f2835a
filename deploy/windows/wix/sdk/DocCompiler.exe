MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L �o�]        � " 0           N%      @   @                       �    ��  @�                           �$ O    @ p                   `    �#                                                              H           .text   T                         `.rsrc   p   @                   @  @.reloc      `     0             @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                0%     H      X  �  	   n   8  D# �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�,  r�  po  
�,  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po  
o  
 (  +,r�  p	o  
o  
 (  +,r+ po  
o  
 (  +,rO po  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  (  
o   

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *{  *"}  *{  *"}  *{  *"}  *{	  *"}	  *{
  *"}
  *{  *"}  *(!  
*"(  *   0 @    r� p}
  }  s"  
}  s"  
}  s"  
}  (!  
-*(#  

o$  
o%  
8�   o&  
t+  r� p('  
-Cr p('  
-Jr3 p('  
-Qrk p('  
-Sr� p('  
-Zr� p('  
-a+qo(  
()  
}  +]o(  
()  
}  +Io(  
}
  +:o(  
()  
}  +&o(  
()  
}  +o(  
()  
}  o*  
:(����u  
	,	o+  
�*   O �.    0 �       r� p}
  }  s"  
}  s"  
}  s"  
}  (!  
o  }  o  }  o  }
  o
  }  o  }  o  }  *{
  *"}
  *{  *"}  *{  *"}  *{  *"}  *{  *"}  *{  *"}  *0 o    r ps,  
�  r% ps,  
�  r3 ps,  
�   r? ps,  
�!  rM p�,  (-  
*s,  
�$  (,  "s,  
�%  rD
 pr; p(X  r� p(X  r� p(.  
>s,  
�&  r� p((  (-  
8s,  
�'  r8 p((  ()  (/  
8s,  
�(  re p8s,  
�)  r p8s,  
�*  rW p()  (-  
8s,  
�+  r� p*s,  
�,  r#! p*s,  
�-  r�" p*s,  
�.  rU& prB+ prZ+ prf+ p(/  
�,  (/  
�/  rt+ p~/  (0  
*s,  
�0  rx+ p~/  (0  
*s,  
�1  r�+ p�,  (-  
*s,  
�2  r/ p8s,  
�3  r61 p8s,  
�4  r�1 p8s,  
�5  r�1 p8s,  
�6  r62 p8s,  
�7  r�2 p*s,  
�8  r�5 p	s,  
�9  rF6 p�,  rV6 p(  

s,  
�:  rZ6 ps,  
�;  r�6 ps,  
�<  r7 ps,  
�=  r(7 ps,  
�?  r67 p(1  
�@  s"  
�  s"  
�  s"  
�  rL7 p
rN7 p+po2  

(3  
(/  ~  o4  
~  o4  
~  rp7 p(0  
o4  
rp7 p(0  
(5  
rt7 p(6  

Xo7  
2�o7  
�o8  
s,  
�>  *rx7 p*  0 T       (9  
,rL7 p*(&  (W  (-  (*  (#  (R  ('  r�7 p(0  
*0 B       (8  (;  (<  (?  (E  (-  (%  *  0 S       (A  (V  (P  (5  (1  (H  (O  (C  (D  * 0 �     ~  ~  rL7 po:  
o;  

8�   �r�7 po<  
,Q,ss�  }l  2
}k  +'}k  ~!  ���  s=  
o>  
�	�
{k  ,+	0�+%~   �($  r�7 po:  
r�7 p(0  
���i?n���r�7 p(?  
*�{  o@  
{  o@  
{  o@  
}  *(&  *�~"  - r�7 p(X  rA9 p(X  (0  
�"  ~"  *�~#  - ry9 p(X  r.; p(X  (0  
�#  ~#  *b~$  �+  s=  
o>  
* 0 �     oA  
oB  
oC  
oD  

{  oA  
oB  
oC  
(O  oE  
oA  
oB  
,@oA  
oB  
oF  
1,{  oA  
oB  
oC  
rf; prj; po  
oE  
rL7 p*  0 �     rx; p
r�; prC< prJ@ pr�A p(6  
(X  r�B pr�B p(X  (6  

	rYD pr_D po  
reD pr�Z p(G  
o  
r[ po  
r:[ po  
r\[ po  
rh[ po  
r|[ p	o  
*b~%  �.  s=  
o>  
* 0 8     oA  
oB  
oC  

(/  {  oE  
r�7 pr�7 p(6  
*0 6   	  -E+H
r�[ p (3  
oH  
(I  
(G  
 (3  
(.  
*  0 �   
  
sJ  
~&  oK  
oL  

+L	o&  
t  oM  
/�o8  
s�  oN  
oC  
s�  oN  
oF  
�
	o*  
-��	u  ,o+  
�o7  
/o7  
�o8  
s�  oN  
*       Xs     0 M       ~'  �2  s=  
o>  
~(  �4  s=  
o>  
~)  �3  s=  
o>  
*   0 �     oA  
oB  
oC  

oA  
oB  
oC  
oA  
oB  
oC  
oD  
rL7 p('  
,oD  
{  oO  
9�   {  oP  
(U  (T  r�[ prf; p(6  

{  oO  
,+{  oP  
(T  	r�[ prf; p(.  

	r�[ pr�[ p(.  

+
	*   0 �     oA  
oB  
oC  

oA  
oB  
oC  
oD  
r�[ pr�[ p(Q  
{  oO  
9�   {  oP  
(U  (T  r�[ prf; p(6  

{  oO  
,+{  oP  
(T  	r�[ prf; p(.  

	r�[ pr�[ p(.  

+
	*0 �     oA  
oB  
oC  

oA  
oB  
oC  
oA  
oB  
oC  
(U  (T  r�[ po<  
,r�[ poR  
,o7  
�o8  
r�[ p(-  

(9  
-+rf; prj; po  
(T  	r \ p(-  
(0  

	r\ p(-  
(0  

	*�~*  �6  s=  
o>  
~+  �7  s=  
o>  
*0 �     oA  
oB  
oC  

oA  
oB  
oC  
oA  
oB  
oC  
oD  
rL7 p('  
,oD  
rf; prj; po  
{  oO  
,x{  oP  
(U  (T  r,\ p(/  

{  oO  
,+{  oP  
(T  	r \ p(-  
(0  

	{
  (0  

+
	*  0 �     oA  
oB  
oC  

oA  
oB  
oC  
oA  
oB  
oC  
rf; prj; po  

rf; prj; po  
r�[ po<  
,r�[ poR  
,o7  
�o8  
(U  (T  r,\ p(/  

(9  
-(T  	r \ p(-  
(0  

	{
  (0  

	*�~,  �9  s=  
o>  
~-  �:  s=  
o>  
*   0 L   
  oA  
oB  
oC  

oA  
oB  
oC  
r^\ po<  
-+rb\ p($  �,  (/  
*0 A   
  oA  
oB  
oC  

oA  
oB  
oC  
o7  
rb\ p($  �,  (/  
*�~.  r�\ p{
  r�7 p(6  
o:  
*�{  1~0  �=  s=  
o>  
+~1  �=  s=  
o>  
* 0 l     oA  
oB  
oC  

oA  
oB  
oC  
rZ+ p(S  
-r�\ p+r�\ pr�\ p('  
-rf+ p+rZ+ p(>  r�\ p(/  
*0 r     s�  %}n  {  �}  r�\ pr�7 p(Q  
r�\ p(-  

%}m  ��  s=  
�T  
s=  
"(U  
{  �}  *n~2  �@  s=  
o>  
*  0 B     oA  
oB  
oC  

(K  (M  
~  rL7 po:  

r�^ pr�^ p(6  
*b~3  �B  s=  
o>  
* 0 M     oA  
oB  
oC  

r3 prL7 p(Q  

r
_ prL7 p(Q  

(M  
r_ pr'_ p(6  
*   0 T       {  ,&~5  r7_ po:  
~7  rg_ po:  
+$~4  r�_ po:  
~6  r�_ po:  
*0 F       {  ,r�_ pr�_ p{
  (-  
(Q  
+r�_ pr�_ p{
  (-  
(Q  
*b~8  �F  s=  
o>  
* 0 w     oA  
oB  
oC  

r�_ prL7 p(V  

r` prL7 p(V  

(#  
rt+ pr` p(V  

r` p�G  s=  
0(U  

r?` p(-  
*�oA  
oB  
oC  
r�` prL7 p(V  
*  0 Z     {  ,~9  r�` po:  
r�` p�I  s=  
(W  
{  ,r�` p
�J  s=  
!(U  
*  0      oA  
oB  
oC  

r�b p(-  
*  0 K     oA  
oB  
oC  
(R  
r�b p(0  

(L  
r�b p(-  

r�b pr�b p(Q  

*F~:  rL7 po:  
*   0 �     o7  
�sX  

sY  

+c	o2  
doZ  
Z0:3@.o[  
&+--/r�b p�,  o\  
&+rc p�,  o\  
&X	o7  
2�o]  
*b~;  �N  s=  
o>  
*0 S     oC  

rc p('  
-r�[ p('  
-r�[ p('  
-+rc p*r*c p*r4c p*~  oC  
oP  
*�~<  rc po:  
~=  r*c po:  
*b~>  �Q  s=  
o>  
*F~  oC  
oP  
*b~?  �S  s=  
o>  
*F~  oC  
oP  
*�r>c p~  r>c poP  
o  
rBc p~  rBc poP  
o  
* 0 �     {  -*o7  
sX  


8�   	o2  
~@  (  +��,>:39	o7  
�/.	�o2  
/.	�o2  
02	�o2  
9�++,$rFc prJc p��;  (-  
(0  
o_  
&+o[  
&	�
	o7  
?f���o]  
*  0 �     (0  o7  
sX  

o`  
+\(a  
%{j  {i  3>rp7 p~  rp7 poP  
o  
rVc p~  r~c poP  
(Q  
(T  o_  
&(b  
-���  o+  
�o]  
*     i�     0     o7  
sX  

sc  

8�   	o2  
	YE   a      �   �   *   ;�   +~,od  
&
o[  
&oe  
+}	o7  
�/r	�o2  

.e,od  
&
o[  
&oe  
+Fof  
]�+ o[  
&�2�+-
	o2  
 .	o2  
o[  
&	�
	o7  
?"���,od  
&
o[  
&r�7 po_  
o]  
*  0 )     o7  
�sX  

+o_  
&�2�o]  
*v(!  
sg  
(i  sg  
(k  *{C  *"}C  *{D  *"}D  *{E  *"}E  *{F  *"}F  *{G  *"}G  *{H  *"}H  *{I  *"}I  *{J  *"}J  *{K  *"}K  *.r�c p(h  
*0 r    sY  Q
8  -�o2  
./�o2  
@-  �oi  
oD  
(�   R��j5; ��:5 ��q8;�    ��:.c8�   -�:i;�    R��j;�   8�   ^��5 C?~;�    ^��.y8�   /5ؠ;�    s$�.: �rg�;�   8  r/d p('  
:�   8j  r3d p('  
:�   8U  r=d p('  
:�   8@  rAd p('  
-p8.  rOd p('  
:�   8  rgd p('  
:�   8  rmd p('  
:�   8�   rd p('  
:�   8�   r�d p('  
:�   8�   *�
�i3(j  
r�d pok  
*Poj  �ol  
8r  �
�i3(j  
r�e pok  
*P�oc  8G  �
�i3(j  
rqf pok  
*P�oe  8  �
�i3(j  
rg pok  
*Poh  �ol  
8�   Pog  8�   (j  
r�g pok  
*Po\  (9  
,A�(m  
-(j  
r+h p�ok  
*P�~n  

(3  
(0  
(o  
o]  +~Po^  (9  
,#P�~n  

(3  
(0  
(o  
o_  +MPo`  (9  
,?�(m  
-(j  
ryh p�ok  
*P�~n  

(3  
(0  
(o  
oa  �
�i?����Po\  (9  
,(j  
r�h pop  
*Po^  (9  
,(j  
r	i pop  
**  0 7      (m  -(l  * sx  oo  �(j  
oq  
op  
� *        !  0 7    s�  
}o  {o  o^  sr  
ss  
{o  oh  ��  st  
(  +
{o  o\  rIi p(v  
8a  s�  �}p  	��  sw  
(  +:A  {p  {o  o\  (�  {o  o^  o  (y  
	o�  
sg  
%rQi p
(0  
ol  
o�  rci poz  
,?{o  o`  
(p  -$rqi p{o  o`  o}  ({  
s|  
z

%{o  oj  o}  
%o�  ~r  %-&~q  ��  s~  
%�r  (	  +o}  

(q  

	sr  
(r  
{o  o^  s�  o  
o�  -	
	(s  X�i?����(t  {o  ob  (9  
-{o  ob  {o  of  (w  {o  od  (9  
-{o  od  {o  o^  (v  * 0 \     Q{M  o�  
,*(�  

+1�(�  

	o�  
,(�  
Q{M  Po4  
+
X�i2�P�*0 W     s�  
r&j p~�  
o  
�4  %
�o�  
+~�  
��o�  ���i2�(�  
(?  
* 0 �     ~L  o�  

8�   oA  
r*j po�  
oM  
oA  
r*j po�  
oF  
o8  
oA  
r*j po�  
oC  
oi  

�oi  
	s�  
o�  
o]  
(6  
 ~L  o�  

o�  
:j���*   0 1     (�  

(m  
-(�  
&s�  
op  
�
,o+  
�*       	& 
    0 1    o�  
s�  

o�  

+G(�  
o�  o�  
,r2j po�  o�  (/  
s�  
zo�  o�  
(�  
-���
  o+  
�o�  

+u(�  
o�  (9  
,%,rmk po�  o�  (/  
s�  
z+9o�  o�  
-r,l po�  o�  (/  
s|  
zo�  &(�  
-���
  o+  
�-r�l ps�  
zo�  
s�  
(u  *       Ti      ~ �     0 6     o  
o�  o�  

+o�  
(u  o*  
-��
,o+  
�*      + 
    0 �    (o  
(�  
~n  
(3  
(0  
sr  
~n  
(3  
(0  
(o  
sr  

o�  
o]  
~s  %-&~q  ��  s�  
%�s  (
  +(  +-r�m ps�  
zr�n p(�  

r�n p(�  
r�n p(�  
r�n p(�  
(�  
r	o pop  
ro pop  
rSo p	(-  
op  
rwo pok  
r�o pok  
r�o pop  
r�o po�  (y  
(-  
op  
rp pop  
r=p pok  
rcp pop  
r�p pop  
r�p po�  (-  
op  
rL7 pop  
r�p pop  
rq po�  (y  
o�  
�,o+  
�(�  
		ryq pop  
	r�q pop  
	r�q pop  
	r�q pop  
	rir pop  
	ryr pop  
	r�r pop  
	r�r pop  
	r!s pop  
	r5s pop  
o�  

+x
(�  
o�  -f	r?s pop  
	r�s po�  (-  
op  
	r�s po�  (-  
op  
	rt po�  (y  
(-  
op  
	rYt pop  

(�  
:|����
�
  o+  
�	rqt pop  
	r}t pop  
	r�t pop  
�	,	o+  
�(�  
ryq pop  
r�q pop  
r�q pop  
r�t pop  
r.u pop  
rXu pop  
rtu pop  
r�u pop  
r!s pop  
r5s pop  
o�  
o�  

8�   
(�  
o�  ,8�   r5s pop  

�

o�  2�+rqt pop  

�

o�  0�r?s pop  
r�s po�  (-  
op  
rt po�  (y  
(-  
op  
rYt pop  

(�  
:S���� 
�
  o+  
�rqt pop  

�

o�  0�rqt pop  
rv pop  
�,o+  
�* A|     �   �   �            E  �   �            �  <              �  �   R              �  �         0 �     (�  

o�  
8�   (�  
(o  
o�  o�  
-i,*o�  r.v poR  
,o�  (�  
rLv po�  
-<
+rfv po�  
	�
	o�  2�rnv po�  o�  \/o�  
o�  
(�  
:p�����
  o+  
�,o+  
�*      ��       �� 
    Js"  
}M  (!  
*Jr�v p
s,  
�L  *J(!  
s"  
(�  *{P  *"}P  *{Q  *"}Q  *{R  *"}R  *{S  *"}S  *{T  *"}T  *{U  *"}U  * 0 �     
sz  o|  o7  
oi  
o~  o}  (�  
r�v po�  
,#o}  o}  o7  
�o8  
o�  
+o}  o�  o{  (�  
o�  
~N  o�  

	o�  
9�   	oM  
:�   ~N  	oF  
o�  
o�  
9�   	oF  
oM  
	oF  
�o8  
�4  %
�%
�o�  
+m�~O  o�  
o�  
,IoA  
r�v po�  
oC  
oD  
	oA  
r�v po�  
oC  
o�  

o�  	
o�  
X�i2�oM  
oF  
�oi  
o�  
o�  ,Qs  %o  %r� po	  s  o"  o�  
r�7 p(�  
o  
rw pr&j po  
o�  +o�  o�  *�r
w p
s,  
�N  rw ps,  
�O  *   0 ,  !  (!  
o}  (�  rXw po�  

o}  (�  (�  o{  (�  o  ~�  
~n  
o�  
(�  (�  ~u  %-&~t  ��  s�  
%�u  (  +(�  ,	(�  0(�  +(�  �(�  o�  rdw poz  
-(j  
rpw po}  ok  
%-&~�  
(�  (�  rf; prj; po  
r�[ pr*c po  
r�[ pr4c po  
(�  o�  r?x poz  
,$rGx p(�  
(�  rd p(�  
(�  ss  
}V  o�  rWx poz  
-2-rXw p
rcx p(�  +#(�  /r�x p
r�x p(�  +	(�  	,s	~�  
~n  
o�  

	rp7 poR  
,	rXw p(0  

	ry po<  
,
	oi  

+)	rp7 po<  
,
	oi  

+o}  (�  
	(y  

	(�  
	(�  *�{V  o  
o�  {V  o�  
/}W  *{X  *"}X  *{Y  *"}Y  *{Z  *"}Z  *{[  *"}[  *{\  *"}\  *{]  *"}]  *{^  *"}^  *{_  *"}_  *{`  *"}`  *{a  *"}a  *{b  *"}b  *{c  *"}c  *�{W  ,{V  o�  
}W  {V  *   0 {   "  (�  -*(�  o�  3(�  o�  o�  
*(�  ,2o�  ,*(�  (�  o�  3*o�  (�  o�  3*(�  
 o�  (�  
�* 0 '     (�  

(�  (y  
(y  
(o  
oD  
* 0 C     (�  

(�  
r�v po�  
-ry po�  
-r'y po�  
,	(�  

+�* 0 r   #  (!  
s"  
(�  o�  

+Co�  
�4  %=�o�  
(�  �o�  
-(�  ��i0+�o�  
o*  
-��
,o+  
�*      Og 
    {e  *"}e  *   0   $  (9  
:  
~d  o�  
8�   oM  
o8  

oA  
oB  
oC  
oM  
oF  
�oi  
19(j  
r1y p�
  %�%�%�,  �%oM  
֌,  �o�  
+|(�  oz  
,	(6  
+>(j  
r�y p�
  %�%�%�,  �%oM  
֌,  �o�  
oF  
~d  oM  
�o�  
o�  
:���*FrSz ps,  
�d  *0 ,   %  ,' ŝ�
+o2  
a � Z
Xo7  
2�*>}i  }j  *z}k  {l  {  oC  
oP  
* 0 �   &  oA  
oB  
oC  

r�7 poR  
-
r�7 po  
+{m  `,%{n  {n  (K  r�7 p(0  
(#  
+7{n  {n  (K  (<  
�4  %
�o�  

{n  ($  
}m  r�z p(-  
*J{o  o\  (y  
*:{p  o�  
*.s�  �q  *f(�  
r^\ p(�  
(6  
*o�  *.s�  �t  *&~n  
�*   BSJB         v2.0.50727     l   X  #~  �  �  #Strings    �<  �z  #US 4�     #GUID   D�  �  #Blob         W�		   �3      G      u   �   �      �   
   y   &      *   R                             �      �
� x� w
s �   �
� �
� �
� _� D� � �
� U
� �� _�
 �s U� �� :
� <F
 �A
 �� �` ��
 fA F
 A �\ ��g �   �� �	� '
� 8�
 �
� � F �� � F �� %� ,F ;F � � C� 
� �	� �� 1�
 �	�� �   ��
 �A u�
 �A
 BA
 �	A ��
 �A :� �� �� �� F
� �N � 0� b� �� �� s� � �    b      � RR5     �,5      �,5     �Sy A Y   0	S5 C Y   bS5 L n   �S5 N z   S5 V �   �S5 d �    k  5 f �   �	  y f �  �  } i �  ,   5 k �  B   5 m �  o   5 o �  �   5 p � ! �  5 q � ! �  5 t �  �I �I �I �I �I �� �� �I �� �� "�Q�]I I �� � �� !� t�Q��
�Q�K
�Q��IQ��I1 ��1 ��1 ��! 	�! Y�! �� G�  � 8 � �I �I � v , � f	   t	 � ) s �I � 9 �
 � � � > 0 
 �	 b $ � H � � ��V��	 �	 7I TI I �I �I � q
 �
1 � ��1 �1 � nI PI �I  |I �I � T� DI XI rI �� �� �I 4I I �I H� �� d%1  ���V��)V�
) �	) �I *� �- �	� �- <	1 
I6 ^5 9  H6 ^R X VP     � "_ �!    � 4f "    �dp R"    ��
�  Z"    �t c"    ���  k"    ��t t"    ��.  |"    ��  �"    �6�  �"    �Ut �"    ��� 	 �"    ��t	 �"    ��� 
 �"    �	t
 �"    �^  �"    �^  �"    �^t ($    �^y �$    ��. 
 �$    �� 
 �$    ���  �$    ��t �$    ���  �$    �	t �$    ���  �$    ��t �$    ��
�  %    �t %    �6�  %    �Ut %    �dp �(    �A.  �(    � ��   )    �  P)    � (�  �)    � m |*    � H  �*    � :  �*    � �V �*    � �V 
+    � s�  $+    � �� �+    � sV n,    � ��  �,    � �� �,    � H� -    � �� �-    � ��   8.    � l�! ,/    � C�" 0    � 0�# �0    � �� $ 1    � ��% 2    � �& �2    � � ' 3    � ��( p3    � �) �3    � a� * �3    � w� +  4    � 5�, �4    � 8 - 5    � �� / 45    � ��0 �5    �  � 1 �5    � ��2 �5    � �� 3 X6    � �� 4 �6    � � 5 �6    � X�6 G7    � I�7 l7    � �� 8 �7    � ��9  8    � ��: W8    � k� ; l8    � �� < 9    � K� =  9    � �> 9    � 4� ? �9    � �� @ �9    � �A �9    � �	� B �9    � F�C �9    � �� D 8:    � �� E ;    � � F �;    � �� G �<    � =�H =    �^ J 7=    �s�J ?=    ���J H=    ��. K P=    �� K Y=    ��. L a=    � L j=    ��. M r=    �� M {=    �<. N �=    �b N �=    ��. O �=    �� O �=    �� P �=    �0tP �=    ���Q �=    ���Q �=    ���R �=    ��R �=    � #pS �=    � ��S \A    � ��U �A    � ��V �C    � 5�W \D    �  �Z �D    � |�\ xE    � C�_ �E    � ��a $G    � ��b xG    � �d �L    � Yg �M    �^ j �M    �dpj �M    �^ j �M    ��. j �M    �� j �M    ��. k �M    �� k �M    �
. l �M    �)
 l �M    ��#m  N    ��-m 	N    ��. n N    �� n N    �h. o "N    �q o ,N    � 
8p "P    �dpr HP    �^?r �R    � nFt �R    �n. u �R    �u u �R    ��. v �R    �� v �R    �w. w �R    �� w �R    �
C x �R    ��
 x �R    ��
C y �R    ��
 y  S    �
. z S    �)
 z S    ��. { S    �� { "S    ��. | *S    �� | 3S    �g. } ;S    �y } DS    �$� ~ LS    �3t~ US    ���  ]S    ��t fS    ��M� nS    ��R� wS    �JX� �S    ��b� $T    � |8 � XT    � q� � �T    �^h� 8U    ��#� @U    �-� LU    � �r� jV    �dp� |V    � ry� �V    �^~� �"    �^ � �V    � � �� �"    �^ � �V    � � �� �"    �^ � �W    � � � � �"    �^ � �W    � � > � �W    �dp� �"    �^ � �W    � �� �W    �  �� �W    �dp� �"    �^ � �W    � a ��    0   h   h  �   �   �   �   �   �   �   "   �   �   �   �   �   �   �   �   � �   �   � �   �   l   �   l   �   �
   �   �   l   l   l   �   l   l   �   l   l   �   �   l      L   �   l   �   l   �   �   �   l   l   �   l   l   �
   z   b   l   �   �   l   �   l   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   h  <	   h   <	   �   +	  �   �   �   �   �
   �
   �   @
   �   j   �   �   �      �      �   �   �   �   �   �   �   
   �   �      a   �   �   �   �   �   �   �   �   �   �   �   �   F      +	   +	   �   �   '	      �   �   �	   �   l   l   �   k   �   .   l	  	 ^  ^  ^
 ) ^ 1 ^ 9 ^ A ^ I ^ Q ^ Y ^ a ^ � ^ ^ q f. y 2 y �. Y08 y . y 	. y M. Y8> � ^ � �C � �C Y�G A �. I �. 1 B. Q �. ) �. io q Dx i ^  ^ yX� ��� ��� � �� Y�� � �� ��� � S� � 
 � ^� Y�� Y�� Y�� Y�� Y4� Y�� �4.  �� � �	� Y�� YU
C YJY�� � 08 � Yt
> � ^� 0Y& Z � �1��7��. YQ.  �� �U
C a4. i VC �oQ$ ^ � �n��� ��C $ �u S� ��� 0� Yk
> � d� � ��� 0�� 0�� 0�� ^ � ^ � W�� ��� ��i 4. ���� � $ �, �", S� � ^ � �3� `
 � U
C 4 ^ �H	EYJQ��V!H	\4 �u�� �cb��� !H	 	�. ^ < ^ D ^����P�L ^����^	� T ��Y��	^ 4 �\ ^< �u ���P3�%� Y�9�� Y�IYL)R	V� fg��m^t�
|� f���� �	� ���1^ < �C d ^ < �l �"d ��9^ d �� l S� < ^ t ��| �"� ^��	�B7�� �D!H	KzDY�> ��� Yt
9!
 Y0b!H	h�� Y�. T �� �`b� ^���< � Y��a��� ��� �"Y�T S�!H	�Y��� @"� � 0 � L � P � T � U � X � a � � �� ��.  �.  �.  �. # �. + #. 3 ). ; :. C O. K �. S #. [ �� c �� c �� c �� c �� c �� c � c �c � c �!c �@c �Ac �`c �ac �cc ��c ��c ��c ��c ��c ��c �c �#c �Cc �cc �ac ��c ��c ��c ��c �	c �!	c �A	c �a	c �
c �!
c �A
c �a
c ��
c ��
c �c �!c �@c �Ac �`c �ac ��c ��c ��c ��c ��c ��c ��c ��c � c �c � c �!c �@c �Ac �`c �ac ��c ��c ��c ��c ��c � 
c � 
c �@
c �`
c ��
k �`c ��c ��c ��c ��c � c � c �@c �`c ��c ��c ��c �`c ��c ��c ��c ��c � c � c �@c �`c ��c ��c ��c ��c � c � c �@c �`c ��c ��c ��c ��c � c � c �@c � c � c � g � � 
->GLV{������'9Jen+AZ����So������        	  
 *   �  ��  �  Y�  ��  
�  �  ��  
�  ��  �  Y�  U�  ��  ��  
�  ��  f�  ��  4�  ��  *�  ��  ��  -
�  ��  ��  ��  ��  ��  ��  �
�  �
�  -
�  ��  ��  }�  7�  ��  ��  W�  *�                	   
 	   	     
    
   
                                      !   Z   [   \   ]   ^ !  _ !  ` #  a #  b %  c %  d '  e '  f )  g )  h +  i +  j -  k -  { /  | /  } 1  ~ 1   3  � 3  � 5  � 5  � 7  � 7  � 9  � 9  � ;  � ;  � =  � =  � ?  � ?  � A  � A  � C  � C  � E  � E  � G  � G  � I  � I  � K  � K  � M  � M  � O  � O  � Q  � Q  � S  � U  � U  l � g?�������������            b             �               �              �	               �               
             	      � N  S  X  ]  b � �� �� ��  ;2=2]� E      <>9__10_0 <GenerateHtmlHelpProject>b__10_0 <>c__DisplayClass51_0 <>c__DisplayClass92_0 <>9__2_0 <.ctor>b__2_0 <>c__DisplayClass3_0 <Run>b__0 <FormParagraphs>b__0 <ProcessListItems>b__0 <>c__DisplayClass3_1 <Run>b__1 IComparable`1 IEnumerable`1 IEnumerator`1 List`1 Int32 <>9__3_2 <Run>b__3_2 Func`2 KeyValuePair`2 IDictionary`2 BlockQuoteEvaluator2 <>9 <Module> <PrivateImplementationDetails> TokenizeHTML _markerOL _markerUL System.IO T value__ get_Meta set_Meta meta mscorlib <>c EscapeBoldItalic get_StrictBoldItalic set_StrictBoldItalic _strictBoldItalic _strictItalic _italic System.Collections.Generic afterDoc doc get_Id set_Id GenerateId get_AfterId set_AfterId Add ordered get_Ignored set_Ignored get_ChmIgnored set_ChmIgnored _listNested System.Collections.Specialized <Meta>k__BackingField <StrictBoldItalic>k__BackingField <Id>k__BackingField <AfterId>k__BackingField <Ignored>k__BackingField <ChmIgnored>k__BackingField <TitleHtmlSafe>k__BackingField <AppendMarkdownTableOfContentsFile>k__BackingField <HtmlHelpProjectFile>k__BackingField <Title>k__BackingField <SourcePath>k__BackingField <RelativePath>k__BackingField <FullPath>k__BackingField <RelativeOutputPath>k__BackingField <Depth>k__BackingField <RealDepth>k__BackingField <AutoHyperlink>k__BackingField <Operation>k__BackingField <LayoutsFolder>k__BackingField <InputFolder>k__BackingField <OutputFolder>k__BackingField <OriginalAfter>k__BackingField <Variables>k__BackingField <AutoNewlines>k__BackingField <LinkEmails>k__BackingField <EncodeProblemUrlCharacters>k__BackingField <IgnoreXsdSimpleTypeInTableOfContents>k__BackingField <ChmDefault>k__BackingField <Parent>k__BackingField <Content>k__BackingField <Text>k__BackingField <EmptyElementSuffix>k__BackingField DoItalicsAndBold _strictBold _bold TrimEnd Append WixBuild.Tools.DocCompiler.Xsd.docCompiler.xsd Replace _leadingWhitespace EncodeCode GetHashCode code get_TitleHtmlSafe set_TitleHtmlSafe get_Message AddRange Invoke _invertedEscapeTable _backslashEscapeTable _escapeTable Enumerable IDisposable RuntimeTypeHandle GetTypeFromHandle tocFile loadOptionsFromConfigFile get_AppendMarkdownTableOfContentsFile set_AppendMarkdownTableOfContentsFile get_HtmlHelpProjectFile set_HtmlHelpProjectFile projectFile Compile Console _newlinesMultiple get_Title set_Title GetFileName get_ProductName GetDirectoryName filename CommandLine commandLine WriteLine get_NewLine Combine _anchorInline _imagesInline lastItemHadADoubleNewline Escape Unescape ValueType TokenType type _autolinkBare Where System.Core Capture NameObjectCollectionBase Dispose Create Write _blockquote STAThreadAttribute CompilerGeneratedAttribute NeutralResourcesLanguageAttribute DebuggableAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute Substitute Byte get_Value TryGetValue value DocCompiler.exe Normalize IndexOf _linkDef _anchorRef _imagesRef Tag _newlinesLeadingTrailing keepGoing ToString RepeatString Substring requiresSorting IsMatch match ComputeStringHash _htmlBlockHash unhash Math get_SourcePath set_SourcePath get_RelativePath set_RelativePath get_FullPath set_FullPath GetFullPath documentPath get_RelativeOutputPath set_RelativeOutputPath outputPath _tabWidth get_Length set_Length EndsWith StartsWith get_Depth set_Depth get_RealDepth set_RealDepth _nestDepth MakeRelativeUri relativeUri outputUri _codeBlock isHtmlBlock block get_AutoHyperlink set_AutoHyperlink _autoHyperlink original _listTopLevel _listLevel Microsoft.Tools.WindowsInstallerXml _blocksHtml VSExtensionsLandingUrl NewsUrl SupportUrl url get_Item set_Item System Trim Random Transform Enum ToBoolean _codeSpan Token Main Join ChangeExtension GetExtension GetFileNameWithoutExtension get_Version get_FileVersion _version get_Location get_Operation set_Operation CommandLineOperation System.Configuration System.Reflection NameValueCollection MatchCollection GroupCollection KeysCollection SearchOption ApplicationException ArgumentException get_Description WixDistribution StringComparison GetBlockPattern GetNestedParensPattern _nestedParensPattern GetNestedBracketsPattern _nestedBracketsPattern Run Markdown CompareTo FileVersionInfo GetVersionInfo DirectoryInfo ShowHelp MarkdownSharp Cleanup Group Setup System.Linq Clear AltDirectorySeparatorChar addr lineNumber StringBuilder get_LayoutsFolder set_LayoutsFolder layoutFolder get_InputFolder set_InputFolder inputFolder get_OutputFolder set_OutputFolder outputFolder _codeEncoder ConfigurationManager other marker WixBuild.Tools.DocCompiler AddAfter get_OriginalAfter set_OriginalAfter StreamWriter TextWriter dir get_Major get_Minor get_Error IEnumerator GetEnumerator ImageReferenceEvaluator EncodeCodeEvaluator ImageInlineEvaluator AnchorInlineEvaluator UnescapeEvaluator BlockQuoteEvaluator AnchorRefEvaluator MatchEvaluator CodeBlockEvaluator LinkEvaluator HyperlinkEvaluator EmailEvaluator HtmlEvaluator CodeSpanEvaluator SetextHeaderEvaluator AtxHeaderEvaluator EscapeBackslashesEvaluator ListEvaluator AnchorRefShortcutEvaluator .ctor .cctor str Abs System.Diagnostics indexedDocs System.Runtime.CompilerServices System.Resources DebuggingModes DoImages Matches EscapeBackslashes get_Variables set_Variables ParseVariables SubstituteVariables EncodeAmpsAndAngles _angles GetFiles _titles DoHorizontalRules _horizontalRules get_AutoNewLines set_AutoNewLines defines get_AutoNewlines set_AutoNewlines _autoNewlines _backslashEscapes _unescapes ignoreXsdSimpleTypes DoBlockQuotes EscapeSpecialCharsWithinTagAttributes GetCustomAttributes get_AppSettings args FormParagraphs FixRelativePaths <>4__this DoHardBreaks HashHTMLBlocks DoCodeBlocks _htmlBlocks DoAutoLinks Equals get_LinkEmails set_LinkEmails _linkEmails _urls ProcessListItems DoCodeSpans _htmlTokens Contains System.Text.RegularExpressions System.Collections StripLinkDefinitions MarkdownOptions StringSplitOptions RegexOptions options VariableSubstitutions _amps get_Groups get_Chars EncodeProblemUrlChars _problemUrlChars DoHeaders ReplacePlaceholders get_EncodeProblemUrlCharacters set_EncodeProblemUrlCharacters _encodeProblemUrlCharacters DoAnchors get_Success EncodeEmailAddress get_Comments TraverseIndexedDocuments OrderIndexedDocuments TryParseArguments get_IgnoreXsdSimpleTypeInTableOfContents set_IgnoreXsdSimpleTypeInTableOfContents AppendMarkdownTableOfContents DoLists Exists layouts get_Keys Concat AppendFormat TelemetryUrlFormat Object GenerateHtmlHelpProject Select get_Product ShortProduct get_Copyright get_LegalCopyright Split get_ChmDefault set_ChmDefault FirstOrDefault ToLowerInvariant _outDent Outdent Environment IndexedDocument document get_Parent set_Parent get_Current get_Content set_Content content get_Count count Convert Sort _wholeList list _anchorRefShortcut RunBlockGamut RunSpanGamut TryLoadLayout Output get_Next MoveNext System.Text get_Text set_Text AppendText CreateText ReadAllText next _headerSetext get_Index MetaAreaRegex RelativeUriRegex KeyValuesRegex get_EmptyElementSuffix set_EmptyElementSuffix _emptyElementSuffix _headerAtx ToCharArray get_Key GetHashKey ContainsKey Assembly assembly GetFilenameOnly Any get_Company CreateDirectory op_Equality IsNullOrEmpty   [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /    / >  -M a r k d o w n . A u t o H y p e r l i n k  +M a r k d o w n . A u t o N e w l i n e s  7M a r k d o w n . E m p t y E l e m e n t S u f f i x  GM a r k d o w n . E n c o d e P r o b l e m U r l C h a r a c t e r s  'M a r k d o w n . L i n k E m a i l s  3M a r k d o w n . S t r i c t B o l d I t a l i c  ^ \ n + | \ n + \ z  
\ n { 2 , }  ^ [   ] *  
 H \ d + H ��
 
                                                 ^ [   ] { { 0 , { 0 } } } \ [ ( . + ) \ ] :     #   i d   =   $ 1 
 
                                                     [   ] * 
 
                                                     \ n ?                                       #   m a y b e   * o n e *   n e w l i n e 
 
                                                     [   ] * 
 
                                                 < ? ( \ S + ? ) > ?                             #   u r l   =   $ 2 
 
                                                     [   ] * 
 
                                                     \ n ?                                       #   m a y b e   o n e   n e w l i n e 
 
                                                     [   ] * 
 
                                                 ( ? : 
 
                                                         ( ? < = \ s )                           #   l o o k b e h i n d   f o r   w h i t e s p a c e 
 
                                                         [ " ( ] 
 
                                                         ( . + ? )                               #   t i t l e   =   $ 3 
 
                                                         [ " ) ] 
 
                                                         [   ] * 
 
                                                 ) ?                                             #   t i t l e   i s   o p t i o n a l 
 
                                                 ( ? : \ n + | \ Z )  ��
 
                         ( < ! ( ? : - - . * ? - - \ s * ) + > ) |                 #   m a t c h   < ! - -   f o o   - - > 
 
                         ( < \ ? . * ? \ ? > ) |                                   #   m a t c h   < ? f o o ? >   M  
 
                         ( < [ A - Z a - z \ / ! $ ] ( ? : [ ^ < > ] | 	) * > )  3  #   m a t c h   < t a g >   a n d   < / t a g >  �o
 
                         (                                                               #   w r a p   w h o l e   m a t c h   i n   $ 1 
 
                                 \ [ 
 
                                         ( { 0 } )                                       #   l i n k   t e x t   =   $ 2 
 
                                 \ ] 
 
 
 
                                 [   ] ?                                                 #   o n e   o p t i o n a l   s p a c e 
 
                                 ( ? : \ n [   ] * ) ?                                   #   o n e   o p t i o n a l   n e w l i n e   f o l l o w e d   b y   s p a c e s 
 
 
 
                                 \ [ 
 
                                         ( . * ? )                                       #   i d   =   $ 3 
 
                                 \ ] 
 
                         )  �+
 
                                 (                                                       #   w r a p   w h o l e   m a t c h   i n   $ 1 
 
                                         \ [ 
 
                                                 ( { 0 } )                               #   l i n k   t e x t   =   $ 2 
 
                                         \ ] 
 
                                         \ (                                             #   l i t e r a l   p a r e n 
 
                                                 [   ] * 
 
                                                 ( { 1 } )                               #   h r e f   =   $ 3 
 
                                                 [   ] * 
 
                                                 (                                       #   $ 4 
 
                                                 ( [ ' " ] )                       #   q u o t e   c h a r   =   $ 5 
 
                                                 ( . * ? )                               #   t i t l e   =   $ 6 
 
                                                 \ 5                                     #   m a t c h i n g   q u o t e 
 
                                                 [   ] *                                 #   i g n o r e   a n y   s p a c e s   b e t w e e n   c l o s i n g   q u o t e   a n d   ) 
 
                                                 ) ?                                     #   t i t l e   i s   o p t i o n a l 
 
                                         \ ) 
 
                                 ) ��
 
                         (                                                               #   w r a p   w h o l e   m a t c h   i n   $ 1 
 
                             \ [ 
 
                                   ( [ ^ \ [ \ ] ] + )                                   #   l i n k   t e x t   =   $ 2 ;   c a n ' t   c o n t a i n   [   o r   ] 
 
                             \ ] 
 
                         ) �S
 
                                         (                               #   w r a p   w h o l e   m a t c h   i n   $ 1 
 
                                         ! \ [ 
 
                                                 ( . * ? )               #   a l t   t e x t   =   $ 2 
 
                                         \ ] 
 
 
 
                                         [   ] ?                         #   o n e   o p t i o n a l   s p a c e 
 
                                         ( ? : \ n [   ] * ) ?           #   o n e   o p t i o n a l   n e w l i n e   f o l l o w e d   b y   s p a c e s 
 
 
 
                                         \ [ 
 
                                                 ( . * ? )               #   i d   =   $ 3 
 
                                         \ ] 
 
 
 
                                         )  �{
 
                             (                                           #   w r a p   w h o l e   m a t c h   i n   $ 1 
 
                                 ! \ [ 
 
                                         ( . * ? )                       #   a l t   t e x t   =   $ 2 
 
                                 \ ] 
 
                                 \ s ?                                   #   o n e   o p t i o n a l   w h i t e s p a c e   c h a r a c t e r 
 
                                 \ (                                     #   l i t e r a l   p a r e n 
 
                                         [   ] * 
 
                                         ( { 0 } )                       #   h r e f   =   $ 3 
 
                                         [   ] * 
 
                                         (                               #   $ 4 
 
                                         ( [ ' " ] )               #   q u o t e   c h a r   =   $ 5 
 
                                         ( . * ? )                       #   t i t l e   =   $ 6 
 
                                         \ 5                             #   m a t c h i n g   q u o t e 
 
                                         [   ] * 
 
                                         ) ?                             #   t i t l e   i s   o p t i o n a l 
 
                                 \ ) 
 
                             ) �M
 
                                 ^ ( . + ? ) 
 
                                 [   ] * 
 
                                 \ n 
 
                                 ( = + | - + )           #   $ 1   =   s t r i n g   o f   = ' s   o r   - ' s 
 
                                 [   ] * 
 
                                 \ n + ��
 
                                 ^ ( \ # { 1 , 6 } )     #   $ 1   =   s t r i n g   o f   # ' s 
 
                                 [   ] * 
 
                                 ( . + ? )               #   $ 2   =   H e a d e r   t e x t 
 
                                 [   ] * 
 
                                 \ # *                   #   o p t i o n a l   c l o s i n g   # ' s   ( n o t   c o u n t e d ) 
 
                                 \ n + �c
 
                         ^ [   ] { 0 , 3 }                   #   L e a d i n g   s p a c e 
 
                                 ( [ - * _ ] )               #   $ 1 :   F i r s t   m a r k e r 
 
                                 ( ? >                       #   R e p e a t e d   m a r k e r   g r o u p 
 
                                         [   ] { 0 , 2 }     #   Z e r o ,   o n e ,   o r   t w o   s p a c e s . 
 
                                         \ 1                 #   M a r k e r   c h a r a c t e r 
 
                                 ) { 2 , }                   #   G r o u p   r e p e a t e d   a t   l e a s t   t w i c e 
 
                                 [   ] *                     #   T r a i l i n g   s p a c e s 
 
                                 $                           #   E n d   o f   l i n e . 
 
                         ��
 
                         (                                                               #   $ 1   =   w h o l e   l i s t 
 
                             (                                                           #   $ 2 
 
                                 [   ] { { 0 , { 1 } } } 
 
                                 ( { 0 } )                                               #   $ 3   =   f i r s t   l i s t   i t e m   m a r k e r 
 
                                 [   ] + 
 
                             ) 
 
                             ( ? s : . + ? ) 
 
                             (                                                           #   $ 4 
 
                                     \ z 
 
                                 | 
 
                                     \ n { { 2 , } } 
 
                                     ( ? = \ S ) 
 
                                     ( ? !                                               #   N e g a t i v e   l o o k a h e a d   f o r   a n o t h e r   l i s t   i t e m   m a r k e r 
 
                                         [   ] * 
 
                                         { 0 } [   ] + 
 
                                     ) 
 
                             ) 
 
                         )  ( ? : { 0 } | { 1 } )  [ * + - ] 
\ d + [ . ]  ^  '( ? : ( ? < = \ n \ n ) | \ A \ n ? )  �y
 
                                         ( ? : \ n \ n | \ A \ n ? ) 
 
                                         (                                                 #   $ 1   =   t h e   c o d e   b l o c k   - -   o n e   o r   m o r e   l i n e s ,   s t a r t i n g   w i t h   a   s p a c e 
 
                                         ( ? : 
 
                                                 ( ? : [   ] { { { 0 } } } )               #   L i n e s   m u s t   s t a r t   w i t h   a   t a b - w i d t h   o f   s p a c e s 
 
                                                 . * \ n + 
 
                                         ) + 
 
                                         ) 
 
                                         ( ( ? = ^ [   ] { { 0 , { 0 } } } \ S ) | \ Z )   #   L o o k a h e a d   f o r   n o n - s p a c e   a t   l i n e - s t a r t ,   o r   e n d   o f   d o c �
 
                                         ( ? < ! \ \ )       #   C h a r a c t e r   b e f o r e   o p e n i n g   `   c a n ' t   b e   a   b a c k s l a s h 
 
                                         ( ` + )             #   $ 1   =   O p e n i n g   r u n   o f   ` 
 
                                         ( . + ? )           #   $ 2   =   T h e   c o d e   b l o c k 
 
                                         ( ? < ! ` ) 
 
                                         \ 1 
 
                                         ( ? ! ` ) M( \ * \ * | _ _ )   ( ? = \ S )   ( . + ? [ * _ ] * )   ( ? < = \ S )   \ 1  s( [ \ W _ ] | ^ )   ( \ * \ * | _ _ )   ( ? = \ S )   ( [ ^ \ r ] * ? \ S [ \ * _ ] * )   \ 2   ( [ \ W _ ] | $ )  =( \ * | _ )   ( ? = \ S )   ( . + ? )   ( ? < = \ S )   \ 1  g( [ \ W _ ] | ^ )   ( \ * | _ )   ( ? = \ S )   ( [ ^ \ r \ * _ ] * ? \ S )   \ 2   ( [ \ W _ ] | $ )  ��
 
                         (                                                       #   W r a p   w h o l e   m a t c h   i n   $ 1 
 
                                 ( 
 
                                 ^ [   ] * > [   ] ?                             #   ' > '   a t   t h e   s t a r t   o f   a   l i n e 
 
                                         . + \ n                                 #   r e s t   o f   t h e   f i r s t   l i n e 
 
                                 ( . + \ n ) *                                   #   s u b s e q u e n t   c o n s e c u t i v e   l i n e s 
 
                                 \ n *                                           #   b l a n k s 
 
                                 ) + 
 
                         ) ��( ^ | \ s ) ( h t t p s ? | f t p ) ( : / / [ - A - Z 0 - 9 + & @ # / % ? = ~ _ | \ [ \ ] \ ( \ ) ! : , \ . ; ] * [ - A - Z 0 - 9 + & @ # / % = ~ _ | \ [ \ ] ] ) ( $ | \ W ) ^ [   ] { 1 ,  }  3& | < | > | \ \ | \ * | _ | \ { | \ } | \ [ | \ ]  s& ( ? ! ( # [ 0 - 9 ] + ) | ( # [ x X ] [ a - f A - F 0 - 9 ] ) | ( [ a - z A - Z ] [ a - z A - Z 0 - 9 ] * ) ; ) %< ( ? ! [ A - Z a - z / ? \ $ ! ] ) 
 E \ d + E " ' * ( ) [ ] $ : _  !\ ` * _ { } [ ] ( ) > # + - . ! \  |  	1 . 1 3  
   H < p >  	< / p >  
 
  ��
 
                                         ( ? >                             #   A t o m i c   m a t c h i n g 
 
                                               [ ^ \ [ \ ] ] +             #   A n y t h i n g   o t h e r   t h a n   b r a c k e t s 
 
                                           | 
 
                                               \ [ 
 
                                                        7  \ ] 
 
                                         ) *  ��
 
                                         ( ? >                             #   A t o m i c   m a t c h i n g 
 
                                               [ ^ ( ) \ s ] +             #   A n y t h i n g   o t h e r   t h a n   p a r e n s   o r   w h i t e s p a c e 
 
                                           | 
 
                                               \ ( 
 
                                                        7  \ ) 
 
                                         ) *  "  
& q u o t ;  i n s | d e l  ��p | d i v | h [ 1 - 6 ] | b l o c k q u o t e | p r e | t a b l e | d l | o l | u l | a d d r e s s | s c r i p t | n o s c r i p t | f o r m | f i e l d s e t | i f r a m e | m a t h �
 
                         ( ? > 	 	 	 	                         #   o p t i o n a l   t a g   a t t r i b u t e s 
 
                             \ s 	 	 	                         #   s t a r t s   w i t h   w h i t e s p a c e 
 
                             ( ? > 
 
                                 [ ^ > " / ] + 	                         #   t e x t   o u t s i d e   q u o t e s 
 
                             | 
 
                                 / + ( ? ! > ) 	 	                         #   s l a s h   n o t   f o l l o w e d   b y   > 
 
                             | 
 
                                 " [ ^ " ] * " 	 	                 #   t e x t   i n s i d e   d o u b l e   q u o t e s   ( t o l e r a t e   > ) 
 
                             | 
 
                                 ' [ ^ ' ] * ' 	                                 #   t e x t   i n s i d e   s i n g l e   q u o t e s   ( t o l e r a t e   > ) 
 
                             ) * 
 
                         ) ? 	 
 
                         �[
 
                                 ( ? > 
 
                                     [ ^ < ] + 	 	 	                 #   c o n t e n t   w i t h o u t   t a g 
 
                                 | 
 
                                     < \ 2 	 	 	                 #   n e s t e d   o p e n i n g   t a g 
 
                                          ��              #   a t t r i b u t e s 
 
                                     ( ? > 
 
                                             / > 
 
                                     | 
 
                                             >  . * ?  ��
 
                                             < / \ 2 \ s * > 	                 #   c l o s i n g   n e s t e d   t a g 
 
                                     ) 
 
                                     | 	 	 	 	 
 
                                     < ( ? ! / \ 2 \ s * >                       #   o t h e r   t a g s   w i t h   a   d i f f e r e n t   n a m e 
 
                                     ) 
 
                                 ) *  \ 2  \ 3  ��
 
                         ( ? > 
 
                                     ( ? > 
 
                                         ( ? < = \ n )           #   S t a r t i n g   a f t e r   a   b l a n k   l i n e 
 
                                         |                       #   o r 
 
                                         \ A \ n ?               #   t h e   b e g i n n i n g   o f   t h e   d o c 
 
                                     ) 
 
                                     (                           #   s a v e   i n   $ 1 
 
 
 
                                         #   M a t c h   f r o m   ` \ n < t a g > `   t o   ` < / t a g > \ n ` ,   h a n d l i n g   n e s t e d   t a g s   
 
                                         #   i n   b e t w e e n . 
 
                                             
 
                                                 [   ] { 0 , $ l e s s _ t h a n _ t a b } 
 
                                                 < ( $ b l o c k _ t a g s _ b _ r e )       #   s t a r t   t a g   =   $ 2 
 
                                                 $ a t t r >                                 #   a t t r i b u t e s   f o l l o w e d   b y   >   a n d   \ n 
 
                                                 $ c o n t e n t                             #   c o n t e n t ,   s u p p o r t   n e s t i n g 
 
                                                 < / \ 2 >                                   #   t h e   m a t c h i n g   e n d   t a g 
 
                                                 [   ] *                                     #   t r a i l i n g   s p a c e s 
 
                                                 ( ? = \ n + | \ Z )                         #   f o l l o w e d   b y   a   n e w l i n e   o r   e n d   o f   d o c u m e n t 
 
 
 
                                     |   #   S p e c i a l   v e r s i o n   f o r   t a g s   o f   g r o u p   a . 
 
 
 
                                                 [   ] { 0 , $ l e s s _ t h a n _ t a b } 
 
                                                 < ( $ b l o c k _ t a g s _ a _ r e )       #   s t a r t   t a g   =   $ 3 
 
                                                 $ a t t r > [   ] * \ n                     #   a t t r i b u t e s   f o l l o w e d   b y   > 
 
                                                 $ c o n t e n t 2                           #   c o n t e n t ,   s u p p o r t   n e s t i n g 
 
                                                 < / \ 3 >                                   #   t h e   m a t c h i n g   e n d   t a g 
 
                                                 [   ] *                                     #   t r a i l i n g   s p a c e s 
 
                                                 ( ? = \ n + | \ Z )                         #   f o l l o w e d   b y   a   n e w l i n e   o r   e n d   o f   d o c u m e n t 
 
                                             
 
                                     |   #   S p e c i a l   c a s e   j u s t   f o r   < h r   / > .   I t   w a s   e a s i e r   t o   m a k e   a   s p e c i a l   
 
                                         #   c a s e   t h a n   t o   m a k e   t h e   o t h e r   r e g e x   m o r e   c o m p l i c a t e d . 
 
                                     
 
                                                 [   ] { 0 , $ l e s s _ t h a n _ t a b } 
 
                                                 < ( h r )                                   #   s t a r t   t a g   =   $ 2 
 
                                                 $ a t t r                                   #   a t t r i b u t e s 
 
                                                 / ? >                                       #   t h e   m a t c h i n g   e n d   t a g 
 
                                                 [   ] * 
 
                                                 ( ? = \ n { 2 , } | \ Z )                   #   f o l l o w e d   b y   a   b l a n k   l i n e   o r   e n d   o f   d o c u m e n t 
 
                                     
 
                                     |   #   S p e c i a l   c a s e   f o r   s t a n d a l o n e   H T M L   c o m m e n t s : 
 
                                     
 
                                             [   ] { 0 , $ l e s s _ t h a n _ t a b } 
 
                                             ( ? s : 
 
                                                 < ! - -   . * ?   - - > 
 
                                             ) 
 
                                             [   ] * 
 
                                             ( ? = \ n { 2 , } | \ Z )                         #   f o l l o w e d   b y   a   b l a n k   l i n e   o r   e n d   o f   d o c u m e n t 
 
                                     
 
                                     |   #   P H P   a n d   A S P - s t y l e   p r o c e s s o r   i n s t r u c t i o n s   ( < ?   a n d   < % ) 
 
                                     
 
                                             [   ] { 0 , $ l e s s _ t h a n _ t a b } 
 
                                             ( ? s : 
 
                                                 < ( [ ? % ] )                                 #   $ 2 
 
                                                 . * ? 
 
                                                 \ 2 > 
 
                                             ) 
 
                                             [   ] * 
 
                                             ( ? = \ n { 2 , } | \ Z )                         #   f o l l o w e d   b y   a   b l a n k   l i n e   o r   e n d   o f   d o c u m e n t 
 
                                             
 
                                     ) 
 
                         ) $ l e s s _ t h a n _ t a b  !$ b l o c k _ t a g s _ b _ r e  !$ b l o c k _ t a g s _ a _ r e  $ a t t r  $ c o n t e n t 2  $ c o n t e n t   < a   h r e f = "    t i t l e = "  >  	< / a >  [   ] * \ n [   ] *     <  < a   h r e f = " { 0 } "    t i t l e = " { 0 } "  > { 0 } < / a >  1< i m g   s r c = " { 0 } "   a l t = " { 1 } "  =  %< h { 1 } > { 0 } < / h { 1 } > 
 
  < h r  o l  u l  !< { 0 } > 
 { 1 } < / { 0 } > 
  \ n { 2 , } \ z  ��( ^ [   ] * )                                         #   l e a d i n g   w h i t e s p a c e   =   $ 1 
 
                                 ( { 0 } )   [   ] +                                   #   l i s t   m a r k e r   =   $ 2 
 
                                 ( ( ? s : . + ? )                                     #   l i s t   i t e m   t e x t   =   $ 3 
 
                                 ( \ n + ) )             
 
                                 ( ? =   ( \ z   |   \ 1   ( { 0 } )   [   ] + ) )  
 
 < p r e > < c o d e >  !
 < / c o d e > < / p r e > 
 
  [   ] * $  
< c o d e >  < / c o d e >  /$ 1 < s t r o n g > $ 3 < / s t r o n g > $ 4  $ 1 < e m > $ 3 < / e m > $ 4  '< s t r o n g > $ 2 < / s t r o n g >  < e m > $ 2 < / e m >  \ n  < b r { 0 } 
    { 2 , } \ n  ^ [   ] * > [   ] ?  
^ [   ] + $       '( \ s * < p r e > . + ? < / p r e > )  A< b l o c k q u o t e > 
 { 0 } 
 < / b l o c k q u o t e > 
 
  ^      $ 1 < $ 2 $ 3 > $ 4  5< ( ( h t t p s ? | f t p ) : [ ^ ' " > \ s ] + ) > ��< 
 
                                             ( ? : m a i l t o : ) ? 
 
                                             ( 
 
                                                 [ - . \ w ] + 
 
                                                 \ @ 
 
                                                 [ - a - z 0 - 9 ] + ( \ . [ - a - z 0 - 9 ] + ) * \ . [ a - z ] + 
 
                                             ) 
 
                                             > +< a   h r e f = " { 0 } " > { 0 } < / a >  m a i l t o :  
" > . + ? :  " >  & # x { 0 : x } ;  
& # { 0 } ;  &  & a m p ;  	& l t ;  	& g t ;  *  _  %  { 0 : x }  '( ? < = . ) < / ? c o d e > ( ? = . )  `  ��d o c c o m p i l e r . e x e   [ - ? ]   [ - h t m l h e l p   p r o j e c t   f i l e ]   i n p u t F o l d e r   o u t p u t F o l d e r   [ l a y o u t F o l d e r ] ?  	h e l p  d  
d e f i n e  a p p e n d m d t o c  h h  h t m l h e l p  
i g n o r e  1i g n o r e x s d s i m p l e t y p e i n t o c  ��M i s s i n g   v a r i a b l e   d e f i n i t i o n   f o r   ' - { 0 } '   o p t i o n .   P r o v i d e   a   v a r i a b l e   d e f i n i t i o n   i n   t h e   f o r m   o f :   n a m e   o r   n a m e = v a l u e . ��M i s s i n g   f i l e n a m e   f o r   ' - { 0 } '   o p t i o n .   P r o v i d e   a   f i l e   t o   a p p e n d   t h e   t a b l e   o f   c o n t e n t s   i n   M a r k d o w n   f o r m a t . ��M i s s i n g   f i l e n a m e   f o r   ' - { 0 } '   o p t i o n .   P r o v i d e   a   f i l e   t o   o u t p u t   t h e   h t m l   h e l p   p r o j e c t . ��M i s s i n g   d i r e c t o r y   f o r   ' - { 0 } '   o p t i o n .   P r o v i d e   a   d i r e c t o r y   t o   i g n o r e   w h e n   p r o c e s s i n g   d o c u m e n t s . SU n r e c o g n i z e d   c o m m a n d l i n e   p a r a m e t e r   ' { 0 } ' . MI n p u t   f o l d e r   ' { 0 } '   c o u l d   n o t   b e   f o u n d . QL a y o u t s   f o l d e r   ' { 0 } '   c o u l d   n o t   b e   f o u n d . =I n p u t   f o l d e r   m u s t   b e   p r o v i d e d .  ?O u t p u t   f o l d e r   m u s t   b e   p r o v i d e d .  * . *  c o n t e n t =  
l a y o u t  ��E r r o r   c o u l d   n o t   f i n d   l a y o u t :   { 0 }   i n   t h e   l a y o u t   f o l d e r :   { 1 }   w h i l e   p r o c e s s i n g   d o c u m e n t :   { 2 }  
  u r i  �9D o c u m e n t :   { 0 }   a n d   d o c u m e n t :   { 1 }   g e n e r a t e   s a m e   i d e n t i f i e r .   C h a n g e   o n e   o f   t h e   f i l e   n a m e s   o r   t r y i n g   c l e a n i n g   a n d   b u i l d i n g   a g a i n   i f   y o u   r e c e n t l y   r e n a m e d   a   f i l e .  ��F o u n d   m u l t i p l e   r o o t   d o c u m e n t s   a t :   { 0 }   a n d   { 1 } .   O n l y   o n e   d o c u m e n t   c a n   b e   t h e   r o o t   ' i n d e x '   f i l e . ��E r r o r   i n   d o c u m e n t :   { 0 }   c a n n o t   f i n d   m a t c h i n g   d o c u m e n t   f o r   m e t a d a t a :   a f t e r = { 1 }  �C a n n o t   f i n d   r o o t   d o c u m e n t .   T h e r e   m u s t   b e   o n e   a n d   o n l y   o n e   d o c u m e n t   n a m e d   ' i n d e x '   i n   t h e   r o o t   t h a t   i s   n o t   a f t e r   a n y   o t h e r   d o c u m e n t . �C a n n o t   f i n d   d e f a u l t   d o c u m e n t .   T h e r e   m u s t   b e   o n e   a n d   o n l y   o n e   d o c u m e n t   w i t h   m e t a   ' c h m :   d e f a u l t '   i n   s e t   o f   d o c u m e n t s   c o m p i l e d   i n t o   a   . c h m . 	. c h m  	. h h k  	. h h c  	. l o g  [ O P T I O N S ]  5C o m p a t i b i l i t y = 1 . 1   o r   l a t e r  #C o m p i l e d   f i l e = { 0 }  #C o n t e n t s   f i l e = { 0 }  I n d e x   f i l e = { 0 }  'D e f a u l t   W i n d o w = M a i n  #D e f a u l t   t o p i c = { 0 }  7D i s p l a y   c o m p i l e   p r o g r e s s = N o  %E r r o r   l o g   f i l e = { 0 }  )F u l l - t e x t   s e a r c h = Y e s ML a n g u a g e = 0 x 4 0 9   E n g l i s h   ( U n i t e d   S t a t e s )  T i t l e = { 0 }  [ W I N D O W S ]  uM a i n = , " { 0 } " , " { 1 } " , " { 2 } " , " { 2 } " , , , , , 0 x 6 3 5 2 0 , , 0 x 3 8 4 e , , , , , , , , 0  ]< ! D O C T Y P E   H T M L   P U B L I C   " - / / I E T F / / D T D   H T M L / / E N " > 
< H T M L >  
< H E A D >  u< M E T A   N A M E = " G E N E R A T O R "   C O N T E N T = " W i X   T o o l s e t   D o c C o m p i l e r " / >  < / H E A D >  
< B O D Y >  I< O B J E C T   T Y P E = " t e x t / s i t e   p r o p e r t i e s " >  O	 < P A R A M   N A M E = " F r a m e N a m e "   V A L U E = " T E X T " / >  < / O B J E C T >  	< U L >  E	 < L I >   < O B J E C T   t y p e = " t e x t / s i t e m a p " >  I	 	 < p a r a m   n a m e = " K e y w o r d "   v a l u e = " { 0 } " >  C	 	 < p a r a m   n a m e = " N a m e "   v a l u e = " { 0 } " >  E	 	 < p a r a m   n a m e = " L o c a l "   v a l u e = " { 0 } " >  	 	 < / O B J E C T >  < / U L >  < / B O D Y >  < / H T M L >  ��< m e t a   n a m e = " G E N E R A T O R "   c o n t e n t = " M i c r o s o f t & r e g ;   H T M L   H e l p   W o r k s h o p   4 . 1 " >  )< ! - -   S i t e m a p   1 . 0   - - > < / H E A D > < B O D Y >  I< O B J E C T   t y p e = " t e x t / s i t e   p r o p e r t i e s " >  Q	 < p a r a m   n a m e = " I m a g e T y p e "   v a l u e = " F o l d e r " >  < / B O D Y > < / H T M L >    ( S i m p l e   T y p e )  s i m p l e _ t y p e _         *   [ { 0 } ] ( { 1 } )  _\ < . + ? ( s r c | h r e f ) \ s * = \ s * [ " ' ] ( ? < u r i > ~ / . + ? ) [ " ' ] . * \ > . m d  k e y  v a l u e  
 
  ^ - - - \ s * $ ;^ ( ? < k e y > \ w + ) : \ s ? ( ? < v a l u e > . + ) $  i n d e x  t i t l e  ��w a r n i n g   -   d o c u m e n t :   { 0 }   d o e s   n o t   h a v e   a   t i t l e .   I t   i s   h i g h l y   r e c o m m e n d   t h a t   a l l   d o c u m e n t s   h a v e   a   t i t l e . c h m  d e f a u l t  a f t e r  E[ i m p l i c i t   d e f a u l t   d o c u m e n t :   i n d e x ]  . . \ i n d e x  Y[ i m p l i c i t   p a r e n t   d e f a u l t   d o c u m e n t :   . . \ i n d e x ]  ~ \  . h t m l  	. h t m  ��I n f i n i t e   l o o p   i n   v a r i a b l e :   { 0 }   i n   f i l e :   { 1 }   o n   l i n e :   { 2 } ,   c o l u m n :   { 3 }  ��U n k n o w n   p r e p r o c e s s o r   v a r i a b l e :   { 0 }   i n   f i l e :   { 1 }   o n   l i n e :   { 2 } ,   c o l u m n :   { 3 }  A{ { ( [ a - z A - Z _ ] [ a - z A - Z 0 - 9 _ \ - \ . ] * ) } } < l i > { 0 } < / l i > 
     `��~e�%@��f��ߦ�      ��$=!%)A   =     
!
%

)
  ���� ��	UY]M  U  ��  Y        ��            8   i   �� �� e4Ya]e4 ��      i a
 i��	 �� i
mq  m mm	  
 mmu4  u u4   mm mme 	   �� ��&@��e$��e$D $e$������ �� 
	 ����� �� 
��   
 �� 
����

��  �	(	 �  a�� a �� �� ���� a�� �M$$e$u$$$$$M$u$	��$��$	  �� ��$!��$����u$$��$��$�� �� �� 
$ ��  �� ��u$$   aaa���� ��   
������
 �
a 	 ��  �z\V4���?_�
:�� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����1 . 1 3       
[ * + - ] \ d + [ . ]        MQe��e$$0H����	��$L�� 9	9       	 e4      e e    
 ��
 ����  e$e$ $e$ e$ e$	  ��
 ��     $$  $ $	  ��$ $	 ��  	 0
 �� $ ( ( ( ( e	( ��( ( $	( ��$        TWrapNonExceptionThrows      $ docCompiler - Software Delivery       3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US   /  <?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://schemas.microsoft.com/wix/2005/DocCompiler"
              xmlns="http://schemas.microsoft.com/wix/2005/DocCompiler">
    <xs:annotation>
        <xs:documentation>
            Schema for the documentation compiler.
        </xs:documentation>
    </xs:annotation>

    <xs:element name="DocCompiler">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="TableOfContents"/>
                <xs:element ref="CopyDirectory" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element ref="CopyFile" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CopyDirectory">
        <xs:complexType>
            <xs:attribute name="Destination" use="required"/>
            <xs:attribute name="Source" use="required"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="CopyFile">
        <xs:complexType>
            <xs:attribute name="Destination" use="required"/>
            <xs:attribute name="Source" use="required"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="TableOfContents">
        <xs:complexType>
            <xs:choice minOccurs="0" maxOccurs="unbounded">
                <xs:element ref="Topic"/>
                <xs:element ref="XmlSchema"/>
            </xs:choice>
        </xs:complexType>
    </xs:element>

    <xs:element name="Topic">
        <xs:complexType>
            <xs:choice minOccurs="0" maxOccurs="unbounded">
                <xs:element ref="Index"/>
                <xs:element ref="Topic"/>
                <xs:element ref="XmlSchema"/>
            </xs:choice>
            <xs:attribute name="SourceFile"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="Index">
        <xs:complexType>
            <xs:attribute name="Title" use="required"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="XmlSchema">
        <xs:complexType>
            <xs:attribute name="Main" type="YesNoType"/>
            <xs:attribute name="SourceFile" use="required"/>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="YesNoType">
        <xs:annotation><xs:documentation>Values of this type will either be "yes" or "no".</xs:documentation></xs:annotation>
        <xs:restriction base='xs:NMTOKEN'>
            <xs:enumeration value="no"/>
            <xs:enumeration value="yes"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
     M2j:�
�2�K[�����7��fPI�����-��$�fw��1j��4U�l�Y9PIK�ԱX��D�G��!%Z�z�+�|{א!�U���?���G�3�T��Dǉ"�흵U�z��K˕�Z��*�    �o�]         �# � RSDS��(!�XsL�P���n��   C:\agent\_work\66\s\build\obj\ship\x86\DocCompiler\DocCompiler.pdb                                                                                                                                                                                                  $%         >%                         0%           _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �   P  �                  8  �                   �                     h  �                   p  �@ �          �4   V S _ V E R S I O N _ I N F O     ���     �   � ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       �@   S t r i n g F i l e I n f o      0 0 0 0 0 4 b 0      C o m m e n t s       @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   h    F i l e D e s c r i p t i o n     d o c C o m p i l e r   -   S o f t w a r e   D e l i v e r y   8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   @   I n t e r n a l N a m e   D o c C o m p i l e r . e x e   � E  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s .   A l l   r i g h t s   r e s e r v e d .     H   O r i g i n a l F i l e n a m e   D o c C o m p i l e r . e x e   \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   8   A s s e m b l y   V e r s i o n   3 . 0 . 0 . 0   �D �          ﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>

<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity version="1.0.0.0" name="MyApplication.app"/>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            P5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      