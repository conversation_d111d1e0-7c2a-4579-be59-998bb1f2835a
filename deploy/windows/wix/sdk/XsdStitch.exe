MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L �o�]        � " 0  0          �O       `    @                       �     ��   @�                           �O  O    `  �                   �     TN                                                               H           .text   �/       0                    `.rsrc   �   `      @              @  @.reloc      �      P              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �O      H     l-  h   	             �M  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�4  r�  po  
�4  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po  
o  
 (  +,r�  p	o  
o  
 (  +,r+ po  
o  
 (  +,rO po   
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  (!  
o"  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *�s#  
}  s$  
}
  (%  
s&  
}  *2s  o  *0 \    
s'  
(  {
  ,1((  
r� po)  
o*  
o+  
(,  
r? p(-  
(.  
{  ,:r� p(-  
(.  
r# p(-  
r� p(-  
r* p(-  
(.  
��  {  s/  
(0  

o1  
&�
,o2  
�s&  

{  o3  
8�   o4  
	(5  

	
o6  
,1
+
�
	

�4  (7  
o6  
-�

�4  (7  

	
o8  
&s/  
		(0  
o9  

o:  
o;  
o1  
&s<  
o:  
o=  
(>  
o?  
o@  
oA  
&�	,	o2  
�oB  
:,����u   ,oC  
�oD  
8R  oE  
;A  oF  
oG  
oH  
8a  oI  
t#  oJ  
9G  oJ  
oK  
oL  
8  oM  
u%  9�   oN  
8�   �u'  9�   oO  
r� p(P  
9�   oQ  
r� p(P  
9�   r poR  
r poR  
oS  
-r  psT  
z%oS  
-r� psT  
zsU  
oF  
oV  
t#  oW  
t)  oX  
,%sY  
oZ  
o[  
oX  
(  &X�i?���o\  
:�����u   ,oC  
�o]  
:�����u   ,oC  
�o^  
oG  
oH  
8j  oI  
t*  oJ  
9P  oJ  
oK  
oL  
8  oM  
u%  9   oN  
8�   �u'  9�   oO  
r� p(P  
9�   oQ  
r� p(P  
9�   r poR  
r poR  
oS  
-r  psT  
z%oS  
-r� psT  
zsU  
 oF  
 oV  
t#  oW  
t)  !!oX  
,.s_  
o`  
oa  
{
  !ob  
&!oc  
oA  
&X�i?���o\  
:�����u   ,oC  
�o]  
:�����u   ,oC  
�od  
:����{  oe  
"+d"oI  
�,  ##(f  
t-  $#(g  
t.  %%u0  ,%t0  oh  
$oi  
+%u1  ,%t1  oh  
$oi  
"o]  
-��"u   ,oC  
�{
  oj  
+oI  
t)  ok  
o]  
-��u   ,oC  
�{	  (l  
sm  
on  
 oo  
op  
oq  
�or  
��&r� p&os  
&ot  
(u  
�**A    �      �   
          �   �   �            �   �   �                9            �  t  \            �  (  �            �  }              /  q   �            �     �            �  5   .                0  8     /  0 �     u0  ,at0  
+Foh  
ov  
t.  u-  ,{  ow  
oh  
ox  
*(  ,*�oh  
oy  
2�+qu1  ,it1  
+N	oh  
ov  
t.  u-  ,{  	ow  
	oh  
ox  
*(  ,*�	oh  
oy  
2�*0 _    
8?  �91  ~z  
(P  
:!  -o{  
./o{  
@�   o|  
rp p(P  
-rt p(P  
-+}  8�  }
  8�  r� po}  
,>�i�%
2/�o{  
.
-�o{  
3s~  
z{  �o8  
&8�  r� ps  
z@o{  
@*  o|  
s�  

s$  
8�   r� p	8�   -7 	o{  
.
		o{  
3r� p(�  
,mob  
&+^\	o{  
3$	oS  
�/"	�o{  
3	�	+"	o{  
3	�+	o{  
�M  (7  
	�		oS  
?S���r� p(�  
,
ob  
&	o�  
%:����3  (!  
o�  
t  (  �G	,	oC  
�{  -(�  
}  +'{	  -(�  
}	  +r� p(�  
s�  
z�
�i?����{	  -}  * A     �     �  
       BSJB         v2.0.50727     l   d  #~  �  P  #Strings       �  #US       #GUID     X  #Blob         W]	   �3      O      
      	   �                                   �      �� j� �? �   $M �M � y ;M QM �M �M tM �� >g	 Xg	 
� �
M
 �? �� ~�
 n  E�	 �
�	 �� ? -  k-  h# �#
 �n  �
-  9� �-  ��	 X
-  �-  -  # i
# �# i-  �-  �	 �	 -  \-  �� � -  � -  [!  ��  � y� w� (M �� Q# v-  �!  
-  a% -  �-  -  _�	 -�	 [ -  -  �� �-  ;-  ��
 �# �# ~�	 �� L� k!  �
�           � �.E    �-	E    i  
  q  �	  R Q��   �� �  �  %� .� �	� �	�P     � �	 �!    � �
 "    �8�  R"    �2  {"    � � �"    � � �)    � t
  �*    � >*	       �
   �
  �   (	   (	   n   �
   (		 2  2  2
 ) 2 1 2 9 2 A 2 I 2 Q 2 Y 2 a 2 i 2 q 2 � 2 � 2 � �1 � 5 � �	1 �� ; � 9
1 � 1 � �1 �^	A � 2 � �F � �F ��	J Q 1 Y +
1 1 �1 a 
1 ) �1 ��r � 		{ � 2 � 2 � 2 � 2 � 2 � �
� �  � ��� � �1 �O� �O� �O� � 2 � R � W �� � � �
1 ��� ^	A ��	� W %�R*� � 1 �W 0� 2 � �  ���� � �6�W <� �
C� � G	�
M� �	R	X^�
dh!T	6�n!�
t)7z11 �-�1 1 9����F )2 A20|�YdIO�2 ����!�
C�
C� �RQ2 Q��Q��� W �I�6	�
C� �a�
da�d1T	6���� ^I*�AL
�� 2�� � � B�� ��� ��Q� y1 y� 1 �O��|�� ���
Y�
F �G ��	#��(��A y2 a20�2 �9�q51 � �
-����	9a2   �.  0.  9.  X. # a. + |. 3 �. ; �. C �. K �. S �. [ %. c �. k H� { S  [  j � �o 6�            H�             ?I                ?�               ?#         +  Q  V  [  `  e  U    Int32 <Module> get_NamespaceURI System.IO T System.Xml.Schema XmlSchema mscorlib Read Add XmlSchemaAnnotated System.Collections.Specialized Replace set_Namespace XmlSchemaExtensionNamespace get_TargetNamespace get_StackTrace XmlSchemaChoice XmlSchemaSequence XmlNode get_Message XmlSchemaObjectTable IEnumerable IDisposable Hashtable get_Particle XmlSchemaParticle particle RuntimeTypeHandle GetTypeFromHandle mainSchemaFile outputFile Console get_Title get_QualifiedName XmlQualifiedName GetFileName set_RefName get_LocalName get_ProductName GetName AssemblyName ReadLine ParseCommandLine WriteLine get_ElementType XmlSchemaComplexType CollectionBase XmlSchemaGroupBase Close Dispose Write XmlSchemaAttribute STAThreadAttribute NeutralResourcesLanguageAttribute DebuggableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute SecurityPermissionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute set_AnyAttribute XmlSchemaAnyAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute get_Value Remove XsdStitch.exe Encoding ToString Substring set_Formatting XsdStitch GetFullPath get_Length StartsWith original XmlSchemaExternal System.Xml Microsoft.Tools.WindowsInstallerXml VSExtensionsLandingUrl NewsUrl SupportUrl get_Item set_Item System Main GetFileNameWithoutExtension get_Version get_FileVersion get_Location set_SchemaLocation set_Indentation get_Annotation XmlSchemaAnnotation System.Xml.Serialization SecurityAction System.Reflection ICollection XmlSchemaCollection StringCollection XmlSchemaObjectCollection ApplicationException ArgumentException get_Description WixDistribution Run FileVersionInfo GetVersionInfo XmlSchemaAppInfo showLogo showHelp get_Markup set_IndentChar XmlReader StreamReader XmlTextReader ValidationEventHandler XmlWriter XmlTextWriter get_Major get_Minor IEnumerator StringEnumerator XmlSchemaCollectionEnumerator XmlSchemaObjectEnumerator GetEnumerator IDictionaryEnumerator .ctor .cctor System.Diagnostics get_Namespaces XmlSerializerNamespaces System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources DebuggingModes get_Includes extensionSchemaFiles get_Attributes GetCustomAttributes get_Values args Microsoft.Tools.WindowsInstaller.Tools get_Items Contains System.Security.Permissions System.Collections get_Chars ReplacePlaceholders get_Elements anyAttributeElements get_Comments anys Concat TelemetryUrlFormat XmlSchemaObject get_Product ShortProduct get_Copyright get_LegalCopyright get_Default XmlSchemaElement XmlElement InsertElement element get_Current get_Count Insert XmlSchemaImport ArrayList MoveNext System.Text ToArray get_Key GetExecutingAssembly assembly XmlSchemaAny get_Company DictionaryEntry op_Equality op_Inequality Empty     [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  mM i c r o s o f t   ( R )   W i n d o w s   I n s t a l l e r   X s d   S t i t c h   v e r s i o n   { 0 }  }C o p y r i g h t   ( C )   M i c r o s o f t   C o r p o r a t i o n   2 0 0 6 .   A l l   r i g h t s   r e s e r v e d .  e  u s a g e :     x s d S t i t c h . e x e   m a i n S c h e m a . x s d   s t i t c h e d . x s d  ��      - e x t   e x t S c h e m a . x s d     a d d s   a n   e x t e n s i o n   s c h e m a   t o   t h e   m a i n   s c h e m a       - n o l o g o                           s u p p r e s s   d i s p l a y i n g   t h e   l o g o   i n f o r m a t i o n Y      - ?                                     t h i s   h e l p   i n f o r m a t i o n 
p a r e n t  qh t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 5 / X m l S c h e m a E x t e n s i o n  n a m e s p a c e  r e f  mT h e   p a r e n t   e l e m e n t   i s   m i s s i n g   t h e   n a m e s p a c e   a t t r i b u t e .  aT h e   p a r e n t   e l e m e n t   i s   m i s s i n g   t h e   r e f   a t t r i b u t e .  x s d S t i t c h . e x e   :   f a t a l   e r r o r   X S D S 0 0 0 1   :   { 0 } 
 
 
 
 S t a c k   T r a c e : 
 
 { 1 }  ?  
n o l o g o  e x t  #I n v a l i d   a r g u m e n t .   /U n k n o w n   a r g u m e n t :   ' { 0 } ' �{c��9O�:������        =M)-1Q   M     
)
-

1
  ���� ��_'imqYuIyqi}����i��������������������������������������  I  ��  Q    
 i���� ii  y     ��   �� ��    ��  i  �  �  ��    �  ��  ��  ��   ����  ��  �� ��   �� �� �  �! �!  �% �) �������� ��  ��
��a   �=�� �z\V4���� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����ph t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 5 / X m l S c h e m a E x t e n s i o n ��.��System.Security.Permissions.SecurityPermissionAttribute, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089T	ExecutionY]a I	I   	 ����         TWrapNonExceptionThrows       WiX Toolset XsdStitch  , 'Windows Installer XML Toolset XsdStitch          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US     Z0$ }���WЍ��JrxX��.�%���ƪx,�Ժ��n�_r�B�B�0\��
b>DR{�-�?�'!>c�,���7��od//~�#?N�7:��&6�U��c���SI��BT୾};��H�1    �o�]         pN  p>  RSDSw_A%��H���I�   C:\agent\_work\66\s\build\obj\ship\x86\XsdStitch\XsdStitch.pdb                                                                                                                                                                                                      �O          �O                          �O            _CorExeMain mscoree.dll     �%  @                                                  �   P  �                  8  �                   �                     h  �                   �  �`            4   V S _ V E R S I O N _ I N F O     ���     �   � ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       �p   S t r i n g F i l e I n f o   L   0 0 0 0 0 4 b 0   h (  C o m m e n t s   W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   X s d S t i t c h   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   T   F i l e D e s c r i p t i o n     W i X   T o o l s e t   X s d S t i t c h   8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   <   I n t e r n a l N a m e   X s d S t i t c h . e x e   � E  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s .   A l l   r i g h t s   r e s e r v e d .     D   O r i g i n a l F i l e n a m e   X s d S t i t c h . e x e   \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   8   A s s e m b l y   V e r s i o n   3 . 0 . 0 . 0   �d  �          ﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>

<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity version="1.0.0.0" name="MyApplication.app"/>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       @     �?                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      