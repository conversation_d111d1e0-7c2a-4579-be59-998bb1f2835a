MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L �o�]        � 0  @          �_       `    @                       �     �   @�                           8_  O    `  d                   �      ^                                                               H           .text   �?       @                    `.rsrc   d   `      P              @  @.reloc      �      `              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                l_      H     �/  �-               �]  �                                   0 r    94  o  
(
  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�+  r�  po  
�+  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po  
o  
 (  +,r�  p	o  
o  
 (  +,r+ po  
o  
 (  +,rO po  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  (  
o  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 ^       r� p(  
o   
o!  
o"  
rO po#  
o$  
r� po#  
o$  
rP po#  
r� po#  
rO po#  
*  0 �     �i/(%  
(  *�
��iڍ*  �i(&  
(%  
(  
�Or� po'  
((  
()  

�3r� po'  
((  
()  

�r� p(*  
()  

� 	* (    1 B     1 ^     1 z   0 �    �  (+  
,r� ps,  
z(+  
,r� ps,  
z,o-  
-r
 ps,  
z(.  
-s/  
zo0  

(.  
-s/  
z(  (  o1  
o2  
+o3  
r p(4  
-o5  
-��,o6  
�-rl ps7  
z(8  
(  (  
(
  	o9  
--r� p((  
s7  
z	o9  
1,rA p((  
s:  
z(8  
o;  
1(<  
-(=  
&(>  
	(  �,o6  
�(  (  r� ps?  
o@  
((  
o#  
*    � #�      D
Q    0 m     o-  
sA  

o2  
+Bo3  
(+  
-3�=  %;�oB  

+	�oC  
X	�i2�o5  
-��
,o6  
�*       Na 
    0 +     s  
}  }	  (D  
�  sE  
oF  
* 0 X     
(G  

�K~  r� po'  
oH  
�2~  r� po'  
oH  
�
~  r� p	o'  
oH  
� *(     	      	$      	=   0 �     ~  rI	 p(I  
o"  

(G  
oJ  

+q	�oK  
-_oL  
+I�oM  
r�	 p(N  
,--o   
oO  
r
 poM  
(P  

+r
 ps7  
zX�i2�	X
	�i2�*  0 �   	  ~  r]
 p(I  
o"  
sQ  

(G  
oJ  
+u�
	oR  
+V�(  ,<r�
 p(S  
	oM  
oT  
(U  
oV  
~  r�
 poH  
X�i2�X�i2�*0 �   
  (W  

�&ݑ   oX  
+roY  

	oZ  
r�
 po[  
,X	o\  
o]  
+o^  
(_  
t*  �o5  
-��,o6  
�(+  
,oT  
�o5  
-��
,o6  
�**(      		 $   D &j       ~� 
    0 -     
�+  (`  
(a  
ob  
(  /*�
+�   0 �    ~  ri po#  
(c  

	od  
��D  
	�ioe  
&�
	,	o6  
��D  &r� p(
  -r� ps7  
zo9  
1!r po9  
�+  �+  (f  
s7  
z�*  +~g  
��o9  
�2�o1  
o9  
�oh  
o9  
�o9  
(i  
(  +r� p�+  (`  
(a  
ob  
(k  
ob  
(  		?�   (  

?�   �o;  
0~g  
+ol  
o;  
H1rV
 pH�+  (f  
s7  
zo;  
 �   1r�
 p �   �+  (f  
s7  
z(a  
ob  

(k  
ob  
	H
(  
 @  (  �8����o9  
:�   9�   �*  %r. p�%rX p�%r| p�+b�(a  
r� p((  
ob  
(  /r� p((  
s7  
z(a  
ob  
�i(  X�i2�o;  
 �   1r p �   �+  (f  
s7  
z(k  
r� pob  
(k  
ob  
(  2 @  (  �iom  
*       1 
    0 0     
+$+֑�3
��i2��i3*�
�i2�*0 #     
+�i/
���+���
2�*�on  
-~  ,~  r� poo  
o"  
*   0 }     sQ  

o2  
+Xo3  
=op  
1+�=  %=�oq  

	�or  
-)	�	�oV  
+(I  
or  
-	oV  
o5  
-��
,o6  
�*      
 dq 
    �~  r� po#  
ss  

�  st  
ou  
*�~  r  poH  
sv  
%(w  
ox  
%oy  
oz  
*({  
*   0 �    o|  
s}  

{  o~  
o2  
+Qo3  

	(S  
	(  
oO  
(�  
,*rT p(�  
-r^ p(�  
,	(	  ,�o5  
-��
,o6  
�:�   {	  9�   {	  oO  
(�  
rT p((  
(.  
,{	  oO  
(�  
rT p((  
+?{	  oO  
(�  
r^ p((  
(.  
,{	  oO  
(�  
r^ p((  
,(	  ,{  (I  
o�  
-o|  
(�  
�&� ,o   
oZ  
oZ  
(�  
,~  rh po  
((  
o#  
*~  r� po  
((  
o#  
~  r po   
(*  
o#  
~  rF p(*  
o#  
+~  r| p(*  
o#  
*      ]| 
      @N  BSJB         v2.0.50727     l   	  #~  t	  4  #Strings    �  �  #US �'     #GUID   �'  4  #Blob         W	   �3      L      	      &   �                                   �      �z Ez DN �   �G oG ,G G �G �G "� �
� �G
 wN XG � Y� ��  Y� �� ��  �� | - � - n - ��  } - l�  �� � �� � - HG � G �
G x�  t�  {	 z j	� �G �� � � �� � �� �� E - �  7 - (� '�	 �� ��  � - �� ��  ��  S�  h�  �� �	� �� �� ]	G �G �} m� �� b - � @�  S � \  ��    �       � &1     � � 1       1    a� �
� i� l
� J�Q��� �} �� 
�P     � �	� �!    � � "    �G� T"    � �� �"    � �� �#    � h� 8%    � V� �%    � �� �%    � �� �&    � �	) 8'    � 2
� �'    � "� �(    � :�  )    � "� ,    � (	� L,    � 2	� {,    � �	 �,    � �  @-    � �! l-    � �(# �-    �A % �-    �A % �-    �  .%       �   �  c   �   �	   m      U
   �   `   �   
   �   3   3   x   ?	   
         B
   �	   �   s   �   �
   �   �   �   �   U
      �   �   �   �   �		 A  A  A
 ) A 1 A 9 A A A I A Q A Y A 9A i :& q �* q 
& Q0 q �
& q p& q 	& Q�	6 � A � ; � ; Qe
? 9 �& A �
& ) & I �
& y & � �g i 	o i �w i �| I�� � �� � � � � ie� q�� � �& Qe
� i�� Qe
� Q$� yA  0; �N
� � A  �� $ \
� , 34 Q��t%�� � A ��)< 0; �A Q�; �N
� ��;��B�A �c& D A Q�
aD dk��vA|���i ��� ���C)i 	�� �
%� l�� c& Q�I:& Qe
�L A � a���):& Q�
�L d�	 T 3\ a �& Q�>
Fd 3l raQ�
l� rI	x�HB� ��� L�Q�
�Q,� 9�)��qQ��r$ ��� �1��1O& Q�Q�
L �AA t A|Q�
.� A a�D� nJ� _ � | a A A:& IA $ R	c��)Q�	m��� $ d�i Q�Q�	�  *.  7.  @.  _. # h. + �. 3 �. ; �. C �. K . S $� [ / _ � � Iq��� e~���� Rd � � � .[�-6PY%�            ��              v$               v�               �                              �            �  F  K  P  U  Z � � E      <>c__DisplayClass6_0 <ResolveDependentAssemblies>b__0 IEnumerable`1 ICollection`1 EventHandler`1 IComparer`1 IEnumerator`1 IList`1 Int32 KeyValuePair`2 IDictionary`2 <Module> Microsoft.Deployment.Tools.MakeSfxCA get_ASCII System.IO T CustomAttributeData Microsoft.Deployment.Compression.Cab mscorlib System.Collections.Generic OpenRead ReflectionOnlyLoad Add Build Find find method Replace replace CopyVersionResource get_Unicode Usage get_Message IDisposable RuntimeTypeHandle GetTypeFromHandle sourceFile destFile outputFile Console get_Title WriteEntryModule module get_Name GetFileName get_CurrentFileName get_FullName get_ProductName GetName AssemblyName GetDirectoryName WriteLine Combine ResourceType get_ProgressType ArchiveProgressType Compare Dispose Create Write CompilerGeneratedAttribute NeutralResourcesLanguageAttribute DebuggableAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute Byte get_Value Save add_ReflectionOnlyAssemblyResolve MakeSfxCA.exe IndexOf Encoding ToString log assemblyPath get_Length length StartsWith get_Ordinal original CompressionLevel sfxdll Microsoft.Tools.WindowsInstallerXml VSExtensionsLandingUrl NewsUrl SupportUrl FileStream outputStream get_Item System ReflectionOnlyLoadFrom Main AppDomain get_CurrentDomain GetExtension GetFileNameWithoutExtension get_Version get_FileVersion Microsoft.Deployment.Compression get_Location System.Reflection ResourceCollection IOException FileLoadException NotSupportedException FileNotFoundException ArgumentNullException BadImageFormatException ArgumentException SecurityException get_Description WixDistribution StringComparison CopyTo CabInfo MethodInfo FileInfo ArchiveInfo FileSystemInfo FileVersionInfo GetVersionInfo MemberInfo DirectoryInfo GetPackFileMap fileMap inputsMap Char sender ResolveEventHandler StringComparer TextWriter inputDir get_Major get_Minor IEnumerator GetEnumerator .ctor .cctor System.Diagnostics GetMethods GetInterfaces System.Runtime.CompilerServices System.Resources Microsoft.Deployment.Resources DebuggingModes ResolveDependentAssemblies PackInputFiles GetExportedTypes GetCustomAttributes FindBytes ReplaceBytes fileBytes GetBytes get_Values BindingFlags ResolveEventArgs ArchiveProgressEventArgs args Equals Contains System.Collections StringSplitOptions ReplacePlaceholders FindEmbeddedUIClass uiClass PackProgress get_Comments get_ConstructorArguments FindEntryPoints entryPoints Exists inputs get_Keys Concat TelemetryUrlFormat entryPointFormat get_IsAbstract Object get_Product ShortProduct PackFileSet offset get_Copyright get_LegalCopyright Split CustomAttributeTypedArgument get_Current GetEntryPoint get_Count GetEntryPointSlotCount Sort SplitList list get_Out output MoveNext System.Text w Array ContainsKey RequiredWIAssembly GetExecutingAssembly TryLoadDependentAssembly assembly get_Company Copy CreateDirectory op_Equality System.Security IsNullOrEmpty    [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  }D e p l o y m e n t   T o o l s   F o u n d a t i o n   c u s t o m   a c t i o n   p a c k a g e r   v e r s i o n   { 0 }  gC o p y r i g h t   ( c )   . N E T   F o u n d a t i o n .   A l l   r i g h t s   r e s e r v e d .  ��U s a g e :   M a k e S f x C A   < o u t p u t c a . d l l >   S f x C A . d l l   < i n p u t c a . d l l >   [ s u p p o r t   f i l e s   . . . ]  sM a k e s   a   s e l f - e x t r a c t i n g   m a n a g e d   M S I   C A   o r   U I   D L L   p a c k a g e . ��S u p p o r t   f i l e s   m u s t   i n c l u d e   M i c r o s o f t . D e p l o y m e n t . W i n d o w s I n s t a l l e r . d l l  ��S u p p o r t   f i l e s   o p t i o n a l l y   i n c l u d e   C u s t o m A c t i o n . c o n f i g / E m b e d d e d U I . c o n f i g  E r r o r :    
o u t p u t  
s f x d l l  
i n p u t s  SM i c r o s o f t . D e p l o y m e n t . W i n d o w s I n s t a l l e r . d l l  �}M i c r o s o f t . D e p l o y m e n t . W i n d o w s I n s t a l l e r . d l l   m u s t   b e   i n c l u d e d   i n   t h e   l i s t   o f   s u p p o r t   f i l e s .   I f   u s i n g   t h e   M S B u i l d   t a r g e t s ,   m a k e   s u r e   t h e   a s s e m b l y   r e f e r e n c e   h a s   t h e   P r i v a t e   ( C o p y   L o c a l )   f l a g   s e t .  UN o   C A   o r   U I   e n t r y   p o i n t s   f o u n d   i n   m o d u l e :    oC A   a n d   U I   e n t r y   p o i n t s   c a n n o t   b e   i n   t h e   s a m e   a s s e m b l y :    )M a k e S f x C A   f i n i s h e d :    m        E r r o r :   F a i l e d   t o   l o a d   d e p e n d e n t   a s s e m b l y :   { 0 } .   { 1 }  SS e a r c h i n g   f o r   a n   e m b e d d e d   U I   c l a s s   i n   { 0 }  cM i c r o s o f t . D e p l o y m e n t . W i n d o w s I n s t a l l e r . I E m b e d d e d U I  !  WM u l t i p l e   I E m b e d d e d U I   i m p l e m e n t a t i o n s   f o u n d .  _S e a r c h i n g   f o r   c u s t o m   a c t i o n   e n t r y   p o i n t s   i n   { 0 }  { 0 } ! { 1 } . { 2 }          { 0 } = { 1 }  {[ M i c r o s o f t . D e p l o y m e n t . W i n d o w s I n s t a l l e r . C u s t o m A c t i o n A t t r i b u t e (  1M o d i f y i n g   S f x C A . d l l   s t u b  ;C u s t o m A c t i o n E n t r y P o i n t { 0 : d 0 3 }  /I n v a l i d   S f x C A . d l l   f i l e .  �MT h e   c u s t o m   a c t i o n   a s s e m b l y   h a s   { 0 }   e n t r y p o i n t s ,   w h i c h   i s   m o r e   t h a n   t h e   m a x i m u m   ( { 1 } ) .   R e f a c t o r   t h e   c u s t o m   a c t i o n s   o r   a d d   m o r e   e n t r y p o i n t   s l o t s   i n   S f x C A \ E n t r y P o i n t s . h .  kE n t r y   p o i n t   n a m e   e x c e e d s   l i m i t   o f   { 0 }   c h a r a c t e r s :   { 1 }  kE n t r y   p o i n t   p a t h   e x c e e d s   l i m i t   o f   { 0 }   c h a r a c t e r s :   { 1 }  )I n i t i a l i z e E m b e d d e d U I  #E m b e d d e d U I H a n d l e r  %S h u t d o w n E m b e d d e d U I  z z z  oI n p u t   S f x C A . d l l   d o e s   n o t   c o n t a i n   e x p o r t e d   e n t r y - p o i n t :   oU I   c l a s s   f u l l   n a m e   e x c e e d s   l i m i t   o f   { 0 }   c h a r a c t e r s :   { 1 }  EI n i t i a l i z e E m b e d d e d U I _ F u l l C l a s s N a m e          { 0 }  P a c k a g i n g   f i l e s  SC o p y i n g   f i l e   v e r s i o n   i n f o   f r o m   { 0 }   t o   { 1 }  	. d l l  	. e x e  ?        L o a d e d   d e p e n d e n t   a s s e m b l y :    g        W a r n i n g :   L o a d e d   m i s m a t c h e d   d e p e n d e n t   a s s e m b l y :    5            L o a d e d   a s s e m b l y       :    5            R e f e r e n c e   a s s e m b l y :    Y        E r r o r :   D e p e n d e n t   a s s e m b l y   n o t   s u p p l i e d :      ��qzN��4�9�      9!%=A   9     

!

%
=  }�� }  5  ��  A 
QUY  I ����   aaei ��]  a	  �� ��  e e    ��   ���� �� ��mem	 ��    ��  �
5quy 5 5}}}  }  	��}}������	 ���   ]��e����e���� ]���	����e�� ��	  ]������e��     �
 '��  
      � � 
     ���e    �%�� a�-�%��  �1 �1��5e	  �� ���z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����RM i c r o s o f t . D e p l o y m e n t . W i n d o w s I n s t a l l e r . d l l Ia 5	5    I  ]I ]]
 a	 a �� 
 ia 	  ��
 a]
 a  5��        TWrapNonExceptionThrows      " Custom action packaging tool.   3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset      
 en-US     {3��"�ƀ�(/��V�Z1�c1�}`:�w��^�X'�2*.���Q.;y
��}Ρ��Z`�R{����x�����I��_��fN1�`j�x�w���箆��~��qJ�)UQ,)����oL�!}8    �o�]         ^  N  RSDS#X����@�d��:�#a   C:\agent\_work\66\s\build\obj\ship\x86\MakeSfxCA\MakeSfxCA.pdb                                                                                                                                                                                                      `_          z_                          l_            _CorExeMain mscoree.dll     �%  @                                                                                                                                      �   P  �                  8  �                   �                     h  �                   d  �`  �          �4   V S _ V E R S I O N _ I N F O     ���     �   � ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       �4   S t r i n g F i l e I n f o      0 0 0 0 0 4 b 0   T   C o m m e n t s   C u s t o m   a c t i o n   p a c k a g i n g   t o o l .   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   ,   F i l e D e s c r i p t i o n         8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   <   I n t e r n a l N a m e   M a k e S f x C A . e x e   � E  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s .   A l l   r i g h t s   r e s e r v e d .     D   O r i g i n a l F i l e n a m e   M a k e S f x C A . e x e   \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   8   A s s e m b l y   V e r s i o n   3 . 0 . 0 . 0   td  �          ﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>

<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity version="1.0.0.0" name="MyApplication.app"/>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   P     �?                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      