MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L )p�]        � 0  @          �\       `    @                       �     �@  @�                           t\  O    `  �                   �     <[                                                               H           .text   �<       @                    `.rsrc   �   `      P              @  @.reloc      �      `              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �\      H     �/  �$       |T  @  �Z  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�1  r�  po  
�1  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po   
o  
 (  +,r�  p	o!  
o  
 (  +,r+ po"  
o  
 (  +,rO po#  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  ($  
o%  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 K       (&  
s'  
}  s(  
}	  s)  
}
  r� pr� ps*  
}  }  }  *F(+  
s  o  *   0 g    s'  

{  %�,  
s-  
s.  
}  {  %�,  
s-  
s/  
{  o0  
o1  
{  o2  
o3  
(4  
o5  
+o6  
(	  o7  
-��u  		,	o8  
�{  o9  
,{  o:  

ݧ  (  {
  ,
(  
݋  {  o9  
,{  o:  

�l  {  ,(;  
{  o<  
{  o=  
�0  {  o>  
{	  o?  
+o@  
t(  {  oA  
oB  
-��u  		,	o8  
�{  o9  
,{  o:  

��  {  o0  
{  oC  

	-{  o:  

ݣ  {  o2  
	oD  
-{  o:  

�~  sE  
oF  
 {  sG  
oH  
oI  
sJ  
(K  

	
oL  
�
,
o8  
�oM  
�,o8  
�{  o2  
oN  
(O  
,{  o:  

��   {  (P  
(Q  
&{  (R  
sS  
oI  
(R  
oT  
(K  
oU  
oV  
�,o8  
�oW  
oX  
�,o8  
��Z{  oY  
o,  
�C{  oZ  
o[  
oM  
o\  
(]  
o,  
uA  -	uB  ,�� {  o:  
*
* A�     k      �             <  %   a            #  
   -              ,   D            �     �            �  M   �             ]   �  �     #      ]   �    C   $  0 �    (;  
(  (^  
s)  

{	  o?  
+<o@  
t(  o_  

+!	�%  {`  
�%  oa  
	X
	�i2�oB  
-��u  ,o8  
�r� pr� p(  sb  
�%  oa  
r� pr� p(  sb  
�%  oa  
r pr p(  sb  
�%  oa  
r pr- p(  sb  
�%  oa  
r7 pr7 p(  sb  
�%  oa  
rE prE p(  sb  
�%  oa  
rS prS p(  sb  
�%  oa  
rY prY p(  sb  
�%  oa  
rg prg p(  sb  
�%  oa  
oc  
od  
+8o@  
�%  {`  
ru poe  
-(  {`  
{f  
(g  
oB  
-��u  ,o8  
�(h  
(  (^  
oc  
od  
+8o@  
�%  {`  
ru poe  
,(  {`  
{f  
(g  
oB  
-��u  ,o8  
�(  ry p(  (g  
(i  
{  o:  
*(   ! Hi      �D�     DW    0 �    
8g  �oj  
r� p(k  
-oj  
r� p(k  
,}
  *-R@ol  
3oj  
(m  
(  8�  ru poe  
-}  8�  {  (n  
o,  
8�  3}  8�  -ol  
./ol  
@�  oj  
r� p(o  
,}  8x  r� p(o  
-
r� p(o  
,){  �%
(p  
}  {  (O  
96  *r� p(o  
,,{  r� pr� p(q  
o,  
{  or  
8�  r� poe  
9�   oj  

	os  
-{  or  
+6	(t  
ou  
(v  
2{  	(w  
o,  
{  ox  
ݓ  &{  	(w  
o,  
�{  &{  	(w  
o,  
�c  r� p(o  
,,{  r� pr� p(q  
o,  
{  oy  
8*  r� poe  
9�   oj  
os  
-{  oy  
+8(t  
ou  
(v  
2{  (z  
o,  
{  o{  
ݽ   &{  (z  
o,  
ݤ   &{  (z  
o,  
݋   r� p(o  
,{  o|  
+pr� p(o  
,0�%
(}  
-{  r� p(~  
o,  
+>�(	  +3r� p(o  
,&�%
�(t  
(  
}  �
&r� ps�  
z@ol  
.
{  o�  
&�
�i?����{  (O  
,}
  *{  (O  
,{  (�  
o,  
*{  (O  
,{  (�  
o,  
*  @    nQ�&    nQ�'    AT�&    AT�'    ,E
  0 �     (�  

{  o�  
{	  o�  
&o_  
+I�%  
{
  	{`  
o�  
,{  	{`  
(�  
o,  
*{
  	{`  
oa  
X�i2�{  o�  
*(&  
*�~  -r) p�  ($  
o�  
s�  
�  ~  *~  *�  *V(  r� p~  o�  
*V(  r� p~  o�  
*V(  r� p~  o�  
*V(  r p~  o�  
*V(  r3 p~  o�  
*V(  rc p~  o�  
*V(  r� p~  o�  
*V(  r� p~  o�  
*V(  r� p~  o�  
*V(  r' p~  o�  
*V(  rW p~  o�  
*V(  r� p~  o�  
*V(  r� p~  o�  
*BSJB         v2.0.50727     l   �	  #~  $
  �  #Strings    �    #US �     #GUID   �  �  #Blob         W�		   �3      N            	   �                                         �      ��
 ��
 K�
 C   �@ �@ `$ x�
 �@ v@ E@ @ �@ )�
 s
$ x@
 P�
 �$ �$
 ^�  2� '� ;	� *F �$ � d� ��
 h
�  :$ \
� �	  �	� �	  �� �$ oF �$ �$ �F
 �t	 �
 ��
 	�
 D

 ��
 _� I$  $ $ F$ � � `	� �	� �
� B	� �	  �  �  �  X (� 
� �$ ��
 �$ R� .� ($ j� �� �� � �
 $ 	$ �$           � �=    )
F=      �F=  
  � �
� � 3
� �� �
� �� �� y  �� �
 �
 �

 Q	 3 + TP     � �" �!    � 5) "    ��
�  T"    ��
  �"    � 73 �"    � 99 �&    � �M  �)    � �? �-    � Q  g.    ��
 	 o.    �	E	 �.    �<�	 �.    �HK	 �.    �<R
 �.    �_R
 �.    ��R
 �.    ��R
 /    �F
R
 /    ��R
 ./    �<R
 D/    ��R
 Z/    �R
 p/    �oR
 �/    ��R
 �/    �9
R
 �/    �yR
    �   �   �  �   :   :   :   �   �	 �
  �
  �

 ) �
 1 �
 9 �
 A �
 I �
 Q �
 Y �
 a �
 i �
 q �
 � �
 I�
 Q�
 Y�
 y�
  � �8 � `< � 
8 �� B � �
8 � �8 � �8 �pH � �
 � �	M � �	M �"
Q Q �8 Y �
8 1 �8 a z
8 ) �8 �Xy � � y �
 � �
 � �
 � �
 � �
 �"� � l� ��
� � �
� � �
� � �	� �� � �
� �� ��� � y
� � 8 � O� � � ��	� � �M ��� � �
 � 
M � =� � y
� � � A�� � O� ���� �
 � �
 ��
� � � � �
 	�� �"y =8 ��)��.��3��8�	 ?�
E� N	�
 	� �� �� �	U!� 8 !�[!� 8 �a��Aj�)~�� N �)�
 � ��)y
� ��H )����� ��	� �P��?����9��� ����A��Ie��� ��M id�ib
�a �0 �� �&
 
�� �R AZ�� ����q�
 � N  R l AI A(	 � N &� p+x�A0�t6a�
;aFB) � �.  f.  o.  �. # �. + �. 3 �. ; �. C �. K �. S �. [ E. c �. k hI � �� { x� � s� � s� s s' q � j�    	V  q\  @b  cb  �b  �b  J
b  �b  @b  �b  "b  sb  �b  =
b  }b       
       	      
                            v �            \.
             J'                J$               Sh               J�               S�                  %  X  ]  b  g  l  M      ToInt32 get_UTF8 <Module> System.IO T mscorlib IllegalSuppressWarningId Load Add HarvestSourceNotSpecified OutputTargetNotSpecified TypeSpecificationForExtensionRequired System.Collections.Specialized HarvestTypeNotFound Replace get_StackTrace get_Message ElevateWarningMessage SuppressWarningMessage IEnumerable IDisposable RuntimeTypeHandle GetTypeFromHandle CommandLineResponseFile GetFile outputFile Console wconsole get_Title get_ProductName GetDirectoryName ParseCommandLine WriteLine extensionType GetType extensionsByType set_Core HarvesterCore HeatCore heatCore get_Culture set_Culture resourceCulture get_InvariantCulture get_HelpMessageVerbose Dispose Parse Create EditorBrowsableState Mutate Write MTAThreadAttribute CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute value heat.exe Microsoft.Tools.WindowsInstallerXml.Serialize get_HelpMessageOptionHeading set_Encoding get_HelpMessageSuppressWarning ToString GetString Substring IsValidArg DeprecatedCommandLineSwitch Flush Path get_Length StartsWith original System.ComponentModel System.Xml Microsoft.Tools.WindowsInstallerXml OutputXml VSExtensionsLandingUrl NewsUrl SupportUrl System resourceMan Main get_HelpMessageBegin LoadExtension get_HelpMessageExtension DuplicateCommandLineOptionInExtension HeatExtension get_FileVersion get_Location set_OmitXmlDeclaration ReadConfiguration get_HelpMessageIndentation System.Globalization PrepareConsoleForLocalization System.Reflection ICollection StringCollection HeatCommandLineOption SEHException UnexpectedException NullReferenceException FormatException ArgumentException OverflowException WixException get_Description WixDistribution AppCommon StringComparison Run CopyTo CultureInfo FileVersionInfo GetVersionInfo get_HelpMessageThisHelpInfo NumberFormatInfo DirectoryInfo get_HelpMessageNoLogo showLogo showHelp DisplayHelp get_LastErrorNumber DisplayToolHeader IFormatProvider get_ResourceManager set_MessageHandler ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler StringWriter XmlWriter StreamWriter TextWriter DisplayToolFooter get_Harvester get_Major get_Minor get_Error get_EncounteredError IllegalWarningIdAsError set_WarningAsError get_HelpMessageTreatWarningAsError IEnumerator StringEnumerator GetEnumerator get_Mutator .ctor .cctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Tools.HeatStrings.resources DebuggingModes set_ShowVerboseMessages get_CommandLineTypes GetCustomAttributes get_Values set_SuppressAllWarnings get_HelpMessageSuppressAllWarnings WixWarnings HeatStrings XmlWriterSettings MessageEventArgs WixWarningEventArgs WixErrorEventArgs args Equals Microsoft.Tools.WindowsInstallerXml.Tools Contains extensions System.Collections ParseOptions extensionOptions get_Chars set_IndentChars ReplacePlaceholders get_HelpMessageTreatAllWarningsAsErrors WixErrors get_Comments Concat Heat heat TelemetryUrlFormat get_HelpMessageOptionFormat get_NumberFormat Object get_Product ShortProduct get_Copyright get_LegalCopyright set_Indent indent WriteStartDocument set_ExtensionArgument extensionArgument get_Current get_Count Convert Harvest SortedList ArrayList get_HelpMessageOut MoveNext System.Text Wix wix Display get_Assembly assembly get_Company CreateDirectory op_Equality IsNullOrEmpty  [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  	H E A T  h e a t . e x e  	- e x t - n o l o g o - i n d e n t   < N > 
- o [ u t ] 	- o u t 
- s w < N > 
- s w a l l - v 
- w x [ N ] 
- w x a l l - - ?   |   - h e l p ?  	h e l p  
n o l o g o  o  o u t  s w a l l  s w  w x a l l  w x  v  e x t  
i n d e n t  3I n v a l i d   n u m e r i c   a r g u m e n t .  kM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . T o o l s . H e a t S t r i n g s  !H e l p M e s s a g e B e g i n  )H e l p M e s s a g e E x t e n s i o n  -H e l p M e s s a g e I n d e n t a t i o n  #H e l p M e s s a g e N o L o g o  /H e l p M e s s a g e O p t i o n F o r m a t  1H e l p M e s s a g e O p t i o n H e a d i n g  H e l p M e s s a g e O u t  =H e l p M e s s a g e S u p p r e s s A l l W a r n i n g s  5H e l p M e s s a g e S u p p r e s s W a r n i n g  /H e l p M e s s a g e T h i s H e l p I n f o  GH e l p M e s s a g e T r e a t A l l W a r n i n g s A s E r r o r s  =H e l p M e s s a g e T r e a t W a r n i n g A s E r r o r  %H e l p M e s s a g e V e r b o s e   K��1�C��]�C0�         ��E)-1I   E     
)
-

1
  ���� ��  (Qimquy}������������ ��  ��  �� i  �� Q  u     }    m m 
 ����q ��    ��  �� �� ��  ��  �� ��Y}����y����   ��   � 	  �   �� 	 ] �)  ��  �- �5    ��
������ �� ]   a  A A ���z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����QUY]a���� A	A      �� ��   �� ��         TWrapNonExceptionThrows       WiX Toolset Harvester   Toolset Harvester          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US     @ 3System.Resources.Tools.StronglyTypedResourceBuilder2.0.0.0     9  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet   
       PADPADPuHc�ď��$��(Ք���DD� �
(���x�~3���Gf��O��jo�   t    +  �   %   �   �  l      3  �  R   �   H e l p M e s s a g e B e g i n     (H e l p M e s s a g e E x t e n s i o n z   ,H e l p M e s s a g e I n d e n t a t i o n �   "H e l p M e s s a g e N o L o g o �   .H e l p M e s s a g e O p t i o n F o r m a t   0H e l p M e s s a g e O p t i o n H e a d i n g   H e l p M e s s a g e O u t   <H e l p M e s s a g e S u p p r e s s A l l W a r n i n g s Y  4H e l p M e s s a g e S u p p r e s s W a r n i n g }  .H e l p M e s s a g e T h i s H e l p I n f o �  FH e l p M e s s a g e T r e a t A l l W a r n i n g s A s E r r o r s �  <H e l p M e s s a g e T r e a t W a r n i n g A s E r r o r   $H e l p M e s s a g e V e r b o s e x  x usage:  heat.exe harvestType harvestSource <harvester arguments> -o[ut] sourceFile.wxs

Supported harvesting types:
4<extension>  extension assembly or "class, assembly"-indentation multiple (overrides default of 4)#skip printing heat logo information   {0,-7}  {1}Options:9specify output file (default: write to current directory)"suppress all warnings (deprecated)Vsuppress all warnings or a specific message ID
            (example: -sw1011 -sw1012)this help information)treat all warnings as errors (deprecated)_treat all warnings or a specific message ID as an error
            (example: -wx1011 -wx1012)verbose output   �&ǾV
���a��������a�5��c\���ո<8^��R��������dQZ��>�t&�P�RqSx=oմ���>O5�z�LIyUO�"��S�拚����rz} �K��"z\��L��6^IXԍ�B�q    )p�]         X[  XK  RSDS�ՕC��M����~���   C:\agent\_work\66\s\build\obj\ship\x86\heat\heat.pdb                                                                                                                                                                                                                �\          �\                          �\            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          �   P  �                  8  �               	  �                     h  �               	  �  �`  X          X4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   T   F i l e D e s c r i p t i o n     W i X   T o o l s e t   H a r v e s t e r   8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   *   I n t e r n a l N a m e   h e a t     � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   : 	  O r i g i n a l F i l e n a m e   h e a t . e x e     \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	��c  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"> 
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Heat" version="*******" processorArchitecture="x86" type="win32"/> 
 <description>WiX Toolset Harvester</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                P     �<                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      