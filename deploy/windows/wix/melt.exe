MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L )p�]        � 0  P          bc       �    @                       �     ��   @�                           c  O    �  �                   �     �a                                                               H           .text   hC       P                    `.rsrc   �   �      `              @  @.reloc      �      p              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Dc      H     `1  �(       �Y  x  Xa  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�9  r�  po  
�9  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po  
o  
 (  +,r�  p	o   
o  
 (  +,r+ po!  
o  
 (  +,rO po"  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  (#  
o$  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 G       (%  
s&  
}  s&  
}	  r� pr� ps'  
}
  }  }  }
  *F((  
s  o  *   0 }    (  {
  o)  
,{
  o*  

�V  {  (+  
-#{  (+  
-{  3{  (+  
,}  {  ,(,  
{  , (  (-  
(.  
{
  o*  

��   {	  o/  
+o0  
{
  (1  
o2  
o3  
-��u  
	,	o4  
�}	  {  -{  (5  
}  {  3(  +{  3(
  �Z{
  o6  
o2  
�C{
  o7  
o8  
o9  
o:  
(;  
o2  
uE  -	uF  ,�� {
  o*  
**   AL     �   %   �                                     ,  C     0 �    
s<  

s=  
{
  s>  
{  (?  
{  o/  
+o0  
(@  
oA  
oB  
o3  
-��u  ,o4  
�r� p(C  
oD  
r� p(C  
oE  
oF  
{
  %�2  
sG  
oH  
{
  %�2  
sG  
oI  
{
  %�2  
sG  
oJ  
{  (K  
(-  
{  {  {  oL  

	,i	oM  
,\{  (N  
sO  
oP  
 oQ  
"oR  
oS  
oT  
oU  
oV  
�y,oW  
��k,2{  ,oX  
-"(  oY  
(Z  
+(  oY  
(Z  
,2{  ,o[  
-"(  o\  
(Z  
+(  o\  
(Z  
�*   AL     7   +   b             "  M   o               w  }  k       0 �     (+  
,r� ps]  
z(+  
,r ps]  
z-r) ps]  
z-r9 ps]  
zo^  
o_  
o`  

-rK p(Z  
* (a  
&(  ob  
,]oc  
od  
+1oe  
t'  %of  
�og  
t8  
of  
�	oh  
oi  
oj  
-��u  ,o4  
��r p(Z  
o7  
(-  
� *    � =�       d {�   0 �     ok  
r pol  

o^  
o_  
r. po`  
oc  
od  
+Woe  
t'  r pof  
�og  
t8  (m  
,-of  
�og  
t8  
	(n  
,(  	(o  
� oj  
-��u  ,o4  
�**      1 c�     0 �    r@ p{  (K  
{  (K  
(o  
{  (p  

{  %-&r� p(C  

{  ,$rT p(q  
rb p(q  

rl p(q  
o^  
o_  
rv po`  
{  sr  
(	  &{  -+os  
{  ,rT p(  	rb p(  ot  
ou  
�,o4  
�,hoc  
od  
+8oe  
t'  u(  		,!	ov  

ow  
,	
(q  
ox  
oj  
-��u  ,o4  
�r� p(C  
%-&(y  
(z  
(q  
{  o{  
�2{  ,{
  (|  
-(  (Z  
+(  (Z  
�*   (   � L�      EL     ��2    0 �    
8z  �9l  o}  
9a  -o~  
./o~  
@�  o  
r� p(m  
,8�%
(�  
-{
  r� p(�  
o2  
*{  �o�  
&8�  r� p(m  
,8�%
(�  
-{
  r� p(�  
(�  
o2  
*�}
  8�  r� p(m  
,}  8�  r� p(m  
,}  8�  r� p(m  
-
r� p(m  
,){
  �%
(�  
}  {  (+  
9B  *r� p(m  
,){
  �%
(�  
}  {  (+  
9  *r� p(m  
,}  8�  r� p(m  
,,{
  r� pr� p(�  
o2  
{
  o�  
8�  r� po�  
9�   o  

	o}  
-{
  o�  
+6	(�  
o�  
(�  
2{
  	(�  
o2  
{
  o�  
�O  &{
  	(�  
o2  
�7  &{
  	(�  
o2  
�  r� p(m  
,,{
  r� pr p(�  
o2  
{
  o�  
8�  r po�  
9�   o  
o}  
-{
  o�  
+8(�  
o�  
(�  
2{
  (�  
o2  
{
  o�  
�x  &{
  (�  
o2  
�_  &{
  (�  
o2  
�F  r p(m  
,{
  o�  
8(  r p(m  
,){
  �%
(�  
}  {  (+  
9�   *r p(m  
,}  8�   r p(m  
-
r p(m  
,}  *{	  o�  
&8�   {  -c{
  (�  
}  {  (+  
,*{  -u{  (�  
(�  
}  {  -S{
  r$ p(�  
o2  
*{  - {
  (�  
}  {  (+  
,*{
  (�  
o2  
�
�i?}���* 4    �Q.)    �QF*    �T)    �T*  0   	  (�  
-r: p(�  
r� ps�  
zr� p�  %�o�  

o�  
ds�  
o�  
8�   o�  
t8  
o�  
t.  ,	o�  
9�   	o�  
	(q  
s�  
o�  
o�  
�s�  
o�  
+�o�  
o�  
3�jo�  
&o�  
	+	�o�  
o�  
		3��,o4  
�o�  
:=���(�  
*      � b�     (%  
*�~  -r� p�  (#  
o�  
s�  
�  ~  *~  *�  *V(  r@ p~  o�  
*V(  rX p~  o�  
*V(  r� p~  o�  
*V(  r� p~  o�  
* BSJB         v2.0.50727     l   �
  #~  �
  �  #Strings    �  �  #US �"     #GUID   �"  �  #Blob         W�		   �3      b               �      	                                   $       � �� oq 
   �� � �� �� �� �� i� @� �� M� ,� R�
 
q � ��
 	 �
8 x8
 E �� �	8 �	� G8 �
8 �8 	8 8 9- �- w 8 �� X8  �  9s 58 .8 p	� �	�  �  )R 3R �>  �	>  �> 
 / 0q �� �
� �	~
 �
 � p�  � �� �� �	8 �
8 �� �
8 �
8 �
8 �>  8 �8 C	� "	� �� 8 [ �- x- Z	� �8 �>  5
>  	8 &8 F
� zR L
� �
8 �8 �
8 O8 �	� $
~ �� �
� �	� �R  �  �>  �>  us    5       � �	8=    y@=      �
@=  
  }� K� �� �� f� �� � �� �
� #� �� �� � �� �� [
� C
� I� �� �� � P     � � �!    � Y "    �j�  T"    �d  �"    � � �"    � �	 �$    � �  �&    � N" �'    � \ . �(    � ? 
 �*    � J8
 �/    � 4> �0    �d  �0    ��
M �0    ��� �0    ��S 1    �$- 1    ��- 31    ��- I1    �\-       [   [  �         r    �   �      �   r       �   �
      �   	 d  d  d
 ) d 1 d 9 d A d I d Q d Y d a d i d q d �d �d �d �d  � #8 � 
< � �8 �:B � f8 � )8 � 8 �jH � d � �M � �M ��Q Q d8 Y X8 1 �	8 a 38 ) �8 ��y � �
� y d � d � d ��� ��� � �
M ��� ��
� �[� ��� � V� � �8 ��� � A� � � � � 9� � �� � �8 � m� y d8 � B8 !/	� � d � d � d� �\� � � � � �� �9^� � F � F � � Ad� �� �� �� � ,� yI, &	d,	n 	r
4	d
4	�9Y� \@Y� Y� � t
� � 08 �[G� t
� � 08 ad `� _
ei�kqpr3y!���V�1��9���J � ���S �1� ������������[�� �ed
�
 j
�#A�8  
$AQ �--1��=��M ��P�wU��Z!� a� � h��!�a��m��w��
 ��������� �!� a�
 �& !a�� �.
 ��m����� � u�!��!� aq�� �%��d �%�a�  d a��i��$ j�$ � �yd y�y�d q�M qq	��N!�d&�m-) � �.  j.  s.  �. # �. + �. 3 �. ; �. C �. K �. S �. [ ?. c �. k bI � �� s m� { �� � �' q � � M��E�    �
Z  �`  (f  �f  �f  `f              	      
 v ����            G~             5{                5�               >=               5-               >�               >R           �       H  X  ]  b  g  l  M    ICollection`1 List`1 ToInt32 IDictionary`2 get_UTF8 <Module> System.IO T get_Data set_Data ValidateMsiMatchesPdb inputPdb mscorlib System.Collections.Generic IllegalSuppressWarningId Load Add TypeSpecificationForExtensionRequired AdditionalArgumentUnexpected System.Collections.Specialized id Field Unbind Record Replace get_StackTrace set_Source get_WAR_MismatchedPackageCode DatabaseOpenMode Microsoft.Deployment.WindowsInstaller.Package InstallPackage installPackage package add_Message get_Message ElevateWarningMessage SuppressWarningMessage get_HelpMessage ExtractFilesInBinaryTable MeltBinaryTable GetEnvironmentVariable IEnumerable IDisposable RuntimeTypeHandle GetTypeFromHandle get_File inputPdbFile GetFile inputFile outputFile Console get_Title MeltModule tableName GetRandomFileName GetFileName get_ProductName GetDirectoryName ParseCommandLine WriteLine Combine GetType GetOutputType outputType get_Culture set_Culture resourceCulture get_InvariantCulture Database Close Dispose Create EditorBrowsableState CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute ClearReadOnlyAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute Execute ReadByte WriteByte TryGetValue value Save melt.exe Microsoft.Tools.WindowsInstallerXml.Serialize Encoding ToString GetString Substring set_Formatting IsValidArg Fetch DeprecatedCommandLineSwitch exportBasePath GetTempPath exportPath VerifyPath path get_Length StartsWith Seek original System.ComponentModel System.Xml Microsoft.Tools.WindowsInstallerXml OutputXml VSExtensionsLandingUrl NewsUrl SupportUrl FileStream MemoryStream get_Item System resourceMan Main SeekOrigin AddExtension UnexpectedFileExtension GetExtension WixExtension get_FileVersion get_Location get_TempFilesLocation set_TempFilesLocation ReadConfiguration set_Indentation System.Globalization PrepareConsoleForLocalization set_SuppressDemodularization suppressExtraction System.Reflection TableCollection StringCollection RowCollection SEHException UnexpectedException NullReferenceException ArgumentNullException FormatException ArgumentException OverflowException WixException get_Description WixDistribution AppCommon StringComparison Run FileInfo CultureInfo FileVersionInfo GetVersionInfo NumberFormatInfo DirectoryInfo showLogo InstallPathMap showHelp set_QuoteChar set_IndentChar get_LastErrorNumber DisplayToolHeader IFormatProvider Unbinder get_ResourceManager BinderFileManager IMessageHandler ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler Decompiler Microsoft.Deployment.WindowsInstaller XmlWriter XmlTextWriter Melter DisplayToolFooter WixVariableResolver get_WAR_FailedToDeleteTempDir get_Major get_Minor get_Error get_EncounteredError IllegalWarningIdAsError set_WarningAsError IEnumerator StringEnumerator GetEnumerator .ctor .cctor System.Diagnostics get_Fields System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Tools.MeltStrings.resources DebuggingModes set_ShowVerboseMessages InstallPackageProperties get_Tables get_Files DeleteTempFiles ExtractFiles names GetCustomAttributes set_SuppressAllWarnings WixWarnings MeltStrings invalidArgs MessageEventArgs WixWarningEventArgs WixErrorEventArgs args get_SourcePaths GetFilePaths Microsoft.Tools.WindowsInstallerXml.Tools Contains System.Collections get_Chars ReplacePlaceholders WixErrors get_Comments Exists get_Rows get_INF_TempDirLocatedAt Concat TelemetryUrlFormat get_NumberFormat exportToSubDirectoriesFormat Object get_Product MeltProduct ShortProduct get_Copyright get_LegalCopyright Melt melt Environment WriteEndDocument WriteStartDocument ExpectedArgument UnsupportedCommandLineArgument get_Current Convert extensionList get_Output MoveNext System.Text OpenView WixFileRow Wix wix Display tidy get_Assembly assembly get_Company CreateDirectory DeleteDirectory GetDirectory op_Equality op_Inequality IsNullOrEmpty get_Property    [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  	M E L T  m e l t . e x e  W I X _ T E M P  t a b l e N a m e  e x p o r t P a t h  p a c k a g e  i n p u t P d b  3T a b l e   { 0 }   d o e s   n o t   e x i s t .  ��A n   e r r o r   o c c u r e d   e x t r a c t i n g   t h e   { 0 }   b i n a r y   t a b l e   f r o m   t h e   i n s t a l l   p a c k a g e .  P a c k a g e C o d e  P r o p e r t y  { 0 }   /   { 1 }  
B i n a r y  	I c o n  	F i l e  W i x F i l e  e x t  	- e x t i d  - 
n o l o g o  
n o t i d y  o  o u t  p d b  s e x t r a c t  s w a l l  s w  w x a l l  w x  v  x  x n  ?  	h e l p  . m s m ,   . m s i  MT h e   p a t h   s p e c i f i e d   d o e s   n o t   e x i s t .   { 0 }  	p a t h  AS e l e c t   ` N a m e ` ,   ` D a t a `   F R O M   ` { 0 } `  kM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . T o o l s . M e l t S t r i n g s  H e l p M e s s a g e  )I N F _ T e m p D i r L o c a t e d A t  3W A R _ F a i l e d T o D e l e t e T e m p D i r  3W A R _ M i s m a t c h e d P a c k a g e C o d e    ��վ�D�.�#lj         ��E)-1I   E     
)
-

1
  ���� ��  ]aei      ] � �   �
  �� �
	mquy]}a���� m Q } }  �! yY ��y  �% �%  �) �- ������a  y  �5 �� �= ����  �A  ��    �E��   
����a  �I   ������������a �� 	 �M  �Q	  ��     �U�Y �]
    �
 	 U � �e  ��  �i �q U Y �

��������������  ���}  �� ��      ��  
  

�� ��  A A ���z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����QUY���� A	A    ����	 ����  ����  �� �� �� ��         TWrapNonExceptionThrows       WiX Toolset MSM Melter   Melter          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US  @ 3System.Resources.Tools.StronglyTypedResourceBuilder4.0.0.0      m  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP̿n������I!쨎5H             �  H e l p M e s s a g e     (I N F _ T e m p D i r L o c a t e d A t B  2W A R _ F a i l e d T o D e l e t e T e m p D i r i  2W A R _ M i s m a t c h e d P a c k a g e C o d e �  �
 usage: melt.exe [-?] [-nologo] {database.msm output.wxs|database.msi output.wixpdb -pdb source.wixpdb} [@responseFile]

   -ext <extension>  extension assembly or "class, assembly"
   -id        friendly identifier to use instead of module id
   -nologo    skip printing melt logo information
   -notidy    do not delete temporary files (useful for debugging)
   -o[ut]     specify output file (default: write to current directory)
   -pdb       specify .wixpdb matching database.msi
   -sextract  suppress extracting .msi payloads when updating .wixpdb
   -sw[N]     suppress all warnings or a specific message ID
              (example: -sw1059 -sw1067)
   -swall     suppress all warnings (deprecated)
   -v         verbose output
   -wx[N]     treat all warnings or a specific message ID as an error
              (example: -wx1059 -wx1067)
   -wxall     treat all warnings as errors (deprecated)
   -x <path>  export binaries from database to this path
              (defaults to output files path)
   -xn        export contents of File, Binary, and Icon tables
              to subdirectories to support patching all types of files.
              Needs -x.
   -? | -help this help information

Environment variables:
   WIX_TEMP   overrides the temporary directory used for cab extraction, binary extraction, ...%Temporary directory located at '{0}'.2Warning, failed to delete temporary directory: {0}DWarning, package codes in .msi '{0}' and .wixpdb '{1}' do not match.       Q�}��Br����t-j�^�� ?J�z����%zâ�(�n�/˲�d���􎯾�p���i��l��%1-m���w�=�~3Y�����*Q4��1N�����q��݊k��b���X
���V�������!    )p�]         �a  �Q  RSDS�o^�[F��4���{   C:\agent\_work\66\s\build\obj\ship\x86\melt\melt.pdb                                                                                                                                                                                                                8c          Rc                          Dc            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              �   P  �                  8  �               	  �                     h  �               	  �  ��  T          T4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   N   F i l e D e s c r i p t i o n     W i X   T o o l s e t   M e l t e r     8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   *   I n t e r n a l N a m e   m e l t     � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   : 	  O r i g i n a l F i l e n a m e   m e l t . e x e     \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	��  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"> 
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Melt" version="*******" processorArchitecture="x86" type="win32"/> 
 <description>WiX Toolset Melter</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       `     d3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      