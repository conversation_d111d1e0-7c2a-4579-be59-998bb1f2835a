MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L (p�]        � 0  0          �L       `    @                       �     ^\  @�                           8L  O    `  �                   �      K                                                               H           .text   �,       0                    `.rsrc   �   `      @              @  @.reloc      �      P              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                lL      H     �)  �       XE  (  �J  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�'  r�  po  
�'  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po   
o  
 (  +,r�  p	o!  
o  
 (  +,r+ po"  
o  
 (  +,rO po#  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  ($  
o%  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *�(&  
s'  
}  r� pr� ps(  
}	  }
  }  *F()  
s  o  *  0 g    
(  {	  o*  
,{	  o+  

�<  {
  (,  
,{  (,  
,}  {
  ,(-  
{  , (
  (.  
(/  
{	  o+  

��  {  o0  
+o1  
{	  (2  
o3  
o4  
-��u  ,o5  
�}  {
  (,  
-
{
  (6  
+{  (6  
{  (,  
,	}  +.{  r� po7  
,{  (8  
(9  
(6  
}  s:  
{	  %�3  
s;  
o<  
r� p(=  
o>  
{
  (,  
-+{  {  o?  

�P&{	  (@  
o3  
�;{  (,  
-{  {  {  oA  

+{  {  oB  

{  ,oC  
&�Z{	  oD  
o3  
�C{	  oE  
oF  
oG  
oH  
(I  
o3  
u5  -	u6  ,�� {	  o+  
-,{	  o+  
**	* Ad     �   )   �              v     �              �  �              �    C     0 h    
8W  �9I  oJ  
9>  -oK  
./oK  
@�  oL  
r p(M  
-
r p(M  
,}  *r p(M  
,5{	  �%
(N  
}  {	  �%
(N  
}  8�  r p(M  
,{	  �%
(N  
}  8�  r% p(M  
,{	  �%
(N  
}
  8e  r+ p(M  
,}
  8L  r9 p(M  
,}  83  rG p(M  
-
rK p(M  
,{	  �%
(O  
}  8�  rS p(M  
,,{	  rS pr_ p(P  
o3  
{	  oQ  
8�  r_ poR  
9�   oL  

	oJ  
-{	  oQ  
+6	(S  
oT  
(U  
2{	  	(V  
o3  
{	  oW  
�Y  &{	  	(V  
o3  
�A  &{	  	(V  
o3  
�)  re p(M  
,{	  oX  
8  ri p(M  
,,{	  ri pru p(P  
o3  
{	  oY  
8�   ru poR  
9�   oL  
oJ  
-{	  oY  
+8(S  
oT  
(U  
2{	  (Z  
o3  
{	  o[  
�g&{	  (Z  
o3  
�Q&{	  (Z  
o3  
�;{  o\  
&+,@oK  
3oL  
(]  
(  +
{  o\  
&�
�i?����*4    �Q    �Q    �Q�    �Q	  (&  
*�~  -r{ p�  ($  
o^  
s_  
�  ~  *~  *�  *V(	  r� p~  o`  
*V(	  r+ p~  o`  
*V(	  rC p~  o`  
*V(	  rm p~  o`  
* BSJB         v2.0.50727     l     #~  �  �  #Strings       �  #US �     #GUID   �  �  #Blob         W�		   �3      <               `                                         )      h� �� �� 1	   �� K� �E �� � �� �� �� ,� �� �
E U�
 �� �E E
 �V  � 3E �
 ^V  � E ~� �E @E \E lE
 a� x� F� \� ��
 �
 �� BE  E �E � E �� �� KE �	� �	� �	� �  �� �
E I
� �	� )E � �� �E �� /E HE +�    	       � ��=     �	=      �	�	=    2% �
% :% �
% % o% �% �	( �, z% �% 0 0 L0 L3 �8P     � 5
= �!    � �D "    ���  R"    �}  �"    � XN �"    � �T t%    � �Z )    �}  $)    �X` P)    ��> W)    ��f _)    �]m	 u)    �� m	 �)    �s
m	 �)    ��m	    �   ^   ^     �	   �	   �	   #	 }  }  }
 ) } 1 } 9 } A } I } Q } Y } a } i } q } � } � } } 	} )}  � �8 � �< � f
8 1u B � �
8 � r8 � 8 1"
H � } �  M � 
M 1�
Q Q g8 Y �
8 1 �8 a �
8 ) \8 A
y � h	� y } � } � } Q�� Y� � "M 1�� Q6� a�� Q�� � o� � #8 i� � D� � 7� �  ��� 1�H �f� ��� � } �}� � l� �� � � � � �� �S
� � � � �� � X	� � � � � 8 � �� y 68 � } 8 �1�M 1+
1I1��C$�s$iS.Y|	 1�6�>�
D� J�9 � Y�  Y@	 YK �3� Y�  � R R�WAQ]}b?i) � �.  �.  �.  �. # �. + �. 3 �. ; �. C �. K �. S . [ X. c �. k {I � �� { �� � �� � �� s �' q � 
    \q  �w  a}  � }  w
}  �} 	   
         
 	      
 v �            �'              q0                qE               z@               zS           �         X  ]  b  g  l  M      ToInt32 <Module> System.IO T Insignia insignia mscorlib IllegalSuppressWarningId Add System.Collections.Specialized Replace get_StackTrace get_Message ElevateWarningMessage SuppressWarningMessage get_HelpMessage GetEnvironmentVariable IDisposable RuntimeTypeHandle GetTypeFromHandle InscribeBundle CommandLineResponseFile GetFile Console wconsole get_Title GetFileName get_ProductName ParseCommandLine WriteLine Combine InscribeBundleEngine GetType get_Culture set_Culture resourceCulture get_InvariantCulture InscribeDatabase Dispose Parse EditorBrowsableState MTAThreadAttribute CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute value insignia.exe ToString GetString Substring DeprecatedCommandLineSwitch bundlePath msiPath GetFullPath bundleWithAttachedContainerPath outputPath get_Length EndsWith StartsWith original System.ComponentModel Microsoft.Tools.WindowsInstallerXml VSExtensionsLandingUrl NewsUrl SupportUrl System resourceMan Main get_EXP_CannotLoadLinkerExtension get_FileVersion get_Location set_TempFilesLocation System.Globalization PrepareConsoleForLocalization System.Reflection StringCollection SEHException UnexpectedException NullReferenceException UnauthorizedAccessException FormatException OverflowException WixException get_Description WixDistribution AppCommon StringComparison Run CultureInfo FileVersionInfo GetVersionInfo NumberFormatInfo showLogo showHelp Inscriber get_LastErrorNumber DisplayToolHeader IFormatProvider get_ResourceManager add_MessageHandler ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler DisplayToolFooter get_WAR_FailedToDeleteTempDir get_Major get_Minor get_Error get_EncounteredError IllegalWarningIdAsError set_WarningAsError StringEnumerator GetEnumerator .ctor .cctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Tools.InsigniaStrings.resources DebuggingModes set_ShowVerboseMessages DeleteTempFiles GetCustomAttributes set_SuppressAllWarnings WixWarnings InsigniaStrings invalidArgs MessageEventArgs WixWarningEventArgs WixErrorEventArgs args Microsoft.Tools.WindowsInstallerXml.Tools Contains get_Chars ReplacePlaceholders WixErrors UnauthorizedAccess get_Comments get_INF_TempDirLocatedAt Concat TelemetryUrlFormat get_NumberFormat Object get_Product ShortProduct get_Copyright get_LegalCopyright Environment UnsupportedCommandLineArgument get_Current Convert MoveNext wix Display tidy get_Assembly assembly get_Company GetFileOrDirectory op_Equality IsNullOrEmpty  [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  	I N S G  I n s i g n i a . e x e  \  W I X _ T E M P  ?  	h e l p  a b  i b  i m  
n o l o g o  
n o t i d y  o  o u t  s w a l l  s w  v  w x a l l  w x  sM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . T o o l s . I n s i g n i a S t r i n g s  ;E X P _ C a n n o t L o a d L i n k e r E x t e n s i o n  H e l p M e s s a g e  )I N F _ T e m p D i r L o c a t e d A t  3W A R _ F a i l e d T o D e l e t e T e m p D i r     �_6�}�I��y�� (X         ��E)-1I   E     
)
-

1
  ���� ��  	]aeim      a �� ��    ��  ��    ��  �� ��	   	 U �� ��  ��  �� ��    A A ���z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����QU���� A	A      �� ��   �� ��         TWrapNonExceptionThrows       WiX Toolset Inscriber   	Inscriber          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US     @ 3System.Resources.Tools.StronglyTypedResourceBuilder*******    ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP���    �   H e l p M e s s a g e     � usage: insignia.exe [-?] [-nologo] [-o[ut] outputPath] [@responseFile]
           [-im databaseFile|-ib bundleFile|-ab bundlePath bundleWithAttachedContainerPath]
   -im        specify the database file to inscribe
   -ib        specify the bundle file from which to extract the engine.
              The engine is stored in the file specified by -o
   -ab        Reattach the engine to a bundle. 
   -nologo    skip printing insignia logo information
   -o[ut]     specify output file. Defaults to databaseFile or bundleFile.
              If out is a directory name ending in '\', output to a file with
               the same name as the databaseFile or bundleFile in that directory
    -sw[N]     suppress all warnings or a specific message ID
               (example: -sw1009 -sw1103)
    -swall     suppress all warnings (deprecated)
    -v         verbose output
    -wx[N]     treat all warnings or a specific message ID as an error
               (example: -wx1009 -wx1103)
   -wxall     treat all warnings as errors (deprecated)
   -? | -help this help information      :�.]%���Ӎ����%�xK��i�1�iv�����\3A�i]+��%�*~����!����Z��D��ce�,
a������֝�
�9�	�}�/��_�;���X�u�T]<�A̃��p_?���U�Uel$    (p�]         K  ;  RSDS�v�a7�J���2. 2�   C:\agent\_work\66\s\build\obj\ship\x86\insignia\insignia.pdb                                                                                                                                                                                                        `L          zL                          lL            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      �   P  �                  8  �               	  �                     h  �               	  �  �`  h          h4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   T   F i l e D e s c r i p t i o n     W i X   T o o l s e t   I n s c r i b e r   8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   2 	  I n t e r n a l N a m e   i n s i g n i a     � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   B 
  O r i g i n a l F i l e n a m e   i n s i g n i a . e x e     \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	�d  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Insignia" version="*******" processorArchitecture="x86"  type="win32"/>
 <description>WiX Toolset Inscriber</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             @     �<                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      