MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L +p�]        � 0  0         E      `   @                       �    �  @�                           �D O    ` �                   �    �C                                                              H           .text   %      0                   `.rsrc   �   `     @             @  @.reloc      �     P             @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �D     H     ă  @�     �          C �                                   0 r    94  o
  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�8  r�  po  
�8  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po  
o  
 (  +,r�  p	o  
o  
 (  +,r+ po  
o  
 (  +,rO po  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  (  
o  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 �     (   
s!  
}
  s!  
}  }  ,J
+>�(  
	,{
  	�  o"  
+r� p�  %�(G  &X�i2�,P
+D�(  ,{  �  o"  
+r� p�  %�(G  &X�i2�*0 �     
}  }  {  s#  

sk  o$  
o%  
�2
r p�  %	o&  
�(G  &{  �l,o'  
�(  ,Q{  /H((  
s)  
o*  
�,o+  
��!&rM p�  (G  &�
,o,  
�{  **   @     06 (    X^ 
     � �       { '�    { >� 
    0 [     �  (  
(-  
o.  

+%o/  
�  �  o0  
(1  
,�o2  
-��u  
	,	o+  
�**     1F     0 �     
+%(3  
o4  
o5  
(1  
,��
��o4  
o6  
2�-
~7  
o8  
+o4  
(3  
(9  
o8  
�2�o4  
 �s:  
(9  
o8  
*   0 .    o;  
o<  
3o;  
o=  
&+o>  
o=  
&s?  

+uo@  
oA  
o@  
oB  
&oC  
r poD  
,;r� poE  
(1  
,oC  
o8  
~7  
oF  
+oE  
oF  
+
o@  
oG  
&o@  
oH  
=z���oI  
oJ  
+o/  
t  
o@  
	oG  
&o2  
-��4u  ,o+  
�oK  
oL  
oM  
&oN  
&oK  
oO  
0�*     � %�     0 h     o;  
oE  
oP  

(  +:oK  
oL  
oQ  
3 oC  
oC  
(1  
,
t  (	  &�oK  
oO  
2�*0 S   	  +(3  
o6  
oR  
(3  
oD  
-�o6  
�.*
+oS  
 .*Xo6  
2�* 0 �   
  oT  
oQ  
3AoT  
t   
r� poU  
(V  
-Or� p�  (G  ,:r� poW  
+-r7 p�  (G  ,r� pr� poX  
oY  
&o<  
(  &*0 *    oQ  
3o4  
(3  
oZ  
0oQ  
.oQ  
.	oQ  
3(  oQ  

.;�  8�  o@  
o[  
+o/  
t  (
  o2  
-��u  
	,	o+  
�r� poC  
(1  
9�  oE  
(�   "JěB�    F�f25u j��
5) h�
;�   ,�t;�   j��
;�  8[   g�L5 ��;�   g�L;  85   �~<;�   F�f2;  8   ���Y5) o�5;�   +�K;a   ���Y;  8�   �nw5 ZG�u;   �nw;�  8�   ��;;   "Jě;O  8�   �~��5u �mn�5) ����;   J\l�;-   �mn�;�  8h   �6�5 ���;I   �6�;'  8B   F�i�;�   �~��;�  8%   ��x�5C #r��5 ��I�;�    #r��;W  8�   >��;�   ��x�;P  8�   ¶�5 �eD�;(   ¶�;�  8�   .���;�    �b!�;9  8�  r p(1  
:L  8�  r' p(1  
:6  8j  rE p(1  
:   8T  rU p(1  
:
  8>  rm p(1  
:�  8(  r� p(1  
:�  8  r� p(1  
:�  8�  r� p(1  
:�  8�  r� p(1  
:�  8�  r� p(1  
:�  8�  r p(1  
:p  8�  r) p(1  
:Z  8�  r= p(1  
:D  8x  rK p(1  
:.  8b  rc p(1  
:  8L  r� p(1  
:  86  r� p(1  
:�   8   r� p(1  
:�   8
  r� p(1  
:�   8�   r� p(1  
:�   8�   r p(1  
:�   8�   r p(1  
:�   8�   r- p(1  
:�   8�   rA p(1  
-{8�   ri p(1  
-h+yru p(1  
-X+ir� p(1  
-H+Yr� p(1  
-8+Ir� p(1  
-(+9t  (D  8�  t  (E  8�  t  (F  8�  (\  
r� p�  %oE  
�(]  
s^  
zr	 poC  
(1  
,t  (C  8A  t  oE  
(�   ZG�uB�   �ȥ-BZ   ��B�    ���5O *r 5 ,B\ ;	   *r ;  8�   h�
;.   ,�t;d   ���;�  8�   ��U5 �(j;�   ��U;(  8�   ;B
;�   j��
;�
   ��;
  8]   5j
!5O �~5 9�\;,   �~;.  8.   g�L;/
   �~<;1	   5j
!;]  8   ���*5 ��k";Y   ���*;  8�
   ���*;v   c6+;\   �ȥ-;|  8�
   y
jLB�    ^->5O F�f25 ��U2;�   F�f2;�  8{
   �5;�   o�5;�   ^->;>  8R
   ��I5 {�>H;�   ��I;R  8,
   ���I;�   +�K;�   y
jL;G  8
   ���Y5O ��!R5 �}�P;r   ��!R;�  8�   �daR;   �`�R;�   ���Y;#  8�   ��h5 oM_;�   ��h;a  8�   �p;H   �ѿt;�	   ZG�u;�  8\   �`��BZ   ��
�B�    �[r�5O O��5 �nw;m   O��;�  8   >�G�;�   ��;�   �[r�;L  8�   ��ܗ5 (j"�;   ��ܗ;�  8�   "Jě;m   V�;�   ��
�;�  8�   ��\�5O J\l�5 ����;X   J\l�;j  8n   �mn�;�   �峯;�   ��\�;  8E   ���5 ��s�;-   ���;]  8   �6�;6   v�U�;�   �`��;l  8�
   >��B�    ���5O �~��5 F�i�;�   �~��;�  8�
   �Q~�;h   "g��;�   ���;�  8�
   #r��5 ��I�;�   #r��;�  8l
   ^c�;�   ���;�   >��;G  8C
   .���5O G&��5 ��x�;�   G&��;P  8
   ¶�;W   ��k�;�   .���;M  8�	   N!�5 �b!�;�   N!�..8�	   ����;�   �K��;7   ���;#  8�	  rg p(1  
:�  8�	  ru p(1  
:�  8s	  r� p(1  
:�  8]	  r� p(1  
:�  8G	  r� p(1  
:�  81	  r� p(1  
:�  8	  r� p(1  
:�  8	  r� p(1  
:�  8�  r	 p(1  
:�  8�  r1	 p(1  
:~  8�  rS	 p(1  
:u  8�  rg	 p(1  
:l  8�  r�	 p(1  
:c  8�  r�	 p(1  
:Z  8k  r�	 p(1  
:Q  8U  r�	 p(1  
:H  8?  r�	 p(1  
:?  8)  r�	 p(1  
:6  8  r�	 p(1  
:-  8�  r
 p(1  
:&  8�  r'
 p(1  
:  8�  r7
 p(1  
:  8�  rS
 p(1  
:
  8�  r_
 p(1  
:  8�  rk
 p(1  
:�  8y  ry
 p(1  
:�  8c  r�
 p(1  
:�  8M  r� p(1  
:�  87  r�
 p(1  
:�  8!  r�
 p(1  
:�  8  r�
 p(1  
:�  8�  r�
 p(1  
:�  8�  r�
 p(1  
:�  8�  r p(1  
:�  8�  r p(1  
:�  8�  r/ p(1  
:�  8�  rM p(1  
:�  8q  rc p(1  
:�  8[  r� p(1  
:�  8E  r� p(1  
:x  8/  r� p(1  
:o  8  r� p(1  
:f  8  r� p(1  
:]  8�  r� p(1  
:T  8�  r� p(1  
:H  8�  r p(1  
:<  8�  r# p(1  
:0  8�  r� p(1  
:$  8  r) p(1  
:  8i  r- p(1  
:  8S  r5 p(1  
:  8=  r p(1  
:�  8'  r' p(1  
:�  8  rU p(1  
:�  8�  rE p(1  
:�  8�  rU p(1  
:�  8�  rm p(1  
:�  8�  r� p(1  
:x  8�  r� p(1  
:b  8�  r� p(1  
:L  8w  r� p(1  
:6  8a  r p(1  
:   8K  r= p(1  
:
  85  rK p(1  
:�  8  rc p(1  
:�  8	  r� p(1  
:�  8�  r� p(1  
:�  8�  r� p(1  
:�  8�  r� p(1  
:�  8�  r� p(1  
:|  8�  r p(1  
:f  8�  r p(1  
:P  8o  r- p(1  
:F  8Y  ri p(1  
:0  8C  ru p(1  
:  8-  r� p(1  
:  8  rk p(1  
:�  8  r� p(1  
:�  8�  r� p(1  
:�  8�  r� p(1  
:�  8�  (  8�  (  8�  (  8�  (  8�  (  8~  (  8q  (  8d  (  8W  (  8J  (  8=  (  80  (  8#  (  8  (  8	  (  8�  (  8�  (   8�  (!  8�  ("  8�  (#  8�  ($  8�  (%  8�  (&  8�  ('  8�  ((  8v  ()  8i  (*  8\  (+  8M  (,  8@  (-  83  (.  8&  (/  8  (0  8  (1  8�   (2  8�   (3  8�   (4  8�   (5  8�   (6  8�   (7  8�   (8  8�   (9  8�   (:  8�   (;  +~(<  +t(=  +j(>  +`(?  +T(@  +H(A  +<(B  +0(D  +$(E  +(F  +t#  (  ,@+,oK  
oL  
�(  		-��oK  
oO  
2�*     c �     0	 �     ~	  o4  
o_  
o`  

8�   o/  
t"  $o4  
oa  
oS  
3b&r� p�  %ob  
rV poc  
od  
�(G  ,4o4  
oa  
oe  
o8  
o4  
oa  
r` pof  
o8  
o2  
:r����u  ,o+  
�*       ��     0	 �     ~	  o4  
o_  
o`  

8�   o/  
t"  $o4  
oa  
oS  
3b&r� p�  %ob  
rV poc  
od  
�(G  ,4o4  
oa  
oe  
o8  
o4  
oa  
r` pof  
o8  
o2  
:r����u  ,o+  
�*       ��     0 }  
  og  

,m
oQ  
3coQ  
3$rd p�  (G  ,Eo>  
oM  
&+6{  o4  
(
  -"r� p�  (G  ,
{  (  oQ  
3Or2 poE  
(h  
,=t  oi  
-.oj  
ok  
(l  
,r> p�  (G  ,om  
oQ  
3>on  

9�   
oQ  
@�   	r� p�  (G  ,to>  
oM  
&*oQ  
3]oo  

,S
oQ  
3Ir2 poE  
(h  
,7{  o4  
(
  -#
r? p�  (G  ,
{  (  *   0 ^     r� pop  

,N"r� p�  (G  ,8o;  
rd poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0 A       rz pos  
,3rz pot  
(l  
,!
r� p�  (G  ,rz pou  
*   0 �     
r� pop  
r� pop  
,0o4  
(l  
,#r� p�  (G  ,
o@  
oB  
&9�   
+:	o@  
,+	o@  
rl pov  
,r� po4  
(1  
,
	o>  

	-�,2r� po4  
(1  
,Cr� p�7  (H  o@  
oB  
&*rY p�  (G  ,
o@  
oB  
&*   0 �    r pop  

r6 pop  
rZ pop  
r� pop  

r� pop  
r� pop  
r� pop  
&r� pop  
---	--9&  o;  
r  pr pr pow  
'(\  
r� p�  (]  
�  (G  ,oT  
ox  
&
og  
oQ  
3og  
oy  
ox  
&,;o@  
oB  
&o;  
r poq  
o4  
o8  
o@  
oG  
&,;o@  
oB  
&o;  
r' poq  
		o4  
o8  
o@  
	oG  
&,;o@  
oB  
&o;  
r? poq  


o4  
o8  
o@  

oG  
&	,;o@  
	oB  
&o;  
rY poq  
	o4  
o8  
o@  
oG  
&,=o@  
oB  
&o;  
rg poq  
o4  
o8  
o@  
oG  
&,=o@  
oB  
&o;  
r� poq  


o4  
o8  
o@  

oG  
&o;  
o<  
o@  
r� poz  
-o;  
o<  
r� pr po{  
,:o4  
(|  
,*3%9r� p�  (G  ,o@  
oB  
&* 0 @     r� pop  

,0o4  
(l  
,#r� p�  (G  ,
o@  
oB  
&*0 H     rM pop  

,8o4  
(I  o4  
(V  
-;rS p�  (G  ,o8  
*0 H     rM pop  

,8o4  
(I  o4  
(V  
-;r$ p�  (G  ,o8  
*0 Z    r� pop  

r# pop  
rC pop  
rM pop  

9�   $rm p�  (G  9�   --o;  
r# poq  
o4  
o8  
o@  
or  
&+S--o;  
rC poq  
o4  
o8  
o@  
o}  
&+$rH p�  (G  ,*o4  
o8  
o@  
oB  
&,:,7o4  
o4  
(V  
,#7rE p�  (G  ,
o@  
oB  
&,:	,7o4  
	o4  
(V  
,#9	r6  p�  (G  ,
o@  
	oB  
&*  0 ^     r� pop  

,N"r7! p�  (G  ,8o;  
rd poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0 ^     r� pop  

,N"r�! p�  (G  ,8o;  
rd poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0     r�" pop  

r�" pop  
r� pop  
9�   $r�" p�  (G  9�   r�# pop  

	--o;  
r�# poq  

	o4  
o8  
o@  
	or  
&+dr�# pop  
-0o;  
r�# poq  
	o4  
o8  
o@  
	o}  
&+$r�# p�  (G  ,*	o4  
o8  
o@  
oB  
&9�   $r�$ p�  (G  9�   rP% pop  
-0o;  
rP% poq  
o4  
o8  
o@  
or  
&+grf% pop  
-2o;  
rf% poq  
o4  
o8  
o@  
o}  
&+$r�% p�  (G  ,*o4  
o8  
o@  
oB  
&,Q"ri& p�  (G  ,;o;  
r' poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*   0 ^     r� pop  

,N"r2' p�  (G  ,8o;  
r' poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0 ^     r� pop  

,N"r�' p�  (G  ,8o;  
r�( poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0 }     r�( pop  

r�( pop  
,ar�( p�  (G  ,Kr� po4  
(1  
,,-)o;  
r�( poq  
r�) po8  
o@  
oG  
&o@  
oB  
&*   0 �    r�" pop  

r�# pop  
r�# pop  
r� pop  

r�( pop  
r� pop  
rM pop  
r* pop  
9�   $r* p�  (G  9�   --o;  
r�# poq  
o4  
o8  
o@  
or  
&+S--o;  
r�# poq  
o4  
o8  
o@  
o}  
&+$r�* p�  (G  ,*o4  
o8  
o@  
oB  
&,:,7o4  
o4  
(V  
,#7r�+ p�  (G  ,
o@  
oB  
&	,Q"	r<, p�  (G  ,;o;  
r�( poq  
	o4  
o8  
o@  
	or  
&o@  
	oB  
&,@,=o4  
(~  
o4  
(V  
,#9r�, p�  (G  ,
o@  
oB  
&,D,@o4  
(~  
o4  
(V  
,%9r�- p�  (G  ,o@  
oB  
&,:o4  
	(|  
,*	3%9r�. p�  (G  ,o@  
oB  
&,8r� po4  
(1  
,%9r�/ p�  (G  ,o@  
oB  
&,#:r�0 p�  (G  ,
o@  
oB  
&*   0	 �    r�# pop  

r�" pop  
,vo4  
|o  
0f-co4  
rh2 p(�  
o�  

	�i3Grl2 p�  (G  ,1	�o8  
o;  
r�" poq  
	�o8  
o@  
or  
&9�   $r�3 p�  (G  9�   --o;  
r�# poq  

o4  
o8  
o@  
or  
&+dr�# pop  
-0o;  
r�# poq  
o4  
o8  
o@  
o}  
&+$r�4 p�  (G  ,*o4  
o8  
o@  
oB  
&o>  
r_5 poE  
(�  
,r�
 poE  
(�  
:  8(\  
r5 p�  %oE  
�(]  
�  (G  9�   o;  
rM poq  
o@  
rM poz  
,&o@  
rM poz  
o4  
r�6 p(9  
o8  
o4  
(l  
,8o@  
rM poz  
,&o@  
rM poz  
o4  
r�6 p(9  
o8  
o4  
(l  
,r�6 po8  
o;  
r�6 poC  
oP  
o@  
oG  
&o=  
oN  
&*Z6r�6 p�  (G  &* 0 ^     r� pop  

,N"r�8 p�  (G  ,8o;  
rd poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0 :    r�# pot  

/rz9 p�  (G  9  o6  
<  o;  
o�  
s�  
r�: pr�: po�  
o;  
(�  
r; p�  %�(]  
o�  
t  ,ErG; pr� po{  
on  
,
oQ  
3o>  
oM  
&o>  
oM  
&*o;  
(�  
ru; p�  %�(]  
o�  
t  
	,E	rG; pr� po{  
on  
,
oQ  
3o>  
oM  
&o>  
oM  
&**  0 w     r� pop  

-*r�; p�  %r�: p�(G  ,Hr�: p(	  *r�: po4  
(h  
,*r@< p�  %r�: p�(G  ,r�: p(	  ** 0     r�" pop  

r�< pop  
9�   $r�< p�  (G  9�   r�# pop  
--o;  
r�# poq  
o4  
o8  
o@  
or  
&+_r�# pop  

	--o;  
r�# poq  

	o4  
o8  
o@  
	o}  
&+$rk= p�  (G  ,*o4  
o8  
o@  
oB  
&,0o4  
(l  
,#r:> p�  (G  ,
o@  
oB  
&* 0 �     r�" pop  

9�   $r�> p�  (G  9�   r�# pop  
--o;  
r�# poq  
o4  
o8  
o@  
or  
&+_r�# pop  
--o;  
r�# poq  
o4  
o8  
o@  
o}  
&+$rv? p�  (G  ,*o4  
o8  
o@  
oB  
&*0 ^     r� pop  

,N"rQ@ p�  (G  ,8o;  
r�@ poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0 ^     r� pop  

,N"rA p�  (G  ,8o;  
rd poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0 S     r� pop  

,C4r�A p�  (G  ,-ry
 po�  
,rM po4  
o{  
o@  
oB  
&* 0 �    rRB pop  

rM pop  
rhB pop  
r|B pop  

rk
 po>  
oE  
(1  
,&,#0r�B p�  (G  ,
o@  
oB  
&,@o4  
ryC poZ  
0,5r�C p�  %o4  
�(G  ,
o@  
oB  
&9�   1r�D p�  (G  9�   o4  
o�  
r_F p(1  
-rkF p(1  
-rsF p(1  
,so;  
r|B poq  
r_F p(1  
-rkF p(1  
- rsF p(1  
-+r�F p+r�F po8  
o@  
or  
&o@  
oB  
&	,3	o4  
r_F p(1  
,!2	r�F p�  (G  ,	r�F po8  
	,3	o4  
rsF p(1  
,!3	r6G p�  (G  ,	r�F po8  
*  0 ?    r�G pop  

r�G pop  
,|r�G po4  
(1  
,#+rH p�  (G  ,Tr� po8  
+Gr� po4  
(h  
,5r� po4  
(h  
,#+rjI p�  (G  ,
o@  
oB  
&9�   ,r�J p�  (G  ,8o;  
rfK poq  
o4  
o8  
o@  
or  
&o@  
oB  
&�Q&,r~K p�  (G  ,8o;  
r� poq  

	o4  
o8  
o@  
	or  
&o@  
oB  
&� *     � P� Q  0 �       o>  
, r- po>  
oE  
(1  
,	(F  o@  
rAL poz  
-o>  
,=rc po>  
oE  
(1  
,&o@  
rAL poz  
,rAL pou  
(F  *   0 N     rM pop  

,>o4  
ryC poZ  
0*5rSL p�  %o4  
�(G  ,r�L po8  
*  0 �     r�	 pop  

9�   o4  
r�L poD  
,2o4  
r�L po�  
,o4  
o4  
o6  
�o5  
o8  
o4  
r�L poD  
-o4  
r�L poD  
,Ho4  
r�L po�  
,5.r�L p�  (G  ,o4  
o4  
o6  
�o5  
o8  
*  0 @     rMN pop  

,0o4  
(l  
,#r_N p�  (G  ,
o@  
oB  
&*0 @     r�< pop  

,0o4  
(l  
,#r�N p�  (G  ,
o@  
oB  
&*�r�< pos  
-.rwO p�  (G  ,r�< po�  
o{  
o�  
*   0 �     o;  
rfP poC  
oP  

r�P p�  (G  ,o>  
ox  
&o>  
oM  
&+'o@  
oA  
o@  
oB  
&o@  
oG  
&o@  
oH  
0�+oK  
oL  
oM  
&oN  
&oK  
oO  
0�*   0 �    rQ pop  

r!Q pop  
r�< pop  
rM pop  

	,#:	r+Q p�  (G  ,
o@  
	oB  
&*r`R p�  (G  9�  ,sr�T po4  
(1  
-r�T po4  
(1  
,Oo;  
rU pr�: poP  
(  ,*r�T po4  
(1  
,
rU po8  
+r+U po8  
*,praU po4  
(1  
-r�U po4  
(1  
,Lo;  
r�U pr�: poP  
(  raU po4  
(1  
,
r�U po8  
+r�U po8  
*,=rV po4  
(1  
,+o;  
r'V pr�: poP  
(  o�  
&*r!Q pop  
-r!Q prOV po{  
s�  
oK  
o�  
	+5	o/  
t  

oQ  
3r]V p
oE  
(1  
,

o�  
&	o2  
-��	u  ,o+  
�o�  
@�   9�   rOV po4  
(1  
,n-ko�  
t  
og  
oQ  
3og  
oM  
&oM  
&oi  
-oj  
ok  
o6  
-om  
r�< po�  
o{  
+Uo�  
	+*	o/  
t  o;  
ryV pr�: poP  


(  	o2  
-��	u  ,o+  
�o�  
--r�< p~7  
o{  
oK  
o�  
	+0	o/  
t  oQ  
3r poE  
(1  
,� 	o2  
-��	u  ,o+  
�9G  o;  
rU pr�: poP  
o;  
r]V pr�: poP  
(  
oT  
oQ  
3	oT  
oT  
o�  
&,oy  
o�  
&s�  
o@  
o[  
	+	o/  
t  o�  
&	o2  
-��	u  ,o+  
�o�  
	+R	o/  
t  r�V po�  
(h  
,1r�V po�  
(h  
,o@  
oB  
&o@  
oG  
&	o2  
-��	u  ,o+  
�*o;  
r]V pr�: poP  
(  **@   �B     �7�     4=q     %?     ]_�    0 H     r!Q pop  

,8r�V po4  
(1  
,& r�V p�  (G  ,r!Q pr�W po{  
*0 �    r�# pop  

r�" pop  
:�   :�   %r�W p�  (G  9Y  o;  
r�X pr�: poP  
o>  
ox  
&o>  
oM  
&+'o@  
oA  

o@  
	oB  
&o@  
	oG  
&o@  
oH  
0�+ oK  
oL  
oM  
&oN  
&oK  
oO  
0�*9�   $r�X p�  (G  9�   --o;  
r�# poq  

o4  
o8  
o@  
or  
&+dr�# pop  
-0o;  
r�# poq  
o4  
o8  
o@  
o}  
&+$r�Y p�  (G  ,*o4  
o8  
o@  
oB  
&* 0      rUZ pop  

rkZ pop  
,0o4  
(l  
,#r}Z p�  (G  ,
o@  
oB  
&,0o4  
(l  
,#r[ p�  (G  ,
o@  
oB  
&* 0 ^     r� pop  

,N"r�[ p�  (G  ,8o;  
rd poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0     r�" pop  

rd\ pop  
9�   $r�\ p�  (G  9�   r�# pop  
--o;  
r�# poq  
o4  
o8  
o@  
or  
&+_r�# pop  

	--o;  
r�# poq  

	o4  
o8  
o@  
	o}  
&+$r5] p�  (G  ,*o4  
o8  
o@  
oB  
&,0o4  
(l  
,#r^ p�  (G  ,
o@  
oB  
&* 0 ^     r� pop  

,N"r�^ p�  (G  ,8o;  
rd poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0 ^     r� pop  

,N"rX_ p�  (G  ,8o;  
rd poq  
o4  
o8  
o@  
or  
&o@  
oB  
&*  0 @     r� pop  

,0o4  
(l  
,#r` p�  (G  ,
o@  
oB  
&*0 S    s�  

oK  
o�  
+&o/  
t  
r�
 p	oE  
(1  
,	o�  
&o2  
-��u  ,o+  
�
og  
oQ  
3og  
o�  
+po/  
t  )r*a p�  (G  ,L
og  
oQ  
3og  
oM  
&oM  
&o>  
o�  
&,o>  
oy  
o�  
&o2  
-��u  ,o+  
�o�  
/BoK  
o�  
+o/  
t  (  o2  
-��u  ,o+  
�* (    2D      w |�      ">    0 �     r� pop  

rSb pop  
,N"reb p�  (G  ,8o;  
rd poq  
o4  
o8  
o@  
or  
&o@  
oB  
&,N"rc p�  (G  ,8o;  
r�c poq  

	o4  
o8  
o@  
	or  
&o@  
oB  
&* 0     r�
 pop  

r�c pop  
:�   (rd p�  (G  9�   o>  
o@  
rM poz  
9�   o>  
o@  
rM poz  
o4  
(�  
o�  
r�d p~7  
o  
r�d p~7  
o  
r�d p~7  
o  
o;  
r�
 poq  

r�d p(9  
o8  
o@  
oG  
&,0o4  
(l  
,#r�d p�  (G  ,
o@  
oB  
&*  0 N    r�G pop  

9;  -r�e p�  (G  9"  o4  
r�L poD  
,2o4  
r�L po�  
,o4  
o4  
o6  
�o5  
o8  
o4  
r�f poD  
9�   o4  
r�L po�  
9�   o4  
r�L poD  
-o4  
r�L poD  
,@o;  
r�f poq  
o4  
o4  
o6  
�o5  
o8  
o@  
or  
&+>o;  
r�f poq  
o4  
o4  
o6  
�o5  
o8  
o@  
or  
&o@  
oB  
&*  0 �     r�f pop  

rM pop  
,o4  
(l  
,!r�f p�  (G  ,r�L po8  
-Nr�g p�  (G  ,8o;  
r�f poq  

o4  
o8  
o@  
or  
&o@  
oB  
&(D  *  0      r�h pop  

,hr�h po4  
(1  
,#!r�h p�  (G  ,@rzi po8  
+3r�i po4  
(1  
,!!r�i p�  (G  ,rGj po8  
(D  * 0 w     r� pop  

-*r�; p�  %r�: p�(G  ,Hr�: p(	  *r�: po4  
(h  
,*r@< p�  %r�: p�(G  ,r�: p(	  ** 0 w     r� pop  

-*r�; p�  %rSj p�(G  ,HrSj p(	  *rSj po4  
(h  
,*r@< p�  %rSj p�(G  ,rSj p(	  ** 0	 8     oE  

o;  
r�j pr�j pow  
'(\  
rk p�  %oE  
�(]  
�  (G  9�   o>  
ox  
&o>  
oM  
&+'o@  
oA  
o@  
oB  
&o@  
oG  
&o@  
oH  
0�+oK  
oL  

	oM  
&	oN  
&oK  
oO  
0�o;  
o<  
o@  
r�l poz  
-o;  
o<  
r�l pr�j po{  
o;  
o<  
o@  
r�l poz  
,o;  
o<  
r�l pou  
*0	 O     r�l poC  
(h  
98  oE  

'(\  
r2m p�  %oE  
�(]  
�  (G  9�   o;  
r�n pr�l pow  
o>  
ox  
&o>  
oM  
&+'o@  
oA  
o@  
oB  
&o@  
oG  
&o@  
oH  
0�+oK  
oL  

	oM  
&	oN  
&oK  
oO  
0�o;  
o<  
o@  
r�n poz  
-o;  
o<  
r�n pr�l po{  
o;  
o<  
o@  
r�n poz  
,o;  
o<  
r�n pou  
** 0	 O     r�n poC  
(h  
98  oE  

'(\  
r[o p�  %oE  
�(]  
�  (G  9�   o;  
r�p pr�n pow  
o>  
ox  
&o>  
oM  
&+'o@  
oA  
o@  
oB  
&o@  
oG  
&o@  
oH  
0�+oK  
oL  

	oM  
&	oN  
&oK  
oO  
0�o;  
o<  
o@  
r q poz  
-o;  
o<  
r q pr�n po{  
o;  
o<  
o@  
r�n poz  
,o;  
o<  
r�n pou  
** 0	 �     rq poC  
(h  
9�  oE  

r� poE  
(1  
,'r- po>  
oE  
(1  
,rA p
+.rrq p
+&rk poE  
(1  
,o@  
r�q poz  
-*'(\  
r�q p�  %oE  
�(]  
�  (G  9�   o;  
res prq pow  
o>  
ox  
&o>  
oM  
&+'o@  
oA  
o@  
oB  
&o@  
oG  
&o@  
oH  
0�+oK  
oL  

	oM  
&	oN  
&oK  
oO  
0�o;  
o<  
o@  
ros poz  
-o;  
o<  
ros prq po{  
o;  
o<  
o@  
r�n poz  
,o;  
o<  
r�n pou  
**  0 �   !  {  �  o�  
,*{  �}  {
  �  o�  
,r�s p
+r�s p
,[r�s p�  %{  �%t(  o�  
�8  �%�%�8  �%(\  
(]  
�%�  o0  
�(�  
+W{  ,{  +r�s prt p�  %�%�%�8  �%(\  
(]  
�%�  o0  
�(�  
* 0 p   "  {  �}  ,/rMt p{  t(  o�  
�8  (\  

(]  
(�  
*{  ,{  +r�s prkt p(\  

(]  
(�  
*0 ,   #  ~  rt po�  

~
  o�  
,rt p(9  

*�r�t ps�  
�	  ru ps�  
�
  r u ps�  
�  *j}  }  (�  
*{  *{  *>}  }  *
*^}  }  (�  
*{  *{  *>}  }  *^}  }  (�  
*{  *{  *>}  }  *j}  }  (�  
*{  *{  *>}  }  *R}  {  (%  
*   0 5   $  sq  
{  u(  ,o�  
,o�  
o�  
ot  *   0 5   %  sK  
{  u(  ,o�  
,o�  
o�  
oN  *   0 3   &  sP  
{  u(  ,o�  
,o�  
o�  
oS  * 0 3   '  s�  
{  u(  ,o�  
,o�  
o�  
o�  * 0 3   (  sU  
{  u(  ,o�  
,o�  
o�  
oX  * 0 3   )  sv  
{  u(  ,o�  
,o�  
o�  
oy  * 0 7   *  sl  
{  u(  ,o�  
,o�  
o�  
oo  * 0 4   +  s{  
{  u(  ,o�  
,o�  
o�  
o~  *0 5   ,  sZ  
{  u(  ,o�  
,o�  
o�  
o]  *   0 3   -  s�  
{  u(  ,o�  
,o�  
o�  
o�  * 0 3   .  s�  
{  u(  ,o�  
,o�  
o�  
o�  *(�  
*r}  }  (�  
*{  *{  *>}  }  *j}  }  (�  
*{  *{  *>}  }  *^}  }  (�  
*{  *{  *>}  }  *b}   }!  (�  
*{   *{!  *>}   }!  *^}"  }#  (�  
*{"  *{#  *>}"  }#  *^}$  }%  (�  
*{$  *{%  *>}$  }%  *^}&  }'  (�  
*{&  *{'  *>}&  }'  *0 A       (   
s!  
}*  s�  
}-  s�  
}.  }3  }5  rZu p}1  *2s�  o�  *  0 O   /  ~�  
~�  
o�  

~�  
o�  
1
(�  
+�oR  

�o5  
	(�  
�&� *      +J 3  0 �   0  
{-  o�  
+ro/  
t7  (�  
+R	�s�  
{*  o�  
o�  
o�  
-({.  o�  
{,  o�  
{+  o  �
X	�i2�o2  
-��u  ,o+  
�{4  ,*(�  

+	�(�  �
X	�i2�*    ~�     0 �  1  (�  {3  ,5(�  
o�  
o
  
(  
r�u po  
(�  
r�u p(�  
(�  
{2  ,krYv p(�  
(�  
r�v p(�  
r)w p(�  
r�w p(�  
rx p(�  
r`x p(�  
r�x p(�  
rPy p(�  
(�  
r�y p(�  
��   {/  -{0  ,{/  {0  (�  +{1  (�  
,
{1  (�  {(  {)  {5  s  },  r�  p(�  
(�  
{-  o�  

+,	o/  
t7  {.  o�  
-r�y p(�  
�
	o2  
-��	u  ,o+  
�-+�rz po&  
o�  
(�  
� *   A4     /  8   g                 �  �     5  0 �  2  -,r�z prI{ ps�  
zs!  

s!  
8B  
s#  

s�  
	o%  
ro{ po�  
o�  
+&o/  
t  t  rM pot  
o�  
o2  
-��u  ,o+  
�r�{ po�  
o�  
+&o/  
t  t  rM pot  
o�  
o2  
-��u  ,o+  
�r�{ po�  
o�  
+0o/  
t  	{*  	t  r�# pot  
o�  
o�  
o2  
-��u  ,o+  
�	,	o'  
�:����o�  
o�  
�7  }(  o�  
{(  o�  
o�  
o�  
�7  })  o�  
{)  o�  
*  Ad     T   3   �             �   3   �             
  =   G            +   1  \  
       0 _  3  ��}2  
8C  �(l  
:0  -oS  
./oS  
@�   oR  
r| p(1  
-)r!| p(1  
-(r%| p(1  
-'r3| p(1  
-&+0}2  8�   }+  8�   }3  8�   }4  8�   r7| poD  
,oR  
}/  8�   rA| poD  
,oR  
}0  +lrK| poD  
,&oR  
(�  
(�  
}5  �E&r[| ps�  
zr�| ps�  
z@oS  
3oR  
(�  
(�  +
{-  o�  
&�
�i?����*     � 
  0 ,   4  ,' ŝ�
+oS  
a � Z
Xo6  
2�*BSJB         v2.0.50727     l   <  #~  �    #Strings    �8  �|  #US l�     #GUID   |�  �	  #Blob         W�		   �3      T      r   �   �      �   ?   
   4                                   d
      Z	0 	
0 q a   ��
 �	�
 �	�
 �	�
 ��
 OP �� f�
 � 	�
 ��
 
� 
�
 � � r �r �r �  �� �� �� �r r �� �r r 8
r 5r
 �� �r Br �� �	� 
� Ar �
r �r r 'r \r r �r cr ( 0 � k  8  �� 0 �
� " � �� � p  �r �  P� � �
r �� �r �
� �� �r f[
 2� ��
 �
�
 ��
 �
�
 �� �  �r u�
 �� �  r  �� Q}    m       � �}-     	W-     ;	Wy  K   W�  P   �W�  U   G
W�  Z    W}  _   lW�  l   %Wq  q   :W�  v   W�   {   �W� " �   �W� $ �   qW� & �   �W- ( �    v   - 6 �   �  � 6 �  ��  ��  ��  q�  �� Q�� � Q�� � Q�� � 1 �1 �1 �� 1� :� � i� F�  � ^� � ^� � ^� � ^� +� � ^� � ^� � ^� � ^� � ^� � ^� � ^� :� �� �� '� � �� 8�  �  H �  ��  �� �� �� i��V�!�V��V���V���V�i�V��
�V� �V�<�V���V��V���V��
�V�Y�V���V�p�V���V�%�V�E
�V�[�V���V�s�V���V���V���V��V���V��V���V�H�V�.�V���V��V�C�V���V���V���V�f�V���V��V�j�V���V���V��V�{�V���V���V�h�V�y�V��V���V�W�V�V �V�:�V�O�V�,�V���V�|�V���V�4�V�V�P     � �� �!    � �	� "    �� T"    �� $#    � i @$    � �
 �$    � � P%    � t" �&    � � * '    � Q2 p'    � C9 (    � �? P=    � z	G ,>    � �M ?    � �T �@    � U[  A    � j[ PA    � �[ LB    � �[ 8E    �  [  �E    � a[! �E    � %[" ,F    � �[# �G    � �[$  H    � �[% lH    � �[& �J    � _['  K    � g[( lK    � �[) �K    � �[* �N    � �[+ |Q    � E[, �Q    � H[-  R    � �a. HS    � �h/ �S    � Q[0 �T    � �[1 �U    � "[2 8V    � $[3 �V    � �[4 W    � �[5 �X    � �[6 TZ    � �h7 �Z    � �[8 @[    � _[9 \    � [: `\    � �[; �\    � [< �\    � H[= �]    � �h> �b    � �[? @c    � 8[@ �d    � �[A pe    � z[B �e    � �[C �f    � 
[D hg    � 0[E �g    � 6[F  h    � �[G �i    � �[H pj    � �[I �k    � L[J �l    � [hK �m    � ~hL $n    � ChM �n    � �hN ,o    � 5hO pp    � �hP �q    � 6hQ (s    � �hR �t    � �oS �u    � �zW hv    � ��Z �v    ��[ �v    ���[ �v    �	�; _ �v    �	;; _ �v    � Z�_ w    �N� a w    ���a )w    �	�; c 1w    �	;; c 9w    � Z�c w    �N� e Iw    ���e aw    �	�; g iw    �	;; g qw    � Z�g w    �N� i �w    ���i �w    �	�; m �w    �	;; m �w    � Z�m w    �N� o �w    � a� o �w    � @p x    � ��s \x    � p
�v �x    � ��w �x    � ��x y    � �y \y    � I�z �y    � �
�~ �y    � 2
�� $z    � ��� dz    � )�� �z    �� � �z    ��#� �z    �	�; � �z    �	;; � �z    � Z�� w    �N� � �z    ���� {    �	�; � {    �	;; � {    � Z�� w    �N� � #{    ���� ;{    �	�; � C{    �	;; � K{    � Z�� w    �N� � [{    ��-� t{    �	�; � |{    �	;; � �{    � Z�� w    �N� � �{    ���� �{    �	�; � �{    �	;; � �{    � Z�� w    �N� � �{    ���� �{    �	�; � �{    �	;; � �{    � Z�� w    �N� � |    ���� |    �	�; � $|    �	;; � ,|    � Z�� w    �N� � <|    �� � �|    � 
�� �|    � �N� }    � ��� �}    � �� �    � �H� �    � ��� ��    � ���    N   o   o  '
   :      i   4   '   �   i   l   �   �   �   �   �    i   l   �   K   �   l   '
   �   �   l   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   K   �   �   K   �   C   �   �    K      ^   "   K      ^   "   K      ^   
   �
      K      ^   +   C   �      C   �      "   "   "   �   �   O   X   �   �   "   
   �
      "   "   �   O   X   �   K      ^   C   �   �    K   �   L   �   K      ^   �   "   K      ^   "   K   �   L   "   K   �   L   "   K   �   L   K   �   �   �   K       5    K   h �  �  �  � 	 � 
 �  �  � 
 �  �  � 	 �  �  �
 ) � 1 � 9 � A � I � Q � 1� �� �� a %
& i �* i M& �0 i �& i �& i 

& �~6 � � � �; � �; �j? 1 x& 9 �& q �& A �& y �& �"g a p Y � � � � f� � � � :� � a� ��& Y� ��� ��� � )� � � � � 90� ��� � S� Y �
& ��� � �� �� � 1
& ��
� ��; �� � ;
 �j� ��� � 1
� �� �� � � � � %o
+]
1� � & ��8� �& � f� 
1_; � %@!�� � pF)o
L� �R� �R)_; � a� (h��
t��y� � �
& �P��
 � 2
�� �R�~
��� 1���}�A� � ��I�� Q�; ��Y��Q1
& �]
� �{�� �
 ��� � �� � �& ��& ���� �� � �
 � � � w�� ��� � M	6 � �		� � �� @� ��  o
�� �	H��N� i���~
��J�����?�� ��!��!H1��� R�� ����& ��8� �& � � � c1)� )�� )fE)_; )�J)�� � �� �& �hx� ~�A�; y�y�y�� 0 � �6 � ��� ��I��Q����AN� A;; � � i�#� ��a��q�-y������� � i�Ei�E�H�z
���N�� ��& �&  �e��& ��mY A��b�y�y�y��Z�i�� V��& ��H� �� �� � a@_; 1�� �� m  �  �   6 � � � � � � � � � � � � � � � � � �  � � � � � � � �  � $� (� ,� 0 4	 8 < @ D H" L' P, T1 X6 \; `@ dE hJ lO pT tY x^ |c �h �m �r �w �| �� �� �� �� �� �� �� �� �� �� �� �� �� ��.  �.  �.  	. # 	. + /	. 3 D	. ; �	. C �	. K �	#c ��
S �$S � [ � _ y � � � � Yn~���� UZ`k~�����O^p����������=Us���        	 	 
   
   
        ��  Q�  ��  Q�  ��  Q�  ��  Q�  ��  Q�  ��  Q�  ��  Q�  ��  Q�  ��  Q�  ��  Q�  ��  Q� L   M   Q   R 	  V   W 
  [   \   m   n   r   s   w   x   |   } !  � #  � %  � '  � )  � +  � - d 5�            ��             �'               ��               �r               �}           
 F  K  P  U  Z  E      localSettingsFile1 settingsFile1 Int32 Dictionary`2 localSettingsFile2 settingsFile2 PackagePlatformIntel64 <Module> <PrivateImplementationDetails> Wix2NamespaceURI get_NamespaceURI WixLocalizationNamespaceURI SetNamespaceURI WixNamespaceURI namespaceURI System.IO T value__ LineInfoCData data mscorlib System.Collections.Generic doc publicId systemId Load Add NamespaceChanged PatchSequenceSupersedeTypeChanged FileSearchNamesCombined RemoveFileNameRequired UpgradeVersionPropertyAttributeRequired ClassRelativePathMustBeAdvertised WebApplicationExtensionIdDeprecated ModuleGuidDeprecated GuidWildcardDeprecated LongNameDeprecated IgnoreModularizationDeprecated RadioGroupDeprecated SrcIsDeprecated PackagePlatformsDeprecated VerbTargetDeprecated PatchSequenceTargetDeprecated RegistryElementDeprecated FeatureFollowParentDeprecated RegistrySearchTypeRegistryDeprecated ProgIdIconFormatted FileSearchParentInvalid RequireComponentGuid PrependChild AppendChild ReplaceChild RemoveChild get_LastChild get_FirstChild UpgradePropertyChild Append Replace AddNamespace CreateWhitespace set_PreserveWhitespace IsLegalWhitespace XmlWhitespace LineInfoWhitespace InspectWhitespace CreateSignificantWhitespace XmlSignificantWhitespace LineInfoSignificantWhitespace FixWhitespace whitespace get_StackTrace CreateEntityReference XmlEntityReference LineInfoEntityReference SelectSingleNode RemoveAttributeNode GetAttributeNode XmlNode InspectNode get_ParentNode CreateTextNode node get_Message message get_NameTable XmlNameTable IEnumerable IDisposable Hashtable RuntimeTypeHandle GetTypeFromHandle inspectSourceFile sourceFile CommandLineResponseFile InspectFile Console wconsole get_Title get_Name GetFileName get_LocalName get_FullName GetIdentifierFromName get_ProductName localname ParseCommandLine WriteLine get_NewLine Clone standalone get_NodeType XmlNodeType GetType CreateDocumentType XmlDocumentType LineInfoDocumentType GetInspectorTestType inspectorTestType InsertBefore get_InvariantCulture get_CurrentCulture Capture OnVerbose Close Dispose TryParse STAThreadAttribute CompilerGeneratedAttribute ExplicitGeneratedAttribute NeutralResourcesLanguageAttribute DebuggableAttribute AssemblyTitleAttribute CreateAttribute RemoveAttribute DefaultOptionalAttribute XmlAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute LineInfoAttribute HasAttribute CompilationRelaxationsAttribute InspectAttribute AssemblyProductAttribute TryGetAttribute SetAttribute AssemblyCopyrightAttribute ParamArrayAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute get_Value set_Value RadioButtonMissingValue Remove WixCop.exe get_ItemOf LastIndexOf get_Encoding set_Encoding encoding get_PreviousSibling get_NextSibling ToString Substring DeclarationMissing XmlnsMissing WhitespacePrecedingCDATAWrong WhitespaceFollowingCDATAWrong WhitespacePrecedingNodeWrong XmlnsValueWrong DeclarationEncodingWrong WhitespacePrecedingEndElementWrong IsMatch ComputeStringHash ignoreErrorsHash searchPath GetFullPath get_Length EndsWith StartsWith namespaceuri PackageCompressedIllegal FragmentRefIllegal CompareOrdinal original PackagePlatformIntel level System.Xml Microsoft.Tools.WindowsInstallerXml get_InnerXml VSExtensionsLandingUrl NewsUrl SupportUrl get_Item set_Item GetNamedItem System Trim Enum Main get_FileVersion version get_Location CreateXmlDeclaration LineInfoDeclaration System.Globalization CreateCDataSection XmlCDataSection System.Reflection ICollection XmlAttributeCollection MatchCollection GroupCollection CreateProcessingInstruction XmlProcessingInstruction LineInfoProcessingInstruction get_LinePosition localLinePosition linePosition DirectoryNotFoundException XmlException InvalidOperationException UnauthorizedAccessException ArgumentException get_Description WixDistribution StringComparison Run InspectorTestTypeUnknown WriteTo CopyTo FileInfo IXmlLineInfo HasLineInfo SetLineInfo CultureInfo FileSystemInfo FileVersionInfo GetVersionInfo showLogo XmlNamedNodeMap showHelp WixCop Group AltDirectorySeparatorChar get_LineNumber localLineNumber lineNumber XmlReader XmlTextReader reader IFormatProvider XmlNamespaceManager InvalidIdentifier ToUpper COMRegistrationTyper InsertAfter XmlWriter StreamWriter XmlTextWriter ToLower baseDir get_Major get_Minor OnError IEnumerator GetEnumerator .ctor .cctor Inspector inspector System.Diagnostics System.Runtime.CompilerServices System.Resources DebuggingModes get_ChildNodes SelectNodes Matches InspectSubDirectories subDirectories GetDirectories ParseSettingsFiles GetFiles exemptFiles FileRedundantNames get_Attributes GetCustomAttributes get_Values GetValues errorsAsWarnings args Equals Microsoft.Tools.WindowsInstaller.Tools Contains System.Text.RegularExpressions System.Collections RegexOptions searchPatterns get_Groups get_Chars ReplacePlaceholders IllegalIdentifierCharacters ignoreErrors fixErrors errors searchPatternResults get_Comments Exists get_Keys Concat TelemetryUrlFormat Object WebFilterLoadOrderIncorrect get_Product ShortProduct target internalSubset get_Copyright get_LegalCopyright Split settingsFileDefault ToUpperInvariant InspectMediaElement InspectTypeLibElement InspectVerbElement InspectProgIdElement ReplaceElement InspectPatchSequenceElement sourceElement InspectUpgradeElement InspectIncludeElement InspectPackageElement InspectUpgradeImageElement InspectTargetImageElement InspectMergeElement InspectRemoveFileElement InspectIniFileElement InspectExternalFileElement InspectFileElement InspectCopyFileElement InspectModuleElement InspectFeatureElement InspectDigitalSignatureElement InspectDigitalCertificateElement CreateElement InspectComponentGroupRefElement InspectFragmentRefElement InspectDirectoryRefElement InspectSFPCatalogElement InspectIniFileSearchElement InspectFileSearchElement InspectRegistrySearchElement InspectUtilElement InspectServiceInstallElement XmlElement InspectControlElement InspectSqlElement InspectIconElement InspectWebApplicationExtensionElement InspectUpgradeVersionElement InspectPermissionElement destinationElement InspectWixLocalizationElement InspectIgnoreModularizationElement InspectRadioButtonElement LineInfoElement InspectHelpElement InspectRadioGroupElement InspectComponentGroupElement InspectWebFilterElement InspectIIsElement InspectClassElement InspectProductElement get_DocumentElement InspectComponentElement InspectShortcutElement InspectProgressTextElement InspectTextElement InspectWixElement InspectBinaryElement InspectCategoryElement InspectDirectoryElement InspectRegistryElement NotEmptyElement InspectPropertyElement element CreateComment XmlComment LineInfoComment Environment XmlDocument LineInfoDocument get_OwnerDocument InspectDocument get_Current get_Count indentationAmount Insert SortedList XmlNodeList ArrayList MoveNext CreateText XmlText LineInfoText get_InnerText set_InnerText InspectText text get_Index WixVariableRegex AddPrefix DeprecatedLocalizationVariablePrefix prefix ToCharArray ContainsKey get_Assembly assembly get_Company Directory directory op_Equality op_Inequality CategoryAppDataEmpty WebApplicationExtensionIdEmpty ServiceInstallPasswordEmpty ProgressTextTemplateEmpty IniFileValueEmpty ControlCheckBoxValueEmpty PropertyValueEmpty TypeLibDescriptionEmpty ClassDescriptionEmpty ServiceInstallLocalGroupEmpty IsNullOrEmpty get_IsEmpty set_IsEmpty UpgradeVersionRemoveFeaturesEmpty ShortcutWorkingDirectoryEmpty     [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  5U n k n o w n   e r r o r   t y p e :   ' { 0 } ' . ET h e   x m l   i s   i n v a l i d .     D e t a i l :   ' { 0 } ' 1C o u l d   n o t   w r i t e   t o   f i l e .  %h t t p : / / w w w . w 3 . o r g /  x m l n s  u t f - 8 yT h e   X M L   d e c l a r a t i o n   e n c o d i n g   i s   n o t   p r o p e r l y   s e t   t o   ' u t f - 8 ' . uT h i s   f i l e   i s   m i s s i n g   a n   X M L   d e c l a r a t i o n   o n   t h e   f i r s t   l i n e .  1 . 0  Yh t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 5 / 1 0 / s c a  C e r t i f i c a t e  C e r t i f i c a t e R e f  M i m e M a p  R e c y c l e T i m e  W e b A d d r e s s  W e b A p p l i c a t i o n  /W e b A p p l i c a t i o n E x t e n s i o n  W e b A p p P o o l  
W e b D i r  !W e b D i r P r o p e r t i e s  W e b E r r o r  W e b F i l t e r  
W e b L o g  W e b P r o p e r t y  'W e b S e r v i c e E x t e n s i o n  W e b S i t e  W e b V i r t u a l D i r  S q l D a t a b a s e  S q l F i l e S p e c  S q l L o g F i l e S p e c  S q l S c r i p t  S q l S t r i n g  F i l e S h a r e  'F i l e S h a r e P e r m i s s i o n  G r o u p  G r o u p R e f  P e r f C o u n t e r  P e r m i s s i o n  	U s e r  IU n k n o w n   s c a   e x t e n s i o n   e l e m e n t   ' { 0 } ' . ]h t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / H e l p E x t e n s i o n  
B i n a r y  C a t e g o r y  C l a s s  C o m p o n e n t  C o m p o n e n t G r o u p  #C o m p o n e n t G r o u p R e f  C o n t r o l  C o p y F i l e  %D i g i t a l C e r t i f i c a t e  !D i g i t a l S i g n a t u r e  D i r e c t o r y  D i r e c t o r y R e f  E x t e r n a l F i l e  F e a t u r e  	F i l e  F i l e S e a r c h  F r a g m e n t R e f  	I c o n  )I g n o r e M o d u l a r i z a t i o n  I n c l u d e  I n i F i l e  I n i F i l e S e a r c h  M e d i a  M e r g e  
M o d u l e  P a c k a g e  P a t c h S e q u e n c e  P r o d u c t  
P r o g I d  P r o g r e s s T e x t  P r o p e r t y  R a d i o B u t t o n  R a d i o G r o u p  R e g i s t r y  R e g i s t r y S e a r c h  R e m o v e F i l e  S e r v i c e I n s t a l l  S F P C a t a l o g  S h o r t c u t  T a r g e t I m a g e  	T e x t  T y p e L i b  U p g r a d e  U p g r a d e I m a g e  U p g r a d e V e r s i o n  	V e r b  W i x  W i x L o c a l i z a t i o n  H t t p H e a d e r  S e r v i c e C o n f i g  X m l F i l e  X m l C o n f i g  ��T h e   l o c a l i z a t i o n   v a r i a b l e   $ ( l o c . { 0 } )   u s e s   a   d e p r e c a t e d   p r e f i x   ' $ ' .     P l e a s e   u s e   t h e   ' ! '   p r e f i x   i n s t e a d .     S i n c e   t h e   p r e f i x   ' $ '   i s   a l s o   u s e d   b y   t h e   p r e p r o c e s s o r ,   i t   h a s   b e e n   d e p r e c a t e d   t o   a v o i d   n a m e s p a c e   c o l l i s i o n s . 	n a m e  !  kT h e r e   s h o u l d   b e   n o   w h i t e s p a c e   p r e c e d i n g   a   C D A T A   n o d e .  aT h e   w h i t e s p a c e   p r e c e d i n g   t h i s   n o d e   i s   i n c o r r e c t .  E r r o r  ��T h i s   s h o u l d   b e   a n   e m p t y   e l e m e n t   s i n c e   i t   c o n t a i n s   n o t h i n g   b u t   w h i t e s p a c e .  kT h e r e   s h o u l d   b e   n o   w h i t e s p a c e   f o l l o w i n g   a   C D A T A   n o d e .  oT h e   w h i t e s p a c e   p r e c e d i n g   t h i s   e n d   e l e m e n t   i s   i n c o r r e c t .  s r c  ��T h e   B i n a r y / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e F i l e   a t t r i b u t e   i n s t e a d .  S o u r c e F i l e  A p p D a t a  �%T h e   C a t e g o r y / @ A p p D a t a   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   a n   e m p t y   s t r i n g .     I f   y o u   w a n t   t h e   v a l u e   t o   b e   n u l l   o r   e m p t y ,   s i m p l y   r e m o v e   t h e   e n t i r e   a t t r i b u t e . D e s c r i p t i o n  R e l a t i v e P a t h  ��T h e   C l a s s / @ D e s c r i p t i o n   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   a n   e m p t y   s t r i n g . A d v e r t i s e  y e s  n o  ��T h e   C l a s s / @ R e l a t i v e P a t h   a t t r i b u t e   w i t h   v a l u e   ' n o '   i s   n o t   n e c e s s a r y   s i n c e   t h i s   e l e m e n t   i s   a d v e r t i s e d . ��T h e   C l a s s / @ R e l a t i v e P a t h   a t t r i b u t e   i s   n o t   s u p p o r t e d   f o r   n o n - a d v e r t i s e d   C l a s s   e l e m e n t s . /D r i v e r A d d R e m o v e P r o g r a m s  #D r i v e r D e l e t e F i l e s  %D r i v e r F o r c e I n s t a l l  D r i v e r L e g a c y  /D r i v e r P l u g A n d P l a y P r o m p t  D r i v e r S e q u e n c e  	G u i d  
D i s k I d  d i f x a p p  
D r i v e r  ch t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / D i f x A p p E x t e n s i o n  �T h e   C o m p o n e n t / @ D r i v e r *   a t t r i b u t e s   a r e   n o w   s e t   v i a   t h e   D r i v e r   e l e m e n t   w h i c h   i s   p a r t   o f   t h e   D i f x A p p   e x t e n s i o n .     A n   x m l n s : d i f x a p p = " h t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / D i f x A p p E x t e n s i o n "   a t t r i b u t e   s h o u l d   b e   a d d e d   t o   t h e   W i x   e l e m e n t   a n d   t h e s e   a t t r i b u t e s   s h o u l d   b e   m o v e d   t o   a   ' d i f x a p p : D r i v e r '   e l e m e n t   w i t h o u t   t h e   ' D r i v e r '   p r e f i x . #A d d R e m o v e P r o g r a m s  D e l e t e F i l e s  F o r c e I n s t a l l  
L e g a c y  #P l u g A n d P l a y P r o m p t  S e q u e n c e  x m l n s : d i f x a p p  ��T h e   C o m p o n e n t / @ D i s k I d   v a l u e   i s   s e t   t o   i t s   d e f a u l t   v a l u e   o f   1 .   O m i t   t h e   D i s k I d   a t t r i b u t e   f o r   s i m p l i f i e d   a u t h o r i n g .  C h e c k B o x V a l u e  ��T h e   C o n t r o l / @ C h e c k B o x V a l u e   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   t h e   e m p t y   s t r i n g . I d  ��T h e   C o m p o n e n t G r o u p / @ I d   s p e c i f i e d   c o n t a i n s   i n v a l i d   c h a r a c t e r s .   R e m o v e   o r   r e p l a c e   t h e   i n v a l i d   c h a r a c t e r s .  ��T h e   C o m p o n e n t G r o u p R e f / @ I d   s p e c i f i e d   c o n t a i n s   i n v a l i d   c h a r a c t e r s .   R e m o v e   o r   r e p l a c e   t h e   i n v a l i d   c h a r a c t e r s .  'D e s t i n a t i o n L o n g N a m e  D e s t i n a t i o n N a m e  )D e s t i n a t i o n S h o r t N a m e  ��T h e   C o p y F i l e / @ D e s t i n a t i o n L o n g N a m e   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   D e s t i n a t i o n N a m e   a t t r i b u t e   i n s t e a d .  ��T h e   C o p y F i l e / @ D e s t i n a t i o n S h o r t N a m e   a n d   D e s t i n a t i o n L o n g N a m e   a t t r i b u t e s   a r e   b o t h   p r e s e n t .     T h e s e   t w o   a t t r i b u t e s   c a n n o t   c o e x i s t .  ��T h e   C o p y F i l e / @ D e s t i n a t i o n N a m e   m a t c h e s   a   C o p y F i l e / @ D e s t i n a t i o n S h o r t N a m e .     T h e s e   t w o   a t t r i b u t e s   s h o u l d   n o t   b e   d u p l i c a t e d .  ��T h e   C o p y F i l e / @ I d   v a l u e   i s   i d e n t i c a l   t o   w h a t   W i X   g e n e r a t e s   f r o m   t h e   @ N a m e   v a l u e .   O m i t   t h e   I d   a t t r i b u t e   f o r   s i m p l i f i e d   a u t h o r i n g .  ��T h e   D i g i t a l C e r t i f i c a t e / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e F i l e   a t t r i b u t e   i n s t e a d .  ��T h e   D i g i t a l S i g n a t u r e / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e F i l e   a t t r i b u t e   i n s t e a d .  L o n g N a m e  L o n g S o u r c e N a m e  ��T h e   D i r e c t o r y / @ L o n g N a m e   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   N a m e   a t t r i b u t e   i n s t e a d .  	N a m e  S h o r t N a m e  ��T h e   D i r e c t o r y / @ S h o r t N a m e   a n d   L o n g N a m e   a t t r i b u t e s   a r e   b o t h   p r e s e n t .     T h e s e   t w o   a t t r i b u t e s   c a n n o t   c o e x i s t .  ��T h e   D i r e c t o r y / @ L o n g S o u r c e   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e N a m e   a t t r i b u t e   i n s t e a d .  S o u r c e N a m e  S h o r t S o u r c e N a m e  ��T h e   D i r e c t o r y / @ S h o r t S o u r c e N a m e   a n d   L o n g S o u r c e   a t t r i b u t e s   a r e   b o t h   p r e s e n t .     T h e s e   t w o   a t t r i b u t e s   c a n n o t   c o e x i s t .  ��T h e   D i r e c t o r y / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   F i l e S o u r c e   a t t r i b u t e   i n s t e a d .  F i l e S o u r c e  ��T h e   D i r e c t o r y R e f / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   F i l e S o u r c e   a t t r i b u t e   i n s t e a d .  ��T h e   E x t e r n a l F i l e / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e   a t t r i b u t e   i n s t e a d .  
S o u r c e  F o l l o w P a r e n t  I n s t a l l D e f a u l t  �
T h e   F e a t u r e / @ F o l l o w P a r e n t   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     V a l u e   o f   ' y e s '   s h o u l d   n o w   b e   r e p r e s e n t e d   w i t h   I n s t a l l D e f a u l t = ' f o l l o w P a r e n t ' . f o l l o w P a r e n t  V i t a l  ��T h e   F i l e / @ L o n g N a m e   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   N a m e   a t t r i b u t e   i n s t e a d .  ��T h e   F i l e / @ S h o r t N a m e   a n d   L o n g N a m e   a t t r i b u t e s   a r e   b o t h   p r e s e n t .     T h e s e   t w o   a t t r i b u t e s   c a n n o t   c o e x i s t .  ��T h e   F i l e / @ N a m e   m a t c h e s   a   F i l e / @ S h o r t N a m e .     T h e s e   t w o   a t t r i b u t e s   s h o u l d   n o t   b e   d u p l i c a t e d .  ��T h e   F i l e / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e   a t t r i b u t e   i n s t e a d .  �T h e   F i l e / @ N a m e   v a l u e   i s   i d e n t i c a l   t o   w h a t   W i X   g e n e r a t e s   f r o m   t h e   @ S o u r c e   v a l u e .   O m i t   t h e   N a m e   a t t r i b u t e   f o r   s i m p l i f i e d   a u t h o r i n g .  ��T h e   F i l e / @ I d   v a l u e   i s   i d e n t i c a l   t o   w h a t   W i X   g e n e r a t e s   f r o m   t h e   @ S o u r c e   v a l u e .   O m i t   t h e   I d   a t t r i b u t e   f o r   s i m p l i f i e d   a u t h o r i n g .  ��T h e   F i l e / @ D i s k I d   v a l u e   i s   s e t   t o   i t s   d e f a u l t   v a l u e   o f   1 .   O m i t   t h e   D i s k I d   a t t r i b u t e   f o r   s i m p l i f i e d   a u t h o r i n g .  �T h e   F i l e / @ V i t a l   v a l u e   i s   s e t   t o   i t s   d e f a u l t   v a l u e   o f   y e s   ( u n l e s s   - s f d v i t a l   s w i t c h   i s   u s e d ) .   O m i t   t h e   V i t a l   a t t r i b u t e   f o r   s i m p l i f i e d   a u t h o r i n g . ��T h e   F i l e / @ S h o r t N a m e   v a l u e   i s   s p e c i f i e d   b u t   i s   a l m o s t   n e v e r   n e e d e d ;   W i X   g e n e r a t e s   s t a b l e   s h o r t   n a m e s   f r o m   t h e   @ N a m e   v a l u e   a n d   t h e   p a r e n t   C o m p o n e n t / @ I d   v a l u e .   O m i t   @ S h o r t N a m e   f o r   s i m p l i f i e d   a u t h o r i n g .  |  �iT h e   F i l e S e a r c h / @ N a m e   a t t r i b u t e   a p p e a r s   t o   c o n t a i n   b o t h   a   s h o r t   a n d   l o n g   f i l e   n a m e .     I t   m a y   o n l y   c o n t a i n   a n   8 . 3   f i l e   n a m e .     A l s o   u s e   t h e   L o n g N a m e   a t t r i b u t e   t o   s p e c i f y   a   l o n g e r   n a m e .  ��T h e   F i l e S e a r c h / @ L o n g N a m e   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   N a m e   a t t r i b u t e   i n s t e a d .  ��T h e   F i l e S e a r c h / @ S h o r t N a m e   a n d   L o n g N a m e   a t t r i b u t e s   a r e   b o t h   p r e s e n t .     T h e s e   t w o   a t t r i b u t e s   c a n n o t   c o e x i s t .  C o m p l i a n c e C h e c k  �T h e   F i l e S e a r c h   e l e m e n t   i s   n o t   s u p p o r t e d   u n d e r   t h e   p a r e n t   e l e m e n t   { 0 } .   I t   m u s t   i n s t e a d   b e   l o c a t e d   u n d e r   a   D i r e c t o r y S e a r c h   e l e m e n t .  !. D i r e c t o r y S e a r c h  - P U T - I D - H E R E - D i r e c t o r y S e a r c h  ��F r a g m e n t R e f ' s   a r e   n o   l o n g e r   s u p p o r t e d .   Y o u   m u s t   f i n d   a   r e f e r e n c a b l e   e l e m e n t   i n   t h e   F r a g m e n t   y o u   a r e   t r y i n g   t o   r e f e r e n c e   a n d   u s e   t h e   a s s o c i a t e d   r e f e r e n c e   f o r   t h a t   t y p e   i n s t e a d .   F o r   e x a m p l e ,   i f   y o u r   F r a g m e n t   h a s   a   P r o p e r t y   i n   i t ,   u s e   a   P r o p e r t y R e f . ��T h e   I c o n / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e F i l e   a t t r i b u t e   i n s t e a d .  �5T h e   I g n o r e M o d u l a r i z a t i o n   e l e m e n t   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   C u s t o m A c t i o n / @ S u p p r e s s M o d u l a r i z a t i o n   o r   P r o p e r t y / @ S u p p r e s s M o d u l a r i z a t i o n   a t t r i b u t e   i n s t e a d .  w i x  Qh t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 6 / w i  ;/ / w i x : C u s t o m A c t i o n [ @ I d = " { 0 } " ]  -S u p p r e s s M o d u l a r i z a t i o n  3/ / w i x : P r o p e r t y [ @ I d = " { 0 } " ]  ��T h e   x m l n s   a t t r i b u t e   i s   m i s s i n g .     I t   m u s t   b e   p r e s e n t   w i t h   a   v a l u e   o f   ' { 0 } ' . qT h e   x m l n s   a t t r i b u t e ' s   v a l u e   i s   w r o n g .     I t   m u s t   b e   ' { 0 } ' . V a l u e  ��T h e   I n i F i l e / @ L o n g N a m e   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   N a m e   a t t r i b u t e   i n s t e a d .  ��T h e   I n i F i l e / @ S h o r t N a m e   a n d   L o n g N a m e   a t t r i b u t e s   a r e   b o t h   p r e s e n t .     T h e s e   t w o   a t t r i b u t e s   c a n n o t   c o e x i s t .  ��T h e   I n i F i l e / @ V a l u e   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   t h e   e m p t y   s t r i n g . ��T h e   I n i F i l e S e a r c h / @ L o n g N a m e   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   N a m e   a t t r i b u t e   i n s t e a d .  ��T h e   I n i F i l e S e a r c h / @ S h o r t N a m e   a n d   L o n g N a m e   a t t r i b u t e s   a r e   b o t h   p r e s e n t .     T h e s e   t w o   a t t r i b u t e s   c a n n o t   c o e x i s t .  ��T h e   M e d i a / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   L a y o u t   a t t r i b u t e   i n s t e a d .  
L a y o u t  ��T h e   M e r g e / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e F i l e   a t t r i b u t e   i n s t e a d .  ��T h e   M o d u l e / @ G u i d   a t t r i b u t e   i s   d e p r e c a t e d .     U s e   t h e   P a c k a g e / @ I d   a t t r i b u t e   i n s t e a d .  C o m p r e s s e d  P l a t f o r m s  P l a t f o r m  ��T h e   P a c k a g e / @ C o m p r e s s e d   a t t r i b u t e   i s   i l l e g a l   u n d e r   a   M o d u l e   e l e m e n t   b e c a u s e   m e r g e   m o d u l e s   m u s t   a l w a y s   b e   c o m p r e s s e d .  I? ? ? ? ? ? ? ? - ? ? ? ? - ? ? ? ? - ? ? ? ? - ? ? ? ? ? ? ? ? ? ? ? ? ��T h e   g u i d   v a l u e   ' { 0 } '   i s   d e p r e c a t e d .     R e m o v e   t h e   P a c k a g e / @ I d   a t t r i b u t e   t o   g e t   t h e   s a m e   f u n c t i o n a l i t y . ��T h e   P a c k a g e / @ P l a t f o r m s   a t t r i b u t e   i s   d e p r e c a t e d .   U s e   P a c k a g e / @ P l a t f o r m   i n s t e a d .     P l a t f o r m   a c c e p t s   o n l y   a   s i n g l e   p l a t f o r m   ( x 8 6 ,   x 6 4 ,   o r   i a 6 4 ) .   I f   t h e   v a l u e   i n   P a c k a g e / @ P l a t f o r m s   c o r r e s p o n d s   t o   o n e   o f   t h o s e   v a l u e s ,   i t   w i l l   b e   u p d a t e d .  i n t e l  x 6 4  i n t e l 6 4  x 8 6  	i a 6 4  ��T h e   P a c k a g e / @ P l a t f o r m   a t t r i b u t e   v a l u e   ' i n t e l '   i s   d e p r e c a t e d .   U s e   ' x 8 6 '   i n s t e a d . ��T h e   P a c k a g e / @ P l a t f o r m   a t t r i b u t e   v a l u e   ' i n t e l 6 4 '   i s   d e p r e c a t e d .   U s e   ' i a 6 4 '   i n s t e a d . S u p e r s e d e  
T a r g e t  1  �eT h e   P a t c h S e q u e n c e / @ S u p e r s e d e   a t t r i b u t e   n o   l o n g e r   a c c e p t s   a n   i n t e g e r   v a l u e .     T h e   v a l u e   o f   ' 1 '   s h o u l d   n o w   b e   ' y e s '   a n d   a l l   o t h e r   v a l u e s   s h o u l d   b e   ' n o '   o r   s i m p l y   d r o p   t h e   a t t r i b u t e . �7T h e   P a t c h S e q u e n c e / @ S u p e r s e d e   a t t r i b u t e   n o   l o n g e r   a c c e p t s   a n   i n t e g e r   v a l u e .     A n y   p r e v i o u s   v a l u e   e x c e p t   f o r   ' 1 '   s h o u l d   b e   a u t h o r e d   b y   d r o p p i n g   t h e   a t t r i b u t e . ��T h e   P a t c h S e q u e n c e / @ T a r g e t   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   P r o d u c t C o d e   a t t r i b u t e   i n s t e a d .  P r o d u c t C o d e  ��T h e   P a t c h S e q u e n c e / @ T a r g e t   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   T a r g e t I m a g e   a t t r i b u t e   i n s t e a d .  E x t e n d e d  ��T h e   g u i d   v a l u e   ' { 0 } '   i s   d e p r e c a t e d .     S e t   t h e   v a l u e   t o   ' * '   i n s t e a d . *  " [  ] "  [ !  [ #  ]  �QT h e   P r o g I d / @ I c o n   a t t r i b u t e ' s   e x p e c t e d   v a l u e   h a s   c h a n g e d .     I t   n o   l o n g e r   s u p p o r t s   a   f o r m a t t e d   s t r i n g   f o r   n o n - a d v e r t i s e d   P r o g I d s .     I n s t e a d ,   s p e c i f y   j u s t   a   f i l e   i d e n t i f i e r . T e m p l a t e  ��T h e   P r o g r e s s T e x t / @ T e m p l a t e   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   t h e   e m p t y   s t r i n g . ��T h e   P r o p e r t y / @ V a l u e   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   t h e   e m p t y   s t r i n g . ��T h e   r e q u i r e d   a t t r i b u t e   R a d i o B u t t o n / @ V a l u e   i s   m i s s i n g .     I n n e r   t e x t   h a s   b e e n   d e p r e c i a t e d   i n   f a v o r   o f   t h e   t h i s   a t t r i b u t e .  !R a d i o B u t t o n G r o u p  ��T h e   R a d i o G r o u p   e l e m e n t   i s   d e p r e c a t e d .     U s e   R a d i o B u t t o n G r o u p   i n s t e a d .  
A c t i o n  	T y p e  �3T h e   R e g i s t r y / @ I d   v a l u e   i s   s p e c i f i e d   b u t   i s   a l m o s t   n e v e r   n e e d e d ;   W i X   a u t o m a t i c a l l y   g e n e r a t e s   s t a b l e   R e g i s t r y   t a b l e   I D s .   O m i t   @ I d   f o r   s i m p l i f i e d   a u t h o r i n g .  �MT h e   R e g i s t r y   e l e m e n t   h a s   b e e n   d e p r e c a t e d .     P l e a s e   u s e   o n e   o f   t h e   n e w   e l e m e n t s   w h i c h   r e p l a c e s   i t s   f u n c t i o n a l i t y :   R e g i s t r y K e y   f o r   c r e a t i n g   r e g i s t r y   k e y s ,   R e g i s t r y V a l u e   f o r   w r i t i n g   r e g i s t r y   v a l u e s ,   R e m o v e R e g i s t r y K e y   f o r   r e m o v i n g   r e g i s t r y   k e y s ,   a n d   R e m o v e R e g i s t r y V a l u e   f o r   r e m o v i n g   r e g i s t r y   v a l u e s .  c r e a t e K e y  Ac r e a t e K e y A n d R e m o v e K e y O n U n i n s t a l l  R e g i s t r y K e y  
c r e a t e  5c r e a t e A n d R e m o v e O n U n i n s t a l l  %r e m o v e K e y O n I n s t a l l  )r e m o v e K e y O n U n i n s t a l l  #R e m o v e R e g i s t r y K e y  r e m o v e O n I n s t a l l  #r e m o v e O n U n i n s t a l l  
r e m o v e  'R e m o v e R e g i s t r y V a l u e  
s t r i n g  R e g i s t r y V a l u e  !M u l t i S t r i n g V a l u e  	R o o t  K e y  r e g i s t r y  ��T h e   R e g i s t r y S e a r c h / @ T y p e   a t t r i b u t e ' s   v a l u e   ' r e g i s t r y '   h a s   b e e n   d e p r e c a t e d .     P l e a s e   u s e   t h e   v a l u e   ' r a w '   i n s t e a d . r a w  �	T h e   R e m o v e F i l e / @ N a m e   a t t r i b u t e   i s   r e q u i r e d .     W i t h o u t   t h i s   a t t r i b u t e   s p e c i f i e d ,   t h i s   i s   b e t t e r   r e p r e s e n t e d   a s   a   R e m o v e F o l d e r   e l e m e n t .  R e m o v e F o l d e r  ��T h e   R e m o v e F i l e / @ L o n g N a m e   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   N a m e   a t t r i b u t e   i n s t e a d .  ��T h e   R e m o v e F i l e / @ S h o r t N a m e   a n d   L o n g N a m e   a t t r i b u t e s   a r e   b o t h   p r e s e n t .     T h e s e   t w o   a t t r i b u t e s   c a n n o t   c o e x i s t .  L o c a l G r o u p  P a s s w o r d  ��T h e   S e r v i c e I n s t a l l / @ L o c a l G r o u p   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   t h e   e m p t y   s t r i n g . ��T h e   S e r v i c e I n s t a l l / @ P a s s w o r d   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   t h e   e m p t y   s t r i n g . ��T h e   S F P C a t a l o g / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e F i l e   a t t r i b u t e   i n s t e a d .  !W o r k i n g D i r e c t o r y  ��T h e   S h o r t c u t / @ L o n g N a m e   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   N a m e   a t t r i b u t e   i n s t e a d .  ��T h e   S h o r t c u t / @ S h o r t N a m e   a n d   L o n g N a m e   a t t r i b u t e s   a r e   b o t h   p r e s e n t .     T h e s e   t w o   a t t r i b u t e s   c a n n o t   c o e x i s t .  ��T h e   S h o r t c u t / @ W o r k i n g D i r e c t o r y   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   t h e   e m p t y   s t r i n g . ��T h e   T a r g e t I m a g e / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e F i l e   a t t r i b u t e   i n s t e a d .  ��T h e   T e x t / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e F i l e   a t t r i b u t e   i n s t e a d .  �'T h e   T y p e L i b / @ D e s c r i p t i o n   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   a n   e m p t y   s t r i n g .     I f   y o u   w a n t   t h e   v a l u e   t o   b e   n u l l   o r   e m p t y ,   s i m p l y   d r o p   t h e   e n t i r e   a t t r i b u t e . �'T h e   U p g r a d e   e l e m e n t   c o n t a i n s   a   d e p r e c a t e d   c h i l d   P r o p e r t y   c h i l d   e l e m e n t .     T h e   P r o p e r t y   e l e m e n t   s h o u l d   b e   m o v e d   t o   t h e   p a r e n t   o f   t h e   U p g r a d e   e l e m e n t .  s r c P a t c h  ��T h e   U p g r a d e I m a g e / @ s r c   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e F i l e   a t t r i b u t e   i n s t e a d .  ��T h e   U p g r a d e I m a g e / @ s r c P a t c h   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   S o u r c e P a t c h   a t t r i b u t e   i n s t e a d .  S o u r c e P a t c h  R e m o v e F e a t u r e s  sT h e   U p g r a d e V e r s i o n / @ P r o p e r t y   a t t r i b u t e   m u s t   b e   s p e c i f i e d .  {  - }  U C  �;T h e   U p g r a d e V e r s i o n / @ R e m o v e F e a t u r e s   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   a n   e m p t y   s t r i n g .     I f   y o u   w a n t   t h e   v a l u e   t o   b e   n u l l   o r   e m p t y ,   s i m p l y   d r o p   t h e   e n t i r e   a t t r i b u t e . ��T h e   V e r b / @ T a r g e t   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   T a r g e t F i l e   o r   T a r g e t P r o p e r t y   a t t r i b u t e   i n s t e a d .  [  T a r g e t F i l e  T a r g e t P r o p e r t y  E x t e n s i o n  ��T h e   W e b A p p l i c a t i o n E x t e n s i o n / @ I d   a t t r i b u t e ' s   v a l u e   c a n n o t   b e   a n   e m p t y   s t r i n g .     U s e   ' * '   f o r   t h e   v a l u e   i n s t e a d . ��T h e   W e b A p p l i c a t i o n E x t e n s i o n / @ I d   a t t r i b u t e   h a s   b e e n   d e p r e c a t e d .     U s e   t h e   E x t e n s i o n   a t t r i b u t e   i n s t e a d .  L o a d O r d e r  - 1 ��T h e   W e b F i l t e r / @ L o a d O r d e r   a t t r i b u t e ' s   v a l u e ,   ' - 1 ' ,   i s   b e t t e r   r e p r e s e n t e d   w i t h   t h e   v a l u e   ' l a s t ' . 	l a s t  0  ��T h e   W e b F i l t e r / @ L o a d O r d e r   a t t r i b u t e ' s   v a l u e ,   ' 0 ' ,   i s   b e t t e r   r e p r e s e n t e d   w i t h   t h e   v a l u e   ' f i r s t ' . f i r s t  eh t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 6 / l o c a l i z a t i o n  v s  Yh t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / V S E x t e n s i o n  ��T h e   { 0 }   e l e m e n t   i s   n o w   p a r t   o f   t h e   V S   e x t e n s i o n .     A n   x m l n s : v s = " h t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / V S E x t e n s i o n "   a t t r i b u t e   s h o u l d   b e   a d d e d   t o   t h e   W i x   e l e m e n t   a n d   t h i s   e l e m e n t   s h o u l d   b e   r e n a m e d   t o   ' v s : { 0 } ' . x m l n s : v s  x m l n s : h e l p  [h t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / I I s E x t e n s i o n  ��T h e   { 0 }   e l e m e n t   i s   n o w   p a r t   o f   t h e   I I S   e x t e n s i o n .     A n   x m l n s : i i s = " h t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / I I s E x t e n s i o n "   a t t r i b u t e   s h o u l d   b e   a d d e d   t o   t h e   W i x   e l e m e n t   a n d   t h i s   e l e m e n t   s h o u l d   b e   r e n a m e d   t o   ' i i s : { 0 } ' . i i s  x m l n s : i i s  x m l n s : s c a  [h t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / S q l E x t e n s i o n  ��T h e   { 0 }   e l e m e n t   i s   n o w   p a r t   o f   t h e   S Q L   e x t e n s i o n .     A n   x m l n s : s q l = " h t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / S q l E x t e n s i o n "   a t t r i b u t e   s h o u l d   b e   a d d e d   t o   t h e   W i x   e l e m e n t   a n d   t h i s   e l e m e n t   s h o u l d   b e   r e n a m e d   t o   ' s q l : { 0 } ' . s q l  x m l n s : s q l  ]h t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / U t i l E x t e n s i o n  P e r m i s s i o n E x  -F i r s t F a i l u r e A c t i o n T y p e  ��T h e   { 0 }   e l e m e n t   i s   n o w   p a r t   o f   t h e   U t i l i t y   e x t e n s i o n .     A n   x m l n s : u t i l = " h t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / U t i l E x t e n s i o n "   a t t r i b u t e   s h o u l d   b e   a d d e d   t o   t h e   W i x   e l e m e n t   a n d   t h i s   e l e m e n t   s h o u l d   b e   r e n a m e d   t o   ' u t i l : { 0 } ' . 	u t i l  x m l n s : u t i l  w a r n i n g  e r r o r  O{ 0 } ( { 1 } )   :   { 2 }   W X C P { 3 : 0 0 0 0 }   :   { 4 }   ( { 5 } )  w i x c o p . e x e  E{ 0 }   :   { 1 }   W X C P { 2 : 0 0 0 0 }   :   { 3 }   ( { 4 } )  { 0 } ( { 1 } )   :   { 2 }  { 0 }   :   { 1 }  _  ��( \ ! | \ $ ) \ ( ( ? < n a m e s p a c e > l o c | w i x ) \ . ( ? < n a m e > [ _ A - Z a - z ] [ 0 - 9 A - Z a - z _ ] + ) \ ) ^ [ ^ a - z A - Z _ ] 9[ ^ A - Z a - z 0 - 9 _ \ . \ $ \ ( \ ) ] | \ . { 2 , } 'w i x c o p . s e t t i n g s . x m l  KW i n d o w s   I n s t a l l e r   X m l   C o p   v e r s i o n   { 0 }  ��C o p y r i g h t   ( C )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s .   A l l   r i g h t s   r e s e r v e d .  _  u s a g e :     w i x c o p . e x e   s o u r c e F i l e   [ s o u r c e F i l e   . . . ]  o      - f               f i x   e r r o r s   a u t o m a t i c a l l y   f o r   w r i t a b l e   f i l e s i      - n o l o g o     s u p p r e s s   d i s p l a y i n g   t h e   l o g o   i n f o r m a t i o n ��      - s               s e a r c h   f o r   m a t c h i n g   f i l e s   i n   c u r r e n t   d i r   a n d   s u b d i r s I      - s e t 1 < f i l e >   p r i m a r y   s e t t i n g s   f i l e u      - s e t 2 < f i l e >   s e c o n d a r y   s e t t i n g s   f i l e   ( o v e r r i d e s   p r i m a r y ) y      - i n d e n t : < n >   i n d e n t a t i o n   m u l t i p l e   ( o v e r r i d e s   d e f a u l t   o f   4 ) C      - ?               t h i s   h e l p   i n f o r m a t i o n U      s o u r c e F i l e   m a y   u s e   w i l d c a r d s   l i k e   * . w x s  3C o u l d   n o t   f i n d   f i l e   " { 0 } "  yw i x c o p . e x e   :   f a t a l   e r r o r   W X C P 0 0 0 1   :   { 0 } 
 
 
 
 S t a c k   T r a c e : 
 
 { 1 }  ��C a n n o t   s p e c i f y   a   s e c o n d a r y   s e t t i n g s   f i l e   ( s e t 2 )   w i t h o u t   a   p r i m a r y   s e t t i n g s   f i l e   ( s e t 1 ) .  %l o c a l S e t t i n g s F i l e 2  7/ S e t t i n g s / I g n o r e E r r o r s / T e s t  ?/ S e t t i n g s / E r r o r s A s W a r n i n g s / T e s t  5/ S e t t i n g s / E x e m p t F i l e s / F i l e  ?  f  
n o l o g o  s  	s e t 1  	s e t 2  i n d e n t :  3I n v a l i d   n u m e r i c   a r g u m e n t .  #I n v a l i d   a r g u m e n t .   FI/�
KB����Z      59!=A   5     


9
!
=  ���� ��HH 
QU Y]  �� ] �� U
eHHi ����  e          uyeyim  }  q mmm  m  � y yy �  �
  � m mmqm q  ��  �� � ��
��eyi	qmm �  �	 �	e��i �%  �- �1 mq yy y yyy yymm myyyyyyyqyyyyyy q  yy
yyyyyyyyyyyy
yyyyyyyyy yymyyqm     ��qqmm  �9 �9 m��yyyyq q
yyyyyqym/yyyyqqq��emiqqmqqm��yyq  yyqymy��memimmyy �	qym     �A } }(������8����,��$��0����4��<��	 } }��  e��i   5ei��  ��  1      
MMQ}emimm � �� �	�z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����Vh t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 3 / 0 1 / w i Ph t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 6 / w i dh t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 6 / l o c a l i z a t i o n                             	   
         
                                                             !   "   #   $   %   &   '   (   )   *   +   ,   -   .   /   0   1   2   3   4   5   6   7   8   9   :   ;   IM������H 1	1    H m qq qq  } mm y �� m q mq qq
 Hm m  y �� �� �� ��	 �� �� �� ��     	(         TWrapNonExceptionThrows       3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset      
 en-US    KZCj!5k4<�����b�>�0���`�R�x�$�		��4��q���E�/�Μ��|`j�PNN���J�u��^;�j,�e�T,Iϸ5�S ���\�Z��9|δl����H���7^L��\    +p�]         �C �3 RSDSOr�ω6�B��vrj�j�   C:\agent\_work\66\s\build\obj\ship\x86\wixcop\WixCop.pdb                                                                                                                                                                                                            �D         �D                         �D           _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �   P  �                  8  �               	  �                     h  �               	  �  �` h          h4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   \   F i l e D e s c r i p t i o n     W i X   E r r o r   C o r r e c t i o n   T o o l   8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   .   I n t e r n a l N a m e   w i x c o p     � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   >   O r i g i n a l F i l e n a m e   w i x c o p . e x e     \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	�d �          <?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"> 
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Wixcop" version="*******" processorArchitecture="x86" type="win32"/> 
 <description>WiX Error Correction Tool</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           @    5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      