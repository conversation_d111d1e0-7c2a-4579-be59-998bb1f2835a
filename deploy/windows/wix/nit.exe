MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L �p�]        � 0  @          nY       `    @                       �     _B  @�                           Y  O    `  �                   �     �W                                                               H           .text   t9       @                    `.rsrc   �   `      P              @  @.reloc      �      `              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                PY      H     �*  �'       |R  �  dW  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�6  r�  po  
�6  (  
o  
 (  +,r�  po   
o  
 (  +,r�  po!  
o  
 (  +,r�  p	o"  
o  
 (  +,r+ po#  
o  
 (  +,rO po$  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  (%  
o&  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *('  
*�s(  
}  s(  
}  }  r� pr� ps)  
}  ('  
*F(*  
s  o  *  0 �    (  {  o+  
{  o,  
,{  o-  
ݍ  {  ,(.  
{  , (
  (/  
(0  
{  o-  
�X  {  o1  

+(2  
{  (3  
o4  
(5  
-���  o6  
�}  {  o7  
-,(
  (/  
{  (  o4  
{  o-  
��   s  %{  o  %{  %�4  
s8  
o  
 o  /!{  (  o4  
{  o-  
�z{  (&  o4  
�Z{  o9  
o4  
�C{  o:  
o;  
o<  
o=  
(>  
o4  
u?  -	u@  ,�� {  o-  
**AL     r   )   �                  X  X               X  o  C     0 �     
8�   �9�   o?  
9�   o?  
3{  r� p(@  
oA  
8�   -oB  
./oB  
3NoC  
r� p(D  
,	}  +er p(D  
-
r	 p(D  
,}  *{  oE  
+5@oB  
3oC  
(F  
(  +{  r� p(@  
oA  
�
�i?!���*�~  -r p�  (%  
oG  
sH  
�  ~  *~   *�   *V(
  ry p~   oI  
*0 )     {%  
(J  
t"  |%  (  +
3�*   0 )     {%  
(L  
t"  |%  (  +
3�*{&  *"}&  *  0 :    (M  

�  sN  
   (O  
(  o1  
8�   (2  

	(P  
%}$  {$  oQ  
r� p�  oR  
oS  
+koT  
(U  
-((  (  {$  r� poV  
 {$  r poW  
�'&�$o:  
(  (  {#  �}#  � oX  
-��,o6  
�,o6  
�(5  
:&�����  o6  
�(O  
&(M  
&�{"  T{#  T*  L    � � (    � � $'   m x�      K ��      * �      �    0 �     {$  oY  
:�   oZ  
=�       09�       ;�   8�       .&    @�   o[  

r- p(D  
}!  *{!  ,eo<  
o\  
 �i  0$ �i  2('  (  {"  �}"  +* �i  0" �i  2(  (  {#  �}#  *{$  o]  
(  (  **  0 (     u0  
{%  ,{%  o^  
*,s_  
z*N(`  
(a  
*~'  *Vr_ p(b  
sH  
�'  *^ �  r� p�  s  *� �  r
 p�  %�6  �%�6  �s  *n �  rI p�  %�s  *n �  rw p�  %�s  *N(c  
(a  
*~(  *Vr_ p(b  
sH  
�(  *N(d  
(a  
*~)  *Vr_ p(b  
sH  
�)  *� m   r� p�  %�6  �s"  *n n   r� p�  %�s"  *n o   r p�  %�s"  *  BSJB         v2.0.50727     l   �  #~  8  <  #Strings    t  \  #US �!     #GUID   �!  �  #Blob         W�		   �3      K      )   (   &      d                                                  o      zU
 !U
 �
    
: ]: �z �6
 !: : �: �: >: �u
 l
z �:
 L
 �z 5z  _  �# EzS �	   �# �z
 s3	 �
 XU
 �u
 @�
 0�
 �� �# 	# �K	 �K	 �K	  _  �K	 tK	 �
K	 gK	 	K	 �K	 �K	 K	 �# \# L# �z "# �# �z % z �z �z �# 	# z �# �z �# �z g6
  _  �# 'z ~ w g	K	 K	 �K	  _  �	| �K	 #    +       � �#=     %
�=    �
�=      ��=  	   q	�= !    J�� '   ��= (    6�� (   ��= ) !   �� ) "  ��= * %  ^� 
� f� Y
� G�V��V�E�V��V�8�V���V�O�V���V�� �V�
�V���V��
�V���V�� �V���V��	�V���V�
�V��V�)�V��� a� �� k� t� 	� �� �� <
� �� ~� �� �� � � �� �� ��P     � �� �!    � �� "    �
�  R"    ��	  Z"    ��	  �"    � �� �"    � %� �$    � i� R"    ��	  �%    �� �%    �� �%    �� �%    ��	 &    �@	 <&    �L
 q&    �C y&    �R$ �&    � I
- (    � �5 )    �vJ R"    ��	  L)    ��	� `)    ��Q g)    �
�  R"    ��	  })    � iV �)    � l[ �)    � � b �)    � � b �)    ��	� *    ��Q  *    �
�   R"    ��	   (*    ��	�  <*    ��Q$ C*    �
� $ R"    ��	 $ Y*    � �
h$ z*    � � n% �*    � �n&    �   �   �  ?   n   n   n   I   I   I   I  �  ~   �   �    �   �      R    �   �    +   �   R   _   �   �   �   �    +   �   �   �    +   �   _   �   � � 	 �	  �	  �	
 ) �	 1 �	 9 �	 A �	 I �	 Q �	 Y �	 a �	 i �	 q �	 � �	 � �	 � �	 � �	 �	  ��	 � �7 � \; � /
7 �A � �
7 � Y7 � �7 �sG � �	 � �	L � �	L �R
P Q �7 Y �
7 1 �7 a s
7 ) !7 ��x � �� y �	  �	 � �	 �� �" ��	� � }L ��� �z� �|	�  �	�  � ��
� � ��  ~� �  9L �	� � �	� � [7 � �� y �7 � %7 ��� ��L �:�  �� �����
 z !��� �	$� �+�>!�Jh>)4 |)�	� )B �)4���9��$ �	�, � ��q 1 I~� Q� � YCL Y�Y����� � �	���	�y�� ����	���	�  �     6 $ e ( | , | 0 � 4 � 8 � < � @ � D � H � L � P � T � X � \ � ` � d �) � �.  �.  �.  �. # �. + �. 3 �. ; �. C . K . S *. [ t. c �. k �I � �� { �� � �� � �� s ��� ��� � � � � ��� ��� �d� ��� ��� �& p � � 2]��    ��       	      �t  �y  �~  V�  ��  ��  ��       
         
    	   	      
  #  u � � ���            �
             �V                �z               �               K	                          �
  �     �
       P  W  \  a  f  k � W M      IEnumerable`1 IEnumerator`1 IList`1 Int32 <Module> SetInternalUI SetExternalUI T mea mscorlib System.Collections.Generic Add Interlocked PackageFailed TestFailed TestSkipped get_IsClosed TestPassed TestNotCreated id <InputFiles>k__BackingField messageRecord FormatRecord Replace LuxNamespace get_StackTrace OpenPackage add_Message remove_Message get_Message InstallMessage OnMessage get_HelpMessage message AddRange CompareExchange Invoke IDisposable RuntimeTypeHandle GetTypeFromHandle InstallerHandle CommandLineResponseFile Console wconsole get_Title resourceName LuxTableName LuxCustomActionName get_ProductName ParseCommandLine WriteLine Combine messageType GetType TestIdMinimumFailure TestIdMaximumFailure get_Culture set_Culture resourceCulture get_Database TestFailedExpressionFalse Dispose Parse Delegate EditorBrowsableState MTAThreadAttribute CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute ParamArrayAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute value TestPassedExpressionTrue Remove nit.exe System.Threading ToString GetString Substring TestPassedPropertyValueMatch TestFailedPropertyValueMismatch get_Length original System.ComponentModel set_Level MessageLevel Microsoft.Tools.WindowsInstallerXml VSExtensionsLandingUrl NewsUrl SupportUrl set_Item System resourceMan Main MessageIcon icon get_FileVersion Session session get_Location TestUnknownOperation RunningMutation mutation System.Globalization PrepareConsoleForLocalization DoAction System.Reflection SourceLineNumberCollection SEHException InstallCanceledException UnexpectedException NullReferenceException InstallerException WixException get_Description WixDistribution AppCommon MessageDefaultButton defaultButton Run TestFailedIndexUnknown CultureInfo FileVersionInfo GetVersionInfo showLogo showHelp get_LastErrorNumber DisplayToolHeader get_ResourceManager resourceManager GetInteger ExternalUIRecordHandler IMessageHandler ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler Microsoft.Deployment.WindowsInstaller TestRunner DisplayToolFooter get_Major get_Minor get_Error get_EncounteredError TestFailedExpressionSyntaxError IEnumerator GetEnumerator .ctor .cctor System.Diagnostics TestFailedIndexOutOfBounds System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Lux.Data.Messages.resources Microsoft.Tools.WindowsInstallerXml.Lux.NitStrings.resources DebuggingModes InstallLogModes set_ShowVerboseMessages GetFiles get_InputFiles set_InputFiles inputFiles TotalTestFailures failures NitVerboses passes GetCustomAttributes NitWarnings WixWarnings NitStrings invalidArgs messageArgs MessageEventArgs WixVerboseEventArgs LuxVerboseEventArgs WixWarningEventArgs LuxWarningEventArgs NitErrorEventArgs WixErrorEventArgs args Contains System.Collections InstallUIOptions MessageButtons buttons get_Chars sourceLineNumbers ReplacePlaceholders NitErrors WixErrors TestIdMinimumSuccess TestIdMaximumSuccess Constants get_Comments runningTests RunTests Concat TelemetryUrlFormat Object get_Product ShortProduct get_Copyright get_LegalCopyright Nit nit MessageResult TestUnknownResult OneHundredPercent UnsupportedCommandLineArgument get_Current TestFailedExpectedEvenNameValueContent get_Count get_FieldCount failureCount passCount MalfunctionNeedInput MoveNext wix Microsoft.Tools.WindowsInstallerXml.Lux Display get_Assembly GetExecutingAssembly assembly get_Company ExecuteStringQuery op_Equality IsNullOrEmpty LuxMutationRunningProperty    [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  N I T  n i t . e x e  
S o u r c e  
n o l o g o  ?  	h e l p  eM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . L u x . N i t S t r i n g s  H e l p M e s s a g e  [S E L E C T   D I S T I N C T   ` M u t a t i o n `   F R O M   ` W i x U n i t T e s t `  /W I X L U X _ R U N N I N G _ M U T A T I O N  I N S T A L L  1W i x R u n I m m e d i a t e U n i t T e s t s  kM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . L u x . D a t a . M e s s a g e s  AN i t E r r o r s _ M a l f u n c t i o n N e e d I n p u t _ 1  ;N i t E r r o r s _ T o t a l T e s t F a i l u r e s _ 1  -N i t E r r o r s _ T e s t F a i l e d _ 1  3N i t E r r o r s _ P a c k a g e F a i l e d _ 1  ?N i t V e r b o s e s _ O n e H u n d r e d P e r c e n t _ 1  1N i t V e r b o s e s _ T e s t P a s s e d _ 1  ;N i t V e r b o s e s _ R u n n i n g M u t a t i o n _ 1     }��ȠZA�@��ʞ�l         }E)-1I   E     
)
-

1
  ���� ��Q  ]ae     ] ]    �� ��   ��  �� �� 
 �         A A y������ �
�
�
    
��	����]�������� ���� ����� ��  � �!�	  �� ��   ���� ��
 �� �-  A�z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����Rh t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 9 / L u x 0W i x R u n I m m e d i a t e U n i t T e s t s .W I X L U X _ R U N N I N G _ M U T A T I O N W i x U n i t T e s t �i  �i  �i  �i  �i  �i  �i  �i  �i  �i  �i  �i  QUuy���� A	A      u  y y   ��  Q Q  ������������ ��  u     , , u y ( Q( u        TWrapNonExceptionThrows      ! WiX Toolset Unit Test Runner   Unit Test Runner          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US     @ 3System.Resources.Tools.StronglyTypedResourceBuilder*******  �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP���    �   H e l p M e s s a g e     � usage:  nit.exe [options] testpackage.msi [...]

   Nit runs the unit tests in one or more test packages.

   options:

   -nologo    skip printing logo information
   -?|-help   this help information  4  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP�-*�5-�U���������p�7YE�9�   E   �       /  n  |   �  @N i t E r r o r s _ M a l f u n c t i o n N e e d I n p u t _ 1     2N i t E r r o r s _ P a c k a g e F a i l e d _ 1 /   ,N i t E r r o r s _ T e s t F a i l e d _ 1 D   :N i t E r r o r s _ T o t a l T e s t F a i l u r e s _ 1 I   >N i t V e r b o s e s _ O n e H u n d r e d P e r c e n t _ 1 n   :N i t V e r b o s e s _ R u n n i n g M u t a t i o n _ 1 �   0N i t V e r b o s e s _ T e s t P a s s e d _ 1 �   -Need one or more input files to be specified.Package failed: {0}{0}#{0} tests failed. {1} tests passed.All {0} tests passed.Running test mutation {0}{0}�[X�}s*o�.��tJ	߱���˦��w�d)nӊ8�C��Bߕ���Ә�a
�O��0�ѴŃ�]��bSZ�k���,�&��T�Os�C�尅:�Q�0	5���z�	�.8����(��M�J[^    �p�]          X   H  RSDS��m�y�I��Mu>ϧ�   C:\agent\_work\66\s\build\obj\ship\x86\nit\nit.pdb                                                                                                                                                                                                                  DY          ^Y                          PY            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �   P  �                  8  �               	  �                     h  �               	  �  �`  \          \4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   `   F i l e D e s c r i p t i o n     W i X   T o o l s e t   L u x   T e s t   R u n n e r   8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   (   I n t e r n a l N a m e   n i t   � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   8   O r i g i n a l F i l e n a m e   n i t . e x e   \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	��c  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"> 
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Lux" version="*******" processorArchitecture="x86" type="win32"/> 
 <description>WiX Toolset Lux Builder and Runner</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                P     p9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      