MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L 'p�]        � 0  `          p       �    @                       �     �  @�                           �o  O    �  �                   �     �n                                                               H           .text   P       `                    `.rsrc   �   �      p              @  @.reloc      �      �              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �o      H     �8  x(        a  �  n  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�5  r�  po  
�5  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po   
o  
 (  +,r�  p	o!  
o  
 (  +,r+ po"  
o  
 (  +,rO po#  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  ($  
o%  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 �       (&  
s'  
}!  s'  
}"  s'  
}#  r� pr� ps(  
}  s'  
}  s'  
}  s'  
}  s'  
}$  }  }  }  s)  
}%  {%  {  %�*  
s+  
o,  
*F(-  
s  o  *0 d
    
s.  
s/  

(  s0  
{"  o1  
+To2  
(3  
		o4  
	o5  
,4{  ,,	o5  
o6  
o7  
{  o8  
,
	o5  
}  o9  
-��u!  

,
o:  
�{  (;  
-{  -{  (<  
s=  
zs>  

{  -s?  
}  {  o@  
�4  {  oA  
{  {  oB  
oC  
+#(D  
o@  
,{  oE  
(F  
-���  o:  
�(  {  oG  
,{  oH  

��  {  o1  
+o2  
{  (I  
o*  
o9  
-��u!  

,
o:  
�}  {  oG  
,{  oH  

�m  {  o@  
-	}  +B{  -:{  o@  
/(J  
s=  
z{  oK  
(L  
r� p(M  
}  {  {  oN  
{  oO  
{  ,(P  
{  ,(	  (Q  
{  oH  

��  {  oR  
{  oS  
{	  oT  
{  oU  
{  oV  
{   oW  
{
  oX  
{  oY  
{  oZ  
{%  o[  
{  o\  
{  o]  
{  o^  
{  o_  
{  r� p(`  
oa  
{  {%  ob  
{!  ,R{!  o1  
+#o2  
=oc  
3{$  od  
&o9  
-��u!  

,
o:  
�{  ,!{#  o@  
2{  -{  :�  se  
sf  
{  %�*  
s+  
og  
{#  o1  
+#o2  
oh  
{  (i  
oj  
o9  
-��u!  

,
o:  
�{  ,m{  +S�ok  
+"(l  
om  
o8  
,on  
(o  
-���  o:  
�X�i2�8�   ok  
+"(l  
om  
op  
-on  
(o  
-���  o:  
�-5ok  
+(l  
on  
(o  
-���  o:  
�{  oG  
,{  oH  

��  oq  
{  or  
{%  os  
oC  
+b(D  
ot  
{  ou  
oh  
ov  
,1{  -ow  
,�4  %ow  
�ox  
(F  
-���  o:  
�{  oy  
oz  
{  %�*  
s+  
o{  
{  {  %�*  
s+  
o|  
{  o1  
8�   o2  
%(}  
(~  
{$  o  
-{$  od  
& oh  
{  {  (�  
o�  
o�  
�T&�  oh  
{  {  (�  
  {  ox  
 o�  
o�  
�&� {  {  (�  
o9  
:B����u!  

,
o:  
�{  oG  
,{  oH  

��  {!  o1  
+io2  
!!(;  
-W!=oc  
3{  o�  
o�  
!od  
&+1!�H  %=�o�  
"{  o�  
o�  
"�"�o�  
o9  
-��u!  

,
o:  
�{$  o1  
+!o2  
#{  o�  
o�  
#od  
&o9  
-��u!  

,
o:  
�-?${  ,{  (�  
(�  
$	$o�  
-%{  oH  

��  o�  
,(  s�  
z{  o�  
o�  
{  -
o�  
@�   {  (�  
%%,%op  
,r� p%(�  
,8o�  
3{  r p(M  
}  +{  r p(M  
}  {  {
  -+{  o�  
{%  {  o�  
o�  
+]{  (�  
&&,&op  
,r� p&(�  
,!o�  
(�  
&{  &(M  
}  {  {  o�  
&݋   ''uK  ,}  {  'o�  
o*  
�d(}  {  (o�  
(o�  
o�  
(o�  
(�  
o*  
(uL  -	(uM  ,��{  ,{  {  o�  
�{  oH  
*
*A�    +   a   �               0   N            �  )   �            }  0   �              0   M            �  /   �            �  /               -     K            �  o                �  *   �     )      �  8        *     t  �   9            z  v   �              .   @                �	  �	  '   '         �	  �	  J   (        +
  ;
         0 �    
8e  �9W  op  
9L  op  
>  -o�  
./o�  
@�  o�  
r+ po8  
,"{  r+ p(�  
o*  
}  8�  r1 po8  
,"{  r1 p(�  
o*  
}  8�  r7 po8  
,"{  r7 p(�  
o*  
}	  8�  r= po8  
,/{  �%
(�  

	(;  
,*{!  	od  
&8R  rA po8  
,C{  rA p(�  
o*  
�%
(�  
-{  (�  
o*  
*�}  8  rO po8  
,"{  rO p(�  
o*  
}
  8�  rU po�  
9�   
o�  
(�  
o�  
ri po8  
,}  8�  �H  %;�%,�o�  
}  +*{  �rs po8  
,{  ~�  
��{  �i2�84  r� po8  
,"{  r� p(�  
o*  
}
  8  r� po8  
,8�%
(�  
-{  r� p(�  
o*  
*{"  �od  
&8�  r� po8  
,1{  �%
(�  
(;  
,*{#  od  
&8  r� po8  
,}  8e  r� po8  
,}  8K  r� p(�  
-
r� p(�  
,){  �%
(�  
}  {  (;  
9	  *r� po8  
,}   8�  r� po8  
,"{  r� p(�  
o*  
}  8�  r� po8  
,"{  r� p(�  
o*  
}  8�  r� po8  
,}  8t  r po8  
,"{  r p(�  
o*  
}  8D  r	 po8  
,"{  r	 p(�  
o*  
}  8  r po8  
,"{  r p(�  
o*  
}  8�  r po8  
,"{  r p(�  
o*  
}  8�  r po8  
,"{  r p(�  
o*  
}  8�  r% po8  
,,{  r% pr1 p(�  
o*  
{  o�  
8J  r1 po�  
9�   o�  
op  
-{  o�  
+8(�  
o�  
(�  
2{  (�  
o*  
{  o�  
��  &{  (�  
o*  
��  &{  (�  
o*  
ݪ  r7 po8  
,){  �%
(�  
}  {  (;  
9t  *r? po8  
,{  o�  
8T  rC po8  
,,{  rC prO p(�  
o*  
{  o�  
8  rO po�  
9�   o�  
		op  
-{  o�  
+8	(�  
o�  
(�  


2{  	(�  
o*  
{  
o�  
ݬ   &{  	(�  
o*  
ݓ   &{  	(�  
o*  
�}rU po8  
,	}  +fr[ p(�  
-
r_ p(�  
,}  *{  od  
&+5op  
1@o�  
3o�  
(�  
(  +
{  od  
&�
�i?����{
  ,{  -(  s�  
z*   4    8T�+    8T�,    hT�+    hT�,  0 �     
8�   oK  
9�   op  
9�   op  
>�   -o�  
./o�  
3no�  
ri po�  
,Io�  
rm p(�  
o�  

	�i3{  	�(�  
o*  
+8{%  	�	�o�  
+%{  od  
&+{  rq p(�  
o�  
�
o@  
?1���* 0 $     (  
{  o�  
(  (�  
(�  
*(&  
*�~&  -r p�  ($  
o�  
s�  
�&  ~&  *~'  *�'  *V(  r� p~'  o�  
*V(  r p~'  o�  
*V(  rW p~'  o�  
*V(  r� p~'  o�  
* BSJB         v2.0.50727     l   �
  #~      #Strings      �  #US �"     #GUID   �"  �  #Blob         W�		   �3      T      '      	   �                                         `      �� � �� �
   � d� �� �� (� �� �� �� E� �F
 �� ��
 /
� �� (� �
3
 ��  P3 �3 L� 93 �3 �3 I�  =  �3 }3
 V�  `�w j   �3 �3 *3 �3 �	3 �	� 8	3 �	3 n	� �	�
 z� �� _� F
 #
,
 7
 � �� 
 � �� l� �3 x3 �	3 N
� 
� �3 �3 �
3 @3 W3 �3 �3 �  �� �3 '3 �
�
 ��  T	� ~	3 !	�  	� �3 Y
, 3� �
� �3 �	� &�           � �	3=    �A=      �A= & 
  x� �� �� {� a� P� =� �� ,
� � �
� �� � 8� �� �� W� d� �
� j
� o� ;� ��  � % � 
� �� �� �� f� �� �
� � ;� � .� �� �� �P     � � �!    � � "    �{�  T"    �u  #    � � #    � 
 $/    � �� �6    � s
% �7    � �
 	 8    �u 	 8    �+	 @8    ���	 G8    ��1	 O8    �
8
 e8    �8
 {8    ��8
 �8    ��8
       �   �  2   �   �   �   �   U	 u  u  u
 ) u 1 u 9 u A u I u Q u Y u a u i u q u � u iu qu yu �u  � �8 � ?
< �  8 �� B � �8 � m8 � �8 �kH � u � �M � M �tQ Q �8 Y �8 1 �	8 a �8 ) 78 �~y � Y� y u � u � u � u � �� �u� � �� �Q� � u � u  u � g8 � q  v  � �
&y �+�A8 �:1�9	 ��=�� B9uI� u �u � )M � 
P� �W g` r� �w �9	9� �
M ���S�� ��!J�!��� � � � ��
� ��� � & � � � 
 � C�� � � �
 � �
 � o � � � ��� U �  � �  � P )I��  � ���j�� v �$ u � u � �� � ��q �$ v  $ g`, r�8 � A�, �9��M � ��� ��� ��� ��� ��� ��� �8 !�� ��� t � �� � u� !}�!��� kH )q )�� �!q !�� q *� 29�8��=9DIv  98!��� �J� Q� )M Qu 9y]� �c��i�  8 � [o� �z� � �9�A�8 A�+y r8 A� 8 �
	�� �
 ��������q��q���V���1������ ��z Bq����	m ���� ��X B	� 	�
 	C �+B	� �$��u �������<B� = �/� ��� {8 �������u$��+) � t.  L.  U.  t. # }. + �. 3 �. ; �. C �. K �. S �. [ . c �. k @I � t� { P� � K� � K� s K' q � ��    <  B  H  H  H  �H       
       	      
 v 
j���            E�             34                3�               <�               <.           W
       #  X  ]  b  g  l  M      List`1 ToInt32 <Module> System.IO T suppressSchema mscorlib System.Collections.Generic IllegalSuppressWarningId Load Add TypeSpecificationForExtensionRequired System.Collections.Specialized Bind SpecifiedBinderNotFound Replace get_StackTrace set_SuppressUISequence suppressUISequence set_SuppressAdvertiseSequence suppressAdvertiseSequence set_SuppressAdminSequence suppressAdminSequence add_Message get_Message ElevateWarningMessage SuppressWarningMessage get_HelpMessage AddRange set_SuppressMsiAssemblyTable suppressMsiAssemblyTable get_EXP_BindFileOptionNotApplicable AddVariable GetEnvironmentVariable IDisposable RuntimeTypeHandle GetTypeFromHandle CommandLineResponseFile set_UnreferencedSymbolsFile unreferencedSymbolsFile GetFile set_OutputFile outputFile get_EXP_CannotLinkObjFilesWithOutpuFile Console wconsole get_Title get_Name GetFileName IllegalBinderClassName get_ProductName GetDirectoryName PostParseCommandLine WriteLine get_Type GetType GetOutputType get_Culture set_Culture resourceCulture get_DefaultCulture get_InvariantCulture Dispose Parse Intermediate EditorBrowsableState MTAThreadAttribute CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute ExpectedWixVariableValue value Save light.exe IndexOf ToString GetCommandLineArgumentsHelpString GetString Substring IsValidArg DeprecatedCommandLineSwitch GetFullPath get_Length StartsWith suppressVersionCheck Link original System.ComponentModel Microsoft.Tools.WindowsInstallerXml outputXml VSExtensionsLandingUrl NewsUrl SupportUrl get_Item System resourceMan Main AddExtension ChangeExtension GetExtension WixExtension get_FileVersion get_Location get_TempFilesLocation set_TempFilesLocation System.Globalization AddLocalization PrepareConsoleForLocalization set_SuppressLocalization suppressLocalization System.Reflection NameValueCollection StringCollection SectionCollection TableDefinitionCollection SEHException UnexpectedException NullReferenceException WixNotIntermediateException InvalidOperationException FormatException WixInvalidIdtException ArgumentException OverflowException WixException WixNotLibraryException get_Description WixDistribution AppCommon StringComparison Run CopyTo CultureInfo FileVersionInfo GetVersionInfo MemberInfo NumberFormatInfo showLogo ParseCommandLinePassTwo PrintHelp showHelp Cleanup Char get_LastErrorNumber DisplayToolHeader IFormatProvider get_CustomBinder WixBinder binder get_ResourceManager get_FileManager BinderFileManager Linker IMessageHandler ConsoleMessageHandler messageHandler AddMessageEventHandler System.CodeDom.Compiler DisplayToolFooter set_WixVariableResolver wixVariableResolver ToLower set_Localizer get_Major get_Minor get_Error get_EncounteredError IllegalWarningIdAsError set_WarningAsError StringEnumerator GetEnumerator .ctor .cctor System.Diagnostics set_AllowDuplicateDirectoryIds allowDuplicateDirectoryIds System.Runtime.InteropServices System.Runtime.CompilerServices set_AllowUnresolvedReferences allowUnresolvedReferences System.Resources Microsoft.Tools.WindowsInstallerXml.Tools.LightStrings.resources DebuggingModes set_ShowPedanticMessages showPedanticMessages set_ShowVerboseMessages set_DropUnrealTables dropUnrealTables bindFiles localizationFiles GetFiles inputFiles set_Cultures cultures GetCustomAttributes set_SuppressAllWarnings WixWarnings LightStrings unparsedArgs invalidArgs MessageEventArgs WixWarningEventArgs WixErrorEventArgs args get_BindPaths get_NamedBindPaths bindPaths get_SourcePaths sourcePaths Equals Microsoft.Tools.WindowsInstallerXml.Tools Contains ProcessExtensions GetLocalizations get_Sections System.Collections get_TableDefinitions get_Chars ReplacePlaceholders WixErrors binderClass get_Comments get_CommandLineArguments set_AllowIdenticalRows allowIdenticalRows set_SectionIdOnRows sectionIdOnRows Concat TelemetryUrlFormat get_NumberFormat Object get_Product ShortProduct Light light get_Copyright get_LegalCopyright Split Environment UnsupportedCommandLineArgument get_Current get_Count Convert extensionList ArrayList MustSpecifyOutputWithMoreThanOneInput set_Output MoveNext wix Display ToArray ToCharArray tidy get_Assembly assembly get_Company GetLibrary GetDirectory op_Equality IsNullOrEmpty    [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  	L G H T  l i g h t . e x e  	. w i x  W I X _ T E M P  . w i x m s p  . w i x o u t  a i  a d  a u  b  
b i n d e r  b f  c u l t u r e s :  	n u l l  n e u t r a l  d u t  e x t  	- e x t l o c  
n o l o g o  
n o t i d y  o  o u t  p e d a n t i c  
s a d m i n  	s a d v  	s l o c  s m a  s s  s t s  s u i  s v  s w a l l  s w  u s f  v  w x a l l  w x  x o  ?  	h e l p  d  =  
S o u r c e  mM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . T o o l s . L i g h t S t r i n g s  )C o m m a n d L i n e A r g u m e n t s  ?E X P _ B i n d F i l e O p t i o n N o t A p p l i c a b l e  GE X P _ C a n n o t L i n k O b j F i l e s W i t h O u t p u F i l e  H e l p M e s s a g e   ߎXG�O�@�dA��f(M         ��E)-1I   E     
)
-

1
  ���� �� ��  ��  h)eimquyU}��y����yyu��������������y������������uy  �� y    Q  �� ��    �� ��  UY	  �� ��y   	 UU� �
  ��     ]  u��  �
 ������� �� i y ��� i    y ���  m m ��� }  �  U   �% �� }mq�� }  �� 
 �] �� }  �� ��
 
 Y   �� ��	 Y �
  �= �E         A A ���z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����QUY]���� A	A    U  �� ��   �� ��         TWrapNonExceptionThrows       WiX Toolset Linker   Linker          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US     @ 3System.Resources.Tools.StronglyTypedResourceBuilder*******     �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP�S��������_��-       �   p   �  (C o m m a n d L i n e A r g u m e n t s     >E X P _ B i n d F i l e O p t i o n N o t A p p l i c a b l e �	  FE X P _ C a n n o t L i n k O b j F i l e s W i t h O u t p u F i l e �	  H e l p M e s s a g e 3
  �   -ai        allow identical rows, identical rows will be treated as a warning (deprecated)
   -ad        allow duplicate directory identities from other libraries (deprecated)
   -au        (experimental) allow unresolved references
              (will not create a valid output) (deprecated)
   -b <path>  specify a binder path to locate all files
              (default: current directory)
              prefix the path with 'name=' where 'name' is the name of your
              named bindpath.
   -bf        bind files into a wixout (only valid with -xo option) (deprecated)
   -binder <classname>  specify a specific custom binder to use provided by
                        an extension (deprecated)
   -cultures:<cultures>  semicolon or comma delimited list of localized
             string cultures to load from .wxl files and libraries.
             Precedence of cultures is from left to right.
   -d<name>[=<value>]  define a wix variable, with or without a value.
   -dut       drop unreal tables from the output image (deprecated)
   -ext <extension>  extension assembly or "class, assembly"
   -loc <loc.wxl>  read localization strings from .wxl file
   -nologo    skip printing light logo information
   -notidy    do not delete temporary files (useful for debugging)
   -o[ut]     specify output file (default: write to current directory)
   -pedantic  show pedantic messages
   -sadmin    suppress default admin sequence actions (deprecated)
   -sadv      suppress default adv sequence actions (deprecated)
   -sloc      suppress localization
   -sma       suppress processing the data in MsiAssembly table (deprecated)
   -ss        suppress schema validation of documents (performance boost) (deprecated)
   -sts       suppress tagging sectionId attribute on rows (deprecated)
   -sui       suppress default UI sequence actions (deprecated)
   -sv        suppress intermediate file version mismatch checking (deprecated)
   -sw[N]     suppress all warnings or a specific message ID
              (example: -sw1009 -sw1103)
   -swall     suppress all warnings (deprecated)
   -usf <output.xml>  unreferenced symbols file
   -v         verbose output
   -wx[N]     treat all warnings or a specific message ID as an error
              (example: -wx1009 -wx1103)
   -wxall     treat all warnings as errors (deprecated)
   -xo        output wixout format instead of MSI format
   -? | -help this help informationCThe -bf (bind files) option is only applicable with the -xo option.FCannot link object files (.wixobj) files with an output file (.wixout)� usage:  light.exe [-?] [-b bindPath] [-nologo] [-out outputFile] objectFile [objectFile ...] [@responseFile]

{0}

Binder arguments:
{1}

Environment variables:
   WIX_TEMP   overrides the temporary directory used for cab creation, msm exploding, ... �o��%����HP����{l��>b���d��?x�5T��VIRt����V��B��ӸG(S�B�%�����'�E�1�����Y�;r�U�㣜��� ��� �(�V�q.�xd2�WZY�^2+�    'p�]         �n  �^  RSDS�
&M)�F�^�p �   C:\agent\_work\66\s\build\obj\ship\x86\light\light.pdb                                                                                                                                                                                                              �o          p                          �o            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              �   P  �                  8  �               	  �                     h  �               	  �  ��  T          T4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   N   F i l e D e s c r i p t i o n     W i X   T o o l s e t   L i n k e r     8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   ,   I n t e r n a l N a m e   l i g h t   � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   < 
  O r i g i n a l F i l e n a m e   l i g h t . e x e   \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	��  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Light" version="*******" processorArchitecture="x86"  type="win32"/>
 <description>WiX Toolset Linker</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       p     0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      