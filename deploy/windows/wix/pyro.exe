MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L *p�]        � 0  P          �c       �    @                       �     V�   @�                           �c  O    �  �                   �     pb                                                               H           .text    D       P                    `.rsrc   �   �      `              @  @.reloc      �      p              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �c      H     @2  �$        W  �
  �a  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�1  r�  po  
�1  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po  
o  
 (  +,r�  p	o   
o  
 (  +,r+ po!  
o  
 (  +,rO po"  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  (#  
o$  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 �       (%  
s&  
}  s&  
}  r� pr� ps'  
}  }  }  }
  }	  }  s(  
}  s)  
}  {  %�*  
s+  
(  s,  
}  {  {  %�*  
s+  
o-  
s&  
}  s&  
}  s.  
}  s.  
}  * 0 )     {   
(/  
t  |   (  +
3�*   0 )     {   
(1  
t  |   (  +
3�*F(2  
s  o  * 0 �    (	  {  o3  
,{  o4  
��  {
  ,{  -}  {  ,(5  
{  ,!(  (6  
(7  
{  o4  
݁  s8  

{  o9  
+A(:  
{  o;  
s<  
{  %�*  
s+  
o=  
o>  
&(?  
-���  o@  
�sA  
{  %�*  
s+  
oB  
sC  
}  {  r� p(D  
oE  
{  {  oF  
{  {  %�*  
s+  
oG  
{  {  oH  
{  {  oI  
{  {  oJ  
{  oK  
�0  {  oL  
{  {  oM  

{  oN  
	8�   	oO  
(P  

{  
oQ  

oR  
	oK  
/
	{  oS  


oT  
,`,G(U  
(  �  %
oT  
oV  
oW  
�%{  oX  
oW  
�(Y  
r� psZ  
z{  
oT  
o[  
	o\  
:P����	u#  ,o@  
�	oN  
	+	oO  
{  (]  
o*  
	o\  
-��	u#  ,o@  
�{  o3  
,{  o4  
�  (  {
  o^  
o_  
/o`  
{  o3  
,{  o4  
��  {  -{  ,{  r p(a  
}  {  {  -{  +ob  
{  ,{  oH  
{  oI  
{  -{  ,!{  ,{  (c  
-{  (d  
&{  {	  oe  
{  oX  
{  of  
{  oX  
{  og  
{  oX  
oh  
oi  
{  oX  
{
  oj  
{  oh  
{  ok  
&ݛ   

ol  
(
  ݇   om  
on  
oW  
oo  
(p  
(
  uG  -	uH  ,��J{  ,A{  ,${  oq  
-,(  {  or  
(s  
+(  {  or  
(s  
�{  o4  
** A�     �   N   �             �  �   y            �  )   �                 M  M     $          M  a  =   %         �  �  J       0     
8  �9�  ot  
9�  -ou  
./ou  
@|  ov  
r p(w  
,}	  8�  r p(w  
,e{  �%
(x  

	(y  
,*	=oz  
3{  	o{  
&8h  	�J  %=�o|  
{  ��o}  
8=  r% p(w  
,j{  �%
(x  
(y  
,*=oz  
3{  o{  
&8�  �J  %=�o|  
{  ��o}  
8�  r+ p(w  
,){  �%
(~  
}  {  (y  
9�  *r1 p(w  
,}
  8w  r� p(w  
,8�%
(  
-{  r= p(�  
o*  
*{  �o{  
&82  rG p(w  
,}  8  rM p(w  
,}  8   r[ p(w  
,}  8�  ri p(w  
,,{  ri pru p(�  
o*  
{  o�  
8�  ru po�  
9�   ov  
ot  
-{  o�  
+8(�  
o�  
(�  
2{  (�  
o*  
{  o�  
�@  &{  (�  
o*  
�'  &{  (�  
o*  
�  r{ p(w  
,,{  r{ pr� p(�  
o*  
{  o�  
8�  r� po�  
9�   ov  
		ot  
-{  o�  
+8	(�  
o�  
(�  


2{  	(�  
o*  
{  
o�  
�g  &{  	(�  
o*  
�N  &{  	(�  
o*  
�5  r� p(w  
-
r� p(w  
,){  �%
(�  
}  {  (y  
9�  *r� p(w  
,){  �%
(�  
}  {  (y  
9�  *r� p(w  
,}  8�  r� p(w  
,}  8�  r� p(w  
,}  8q  r� p(w  
,}  8X  r� p(w  
,}  8?  r� p(w  
9�   �%
(  
-{  (�  
o*  
*�{  �%
(�  
(y  
,*{  o�  
,{  (�  
o*  
*{  o�  
{  o�  
8�   r� p(w  
,{  o�  
8�   r� p(w  
-
r� p(w  
,}  *{  o{  
&+T@ou  
3ov  
(�  
(	  +5{
  - {  (�  
}
  {
  (y  
,*{  o{  
&�
�i?����* 4    qT�&    qT�'    JT�&    JT�'  Z{   ,
{   o�  
* 0 �    {  o�  
o�  

+No�  
t0  {  o�  
,2
+$	�{  oX  
o�  
o}  
X	�i2�o�  
-��u#  ,o@  
�{  o�  
o�  

+To�  
t0  {  o�  
,4
+%	�	{  oX  
o�  
	o}  
X	�i2�o�  
-��u#  ,o@  
�{  oN  

+!
oO  
{  oX  
o�  
o{  
&
o\  
-��
u#  ,o@  
�{  oN  

+!
oO  
{  oX  
o�  
o{  
&
o\  
-��
u#  ,o@  
�*   4    Zk      � `�      .?     a.�    (%  
*�~!  -r� p�  (#  
o�  
s�  
�!  ~!  *~"  *�"  *V(
  rW p~"  o�  
*V(
  r� p~"  o�  
*V(
  r� p~"  o�  
*V(
  r� p~"  o�  
* BSJB         v2.0.50727     l    
  #~  l
  �  #Strings    d    #US t     #GUID   �  <  #Blob         W�		   �3      P      "         �                     	                                J< �< �
 �   �D -D �B � �D �D �D jD D w\ �B �D
 �
 �B �B �	� !	�
 j�   T   T  �	� .
�
 V�  (< T k�c �
   \�
 �
�  �� �B �� �B �B �B w� �

 C 
 Z
 3	\ }
 �
 �� @B 
 B �B �B 
B � # #� �	� ;B B 	� �	� �	� 	B �B R� �� �&  �&  �&  r� �� 9� �B � k� �B -B � LB �
 �� ?{             � �=    �c
=      ^c
= !   &g �g .g �g g �
j (	m �g �
j 6 j �
q jq &g �
u � } �	� 0g �g Uj �j �j �j ej j < j �j B
� Q
q (
q �� �� �� k� ��P     � %� �!    � �� "    ��  T"    ��
  8#    �&�  p#    �2�  �#    � w� �#    � >� P)    � f�	 �/    � z�
 �/    � �	  �1    ��
  �1    �/	� �1    ��e �1    ��� �1    �y	� �1    ��� 2    ��� )2    �V
�    �   �   �  �         �   �   �   2    	 �
  �
  �

 ) �
 1 �
 9 �
 A �
 I �
 Q �
 Y �
 a �
 i �
 q �
 � �
 Q�
 Y�
 y�
  � �8 � �< � c8 �B � �8 � V8 � �8 ��
H � �
 � t
M � ~
M ��Q Q �8 Y �8 1 8 a �8 ) L8 ��y � � y �
 � �
 � �
  �
  �
 � �� � �
� � �
 � &� � �
 ��� ��� �� �&� ��
� � �M ��� �w�
� � �
  �
$ 6 9�
 &� � � !$ y� � � �
 � &� � �
 ��&�� �*
+�&� � � � I � � � BM � B1�f8� �
A	68 � G�|N� |NfUc	_i�ey �ky 48 �C	_��q��
 �S	{	y� ��� �  � BM � �
�	��� � p���� �
 �C �� � i��^��\ �
�!�
�)A8 )�k)8 1����� ��8 �w���M ���G����I�������� � ��� �  I�	IQ1� �q!�: ��)i�ei�1i 71o �c ��
 1�
�M I	1� ? �E1I � K � S�� qYI�_� �� yw��
�I6�� 0����Iy� ����;
��
����a�
�a=�) � .  �.  �.  . # . + 9. 3 L. ; R. C X. K i. S ~. [ �. c R. k �I � � { �� � �� s �� s �� s �s �' q � � �f    �Q     3	�  ��  }	�  ��  ��  Z
�       
             	      
 v � � �            ��             �K                �B               ��               �C           m       0  X  ]  b  g  l a �  M      List`1 ToInt32 Dictionary`2 <Module> System.IO T mea delta suppressWixPdb mscorlib System.Collections.Generic IllegalSuppressWarningId Load Add Interlocked inputTransformsOrdered BaselineRequired TypeSpecificationForExtensionRequired System.Collections.Specialized Bind Replace get_StackTrace add_Message remove_Message get_Message ElevateWarningMessage SuppressWarningMessage OnMessage get_HelpMessage CompareExchange Invoke GetEnvironmentVariable IDisposable RuntimeTypeHandle GetTypeFromHandle set_PdbFile pdbFile CommandLineResponseFile GetFile inputFile outputFile Console wconsole get_Title get_ProductName ParseCommandLine WriteLine Combine GetType get_CurrentUICulture get_Culture set_Culture resourceCulture get_InvariantCulture NameObjectCollectionBase Dispose Parse Delegate EditorBrowsableState CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute value Remove pyro.exe IndexOf System.Threading ToString GetString Substring IsValidArg set_DeltaBinaryPatch DeprecatedCommandLineSwitch set_CabCachePath cabCachePath VerifyPath get_Length StartsWith original System.ComponentModel Microsoft.Tools.WindowsInstallerXml VSExtensionsLandingUrl NewsUrl SupportUrl get_Item System DuplicateTransform PatchTransform resourceMan Main AddExtension ChangeExtension WixExtension get_FileVersion set_SetMsiAssemblyNameFileVersion get_Location get_TempFilesLocation set_TempFilesLocation System.Globalization PrepareConsoleForLocalization System.Reflection NameValueCollection StringCollection KeysCollection SEHException UnexpectedException NullReferenceException FormatException ArgumentException OverflowException WixException get_Description WixDistribution AppCommon StringComparison Run CopyTo set_SuppressFileHashAndInfo suppressFileHashAndInfo CultureInfo FileVersionInfo GetVersionInfo NumberFormatInfo DirectoryInfo showLogo Pyro pyro showHelp Char get_LastErrorNumber DisplayToolHeader IFormatProvider WixBinder binder get_ResourceManager get_FileManager set_FileManager get_BinderFileManager get_EXP_CannotLoadBinderFileManager PrepareDataForFileManager IMessageHandler ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler DisplayToolFooter set_WixVariableResolver wixVariableResolver get_WAR_FailedToDeleteTempDir get_Major get_Minor get_Error get_EncounteredError IllegalWarningIdAsError set_WarningAsError IEnumerator StringEnumerator GetEnumerator .ctor .cctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Tools.PyroStrings.resources DebuggingModes set_ShowVerboseMessages set_SuppressAssemblies suppressAssemblies DeleteTempFiles suppressFiles GetCustomAttributes GetValues set_SuppressAllWarnings WixWarnings PyroStrings unparsedArgs MessageEventArgs WixWarningEventArgs WixErrorEventArgs args get_UpdatedNamedBindPaths updatedNamedBindPaths get_TargetNamedBindPaths targetNamedBindPaths get_UpdatedSourcePaths updatedSourcePaths get_TargetSourcePaths targetSourcePaths Microsoft.Tools.WindowsInstallerXml.Tools AttachTransforms inputTransforms set_AllowEmptyTransforms allowEmptyTransforms Contains extensions setAssemblyFileVersions System.Collections get_Chars ReplacePlaceholders WixErrors set_ReuseCabinets reuseCabinets get_Comments Exists get_Keys get_INF_TempDirLocatedAt Concat TelemetryUrlFormat get_NumberFormat Object get_Product ShortProduct get_Copyright get_LegalCopyright Split Environment UnsupportedCommandLineArgument get_Current get_Count Convert ArrayList set_Output get_PatchOutput MoveNext wix Display tidy ContainsKey get_Assembly assembly get_Company CreateDirectory GetDirectory op_Equality IsNullOrEmpty  [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  	P Y R O  p y r o . e x e  W I X _ T E M P  e x t  . w i x p d b  a e t  b t  b u  c c  d e l t a  	- e x t f v  
n o l o g o  
n o t i d y  s w a l l  s w  w x a l l  w x  o  o u t  
p d b o u t  r e u s e c a b  s a  s f  s h  	s p d b  t  v  ?  	h e l p  kM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . T o o l s . P y r o S t r i n g s  ?E X P _ C a n n o t L o a d B i n d e r F i l e M a n a g e r  H e l p M e s s a g e  )I N F _ T e m p D i r L o c a t e d A t  3W A R _ F a i l e d T o D e l e t e T e m p D i r     t߭j��:E�Ī�1-Ov         ��E)-1I   E     
)
-

1
  ���� ��]a ��  QQQQ ������    
Q  %uyY}������������     } }        i  Ye  �� �� ��	 YY��  ��  ��  ��	 �� �� � u   �
  � � �  � � 
   
 e   	 e  � � �-  �1 ��  �        e
������  �A  ��     m  Y  A A ���z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����UY]aeimQ���� A	A     ��  �� ��   �� ��         TWrapNonExceptionThrows       WiX Toolset Patch Builder   
Patch Builder          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US     @ 3System.Resources.Tools.StronglyTypedResourceBuilder*******  �
  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADPˮ��̿n������I!    �   C   ^   �  >E X P _ C a n n o t L o a d B i n d e r F i l e M a n a g e r     H e l p M e s s a g e �   (I N F _ T e m p D i r L o c a t e d A t �  2W A R _ F a i l e d T o D e l e t e T e m p D i r 	  �cannot load binder file manager: {0}.  pyro can only load one binder file manager and has already loaded binder file manager: {1}.� usage: pyro.exe [-?] [-nologo] inputFile -out outputFile [-t baseline wixTransform] [@responseFile]

   -aet       allow patches to be created with one or more empty product transforms
   -bt <path> new bind path to replace the original target path.
              It accepts two formats matching the exact light behavior.
              (example: -bt name1=c:\feature1\component1 -bt c:\feature2)
   -bu <path> new bind paths to replace the bind paths for the updated input.
              It accepts two formats matching the exact light behavior.
              example: -bu name1=\\serverA\feature1\component1
                       -bu \\serverA\feature2
   -cc <path> path to cache built cabinets
   -delta     create binary delta patch (instead of whole file patch)
   -ext <extension>  extension assembly or "class, assembly"
   -fv        update 'fileVersion' entries in the MsiAssemblyName table
   -nologo    skip printing logo information
   -notidy    do not delete temporary files (useful for debugging)
   -o[ut]     specify output file
   -pdbout <output.wixpdb>  save the WixPdb to a specific file
              (default: same name as output with wixpdb extension)
   -reusecab  reuse cabinets from cabinet cache
   -sa        suppress assemblies: do not get assembly name information
              for assemblies
   -sf        suppress files: do not get any file information
              (equivalent to -sa and -sh)
   -sh        suppress file info: do not get hash, version, language, etc
   -spdb      suppress outputting the WixPdb
   -sw[N]     suppress all warnings or a specific message ID
              (example: -sw1011 -sw1012)
   -swall     suppress all warnings (deprecated)
   -t baseline  transform  one or more wix transforms and its baseline
   -v         verbose output
   -wx[N]     treat all warnings or a specific message ID as an error
              (example: -wx1011 -wx1012)
   -wxall     treat all warnings as errors (deprecated)
   -? | -help this help information

Environment variables:
   WIX_TEMP   overrides the temporary directory used for cab extraction, binary extraction, ...%Temporary directory located at '{0}'.2Warning, failed to delete temporary directory: {0}   ҲT-�0�&lak�29@�n��1qC�l�	�˸�@4���2e�KR���Y�5q�s?B"�#�5̋�Y!��P[Si�4o��@�n�"��.�:Ө�Ab�
��1��?�iu�Vכmb7Nx)輌�?���;    *p�]         �b  �R  RSDS��'�n�@���.i7T*   C:\agent\_work\66\s\build\obj\ship\x86\pyro\pyro.pdb                                                                                                                                                                                                                �c          �c                          �c            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      �   P  �                  8  �               	  �                     h  �               	  �  ��  `          `4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   \   F i l e D e s c r i p t i o n     W i X   T o o l s e t   P a t c h   B u i l d e r   8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   *   I n t e r n a l N a m e   p y r o     � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   : 	  O r i g i n a l F i l e n a m e   p y r o . e x e     \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	� �  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Pyro" version="*******" processorArchitecture="x86" type="win32"/>
 <description>WiX Toolset Patch Builder</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      `     �3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      