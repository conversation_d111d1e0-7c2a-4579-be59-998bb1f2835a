MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L �p�]        � 0  P          Fd       �    @                       �     ��   @�                           �c  O    �  �                   �     �b                                                               H           .text   LD       P                    `.rsrc   �   �      `              @  @.reloc      �      p              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                (d      H     d-  h/       �\  p  <b  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�H  r�  po   
�H  (!  
o  
 (  +,r�  po"  
o  
 (  +,r�  po#  
o  
 (  +,r�  p	o$  
o  
 (  +,r+ po%  
o  
 (  +,rO po&  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  ('  
o(  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *()  
*  0 )     {  
(*  
t  |  (  +
3�*   0 )     {  
(,  
t  |  (  +
3�*"}  *{  *"}  *{  *"}  *0 7     s  
o  o	  o  o  o
  -(  +*o
  * 0 2     (  
(  +-
(#  (  *{  (/  
-(  *  0 (     u  
{  ,{  o0  
*,s1  
z*0 �     s2  
}  s3  

(  9�   o4  
<�   o5  
o6  
+~(7  

{  	o8  
o9  
&	o:  
r� po;  
,Mo<  
o=  
+o>  
t&  /o?  
o@  
oA  
-��u'  ,oB  
�(C  
:v�����  oB  
�*   w *�      6 ��     0     sD  

r� poE  
r� poF  
r� poG  
{  (H  
r� p(I  
oJ  
r# poK  
r+ poL  
sM  
oN  
oO  
oP  
oQ  
+!oR  
sS  
oT  
oP  
oA  
-��,oB  
�sU  
oV  
sW  

	oX  
{  (	  +(,  (  {  	(Z  
o[  
o\  
o]  
�,oB  
�*      { .�      �     0 �   	  s^  

o_  
+ o>  
t!  o`  
3	oa  
+�PoA  
-��u'  
	,	oB  
�ob  
+(c  
od  
(e  
-���
  oB  
�*   
 ,9      R q     0   
  sf  

{  �0  
sg  
oh  
{  ,?{  oi  
+oj  
(k  

	ol  
om  
-��u'  ,oB  
�sn  
{  9�   {  oo  
+s(p  
(q  
(r  
,Xos  
(t  
ou  
(  �6&�  os  
(v  
		ow  
(  �&("  (  � (x  
-���  oB  
�*  4   1 P       � "� 7    � "� 8   � �    vsy  
}  s3  
}  ()  
*  0 D       sy  
}  s3  
}   s3  
}!  }#  ry pr� psz  
}%  ()  
*F({  
s  o  *  0 �    (  {%  o|  
,{%  o}  

݆  {#  ,(~  
{$  , (  (  
(�  
{%  o}  

�Q  {!  oo  
+(p  
{%  (�  
o�  
(x  
-���  oB  
�}!  {   o�  
,
{"  (/  
,,(  (  
{%  (!  o�  
{%  o}  

��   {"  (�  
(/  
,{"  r� p(�  
}"  {  (�  
{  {   {"  {%  %��  
sg  
(  &�X
{%  	o�  
o�  
�C{%  o�  
o�  
o�  
o�  
(�  
o�  
u\  -	u]  ,�� {%  o}  
**   AL     f   '   �                  G  G     ;          G  \  C   <  0 �    
8~  �9p  o�  
9e  o�  
3{   r� p(�  
o�  
8A  -o�  
./o�  
@�   o�  
r� p(�  
,8�%
(�  
-{%  r� p(�  
o�  
*{  �o�  
&8�   r� p(�  
,}#  8�   r� p(�  
-
r� p(�  
,({%  �%
(�  

	(/  
,*	}"  8�   r� p(�  
,{%  o�  
+er� p(�  
-
r� p(�  
,}$  *{!  o@  
+5@o�  
3o�  
(�  
(  +{   r� p(�  
o�  
�
�i?y���*�~&  -r� p�  ('  
o�  
s�  
�&  ~&  *~'  *�'  *V(  rM p~'  o�  
*N(�  
(�  
*~(  *Vre p(�  
s�  
�(  *^ A  r� p�  s  *n B  r p�  %�s  *^ C  ra p�  s  *N(�  
(�  
*~)  *Vre p(�  
s�  
�)  *N(�  
(�  
*~*  *Vre p(�  
s�  
�*  *� 	   r� p�  %�%�H  �s(  *BSJB         v2.0.50727     l   T  #~  �  p  #Strings    0!  �  #US '     #GUID   ('  @  #Blob         W_�		   �3      `      *   ,   %      �                        
                     	        W      bb 	b �	 
   �� E� �� �C �� �� �� �� &� s� �� 
� <4 � ��
 E
	 `� � �
g g
 	�  ' d   d  
b  d  �
g Yg 5 d  �g� n	  � �   �g �� Gg �� +_ h_ K_ �
\  d  �S X\ 4 d  	gk �   �
g
 ��  /g �g 
g �	g �	g �
g �� �	g �	�
 % V	 �
� 9
�
 �/
 �/ E	g �� 1g 	g �� . � 1� � �� � � �v
 �g `	g K  $_�    q_ g ^K  +	g 
g �
g h� �
g Kg �	� |	C �g 'g Og    B       � 
gI     �I    ��I    ��I      �
�I &    k�} (   <�I )     E�) $  �
�I * '   �* (  �
�I + +  �� &� ��  � ��V��V���V�R�V���V�U�V�7�V���V�� �V�j�V�B�V�T�V�u�V�� �V���V���V���V�(�V���V�"
�V�W� 
� v
� �� X� �� 
� v
� �
� X� d
� m
� �
� �� � �
� �
� �
�P     � ( �!    � � "    �� R"    ��  \"    �p� �"    �|� �"    �� �"    �X
 �"    �g
& �"    ��/	 �"    �I 	 �"    � �7
 8#    � �� x#    ��I �#    � �/ �$    � :O �%    � �X �&    � �i �'    ��  (    ��  X(    � �u l(    � 
{ t*    � �� R"    ��  ,    ��
� ;,    �l� B,    �x� J,    ��� `,    ��h t,    ��
� {,    �� R"    ��  �,    � )� �,    � � �,    � �� �,    ��h �,    ��
�  �,    ��  R"    ��   -    ��h  "-    ��
�$ )-    ��$ R"    �� $ ?-    � 4�$    &   �   �  '   1   1   1   1   1   �   v
   X   �   W       ?   B
   �   }   }   }   1      
   �   �
   c      
   �   �
      
   �   �
   M     ] 	 �  �  �
 ) � 1 � 9 � A � I � Q � Y � a � i � q � � � � � � � �� ��  �� �& !� � h> � U
B � �> 9H � A> � �> � X> 9�N � � � tS � ~S 9�W Q > Y 3> 1 �	> a > ) y> I � �
� � � Y�� a�� YP� iL� i�� 9D� � �� ���  �  � $ �S $ �4, �J4 �`$ �e � l	7
rq�x!�y��)��1�� � �)>�9� 4 >�A� A  A� AG ��9��A� AL AX I� I� �I��A
�< ��D �`i� i  Q� Q
�Y� Yf i�q��q� Q�q� L � ���	�3L � �L �9T �`\ �VT >��� � ���p�� ����> �� �����>�$ �  �9d �`����� ����� ������ ����d >�� � ��  ��������
S ��
�����b�������  �S �������������> �.�� �> �+> ��	9S �O
 �9!9�&9(+��1�� 8� � >�C�
 ��NI�T��Y��`� �h� Es� �z)�h1�h  3  �   � $ � ( � , � 0  4  8  <  @  D  H  L  P   T % X * \ / ` 4 d 4' { �.  �.  �.  
. # . + 9. 3 P. ; V. C \. K m. S �. [ �. c V. k �� � �� � 
� � �� � �� � �� � �� � 
�� �D� ��� ��� �d� �  9- w � � � � � �
^�	    �a       	 	  
   ��  k
�  ��  M�  �
�  ��  ��  �
�  �
�  �
�             	   
    	      
   
        %   )  | $*@V��+CL��            ��             [                �               �O              6               \               �<               �p           �  h     �       U  ^  c  h  m  r W � [ � ] � � �  Y    IEnumerable`1 IEnumerator`1 HashSet`1 List`1 Int32 IDictionary`2 <Module> System.IO T mea mscorlib System.Collections.Generic set_Id Load Add Interlocked TestSkipped TypeSpecificationForExtensionRequired set_Compressed TestNotCreated System.Collections.Specialized id AddChild Replace LuxNamespace get_StackTrace GenerateTestSource testSource set_UpgradeCode Package add_Message remove_Message get_Message OnMessage get_HelpMessage message set_Language AddRange CompareExchange Invoke Table Enumerable IDisposable RuntimeTypeHandle GetTypeFromHandle CommandLineResponseFile inputFile set_OutputFile outputFile file Console wconsole get_Title set_Name resourceName LuxTableName LuxCustomActionName get_ProductName ParseCommandLine WriteLine Combine set_InstallScope get_Type InstallScopeType SectionType YesNoType GetType System.Core TestIdMinimumFailure TestIdMaximumFailure get_Culture set_Culture resourceCulture TestFailedExpressionFalse Dispose Parse Create Delegate Intermediate Generate EditorBrowsableState MTAThreadAttribute CompilerGeneratedAttribute GeneratedCodeAttribute UnverifiableCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute SecurityPermissionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute ParamArrayAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute value TestPassedExpressionTrue Remove lux.exe Microsoft.Tools.WindowsInstallerXml.Serialize UnitTestRef System.Threading ToString GetString Substring IsValidArg TestPassedPropertyValueMatch TestFailedPropertyValueMismatch GetFullPath get_Length original System.ComponentModel set_Level MessageLevel System.Xml Microsoft.Tools.WindowsInstallerXml OutputXml VSExtensionsLandingUrl NewsUrl SupportUrl get_Item set_Item System resourceMan Main AddExtension ChangeExtension GetExtension GetFileNameWithoutExtension WixExtension WixLuxExtension set_Version get_FileVersion get_Location TestUnknownOperation ReadConfiguration System.Globalization PrepareConsoleForLocalization SecurityAction Section System.Reflection TableCollection StringCollection SectionCollection TableDefinitionCollection SourceLineNumberCollection RowCollection KeyCollection SEHException UnexpectedException NullReferenceException WixNotIntermediateException WixException WixNotLibraryException get_Description WixDistribution AppCommon Run TestFailedIndexUnknown CultureInfo FileVersionInfo GetVersionInfo showLogo showHelp System.Linq get_LastErrorNumber DisplayToolHeader get_ResourceManager resourceManager Linker IMessageHandler ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler GeneratingConsumer set_Manufacturer XmlWriter DisplayToolFooter get_Major get_Minor get_Error get_EncounteredError TestFailedExpressionSyntaxError IEnumerator StringEnumerator GetEnumerator Generator .ctor .cctor System.Diagnostics unitTestIds TestFailedIndexOutOfBounds System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Lux.Data.Messages.resources Microsoft.Tools.WindowsInstallerXml.Lux.LuxStrings.resources DebuggingModes set_ShowVerboseMessages get_Tables sectionFiles GetFiles get_InputFiles set_InputFiles inputFiles LuxBuildVerboses GetCustomAttributes LuxBuildWarnings WixWarnings LuxStrings XmlWriterSettings invalidArgs messageArgs MessageEventArgs WixVerboseEventArgs LuxVerboseEventArgs WixWarningEventArgs LuxWarningEventArgs WixErrorEventArgs LuxErrorEventArgs args Contains set_Extensions extensions System.Security.Permissions get_Sections LoadSections System.Collections sections get_TableDefinitions get_Chars sourceLineNumbers ReplacePlaceholders LuxBuildErrors WixErrors TestIdMinimumSuccess TestIdMaximumSuccess Constants LoadFragments get_InputFragments inputFragments get_Comments FindUnitTests NoUnitTests Exists get_Rows get_Keys Concat TelemetryUrlFormat Object get_Product ShortProduct get_Copyright get_LegalCopyright TestUnknownResult set_Indent ISchemaElement WriteEndDocument WriteStartDocument UnsupportedCommandLineArgument get_Current TestFailedExpectedEvenNameValueContent get_Count testCount extensionList CouldntLoadInput MalfunctionNeedInput MoveNext Row Wix wix Microsoft.Tools.WindowsInstallerXml.Extensions.Serialize.Lux Microsoft.Tools.WindowsInstallerXml.Lux lux Display GetPrimaryKey get_Assembly GetExecutingAssembly assembly Any get_Company Library GetFileOrDirectory op_Equality System.Security IsNullOrEmpty LuxMutationRunningProperty     [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  W i x U n i t T e s t  *  	1 0 3 3  L u x  #  L u x   t e s t   p r o j e c t  1 . 0  M{ F B B D F C 6 0 - 6 E F F - 4 2 7 E - 8 B 6 B - 7 6 9 6 A 3 C 7 0 6 6 B } L U X  l u x . e x e  	. w x s  
S o u r c e  e x t  	- e x t 
n o l o g o  o  o u t  v  ?  	h e l p  eM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . L u x . L u x S t r i n g s  H e l p M e s s a g e  kM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . L u x . D a t a . M e s s a g e s  KL u x B u i l d E r r o r s _ M a l f u n c t i o n N e e d I n p u t _ 1  CL u x B u i l d E r r o r s _ C o u l d n t L o a d I n p u t _ 1  9L u x B u i l d E r r o r s _ N o U n i t T e s t s _ 1  KL u x B u i l d V e r b o s e s _ G e n e r a t i n g C o n s u m e r _ 1   ���sO�	s�0un        =  �Q)-1U   Q     
)
-

1
  �%�) �%aaa �-�-�-    
a	 u 
u
u  } y }(i������������������mi	����  �� 	����  �� 	����         �9 ��  �=  ��       ��������������   �E �I �Mu	  �� ��
u 	 ���� ��i��������������i��  �Q	  �� ����	����  %
������������������  a  �� �� ����  �Y ���Y  �� ���Y  ������  � e  }  �% } 	 u      } 
 ��   M M �
 �
 ��  M�z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����Rh t t p : / / s c h e m a s . m i c r o s o f t . c o m / w i x / 2 0 0 9 / L u x 0W i x R u n I m m e d i a t e U n i t T e s t s .W I X L U X _ R U N N I N G _ M U T A T I O N W i x U n i t T e s t �i  �i  �i  �i  �i  �i  �i  �i  �i  �i  �i  �i  ��.��System.Security.Permissions.SecurityPermissionAttribute, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089TSkipVerificationeima����� M	M  e  i i  u ueia y u ������  ����     ��  � �    ��    ,( e( i( u(  �� � ( ��        TWrapNonExceptionThrows      " WiX Toolset Unit Test Builder   Unit Test Builder          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US     @ 3System.Resources.Tools.StronglyTypedResourceBuilder*******  c  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP���    �   H e l p M e s s a g e     � usage:  lux.exe [options] file.wxs [...] -out test[.wxs]

   Lux creates a WiX source file that consumes unit tests declared 
   in one or more WiX source files. 

   options:

   -ext <extension>  extension assembly or "class, assembly"
   -nologo    skip printing logo information
   -o[ut]     specify output file
   -v         verbose output
   -?|-help   this help information   ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADPs5y����: /J��:    G   �   �   �  BL u x B u i l d E r r o r s _ C o u l d n t L o a d I n p u t _ 1     JL u x B u i l d E r r o r s _ M a l f u n c t i o n N e e d I n p u t _ 1 j   8L u x B u i l d E r r o r s _ N o U n i t T e s t s _ 1 �   JL u x B u i l d V e r b o s e s _ G e n e r a t i n g C o n s u m e r _ 1 �   hFailed to load input file '{0}'. Valid formats are WiX object (.wixobj) and WiX library (.wixlib) files.ANeed one or more input files and one output file to be specified..No unit tests were found in given input files..Generating project '{0}' to consume {1} tests. �$�y�<FX@�e�����|�a�����[���!l���:��r$�p���j���Z�k���zk�����Af�Q�����Š�vrAc���,�EUh�79k̇����_Εx�=h];φ�u���P�j�t    �p�]         �b  �R  RSDS%ܯ33�H�L?���1   C:\agent\_work\66\s\build\obj\ship\x86\lux\lux.pdb                                                                                                                                                                                                                  d          6d                          (d            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          �   P  �                  8  �               	  �                     h  �               	  �  ��  T          T4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   X   F i l e D e s c r i p t i o n     W i X   T o o l s e t   L u x   B u i l d e r   8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   (   I n t e r n a l N a m e   l u x   � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   8   O r i g i n a l F i l e n a m e   l u x . e x e   \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	��  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"> 
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Lux" version="*******" processorArchitecture="x86" type="win32"/> 
 <description>WiX Toolset Lux Builder</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   `     H4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      