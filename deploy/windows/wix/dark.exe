MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L )p�]        � 0  @          �V       `    @                       �     �	  @�                           �V  O    `  �                   �     PU                                                               H           .text   �6       @                    `.rsrc   �   `      P              @  @.reloc      �      `              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �V      H     �-  L        �M  �  �T  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�,  r�  po  
�,  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po  
o  
 (  +,r�  p	o   
o  
 (  +,r+ po!  
o  
 (  +,rO po"  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  (#  
o$  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 @       (%  
s&  
}  s&  
}  r� pr� ps'  
}
  }  }  *F((  
s  o  *  0 H    
(  {
  o)  
,{
  o*  
�  {	  -	}  +S{  -K{  -{	  (+  
r� p(,  
}  +&{  {	  (+  
r� p(,  
(-  
}  {  ,(.  
{  ,!(  (/  
(0  
{
  o*  
�  {  o1  
+o2  
{
  (3  
o4  
o5  
-��u  ,o6  
�}  s7  

s8  
{
  %�4  
s9  
s:  
o;  
s<  
{  (=  
{  o1  
+o2  
(>  
o?  
o@  
o5  
-��u  ,o6  
�{  oA  
{  oB  
{  oC  
{  oD  
r� p(E  
oF  
{  (G  
-{  oH  
r� p(E  
oI  
{
  %�4  
s9  
oJ  
{
  %�4  
s9  
oK  
{	  (+  
(/  
{	  {
  {  oL  

	9  {
  .{
  .{  ,	{  sM  
oN  
8�   	oO  
		9�   
	oP  
-{
  o*  
�}   {  (Q  
(R  
(S  
&{  (T  
sU  


oV  

 oW  

"oX  

oY  

oZ  
	
o[  

o\  
�2{
  {  o]  
(^  
o4  
{
  o*  
��   �
,
o_  
���   {
  o`  
o4  
ݮ   
{
  
o]  

oa  
ob  

oc  
(d  
o4  

uB  -	
uC  ,��k,2{  ,oe  
-"(  of  
(g  
+(
  of  
(g  
,2{  ,oh  
-"(  oi  
(g  
+(
  oi  
(g  
�{
  o*  
**A�     �   )   �             _  +   �             �  c   ,  2         �  �   `                k  q     !         k  �  C            �  �  k       0 -    
8  �9  oj  
9  -ok  
./ok  
@�  ol  
r	 p(m  
,8�%
(n  
-{
  r p(o  
o4  
*{  �op  
&8�  r p(m  
,}  8�  r) p(m  
,}  8k  r7 p(m  
-
r; p(m  
,N{
  �%
(q  

	(G  
-2	rC por  
-
	rG por  
,	}  8  	}  8  *rK p(m  
,}  8�  rS p(m  
,}  8�  r] p(m  
,}  8�  rg p(m  
,}  8�  ro p(m  
,,{
  ro pr{ p(s  
o4  
{
  ot  
8f  r{ pou  
9�   ol  
oj  
-{
  ot  
+8(v  
ow  
(x  
2{
  (y  
o4  
{
  oz  
��  &{
  (y  
o4  
��  &{
  (y  
o4  
��  r� p(m  
,,{
  r� pr� p(s  
o4  
{
  o{  
8�  r� pou  
9�   ol  
oj  
-{
  o{  
+8(v  
ow  
(x  
2{
  (|  
o4  
{
  o}  
�!  &{
  (|  
o4  
�  &{
  (|  
o4  
��  r� p(m  
,{
  o~  
8�  r� p(m  
,4{
  �%
(  
}  {  (G  
,*�}  8�  r� p(m  
,}  8w  r� p(m  
-
r� p(m  
,}  *{  op  
&8C  {	  :�   {
  (�  
}	  {	  (G  
,*{
  :
  {	  (�  
(v  
o�  
r� p(m  
-Hr� p(m  
-Fr� p(m  
-Dr� p(m  
-?r� p(m  
-:r� p(m  
-5+<}
  8�   }
  8�   }
  +}
  +v}
  +m}
  +d{
  {	  (+  
r� p(�  
o4  
*{  -({  - {
  (�  
}  {  (G  
,*{
  (�  
o4  
�
�i?����*   4    �T&"    �T?#    �T�"    �T#  (%  
*�~  -r1 p�  (#  
o�  
s�  
�  ~  *~  *�  *V(	  r� p~  o�  
*V(	  r� p~  o�  
*V(	  r� p~  o�  
*   BSJB         v2.0.50727     l   H  #~  �  ,  #Strings    �    #US �     #GUID     H  #Blob         W�		   �3      G               �                                         �      ��
 Z�
 P
 �
   WY �Y +T Cc
 nY AY Y �Y �Y ��
 �T �
Y
 P
 �T nT
 k�  �� � @	� ;
� �� ~
�
 
�  XT �� �
� U	� �T �� �T �T
 �(	 �P
 ��
 ��
 &
 ��
 *� T  T "T dT � �� �5  �T �� �� �� 	� 0� 

T u	� �� 5  M5  
�
 4� K	� q� 	� �T |c
 �� <& h
T �T           � ��=    � =      � =    A� �� I� �� *� y� p
� �� �� 	� � �� �  � d� [� 3� h� �� * � �
� [� V�P     � ]� �!    �  � "    �I
�  T"    �C
  �"    � g� �"    � 
� �'    � �� $-    �C
  ,-    ��� X-    �>� _-    �J� g-    �1�	 }-    ���	 �-    ��	�	    �   �
   �
  x            �	 C
  C
  C

 ) C
 1 C
 9 C
 A C
 I C
 Q C
 Y C
 a C
 i C
 q C
 !C
 )C
 1C
 QC
  � �8 � -< � {8 Y� B � �8 � �8 � �8 YJH � C
 � �	M � �	M Y�Q Q �
8 Y �8 1 �8 a �8 ) �8 ivy � �� y C
 � C
 � C
 y;� ��	� � �M ��� �y� �� y�� ��� yc	� � -
� � \
8 �=
� � �
� � �
� � � � C
 � C
 �C
� �C
� � '� � C
 y� � c � � l� � l� �  � H � � �   �A� � � Y� � f � � � � � � � � � � �C
 � �� �� ���� ��� ��
$�	 +� C
1�  � {9� m9� 0>�*
 � E�
 � 8 �	L�{ 	�	T	Zy 8 � 8 �`� �� � �8 ��i� �� � �8 Y�M YS{Y&�Y�!?�l �� h �!�
�Y�H �J��� Y�H Af�A��1 �J �� �	
 �	�� � !�
�!����� Y�	��L� �i�
�9C
�9�) � ".  �.  .  ". # +. + G. 3 W. ; ]. C c. K t. S �. [ �. c ]. k �I � "� s � { B� � B' q � o    ��  s�  5�  ��  �	� 	   
         
 	    v �            ��             �A                �T               ��
               ��           �
       ?  X  ]  b  g  l  M    ToInt32 get_UTF8 <Module> set_SuppressUI suppressUI System.IO T mscorlib IllegalSuppressWarningId Load Add TypeSpecificationForExtensionRequired AdditionalArgumentUnexpected System.Collections.Specialized Unbind Replace get_StackTrace add_Message get_Message ElevateWarningMessage SuppressWarningMessage get_HelpMessage GetEnvironmentVariable IDisposable RuntimeTypeHandle GetTypeFromHandle inputFile outputFile Decompile Console get_Title GetFileName get_ProductName GetDirectoryName ParseCommandLine WriteLine Combine GetType OutputType outputType set_Core HarvesterCore get_Culture set_Culture resourceCulture get_InvariantCulture Close Dispose EditorBrowsableState Mutate CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute value Save dark.exe Microsoft.Tools.WindowsInstallerXml.Serialize set_SuppressRelativeActionSequencing suppressRelativeActionSequencing Encoding ToString GetString Substring set_Formatting IsValidArg DeprecatedCommandLineSwitch set_ExportFilePath exportBasePath GetFullPath VerifyPath get_Length EndsWith StartsWith Dark dark original System.ComponentModel System.Xml Microsoft.Tools.WindowsInstallerXml OutputXml outputXml VSExtensionsLandingUrl NewsUrl SupportUrl System resourceMan Main AddExtension ChangeExtension UnexpectedFileExtension GetExtension WixExtension get_FileVersion get_Location get_TempFilesLocation set_TempFilesLocation ReadConfiguration set_Indentation System.Globalization PrepareConsoleForLocalization System.Reflection StringCollection SEHException UnexpectedException NullReferenceException FormatException OverflowException WixException get_Description WixDistribution AppCommon Run CultureInfo FileVersionInfo GetVersionInfo NumberFormatInfo DirectoryInfo showLogo showHelp set_QuoteChar set_IndentChar get_LastErrorNumber DisplayToolHeader IFormatProvider Unbinder get_ResourceManager BinderFileManager ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler Decompiler XmlWriter XmlTextWriter DisplayToolFooter WixVariableResolver ToLower get_WAR_FailedToDeleteTempDir get_Major get_Minor get_Error get_EncounteredError FileWriteError IllegalWarningIdAsError set_WarningAsError StringEnumerator GetEnumerator Mutator .ctor .cctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Tools.DarkStrings.resources DebuggingModes set_ShowVerboseMessages set_SuppressCustomTables suppressCustomTables set_SuppressDroppingEmptyTables suppressDroppingEmptyTables DeleteTempFiles GetCustomAttributes set_SuppressAllWarnings WixWarnings DarkStrings invalidArgs MessageEventArgs WixWarningEventArgs WixErrorEventArgs args Microsoft.Tools.WindowsInstallerXml.Tools Contains get_Chars ReplacePlaceholders WixErrors get_Comments get_INF_TempDirLocatedAt Concat TelemetryUrlFormat get_NumberFormat Object get_Product ShortProduct get_Copyright get_LegalCopyright Environment WriteEndDocument WriteStartDocument UnsupportedCommandLineArgument get_Current Convert extensionList Output MoveNext System.Text Wix wix Display tidy get_Assembly assembly get_Company CreateDirectory GetFileOrDirectory GetDirectory outputDirectory op_Equality IsNullOrEmpty   [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  	D A R K  d a r k . e x e  	. w x s  W I X _ T E M P  e x t  	- e x t 
n o l o g o  
n o t i d y  o  o u t  \  /  s c t  	s d e t  	s r a s  s u i  s w a l l  s w  w x a l l  w x  v  x  x o  ?  	h e l p  	. m s i  	. m s m  	. m s p  	. m s t  	. e x e  	. p c p  E. m s i ,   . m s m ,   . m s p ,   . m s t ,   . e x e ,   . p c p  kM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . T o o l s . D a r k S t r i n g s  H e l p M e s s a g e  )I N F _ T e m p D i r L o c a t e d A t  3W A R _ F a i l e d T o D e l e t e T e m p D i r   ����Q�@���x����         ��E)-1I   E     
)
-

1
  ���� ��  ]aeimquy}������       m �� ��  �� �� Q u u  iY ���� yi y ��  �� ��  �� �� �  �  �� � 	     � 	 U ��  ��  � � U ��  A A ���z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����QUY���� A	A      �� ��   �� ��         TWrapNonExceptionThrows       WiX Toolset Decompiler   
Decompiler          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US  @ 3System.Resources.Tools.StronglyTypedResourceBuilder*******      �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP̿n������I!H          K  H e l p M e s s a g e     (I N F _ T e m p D i r L o c a t e d A t (  2W A R _ F a i l e d T o D e l e t e T e m p D i r O  �
 usage: dark.exe [-?] [-nologo] database.msi [source.wxs] [@responseFile]

   -ext <extension>  extension assembly or "class, assembly"
   -nologo    skip printing dark logo information
   -notidy    do not delete temporary files (useful for debugging)
   -o[ut]     specify output file (default: write .wxs to current directory)
   -sct       suppress decompiling custom tables
   -sdet      suppress dropping empty tables (adds EnsureTable as appropriate)
   -sras      suppress relative action sequencing
              (use explicit sequence numbers)
   -sui       suppress decompiling UI-related tables
   -sw[N]     suppress all warnings or a specific message ID
              (example: -sw1059 -sw1067)
   -swall     suppress all warnings (deprecated)
   -v         verbose output
   -wx[N]     treat all warnings or a specific message ID as an error
              (example: -wx1059 -wx1067)
   -wxall     treat all warnings as errors (deprecated)
   -x <path>  export binaries from cabinets and embedded binaries to <path>
   -xo        output wixout instead of WiX source code
              (mandatory for transforms and patches)
   -? | -help this help information

Environment variables:
   WIX_TEMP   overrides the temporary directory used for cab extraction, binary extraction, ...%Temporary directory located at '{0}'.2Warning, failed to delete temporary directory: {0}      rQ"�&�w�.��.E(�<.��g:Y�䇳�b��ѡe`;U��w4�,���E�����1�X��Wct����V�h���AH4���Լy줪�)�`o��� k�vNPfo��Z�P�_��$#    )p�]         lU  lE  RSDS�
��ʗ@��=Z�   C:\agent\_work\66\s\build\obj\ship\x86\dark\dark.pdb                                                                                                                                                                                                                �V          �V                          �V            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      �   P  �                  8  �               	  �                     h  �               	  �  �`  \          \4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   V   F i l e D e s c r i p t i o n     W i X   T o o l s e t   D e c o m p i l e r     8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   *   I n t e r n a l N a m e   d a r k     � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   : 	  O r i g i n a l F i l e n a m e   d a r k . e x e     \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	��c  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"> 
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Dark" version="*******" processorArchitecture="x86" type="win32"/> 
 <description>WiX Toolset Decompiler</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           P     �6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      