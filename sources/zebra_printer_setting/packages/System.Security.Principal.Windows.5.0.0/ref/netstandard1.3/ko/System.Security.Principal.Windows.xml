<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Principal.Windows</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle">
      <summary>[보안 중요] Windows 스레드 또는 프로세스 액세스 토큰에 대한 SafeHandle을 제공합니다.자세한 내용은 액세스 토큰을 참조하세요.</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.#ctor(System.IntPtr)">
      <summary>[보안 중요] <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="handle">사용할 기존 핸들을 나타내는 <see cref="T:System.IntPtr" /> 개체입니다.<see cref="F:System.IntPtr.Zero" />를 사용하면 잘못된 핸들이 반환됩니다.</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.InvalidHandle">
      <summary>[보안 중요] <see cref="F:System.IntPtr.Zero" />로 <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> 개체를 인스턴스화하여 잘못된 핸들을 반환합니다.</summary>
      <returns>
        <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> 개체를 반환합니다.</returns>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.IsInvalid">
      <summary>[보안 중요] 핸들이 잘못되었는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>핸들이 잘못되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityNotMappedException">
      <summary>해당 ID를 알려진 ID로 매핑할 수 없는 보안 주체에 대한 예외를 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor">
      <summary>
        <see cref="T:System.Security.Principal.IdentityNotMappedException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:System.Security.Principal.IdentityNotMappedException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 내부 예외를 사용하여 <see cref="T:System.Security.Principal.IdentityNotMappedException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다.</param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" />가 Null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityNotMappedException.UnmappedIdentities">
      <summary>
        <see cref="T:System.Security.Principal.IdentityNotMappedException" /> 예외에 대해 매핑되지 않은 ID 컬렉션을 나타냅니다.</summary>
      <returns>매핑되지 않은 ID의 컬렉션입니다.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReference">
      <summary>ID를 나타내며 <see cref="T:System.Security.Principal.NTAccount" /> 및 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 클래스의 기본 클래스입니다.이 클래스는 공용 생성자를 제공하지 않으므로 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Equals(System.Object)">
      <summary>지정한 개체가 <see cref="T:System.Security.Principal.IdentityReference" /> 클래스의 이 인스턴스와 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="o" />가 이 <see cref="T:System.Security.Principal.IdentityReference" /> 인스턴스와 같은 내부 형식 및 값을 가지는 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">이 <see cref="T:System.Security.Principal.IdentityReference" /> 인스턴스와 비교할 개체 또는 Null 참조입니다.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.GetHashCode">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" />에 대한 해시 함수 역할을 합니다.<see cref="M:System.Security.Principal.IdentityReference.GetHashCode" />는 해시 알고리즘 및 해시 테이블과 같은 데이터 구조에 사용하기 적당합니다.</summary>
      <returns>해당 <see cref="T:System.Security.Principal.IdentityReference" /> 개체의 해시 코드를 반환합니다.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.IsValidTargetType(System.Type)">
      <summary>지정한 형식이 <see cref="T:System.Security.Principal.IdentityReference" /> 클래스에 대해 유효한 변환 형식인지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="targetType" />이 <see cref="T:System.Security.Principal.IdentityReference" /> 클래스에 대해 유효한 변환 형식이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.IdentityReference" />에서 변환할 수 있는 유효한 대상인지 쿼리할 형식입니다.유효한 대상 형식은 다음과 같습니다.<see cref="T:System.Security.Principal.NTAccount" /><see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Equality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>두 <see cref="T:System.Security.Principal.IdentityReference" /> 개체가 동일한지 비교합니다.두 개체가 <see cref="P:System.Security.Principal.IdentityReference.Value" /> 속성에 의해 반환된 것과 동일한 정식 이름 표현을 가지거나 둘 다 null인 경우 같은 것으로 간주됩니다.</summary>
      <returns>
        <paramref name="left" />와 <paramref name="right" />가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">같은지 비교할 때 사용할 왼쪽 <see cref="T:System.Security.Principal.IdentityReference" /> 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
      <param name="right">같은지 비교할 때 사용할 오른쪽 <see cref="T:System.Security.Principal.IdentityReference" /> 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Inequality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>두 <see cref="T:System.Security.Principal.IdentityReference" /> 개체가 동일하지 않은지 비교합니다.두 개체가 <see cref="P:System.Security.Principal.IdentityReference.Value" /> 속성에 의해 반환된 것과 다른 정식 이름 표현을 가지거나 개체 중 하나는 null이고 나머지는 그렇지 않을 경우 다른 것으로 간주됩니다.</summary>
      <returns>
        <paramref name="left" />와 <paramref name="right" />가 같지 않으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">다른지 비교할 때 사용할 왼쪽 <see cref="T:System.Security.Principal.IdentityReference" /> 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
      <param name="right">다른지 비교할 때 사용할 오른쪽 <see cref="T:System.Security.Principal.IdentityReference" /> 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.ToString">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 표시한 ID의 문자열 표현을 반환합니다.</summary>
      <returns>문자열 형식의 ID입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Translate(System.Type)">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 표시한 계정 이름을 다른 <see cref="T:System.Security.Principal.IdentityReference" /> 파생 형식으로 변환합니다.</summary>
      <returns>변환된 ID입니다.</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.IdentityReference" />에서 변환할 대상 형식입니다. </param>
    </member>
    <member name="P:System.Security.Principal.IdentityReference.Value">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 표시한 ID의 문자열 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 표시한 ID의 문자열 값입니다.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReferenceCollection">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> 개체의 컬렉션을 나타내고 <see cref="T:System.Security.Principal.IdentityReference" /> 파생 개체 집합을 <see cref="T:System.Security.Principal.IdentityReference" /> 파생 형식으로 변환할 수단을 제공합니다. </summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor">
      <summary>컬렉션의 0 항목을 사용하여 <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor(System.Int32)">
      <summary>지정된 초기 크기를 사용하여 <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="capacity">컬렉션의 초기 항목 수입니다.<paramref name="capacity" /> 값은 힌트를 제공하기 위한 것일 뿐이며 반드시 작성된 최대 항목의 수에 해당하는 것은 아닙니다.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Add(System.Security.Principal.IdentityReference)">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션에 <see cref="T:System.Security.Principal.IdentityReference" /> 개체를 추가합니다.</summary>
      <param name="identity">컬렉션에 추가할 <see cref="T:System.Security.Principal.IdentityReference" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Clear">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션에서 <see cref="T:System.Security.Principal.IdentityReference" /> 개체를 모두 지웁니다.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Contains(System.Security.Principal.IdentityReference)">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션에 지정된 <see cref="T:System.Security.Principal.IdentityReference" /> 개체가 들어 있는지 여부를 나타냅니다.</summary>
      <returns>컬렉션에 지정된 개체가 포함되어 있으면 true입니다.</returns>
      <param name="identity">확인할 <see cref="T:System.Security.Principal.IdentityReference" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.CopyTo(System.Security.Principal.IdentityReference[],System.Int32)">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션을 지정된 인덱스에서 시작하여 <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션을 복사할 <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 배열 개체입니다.</param>
      <param name="offset">
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션이 복사될 <paramref name="array" />의 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Count">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션의 항목 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션의 <see cref="T:System.Security.Principal.IdentityReference" /> 개체 수입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션을 반복하는 데 사용할 수 있는 열거자를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션에 대한 열거자입니다.</returns>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Item(System.Int32)">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션의 지정된 인덱스에 있는 노드를 설정하거나 가져옵니다.</summary>
      <returns>컬렉션의 지정된 인덱스에 있는 <see cref="T:System.Security.Principal.IdentityReference" />입니다.<paramref name="index" />가 컬렉션의 노드 수보다 크거나 같으면 반환 값은 null입니다.</returns>
      <param name="index">
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션의 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Remove(System.Security.Principal.IdentityReference)">
      <summary>컬렉션에서 지정된 <see cref="T:System.Security.Principal.IdentityReference" /> 개체를 제거합니다.</summary>
      <returns>지정된 개체가 컬렉션에서 제거되었으면 true입니다.</returns>
      <param name="identity">제거할 <see cref="T:System.Security.Principal.IdentityReference" /> 개체입니다.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.System#Collections#Generic#ICollection{T}#IsReadOnly"></member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션을 반복하는 데 사용할 수 있는 열거자를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션에 대한 열거자입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type)">
      <summary>컬렉션의 개체를 지정된 형식으로 변환합니다.이 메서드를 호출하면 두 번째 매개 변수를 false로 설정하여 <see cref="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)" />를 호출하는 것과 동일한 결과를 가져옵니다. 즉, 변환에 실패한 항목에 대해 예외가 throw되지 않습니다.</summary>
      <returns>원래 컬렉션의 변환된 내용을 나타내는 <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션입니다.</returns>
      <param name="targetType">컬렉션의 항목을 변환할 형식입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)">
      <summary>컬렉션의 개체를 지정된 형식으로 변환하고 지정된 내결함성을 사용하여 변환 매핑이 없는 형식과 관련된 오류를 처리하거나 무시합니다.</summary>
      <returns>원래 컬렉션의 변환된 내용을 나타내는 <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 컬렉션입니다.</returns>
      <param name="targetType">컬렉션의 항목을 변환할 형식입니다.</param>
      <param name="forceSuccess">변환 오류 처리 방식을 결정하는 부울 값입니다.<paramref name="forceSuccess" />가 true이면 이동에 대한 매핑이 발견되지 않기 때문에 변환 오류가 발생하여 변환이 실패하고 예외가 throw됩니다.<paramref name="forceSuccess" />가 false이면 이동에 대한 매핑이 발견되지 않기 때문에 변환되지 못한 형식이 반환되는 컬렉션으로 변환되지 않고 복사됩니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.NTAccount">
      <summary>사용자 또는 그룹 계정을 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String)">
      <summary>지정된 이름을 사용하여 <see cref="T:System.Security.Principal.NTAccount" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">
        <see cref="T:System.Security.Principal.NTAccount" /> 개체를 만드는 데 사용되는 이름입니다.이 매개 변수는 null 또는 빈 문자열일 수 없습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" />이 빈 문자열인 경우또는<paramref name="name" />가 너무 깁니다.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String,System.String)">
      <summary>지정된 도메인 이름 및 계정 이름을 사용하여 <see cref="T:System.Security.Principal.NTAccount" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="domainName">도메인의 이름입니다.이 매개 변수는 null 또는 빈 문자열일 수 있습니다.값이 Null인 도메인 이름은 빈 문자열처럼 취급됩니다.</param>
      <param name="accountName">계정의 이름입니다.이 매개 변수는 null 또는 빈 문자열일 수 없습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="accountName" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="accountName" />이 빈 문자열인 경우또는<paramref name="accountName" />가 너무 깁니다.또는<paramref name="domainName" />가 너무 깁니다.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Equals(System.Object)">
      <summary>이 <see cref="T:System.Security.Principal.NTAccount" /> 개체가 지정된 개체와 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="o" />가 이 <see cref="T:System.Security.Principal.NTAccount" /> 개체와 같은 내부 형식 및 값을 가지는 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">이 <see cref="T:System.Security.Principal.NTAccount" /> 개체와 비교할 개체이거나 null입니다.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.GetHashCode">
      <summary>현재 <see cref="T:System.Security.Principal.NTAccount" /> 개체의 해시 함수로 사용됩니다.<see cref="M:System.Security.Principal.NTAccount.GetHashCode" /> 메서드는 해시 테이블과 같은 해시 알고리즘 및 데이터 구조에 적합합니다.</summary>
      <returns>현재 <see cref="T:System.Security.Principal.NTAccount" /> 개체의 해시 값입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)">
      <summary>지정한 형식이 <see cref="T:System.Security.Principal.NTAccount" /> 클래스에 대해 유효한 변환 형식인지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="targetType" />이 <see cref="T:System.Security.Principal.NTAccount" /> 클래스에 대해 유효한 변환 형식이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.NTAccount" />에서 변환할 수 있는 유효한 대상인지 쿼리할 형식입니다.유효한 대상 형식은 다음과 같습니다.- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Equality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>두 <see cref="T:System.Security.Principal.NTAccount" /> 개체가 동일한지 비교합니다.두 개체가 <see cref="P:System.Security.Principal.NTAccount.Value" /> 속성에 의해 반환된 것과 동일한 정식 이름 표현을 가지거나 둘 다 null인 경우 같은 것으로 간주됩니다.</summary>
      <returns>
        <paramref name="left" />와 <paramref name="right" />가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">같음 비교에 사용할 왼쪽 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
      <param name="right">같음 비교에 사용할 오른쪽 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Inequality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>두 <see cref="T:System.Security.Principal.NTAccount" /> 개체가 동일하지 않은지 비교합니다.두 개체가 <see cref="P:System.Security.Principal.NTAccount.Value" /> 속성에 의해 반환된 것과 다른 정식 이름 표현을 가지거나 개체 중 하나는 null이고 나머지는 그렇지 않을 경우 다른 것으로 간주됩니다.</summary>
      <returns>
        <paramref name="left" />와 <paramref name="right" />가 같지 않으면 true이고, 같으면 false입니다.</returns>
      <param name="left">다음 비교에 사용할 왼쪽 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
      <param name="right">다름 비교에 사용할 오른쪽 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.ToString">
      <summary>
        <see cref="T:System.Security.Principal.NTAccount" /> 개체가 나타내는 계정의 이름을 Domain\Account 형식으로 반환합니다.</summary>
      <returns>Domain\Account 형식의 계정 이름입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Translate(System.Type)">
      <summary>
        <see cref="T:System.Security.Principal.NTAccount" /> 개체로 표시된 계정 이름을 다른 <see cref="T:System.Security.Principal.IdentityReference" /> 파생 형식으로 변환합니다.</summary>
      <returns>변환된 ID입니다.</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.NTAccount" />에서 변환할 대상 형식입니다.대상 형식은 <see cref="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)" /> 메서드에 의해 유효한 것으로 간주되는 형식이어야 합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " />이 <see cref="T:System.Security.Principal.IdentityReference" /> 형식이 아닌 경우</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">ID 참조의 일부 또는 전부를 변환할 수 없습니다.</exception>
      <exception cref="T:System.SystemException">소스 계정 이름이 너무 깁니다.또는Win32 오류 코드가 반환되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.NTAccount.Value">
      <summary>이 <see cref="T:System.Security.Principal.NTAccount" /> 개체에 대한 대문자 문자열 표시를 반환합니다.</summary>
      <returns>이 <see cref="T:System.Security.Principal.NTAccount" /> 개체에 대한 대문자 문자열 표시입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.SecurityIdentifier">
      <summary>SID(보안 식별자)를 나타내며 SID의 마샬링 및 비교 작업을 제공합니다.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Byte[],System.Int32)">
      <summary>SID(보안 식별자)의 지정된 이진 표시를 사용하여 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="binaryForm">SID를 나타내는 바이트 배열입니다.</param>
      <param name="offset">시작 인덱스로 사용할 <paramref name="binaryForm" />의 바이트 오프셋입니다. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.IntPtr)">
      <summary>SID(보안 식별자)의 이진 형식을 나타내는 정수를 사용하여 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="binaryForm">SID의 이진 형식을 나타내는 정수입니다.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Security.Principal.WellKnownSidType,System.Security.Principal.SecurityIdentifier)">
      <summary>알려진 특정 SID(보안 식별자) 형식 및 도메인 SID를 사용하여 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="sidType">열거형 값 중 하나입니다.이 값은 <see cref="F:System.Security.Principal.WellKnownSidType.LogonIdsSid" />이면 안 됩니다.</param>
      <param name="domainSid">도메인 SID입니다.이 값은 다음 <see cref="T:System.Security.Principal.WellKnownSidType" /> 값에 대해 필수이며다른 <see cref="T:System.Security.Principal.WellKnownSidType" /> 값에 대해서는 무시됩니다.- <see cref="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountGuestSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountComputersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountControllersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.String)">
      <summary>SDDL(Security Descriptor Definition Language) 형식의 지정된 SID(보안 식별자)를 사용하여 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="sddlForm">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체를 만드는 데 사용하는 SID의 SDDL 문자열입니다.</param>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.AccountDomainSid">
      <summary>SID(보안 식별자)가 Windows 계정 SID를 나타낼 경우 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID에서 계정 도메인 SID 부분을 반환합니다.SID가 Windows 계정 SID를 나타내지 않으면 이 속성은 <see cref="T:System.ArgumentNullException" />을 반환합니다.</summary>
      <returns>SID가 Windows 계정 SID를 나타낼 경우 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID에서 계정 도메인 SID 부분을 반환하고, 그렇지 않으면 <see cref="T:System.ArgumentNullException" />을 반환합니다.</returns>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.BinaryLength">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID(보안 식별자)의 길이(바이트 단위)를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID의 길이(바이트 단위)입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.CompareTo(System.Security.Principal.SecurityIdentifier)">
      <summary>현재 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체를 지정된 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체와 비교합니다.</summary>
      <returns>이 인스턴스와 <paramref name="sid" />의 상대 값을 나타내는 부호 있는 숫자입니다.반환 값 설명 0보다 작음 이 인스턴스는 <paramref name="sid" />보다 작습니다. Zero 이 인스턴스는 <paramref name="sid" />와 같습니다. 0보다 큼 이 인스턴스는 <paramref name="sid" />보다 큽니다. </returns>
      <param name="sid">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Object)">
      <summary>이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체가 지정된 개체와 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="o" />가 이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체와 같은 내부 형식 및 값을 가지는 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체와 비교할 개체이거나 null입니다.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Security.Principal.SecurityIdentifier)">
      <summary>지정된 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체가 현재 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체와 같은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="sid" />의 값이 현재 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체의 값과 같으면 true입니다.</returns>
      <param name="sid">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 클래스로 표시된 특정 SID(보안 식별자)의 이진 표현을 바이트 배열로 복사합니다.</summary>
      <param name="binaryForm">복사된 SID를 받을 바이트 배열입니다.</param>
      <param name="offset">시작 인덱스로 사용할 <paramref name="binaryForm" />의 바이트 오프셋입니다. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetHashCode">
      <summary>현재 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체의 해시 함수로 사용됩니다.<see cref="M:System.Security.Principal.SecurityIdentifier.GetHashCode" /> 메서드는 해시 테이블과 같은 해시 알고리즘 및 데이터 구조에 적합합니다.</summary>
      <returns>현재 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체의 해시 값입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsAccountSid">
      <summary>이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID(보안 식별자)가 유효한 Windows 계정 SID인지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID가 유효한 Windows 계정 SID이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsEqualDomainSid(System.Security.Principal.SecurityIdentifier)">
      <summary>이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID(보안 식별자)가 지정된 SID와 동일한 도메인에 속하는지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID가 <paramref name="sid" /> SID와 동일한 도메인에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="sid">이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체와 비교할 SID입니다.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)">
      <summary>지정한 형식이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 클래스에 대해 유효한 변환 형식인지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="targetType" />이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 클래스에 대해 유효한 변환 형식이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.SecurityIdentifier" />에서 변환할 수 있는 유효한 대상인지 쿼리할 형식입니다.유효한 대상 형식은 다음과 같습니다.- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsWellKnown(System.Security.Principal.WellKnownSidType)">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체가 알려진 특정 SID(보안 식별자) 형식과 일치하는지 여부를 나타내는 값을 반환합니다. </summary>
      <returns>
        <paramref name="type" />이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체에 대한 SID 형식이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="type">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체와 비교할 값입니다.</param>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MaxBinaryLength">
      <summary>보안 식별자의 이진 표현에 대한 최대 크기(바이트 단위)를 반환합니다.</summary>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MinBinaryLength">
      <summary>보안 식별자의 이진 표현에 대한 최소 크기(바이트 단위)를 반환합니다.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Equality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>두 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체가 동일한지 비교합니다.두 개체가 <see cref="P:System.Security.Principal.SecurityIdentifier.Value" /> 속성에 의해 반환된 것과 동일한 정식 표현을 가지거나 둘 다 null인 경우 같은 것으로 간주됩니다.</summary>
      <returns>
        <paramref name="left" />와 <paramref name="right" />가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">같음 비교에 사용할 왼쪽 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
      <param name="right">같음 비교에 사용할 오른쪽 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Inequality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>두 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체가 동일하지 않은지 비교합니다.두 개체가 <see cref="P:System.Security.Principal.SecurityIdentifier.Value" /> 속성에 의해 반환된 것과 다른 정식 이름 표현을 가지거나 개체 중 하나는 null이고 나머지는 그렇지 않을 경우 다른 것으로 간주됩니다.</summary>
      <returns>
        <paramref name="left" />와 <paramref name="right" />가 같지 않으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">다음 비교에 사용할 왼쪽 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
      <param name="right">다름 비교에 사용할 오른쪽 피연산자입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.ToString">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 계정에 대해 SDDL(Security Descriptor Definition Language) 형식의 SID(보안 식별자)를 반환합니다.SDDL 형식의 예로는 S-1-5-9를 들 수 있습니다.</summary>
      <returns>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 계정에 대한 SDDL 형식의 SID입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Translate(System.Type)">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 계정 이름을 다른 <see cref="T:System.Security.Principal.IdentityReference" /> 파생 형식으로 변환합니다.</summary>
      <returns>변환된 ID입니다.</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.SecurityIdentifier" />에서 변환할 대상 형식입니다.대상 형식은 <see cref="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)" /> 메서드에 의해 유효한 것으로 간주되는 형식이어야 합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " />이 <see cref="T:System.Security.Principal.IdentityReference" /> 형식이 아닌 경우</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">ID 참조의 일부 또는 전부를 변환할 수 없습니다.</exception>
      <exception cref="T:System.SystemException">Win32 오류 코드가 반환되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.Value">
      <summary>이 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID(보안 식별자)에 대한 대문자 SDDL(Security Descriptor Definition Language) 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 표시된 SID에 대한 대문자 SDDL 문자열입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.TokenAccessLevels">
      <summary>액세스 토큰과 관련된 사용자 계정의 권한을 정의합니다. </summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustDefault">
      <summary>사용자는 토큰의 기본 소유자, 기본 그룹 또는 DACL(임의 액세스 제어 목록)을 변경할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustGroups">
      <summary>사용자는 토큰에 포함된 그룹의 특성을 변경할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges">
      <summary>사용자는 토큰의 권한을 활성화하거나 비활성화할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustSessionId">
      <summary>사용자는 토큰의 세션 식별자를 조정할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AllAccess">
      <summary>사용자는 토큰에 대해 가능한 모든 액세스 권한을 갖습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AssignPrimary">
      <summary>사용자는 기본 토큰을 프로세스에 연결할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Duplicate">
      <summary>사용자는 토큰을 복제할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Impersonate">
      <summary>사용자는 클라이언트를 가장할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.MaximumAllowed">
      <summary>
        <see cref="T:System.Security.Principal.TokenAccessLevels" /> 열거형에 대해 지정할 수 있는 최대값입니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Query">
      <summary>사용자는 토큰을 쿼리할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.QuerySource">
      <summary>사용자는 토큰의 소스를 쿼리할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Read">
      <summary>사용자는 토큰에 대해 표준 읽기 권한 및 <see cref="F:System.Security.Principal.TokenAccessLevels.Query" /> 권한을 갖습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Write">
      <summary>사용자는 토큰에 대해 표준 쓰기 권한 및 <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges,F:System.Security.Principal.TokenAccessLevels.AdjustGroups" /> 및 <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustDefault" /> 권한을 갖습니다.</summary>
    </member>
    <member name="T:System.Security.Principal.WellKnownSidType">
      <summary>일반적으로 사용되는 SID(보안 식별자) 집합을 정의합니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid">
      <summary>계정 관리자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid">
      <summary>인증서 관리자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountComputersSid">
      <summary>계정 컴퓨터 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountControllersSid">
      <summary>계정 컨트롤러 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid">
      <summary>계정 도메인 관리자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid">
      <summary>계정 도메인 게스트 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid">
      <summary>계정 도메인 사용자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid">
      <summary>엔터프라이즈 관리자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountGuestSid">
      <summary>계정 게스트 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid">
      <summary>계정 Kerberos 대상 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid">
      <summary>정책 관리자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid">
      <summary>RAS 및 IAS 서버 계정과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid">
      <summary>스키마 관리자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AnonymousSid">
      <summary>익명 계정에 대한 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AuthenticatedUserSid">
      <summary>인증된 사용자에 대한 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BatchSid">
      <summary>일괄 처리 프로세스에 대한 SID를 나타냅니다.이 SID는 일괄 처리 작업으로 로그온할 때 토큰의 프로세스에 추가됩니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAccountOperatorsSid">
      <summary>계정 운영자 계정과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAdministratorsSid">
      <summary>관리자 계정과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAuthorizationAccessSid">
      <summary>Windows 인증 액세스 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinBackupOperatorsSid">
      <summary>백업 운영자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinDomainSid">
      <summary>도메인 계정과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinGuestsSid">
      <summary>게스트 계정과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinIncomingForestTrustBuildersSid">
      <summary>사용자가 들어오는 포리스트 트러스트를 만들 수 있도록 하는 SID를 나타냅니다.이 SID는 포리스트의 루트 도메인에서 Incoming Forest Trust Builders 기본 제공 그룹의 멤버인 사용자 토큰에 추가됩니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinNetworkConfigurationOperatorsSid">
      <summary>네트워크 운영자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceLoggingUsersSid">
      <summary>컴퓨터를 모니터링하는 원격 액세스 권한을 가진 사용자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceMonitoringUsersSid">
      <summary>해당 컴퓨터에 성능 카운터 로깅을 예약하는 원격 액세스 권한을 가진 사용자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPowerUsersSid">
      <summary>고급 사용자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPreWindows2000CompatibleAccessSid">
      <summary>Windows 2000 이전 호환 가능 계정과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPrintOperatorsSid">
      <summary>인쇄 운영자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinRemoteDesktopUsersSid">
      <summary>원격 데스크톱 사용자와 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinReplicatorSid">
      <summary>복제자 계정과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinSystemOperatorsSid">
      <summary>시스템 운영자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinUsersSid">
      <summary>기본 제공 사용자 계정과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupServerSid">
      <summary>작성자 그룹 서버 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupSid">
      <summary>개체의 작성자 그룹과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerServerSid">
      <summary>작성자 소유자 서버 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerSid">
      <summary>개체의 소유자 또는 작성자와 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DialupSid">
      <summary>전화 접속 계정에 대한 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DigestAuthenticationSid">
      <summary>Microsoft 다이제스트 인증 패키지가 클라이언트를 인증했을 때 존재하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.EnterpriseControllersSid">
      <summary>엔터프라이즈 컨트롤러의 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.InteractiveSid">
      <summary>대화형 계정에 대한 SID를 나타냅니다.이 SID는 대화형으로 로그온할 때 토큰의 프로세스에 추가됩니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalServiceSid">
      <summary>로컬 서비스와 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSid">
      <summary>로컬 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSystemSid">
      <summary>로컬 시스템과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LogonIdsSid">
      <summary>로그온 ID와 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.MaxDefined">
      <summary>
        <see cref="T:System.Security.Principal.WellKnownSidType" /> 열거형에 정의된 최대 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkServiceSid">
      <summary>네트워크 서비스와 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkSid">
      <summary>네트워크 계정에 대한 SID를 나타냅니다.이 SID는 네트워크에서 로그온할 때 토큰의 프로세스에 추가됩니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NTAuthoritySid">
      <summary>Windows NT 권한에 대한 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NtlmAuthenticationSid">
      <summary>Microsoft NTLM 인증 패키지가 클라이언트를 인증했을 때 존재하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NullSid">
      <summary>Null SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid">
      <summary>선택적 인증 옵션이 설정되어 있는 포리스트에서 사용자가 인증했을 때 존재하는 SID를 나타냅니다.이 SID가 있으면 <see cref="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid" />가 존재할 수 없습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ProxySid">
      <summary>프록시 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RemoteLogonIdSid">
      <summary>원격 로그온과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RestrictedCodeSid">
      <summary>제한된 코드에 대한 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SChannelAuthenticationSid">
      <summary>보안 채널(SSL/TLS) 인증 패키지가 클라이언트를 인증했을 때 존재하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SelfSid">
      <summary>자신에 대한 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ServiceSid">
      <summary>서비스에 대한 SID를 나타냅니다.이 SID는 서비스로 로그온할 때 토큰의 프로세스에 추가됩니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.TerminalServerSid">
      <summary>터미널 서버 계정과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid">
      <summary>선택적 인증 옵션이 설정되어 있지 않은 트러스트나 포리스트 내에서 사용자가 인증했을 때 존재하는 SID를 나타냅니다.이 SID가 있으면 <see cref="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid" />가 존재할 수 없습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WinBuiltinTerminalServerLicenseServersSid">
      <summary>터미널 서버 라이센스를 발급할 수 있는 SID가 서버에 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WorldSid">
      <summary>모든 사람과 일치하는 SID를 나타냅니다.</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsBuiltInRole">
      <summary>
        <see cref="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.String)" />에 사용될 일반 역할을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.AccountOperator">
      <summary>계정 운영자는 컴퓨터나 도메인에 있는 사용자 계정을 관리합니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Administrator">
      <summary>관리자는 컴퓨터나 도메인에 대한 무제한적인 모든 액세스 권한을 가지고 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.BackupOperator">
      <summary>백업 운영자는 파일의 백업 및 복원을 목적으로 하는 보안 제한을 재정의할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Guest">
      <summary>게스트는 사용자보다 제한을 많이 받습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PowerUser">
      <summary>고급 사용자는 일부 제한이 있지만 대부분의 관리 권한을 가지고 있으므로인증된 응용 프로그램 외에도 이전 응용 프로그램을 실행할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PrintOperator">
      <summary>인쇄 운영자는 프린터를 제어할 수 있습니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Replicator">
      <summary>복제기는 도메인 내의 파일 복제를 지원합니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.SystemOperator">
      <summary>시스템 운영자는 특정 컴퓨터를 관리합니다.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.User">
      <summary>사용자는 실수나 고의로 시스템 구성을 변경할 수 없으며인증된 응용 프로그램은 실행할 수 있지만 대부분의 이전 응용 프로그램은 실행할 수 없습니다.</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsIdentity">
      <summary>Windows 사용자를 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr)">
      <summary>지정된 Windows 계정 토큰이 나타내는 사용자에 대해 <see cref="T:System.Security.Principal.WindowsIdentity" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="userToken">코드를 실행하고 있는 사용자를 나타내는 계정 토큰입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr,System.String)">
      <summary>지정된 Windows 계정 토큰 및 지정된 인증 형식이 나타내는 사용자에 대한 <see cref="T:System.Security.Principal.WindowsIdentity" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="userToken">코드를 실행하고 있는 사용자를 나타내는 계정 토큰입니다. </param>
      <param name="type">정보 전달만을 목적으로 합니다. 사용자를 식별하는 데 사용되는 인증 형식입니다.자세한 내용은 설명 부분을 참조하세요.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.AccessToken">
      <summary>[보안 중요] 이 <see cref="T:System.Security.Principal.WindowsIdentity" /> 인스턴스의 이 <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />을 가져옵니다. </summary>
      <returns>
        <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />를 반환합니다.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose">
      <summary>
        <see cref="T:System.Security.Principal.WindowsIdentity" />에서 사용하는 모든 리소스를 해제합니다. </summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Security.Principal.WindowsIdentity" />에서 사용하는 관리되지 않는 리소스를 해제하고 관리되는 리소스를 선택적으로 해제할 수 있습니다. </summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetAnonymous">
      <summary>코드에서 익명의 사용자를 나타내는 데 센티널 값으로 사용할 수 있는 <see cref="T:System.Security.Principal.WindowsIdentity" /> 개체를 반환합니다.속성 값은 Windows 운영 체제에서 사용하는 익명의 기본 ID를 나타내지 않습니다.</summary>
      <returns>익명 사용자를 나타내는 개체입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent">
      <summary>현재 Windows 사용자를 나타내는 <see cref="T:System.Security.Principal.WindowsIdentity" /> 개체를 반환합니다.</summary>
      <returns>현재 사용자를 나타내는 개체입니다.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Boolean)">
      <summary>
        <paramref name="ifImpersonating" /> 매개 변수의 값에 따라 스레드나 프로세스에 대한 Windows ID를 나타내는 <see cref="T:System.Security.Principal.WindowsIdentity" /> 개체를 반환합니다.</summary>
      <returns>Windows 사용자를 나타내는 개체입니다.</returns>
      <param name="ifImpersonating">스레드가 현재 가장하고 있는 경우에만 <see cref="T:System.Security.Principal.WindowsIdentity" />를 반환하려면 true이고, 스레드가 가장하고 있는 경우 스레드의 <see cref="T:System.Security.Principal.WindowsIdentity" />를 반환하거나 스레드가 현재 가장하고 있지 않은 경우 프로세스의 <see cref="T:System.Security.Principal.WindowsIdentity" />를 반환하려면 false입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Security.Principal.TokenAccessLevels)">
      <summary>지정한 희망 토큰 액세스 수준을 사용하여 현재 Windows 사용자를 나타내는 <see cref="T:System.Security.Principal.WindowsIdentity" /> 개체를 반환합니다.</summary>
      <returns>현재 사용자를 나타내는 개체입니다.</returns>
      <param name="desiredAccess">열거형 값의 비트 조합입니다. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Groups">
      <summary>현재 Windows 사용자가 속해 있는 그룹을 가져옵니다.</summary>
      <returns>현재 Windows 사용자가 속해 있는 그룹을 나타내는 개체입니다.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.ImpersonationLevel">
      <summary>사용자의 가장 수준을 가져옵니다.</summary>
      <returns>가장 수준을 지정하는 열거형 값 중 하나입니다. </returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsAnonymous">
      <summary>사용자 계정이 시스템에서 익명 계정으로 식별되는지를 나타내는 값을 가져옵니다.</summary>
      <returns>사용자 계정이 익명 계정이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsGuest">
      <summary>사용자 계정이 시스템에서 <see cref="F:System.Security.Principal.WindowsAccountType.Guest" /> 계정으로 식별되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>사용자 계정이 <see cref="F:System.Security.Principal.WindowsAccountType.Guest" /> 계정이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsSystem">
      <summary>사용자 계정이 시스템에서 <see cref="F:System.Security.Principal.WindowsAccountType.System" /> 계정으로 식별되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>사용자 계정이 <see cref="F:System.Security.Principal.WindowsAccountType.System" /> 계정이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Owner">
      <summary>토큰 소유자의 SID(보안 식별자)를 가져옵니다.</summary>
      <returns>토큰 소유자에 대한 개체입니다.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)">
      <summary>가장된 Windows ID로 지정된 작업을 실행합니다.가장된 메서드 호출을 사용하여 <see cref="T:System.Security.Principal.WindowsImpersonationContext" />에서 함수를 실행하지 않고 <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" />를 사용하여 매개 변수로 직접 함수를 제공할 수 있습니다.</summary>
      <param name="safeAccessTokenHandle">가장 Windows ID의 SafeAccessTokenHandle입니다.</param>
      <param name="action">실행할 System.Action입니다. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated``1(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Func{``0})">
      <summary>가장된 Windows ID로 지정된 함수를 실행합니다.가장된 메서드 호출을 사용하여 <see cref="T:System.Security.Principal.WindowsImpersonationContext" />에서 함수를 실행하지 않고 <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" />를 사용하여 매개 변수로 직접 함수를 제공할 수 있습니다.</summary>
      <returns>함수 결과를 반환합니다.</returns>
      <param name="safeAccessTokenHandle">가장 Windows ID의 SafeAccessTokenHandle입니다.</param>
      <param name="func">실행할 System.Func입니다.</param>
      <typeparam name="T">함수에서 사용되고 반환되는 개체의 형식입니다.</typeparam>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.User">
      <summary>사용자의 SID(보안 식별자)를 가져옵니다.</summary>
      <returns>사용자에 대한 개체입니다.</returns>
    </member>
    <member name="T:System.Security.Principal.WindowsPrincipal">
      <summary>코드에서 Windows 사용자의 Windows 그룹 멤버 자격을 확인할 수 있습니다.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.#ctor(System.Security.Principal.WindowsIdentity)">
      <summary>지정된 <see cref="T:System.Security.Principal.WindowsIdentity" /> 개체를 사용하여 <see cref="T:System.Security.Principal.WindowsPrincipal" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="ntIdentity">
        <see cref="T:System.Security.Principal.WindowsPrincipal" />의 새 인스턴스를 만들 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ntIdentity" />가 null입니다. </exception>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Int32)">
      <summary>현재 보안 주체가 지정된 RID(상대 식별자)를 갖는 Windows 사용자 그룹에 속하는지 여부를 확인합니다.</summary>
      <returns>현재 보안 주체가 지정된 Windows 사용자 그룹의 멤버이면, 즉 특정 역할에 속해 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="rid">보안 주체의 멤버 자격 상태를 확인하는 데 사용되는 Windows 사용자 그룹의 RID입니다. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.SecurityIdentifier)">
      <summary>현재 보안 주체가 지정된 SID(보안 식별자)를 갖는 Windows 사용자 그룹에 속하는지 여부를 확인합니다.</summary>
      <returns>현재 보안 주체가 지정된 Windows 사용자 그룹의 멤버이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="sid">Windows 사용자 그룹을 고유하게 식별하는 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sid" />가 null입니다.</exception>
      <exception cref="T:System.Security.SecurityException">Windows에서 Win32 오류를 반환한 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.WindowsBuiltInRole)">
      <summary>현재 보안 주체가 지정된 <see cref="T:System.Security.Principal.WindowsBuiltInRole" />을 갖는 Windows 사용자 그룹에 속하는지 여부를 확인합니다.</summary>
      <returns>현재 보안 주체가 지정된 Windows 사용자 그룹의 멤버이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="role">
        <see cref="T:System.Security.Principal.WindowsBuiltInRole" /> 값 중 하나입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="role" />은(는) 올바른 <see cref="T:System.Security.Principal.WindowsBuiltInRole" /> 값이 아닙니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
  </members>
</doc>