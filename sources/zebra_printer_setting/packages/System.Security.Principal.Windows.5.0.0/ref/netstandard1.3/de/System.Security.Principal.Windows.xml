<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Principal.Windows</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle">
      <summary>[SICHERHEITSRELEVANT] Stellt ein sicheres Handle zu einem Windows-Thread oder Prozesszugriffstoken bereit.Weitere Informationen finden Sie unter Zugriffstoken.</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.#ctor(System.IntPtr)">
      <summary>[SICHERHEITSRELEVANT] Initialisiert eine neue Instanz der <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />-Klasse.</summary>
      <param name="handle">Ein <see cref="T:System.IntPtr" />-Objekt, das das zu verwendende, bereits vorhandene Handle darstellt.Bei Verwendung von <see cref="F:System.IntPtr.Zero" /> wird ein ungültiges Handle zurückgegeben.</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.InvalidHandle">
      <summary>[SICHERHEITSRELEVANT] Gibt ein ungültiges Handle zurück, indem ein <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />-Objekt mit <see cref="F:System.IntPtr.Zero" /> instanziiert wird.</summary>
      <returns>Gibt ein <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />-Objekt zurück.</returns>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.IsInvalid">
      <summary>[SICHERHEITSRELEVANT] Ruft einen Wert ab, der angibt, ob das Handle ungültig ist.</summary>
      <returns>true, wenn das Handle ungültig ist, andernfalls false.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityNotMappedException">
      <summary>Stellt eine Ausnahme für einen Principal dar, dessen Identität keiner bekannten Identität zugeordnet werden konnte.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.IdentityNotMappedException" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.IdentityNotMappedException" />-Klasse, indem die angegebene Fehlermeldung verwendet wird.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.IdentityNotMappedException" />-Klasse unter Verwendung der angegebenen Fehlermeldung und der angegebenen internen Ausnahme.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn <paramref name="inner" /> nicht NULL ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityNotMappedException.UnmappedIdentities">
      <summary>Stellt die Auflistung nicht zugeordneter Identitäten für eine <see cref="T:System.Security.Principal.IdentityNotMappedException" />-Ausnahme dar.</summary>
      <returns>Die Auflistung nicht zugeordneter Identitäten.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReference">
      <summary>Stellt eine Identität dar und ist die Basisklasse für die <see cref="T:System.Security.Principal.NTAccount" />-Klasse und die <see cref="T:System.Security.Principal.SecurityIdentifier" />-Klasse.Diese Klasse stellt keinen öffentlichen Konstruktor bereit und kann deshalb nicht geerbt werden.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Equals(System.Object)">
      <summary>Gibt einen Wert zurück, der angibt, ob das angegebene Objekt gleich dieser Instanz der <see cref="T:System.Security.Principal.IdentityReference" />-Klasse ist.</summary>
      <returns>true, wenn <paramref name="o" /> ein Objekt mit demselben zugrunde liegenden Typ und Wert wie diese <see cref="T:System.Security.Principal.IdentityReference" />-Instanz ist, andernfalls false.</returns>
      <param name="o">Ein Objekt, das mit dieser <see cref="T:System.Security.Principal.IdentityReference" />-Instanz verglichen werden soll, oder ein NULL-Verweis.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.GetHashCode">
      <summary>Fungiert als eine Hashfunktion für <see cref="T:System.Security.Principal.IdentityReference" />.<see cref="M:System.Security.Principal.IdentityReference.GetHashCode" /> ist für die Verwendung in Hashalgorithmen und Datenstrukturen, z. B. in einer Hashtabelle, geeignet.</summary>
      <returns>Der Hashcode für dieses <see cref="T:System.Security.Principal.IdentityReference" />-Objekt.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.IsValidTargetType(System.Type)">
      <summary>Gibt einen Wert zurück, der angibt, ob der angegebene Typ ein gültiger Verschiebungstyp für die <see cref="T:System.Security.Principal.IdentityReference" />-Klasse ist.</summary>
      <returns>true, wenn <paramref name="targetType" /> ein gültiger Verschiebungstyp für die <see cref="T:System.Security.Principal.IdentityReference" />-Klasse ist, andernfalls false.</returns>
      <param name="targetType">Der Typ, dessen Gültigkeit abgefragt wird, und der als eine Konvertierung von <see cref="T:System.Security.Principal.IdentityReference" /> fungieren soll.Die folgenden Zieltypen sind gültig:<see cref="T:System.Security.Principal.NTAccount" /><see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Equality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>Vergleicht zwei <see cref="T:System.Security.Principal.IdentityReference" />-Objekte auf Gleichheit.Diese werden als gleich betrachtet, wenn Sie dieselbe kanonische Namensdarstellung besitzen, wie die von der <see cref="P:System.Security.Principal.IdentityReference.Value" />-Eigenschaft zurückgegebene Darstellung, oder wenn beide null sind.</summary>
      <returns>true, wenn <paramref name="left" /> und <paramref name="right" /> gleich sind, andernfalls false.</returns>
      <param name="left">Der linke <see cref="T:System.Security.Principal.IdentityReference" />-Operand, der für die Gleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
      <param name="right">Der rechte <see cref="T:System.Security.Principal.IdentityReference" />-Operand, der für die Gleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Inequality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>Vergleicht zwei <see cref="T:System.Security.Principal.IdentityReference" />-Objekte auf Ungleichheit.Diese werden als ungleich betrachtet, wenn Sie andere kanonische Namensdarstellungen besitzen, als die von der <see cref="P:System.Security.Principal.IdentityReference.Value" />-Eigenschaft zurückgegebene Darstellung, oder wenn eines der Objekte null ist und das andere nicht.</summary>
      <returns>true, wenn <paramref name="left" /> und <paramref name="right" /> ungleich sind, andernfalls false.</returns>
      <param name="left">Der linke <see cref="T:System.Security.Principal.IdentityReference" />-Operand, der für die Ungleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
      <param name="right">Der rechte <see cref="T:System.Security.Principal.IdentityReference" />-Operand, der für die Ungleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.ToString">
      <summary>Ruft die Zeichenfolgendarstellung der durch das <see cref="T:System.Security.Principal.IdentityReference" />-Objekt dargestellten Identität ab.</summary>
      <returns>Die Identität im Zeichenfolgenformat.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Translate(System.Type)">
      <summary>Übersetzt den Kontonamen, der durch das <see cref="T:System.Security.Principal.IdentityReference" />-Objekt dargestellt wird, in einen anderen von <see cref="T:System.Security.Principal.IdentityReference" /> abgeleiteten Typ.</summary>
      <returns>Die konvertierte Identität.</returns>
      <param name="targetType">Der Zieltyp für die Konvertierung von <see cref="T:System.Security.Principal.IdentityReference" />. </param>
    </member>
    <member name="P:System.Security.Principal.IdentityReference.Value">
      <summary>Ruft den Zeichenfolgenwert der durch das <see cref="T:System.Security.Principal.IdentityReference" />-Objekt dargestellten Identität ab.</summary>
      <returns>Der Zeichenfolgenwert der durch das <see cref="T:System.Security.Principal.IdentityReference" />-Objekt dargestellten Identität.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReferenceCollection">
      <summary>Stellt eine Auflistung von <see cref="T:System.Security.Principal.IdentityReference" />-Objekten dar und stellt ein Verfahren bereit, mit dem Gruppen von aus <see cref="T:System.Security.Principal.IdentityReference" /> abgeleiteten Objekten in von <see cref="T:System.Security.Principal.IdentityReference" /> abgeleitete Typen konvertiert werden können. </summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Klasse mit 0 (null) Elementen in der Auflistung.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Klasse unter Verwendung der angegebenen Anfangsgröße.</summary>
      <param name="capacity">Die anfängliche Anzahl der Elemente in der Auflistung.Der Wert von <paramref name="capacity" /> ist nur ein Hinweis; er bezeichnet nicht unbedingt die maximale Anzahl der erstellten Elemente.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Add(System.Security.Principal.IdentityReference)">
      <summary>Fügt der <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt hinzu.</summary>
      <param name="identity">Das <see cref="T:System.Security.Principal.IdentityReference" />-Objekt, das zur Auflistung hinzugefügt werden soll.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Clear">
      <summary>Löscht alle <see cref="T:System.Security.Principal.IdentityReference" />-Objekte aus der <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Contains(System.Security.Principal.IdentityReference)">
      <summary>Gibt an, ob die <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung das angegebene <see cref="T:System.Security.Principal.IdentityReference" />-Objekt enthält.</summary>
      <returns>true, wenn die Auflistung das angegebene Objekt enthält.</returns>
      <param name="identity">Das <see cref="T:System.Security.Principal.IdentityReference" />-Objekt, für das eine Überprüfung erfolgen soll.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.CopyTo(System.Security.Principal.IdentityReference[],System.Int32)">
      <summary>Kopiert die <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung in ein <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Array, wobei am angegebenen Index begonnen wird.</summary>
      <param name="array">Ein <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Arrayobjekt, in das die <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung kopiert werden soll.</param>
      <param name="offset">Der nullbasierte Index in <paramref name="array" />, an dem die <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung kopiert werden soll.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Count">
      <summary>Ruft die Anzahl der Elemente in der <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung ab.</summary>
      <returns>Die Anzahl der <see cref="T:System.Security.Principal.IdentityReference" />-Objekte in der <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.GetEnumerator">
      <summary>Ruft einen Enumerator ab, mit dem die <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung durchlaufen werden kann.</summary>
      <returns>Ein Enumerator für die <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung.</returns>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Item(System.Int32)">
      <summary>Ruft den Knoten am angegebenen Index der <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung ab oder legt diesen fest.</summary>
      <returns>Die <see cref="T:System.Security.Principal.IdentityReference" /> am angegebenen Index in der Auflistung.Wenn <paramref name="index" /> größer als die Anzahl oder gleich der Anzahl der Knoten in der Auflistung ist, ist der Rückgabewert null.</returns>
      <param name="index">Der nullbasierte Index in der <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Remove(System.Security.Principal.IdentityReference)">
      <summary>Entfernt das angegebene <see cref="T:System.Security.Principal.IdentityReference" />-Objekt aus der Auflistung.</summary>
      <returns>true, wenn das angegebene Objekt aus der Auflistung entfernt wurde.</returns>
      <param name="identity">Das zu entfernende <see cref="T:System.Security.Principal.IdentityReference" />-Objekt.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.System#Collections#Generic#ICollection{T}#IsReadOnly"></member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Ruft einen Enumerator ab, mit dem die <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung durchlaufen werden kann.</summary>
      <returns>Ein Enumerator für die <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type)">
      <summary>Konvertiert die Objekte in der Auflistung in den angegebenen Typ.Der Aufruf dieser Methode entspricht dem Aufruf von <see cref="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)" />, wobei der zweite Parameter auf false festgelegt wird. Das bedeutet, dass für Elemente, die nicht konvertiert werden können, keine Ausnahme ausgelöst wird.</summary>
      <returns>Eine <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung, die den konvertierten Inhalt der ursprünglichen Auflistung darstellt.</returns>
      <param name="targetType">Den Typ, in den Elemente in der Auflistung konvertiert werden.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)">
      <summary>Konvertiert die Objekte in der Auflistung in den angegebenen Typ und verwendet die angegebene Fehlertoleranz, um Fehler zu bearbeiten oder zu ignorieren, die einem Typ zugeordnet sind, der keine Konvertierungszuordnung besitzt.</summary>
      <returns>Eine <see cref="T:System.Security.Principal.IdentityReferenceCollection" />-Auflistung, die den konvertierten Inhalt der ursprünglichen Auflistung darstellt.</returns>
      <param name="targetType">Den Typ, in den Elemente in der Auflistung konvertiert werden.</param>
      <param name="forceSuccess">Ein boolescher Wert, der bestimmt, wie Konvertierungsfehler behandelt werden.Wenn <paramref name="forceSuccess" /> auf true festgelegt ist, führen Konvertierungsfehler aufgrund einer fehlenden Zuordnung für die Verschiebung dazu, dass die Konvertierung nicht durchgeführt werden kann, und Ausnahmen werden ausgelöst.Wenn <paramref name="forceSuccess" /> auf false festgelegt ist, werden Typen, die aufgrund einer fehlenden Zuordnung für die Verschiebung nicht konvertiert werden konnten, ohne Konvertierung in die zurückgegebene Auflistung kopiert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.NTAccount">
      <summary>Stellt ein Benutzer- oder Gruppenkonto dar.</summary>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.NTAccount" />-Klasse unter Verwendung des angegebenen Namens.</summary>
      <param name="name">Der zum Erstellen des <see cref="T:System.Security.Principal.NTAccount" />-Objekts verwendete Name.Dieser Parameter darf nicht null oder eine leere Zeichenfolge sein.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist eine leere Zeichenfolge.– oder –<paramref name="name" /> ist zu lang.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.NTAccount" />-Klasse unter Verwendung des angegebenen Domänen- und Kontonamens. </summary>
      <param name="domainName">Der Name der Domäne.Dieser Parameter kann null oder eine leere Zeichenfolge sein.Domänennamen, die NULL-Werte sind, werden wie eine leere Zeichenfolge behandelt.</param>
      <param name="accountName">Der Kontoname.Dieser Parameter darf nicht null oder eine leere Zeichenfolge sein.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="accountName" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="accountName" /> ist eine leere Zeichenfolge.– oder –<paramref name="accountName" /> ist zu lang.– oder –<paramref name="domainName" /> ist zu lang.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Equals(System.Object)">
      <summary>Gibt einen Wert zurück, der angibt, ob dieses <see cref="T:System.Security.Principal.NTAccount" />-Objekt gleich einem angegebenen Objekt ist.</summary>
      <returns>true, wenn <paramref name="o" /> ein Objekt mit demselben zugrunde liegenden Typ und Wert wie dieses <see cref="T:System.Security.Principal.NTAccount" />-Objekt ist, andernfalls false.</returns>
      <param name="o">Ein Objekt, das mit diesem <see cref="T:System.Security.Principal.NTAccount" />-Objekt verglichen werden soll, oder null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.GetHashCode">
      <summary>Fungiert als Hashfunktion für das aktuelle <see cref="T:System.Security.Principal.NTAccount" />-Objekt.Sie können die <see cref="M:System.Security.Principal.NTAccount.GetHashCode" />-Methode in Hashalgorithmen und Datenstrukturen wie Hashtabellen verwenden.</summary>
      <returns>Ein Hashwert für das aktuelle <see cref="T:System.Security.Principal.NTAccount" />-Objekt.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)">
      <summary>Gibt einen Wert zurück, der angibt, ob der angegebene Typ ein gültiger Verschiebungstyp für die <see cref="T:System.Security.Principal.NTAccount" />-Klasse ist.</summary>
      <returns>true, wenn <paramref name="targetType" /> ein gültiger Verschiebungstyp für die <see cref="T:System.Security.Principal.NTAccount" />-Klasse ist, andernfalls false.</returns>
      <param name="targetType">Der Typ, dessen Gültigkeit abgefragt wird, und der als eine Konvertierung von <see cref="T:System.Security.Principal.NTAccount" /> fungieren soll.Die folgenden Zieltypen sind gültig:- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Equality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>Vergleicht zwei <see cref="T:System.Security.Principal.NTAccount" />-Objekte auf Gleichheit.Diese werden als gleich betrachtet, wenn Sie dieselbe kanonische Namensdarstellung besitzen, wie die von der <see cref="P:System.Security.Principal.NTAccount.Value" />-Eigenschaft zurückgegebene Darstellung, oder wenn beide null sind.</summary>
      <returns>true, wenn <paramref name="left" /> und <paramref name="right" /> gleich sind, andernfalls false.</returns>
      <param name="left">Der linke Operand, der für die Gleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
      <param name="right">Der rechte Operand, der für die Gleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Inequality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>Vergleicht zwei <see cref="T:System.Security.Principal.NTAccount" />-Objekte auf Ungleichheit.Diese werden als ungleich betrachtet, wenn Sie andere kanonische Namensdarstellungen besitzen, als die von der <see cref="P:System.Security.Principal.NTAccount.Value" />-Eigenschaft zurückgegebenen Darstellungen, oder wenn eines der Objekte null ist und das andere nicht.</summary>
      <returns>true, wenn <paramref name="left" /> und <paramref name="right" /> ungleich sind, andernfalls false.</returns>
      <param name="left">Der linke Operand, der für die Ungleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
      <param name="right">Der rechte Operand, der für die Ungleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.ToString">
      <summary>Gibt den Kontonamen im Format Domäne\Konto für das durch das <see cref="T:System.Security.Principal.NTAccount" />-Objekt dargestellte Konto zurück.</summary>
      <returns>Der Kontoname im Format Domäne\Konto.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Translate(System.Type)">
      <summary>Übersetzt den Kontonamen, der durch das <see cref="T:System.Security.Principal.NTAccount" />-Objekt dargestellt wird, in einen anderen von <see cref="T:System.Security.Principal.IdentityReference" /> abgeleiteten Typ.</summary>
      <returns>Die konvertierte Identität.</returns>
      <param name="targetType">Der Zieltyp für die Konvertierung von <see cref="T:System.Security.Principal.NTAccount" />.Der Zieltyp muss ein Typ sein, der von der <see cref="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)" />-Methode als gültig betrachtet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " />ist kein <see cref="T:System.Security.Principal.IdentityReference" />-Typ.</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">Manche oder alle Identitätsverweise konnten nicht übersetzt werden.</exception>
      <exception cref="T:System.SystemException">Der Quellkontoname ist zu lang.– oder –Ein Win32-Fehlercode wurde zurückgegeben.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.NTAccount.Value">
      <summary>Gibt eine Zeichenfolgendarstellung dieses <see cref="T:System.Security.Principal.NTAccount" />-Objekts in Großbuchstaben zurück.</summary>
      <returns>Die Zeichenfolgendarstellung dieses <see cref="T:System.Security.Principal.NTAccount" />-Objekts in Großbuchstaben.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.SecurityIdentifier">
      <summary>Stellt eine Sicherheits-ID (SID) dar und bietet Marshalling und Vergleichsoperationen für SIDs.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Byte[],System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.SecurityIdentifier" />-Klasse unter Verwendung einer angegebenen binären Darstellung einer Sicherheits-ID.</summary>
      <param name="binaryForm">Das Bytearray, das die SID darstellt.</param>
      <param name="offset">Das Byteoffset, das als Startindex in <paramref name="binaryForm" /> verwendet werden soll. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.IntPtr)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.SecurityIdentifier" />-Klasse mit einer ganzen Zahl, die die Binärform einer Sicherheits-ID (SID) darstellt.</summary>
      <param name="binaryForm">Eine ganze Zahl, die die Binärform einer SID darstellt.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Security.Principal.WellKnownSidType,System.Security.Principal.SecurityIdentifier)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.SecurityIdentifier" />-Klasse unter Verwendung des angegebenen bekannten Typs der Sicherheits-ID und der Domänen-SID.</summary>
      <param name="sidType">Einer der Enumerationswerte.Dieser Wert darf nicht <see cref="F:System.Security.Principal.WellKnownSidType.LogonIdsSid" /> sein.</param>
      <param name="domainSid">Die Domänen-SID.Dieser Wert ist für die folgenden <see cref="T:System.Security.Principal.WellKnownSidType" />-Werte erforderlich.Dieser Parameter wird für alle anderen <see cref="T:System.Security.Principal.WellKnownSidType" />-Werte ignoriert.- <see cref="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountGuestSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountComputersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountControllersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.SecurityIdentifier" />-Klasse unter Verwendung der angegebenen Sicherheits-ID im SDDL-Format (Security Descriptor Definition Language).</summary>
      <param name="sddlForm">SDDL-Zeichenfolge für die SID, die zum Erstellen des <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekts verwendet wird.</param>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.AccountDomainSid">
      <summary>Gibt den Teil der Kontodomänen-SID von der Sicherheits-ID (SID) zurück, die durch das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellt wird, wenn die SID die SID eines Windows-Kontos darstellt.Wenn die SID nicht die SID eines Windows-Kontos darstellt, gibt diese Eigenschaft <see cref="T:System.ArgumentNullException" /> zurück.</summary>
      <returns>Der Teil der Kontodomänen-SID der SID, die durch das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellt wird, wenn die SID die SID eines Windows-Kontos darstellt. Andernfalls wird <see cref="T:System.ArgumentNullException" /> zurückgegeben.</returns>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.BinaryLength">
      <summary>Gibt die Länge in Bytes der durch das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellten Sicherheits-ID zurück.</summary>
      <returns>Die Länge in Bytes der durch das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellten SID.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.CompareTo(System.Security.Principal.SecurityIdentifier)">
      <summary>Vergleicht das aktuelle <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt mit dem angegebenen <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt.</summary>
      <returns>Eine Zahl mit Vorzeichen, die das Verhältnis zwischen dem Wert dieser Instanz und <paramref name="sid" /> angibt.Rückgabewert BeschreibungKleiner als 0 Diese Instanz ist kleiner als <paramref name="sid" />. Zero Diese Instanz ist gleich <paramref name="sid" />. Größer als 0 (null) Diese Instanz ist größer als <paramref name="sid" />. </returns>
      <param name="sid">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Object)">
      <summary>Gibt einen Wert zurück, der angibt, ob dieses <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt gleich einem angegebenen Objekt ist.</summary>
      <returns>true, wenn <paramref name="o" /> ein Objekt mit demselben zugrunde liegenden Typ und Wert wie dieses <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt ist, andernfalls false.</returns>
      <param name="o">Ein Objekt, das mit diesem <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt verglichen werden soll, oder null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Security.Principal.SecurityIdentifier)">
      <summary>Gibt an, ob das angegebene <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt mit dem aktuellen <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt identisch ist.</summary>
      <returns>true, wenn der Wert von <paramref name="sid" /> dem Wert des aktuellen <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekts entspricht.</returns>
      <param name="sid">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Kopiert die binäre Darstellung der angegebenen Sicherheits-ID, die durch die <see cref="T:System.Security.Principal.SecurityIdentifier" />-Klasse dargestellt wird, in ein Bytearray.</summary>
      <param name="binaryForm">Das Bytearray, in das die SID kopiert wird.</param>
      <param name="offset">Das Byteoffset, das als Startindex in <paramref name="binaryForm" /> verwendet werden soll. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetHashCode">
      <summary>Fungiert als Hashfunktion für das aktuelle <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt.Sie können die <see cref="M:System.Security.Principal.SecurityIdentifier.GetHashCode" />-Methode in Hashalgorithmen und Datenstrukturen wie Hashtabellen verwenden.</summary>
      <returns>Ein Hashwert für das aktuelle <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsAccountSid">
      <summary>Gibt einen Wert zurück, der angibt, ob die durch dieses <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellte SID die gültige Sicherheits-ID eines Windows-Kontos ist.</summary>
      <returns>true, wenn die durch dieses <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellte SID die gültige SID eines Windows-Kontos ist, andernfalls false.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsEqualDomainSid(System.Security.Principal.SecurityIdentifier)">
      <summary>Gibt einen Wert zurück, der angibt, ob die durch dieses <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellte SID aus derselben Domäne stammt wie die angegebene Sicherheits-ID.</summary>
      <returns>true, wenn sich die durch dieses <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellte SID in derselben Domäne befindet wie die <paramref name="sid" />-SID, andernfalls false.</returns>
      <param name="sid">Die SID, die mit diesem <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)">
      <summary>Gibt einen Wert zurück, der angibt, ob der angegebene Typ ein gültiger Verschiebungstyp für die <see cref="T:System.Security.Principal.SecurityIdentifier" />-Klasse ist.</summary>
      <returns>true, wenn <paramref name="targetType" /> ein gültiger Verschiebungstyp für die <see cref="T:System.Security.Principal.SecurityIdentifier" />-Klasse ist, andernfalls false.</returns>
      <param name="targetType">Der Typ, dessen Gültigkeit abgefragt wird, und der als eine Konvertierung von <see cref="T:System.Security.Principal.SecurityIdentifier" /> fungieren soll.Die folgenden Zieltypen sind gültig:- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsWellKnown(System.Security.Principal.WellKnownSidType)">
      <summary>Gibt einen Wert zurück, der angibt, ob das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dem angegebenen bekannten Typ der Sicherheits-ID entspricht. </summary>
      <returns>true, wenn <paramref name="type" /> der SID-Typ für das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt ist, andernfalls false.</returns>
      <param name="type">Ein Wert, der mit dem <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt verglichen werden soll.</param>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MaxBinaryLength">
      <summary>Gibt die maximale Größe in Bytes für die binäre Darstellung der Sicherheits-ID zurück.</summary>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MinBinaryLength">
      <summary>Gibt die Mindestgröße in Bytes für die binäre Darstellung der Sicherheits-ID zurück.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Equality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>Vergleicht zwei <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekte auf Gleichheit.Diese werden als gleich betrachtet, wenn Sie dieselbe kanonische Darstellung besitzen, wie die von der <see cref="P:System.Security.Principal.SecurityIdentifier.Value" />-Eigenschaft zurückgegebene Darstellung, oder wenn beide null sind.</summary>
      <returns>true, wenn <paramref name="left" /> und <paramref name="right" /> gleich sind, andernfalls false.</returns>
      <param name="left">Der linke Operand, der für die Gleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
      <param name="right">Der rechte Operand, der für die Gleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Inequality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>Vergleicht zwei <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekte auf Ungleichheit.Diese werden als ungleich betrachtet, wenn Sie andere kanonische Namensdarstellungen besitzen, als die von der <see cref="P:System.Security.Principal.SecurityIdentifier.Value" />-Eigenschaft zurückgegebene Darstellung, oder wenn eines der Objekte null ist und das andere nicht.</summary>
      <returns>true, wenn <paramref name="left" /> und <paramref name="right" /> ungleich sind, andernfalls false.</returns>
      <param name="left">Der linke Operand, der für die Ungleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
      <param name="right">Der rechte Operand, der für die Ungleichheitsprüfung verwendet werden soll.Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.ToString">
      <summary>Gibt die Sicherheits-ID (SID) im SDDL-Format (Security Descriptor Definition Language) für das durch das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellte Konto zurück.Ein Beispiel für das SDDL-Formats ist S-1-5-9.</summary>
      <returns>Die SID im SDDL-Format für das durch das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellte Konto.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Translate(System.Type)">
      <summary>Übersetzt den Kontonamen, der durch das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellt wird, in einen anderen von <see cref="T:System.Security.Principal.IdentityReference" /> abgeleiteten Typ.</summary>
      <returns>Die konvertierte Identität.</returns>
      <param name="targetType">Der Zieltyp für die Konvertierung von <see cref="T:System.Security.Principal.SecurityIdentifier" />.Der Zieltyp muss ein Typ sein, der von der <see cref="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)" />-Methode als gültig betrachtet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " />ist kein <see cref="T:System.Security.Principal.IdentityReference" />-Typ.</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">Manche oder alle Identitätsverweise konnten nicht übersetzt werden.</exception>
      <exception cref="T:System.SystemException">Ein Win32-Fehlercode wurde zurückgegeben.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.Value">
      <summary>Gibt eine SDDL-Zeichenfolge (Security Descriptor Definition Language) in Großbuchstaben für die durch das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt darstellte Sicherheits-ID zurück.</summary>
      <returns>Eine SDDL-Zeichenfolge in Großbuchstaben für die durch das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt dargestellte SID.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.TokenAccessLevels">
      <summary>Definiert die Berechtigungen des Benutzerkontos, das dem Zugriffstoken zugeordnet ist. </summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustDefault">
      <summary>Der Benutzer kann den Standardbesitzer, die primäre Gruppe oder die DACL (Discretionary Access List, freigegebene Zugriffssteuerungsliste) des Tokens ändern.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustGroups">
      <summary>Der Benutzer kann die Attribute der Gruppen im Token ändern.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges">
      <summary>Der Benutzer kann Berechtigungen im Token aktivieren oder deaktivieren.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustSessionId">
      <summary>Der Benutzer kann die Sitzungs-ID des Tokens anpassen.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AllAccess">
      <summary>Der Benutzer verfügt über den insgesamt möglichen Zugriff auf das Token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AssignPrimary">
      <summary>Der Benutzer kann ein primäres Token an einen Prozess anfügen.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Duplicate">
      <summary>Der Benutzer kann das Token duplizieren.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Impersonate">
      <summary>Der Benutzer kann für einen Client einen Identitätswechsel ausführen.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.MaximumAllowed">
      <summary>Der maximale Wert, der für die <see cref="T:System.Security.Principal.TokenAccessLevels" />-Enumeration zugewiesen werden kann.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Query">
      <summary>Der Benutzer kann das Token abfragen.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.QuerySource">
      <summary>Der Benutzer kann die Quelle des Tokens abfragen.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Read">
      <summary>Der Benutzer verfügt über Standardleseberechtigungen und die <see cref="F:System.Security.Principal.TokenAccessLevels.Query" />-Berechtigung für das Token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Write">
      <summary>Der Benutzer verfügt über Standardschreibberechtigungen und die <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges,F:System.Security.Principal.TokenAccessLevels.AdjustGroups" />-Berechtigung sowie die <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustDefault" />-Berechtigung für das Token.</summary>
    </member>
    <member name="T:System.Security.Principal.WellKnownSidType">
      <summary>Definiert eine Gruppe häufig verwendeter Sicherheits-IDs (SIDs).</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid">
      <summary>Gibt eine SID an, die der Gruppe der Kontoadministratoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid">
      <summary>Gibt eine SID an, die der Gruppe der Zertifikatsadministratoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountComputersSid">
      <summary>Gibt eine SID an, die der Gruppe der Computerkonten entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountControllersSid">
      <summary>Gibt eine SID an, die der Gruppe der Controllerkonten entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid">
      <summary>Gibt eine SID an, die der Gruppe der Domänenadministratorenkonten entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid">
      <summary>Gibt eine SID an, die der Gruppe der Domänengastkonten entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid">
      <summary>Gibt eine SID an, die der Gruppe der Domänenbenutzerkonten entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid">
      <summary>Gibt eine SID an, die der Gruppe der Organisationsadministratoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountGuestSid">
      <summary>Gibt eine SID an, die der Gruppe der Gastkonten entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid">
      <summary>Gibt eine SID an, die der Gruppe der Kerberos-Zielkonten entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid">
      <summary>Gibt eine SID an, die der Gruppe der Richtlinienadministratoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid">
      <summary>Gibt eine SID an, die dem RAS- und IAS-Serverkonto entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid">
      <summary>Gibt eine SID an, die der Gruppe der Schemaadministratoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AnonymousSid">
      <summary>Gibt eine SID für das anonyme Konto an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AuthenticatedUserSid">
      <summary>Gibt eine SID für einen authentifizierten Benutzer an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BatchSid">
      <summary>Gibt eine SID für einen Batchprozess an.Diese SID wird dem Prozess eines Tokens beim Anmelden als Batchauftrag hinzugefügt.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAccountOperatorsSid">
      <summary>Gibt eine SID an, die dem Konto der Konten-Operatoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAdministratorsSid">
      <summary>Gibt eine SID an, die dem Administratorkonto entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAuthorizationAccessSid">
      <summary>Gibt eine SID an, die der Windows-Autorisierungszugriffsgruppe entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinBackupOperatorsSid">
      <summary>Gibt eine SID an, die der Gruppe der Sicherungs-Operatoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinDomainSid">
      <summary>Gibt eine SID an, die dem Domänenkonto entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinGuestsSid">
      <summary>Gibt eine SID an, die dem Gastkonto entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinIncomingForestTrustBuildersSid">
      <summary>Gibt eine SID an, die es Benutzern ermöglicht, eingehende Gesamtstrukturvertrauensstellungen zu erstellen.Sie wird dem Token von Benutzern hinzugefügt, die Mitglieder der integrierten Gruppe Erstellungen eingehender Gesamtstrukturvertrauensstellung in der Stammdomäne der Gesamtstruktur sind.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinNetworkConfigurationOperatorsSid">
      <summary>Gibt eine SID an, die der Gruppe der Netzwerk-Operatoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceLoggingUsersSid">
      <summary>Gibt eine SID für die Gruppe von Benutzern an, die Remotezugriff zur Überwachung des Computers haben.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceMonitoringUsersSid">
      <summary>Gibt eine SID für die Gruppe von Benutzern an, die Remotezugriff zur Planung der Protokollierung für Leistungsindikatoren auf diesem Computer haben.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPowerUsersSid">
      <summary>Gibt eine SID an, die der Gruppe der Hauptbenutzer entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPreWindows2000CompatibleAccessSid">
      <summary>Gibt eine SID an, die den Windows 2000-kompatiblen Konten entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPrintOperatorsSid">
      <summary>Gibt eine SID an, die der Gruppe der Druck-Operatoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinRemoteDesktopUsersSid">
      <summary>Gibt eine SID an, die den Remotedesktopbenutzern entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinReplicatorSid">
      <summary>Gibt eine SID an, die dem Replikationsdienstkonto entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinSystemOperatorsSid">
      <summary>Gibt eine SID an, die der Gruppe der System-Operatoren entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinUsersSid">
      <summary>Gibt eine SID an, die den integrierten Benutzerkonten entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupServerSid">
      <summary>Gibt eine Erstellergruppenserver-SID an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupSid">
      <summary>Gibt eine SID an, die der Erstellergruppe eines Objekts entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerServerSid">
      <summary>Gibt eine Erstellerbesitzerserver-SID an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerSid">
      <summary>Gibt eine SID an, die dem Besitzer oder dem Ersteller eines Objekts entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DialupSid">
      <summary>Gibt eine SID für ein DFÜ-Konto an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DigestAuthenticationSid">
      <summary>Gibt eine SID an, die beim Authentifizieren des Clients durch das Microsoft Digest-Authentifizierungspaket vorhanden war.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.EnterpriseControllersSid">
      <summary>Gibt eine SID für einen Organisationscontroller an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.InteractiveSid">
      <summary>Gibt eine SID für ein interaktives Konto an.Diese SID wird dem Prozess eines Tokens bei der interaktiven Anmeldung hinzugefügt.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalServiceSid">
      <summary>Gibt eine SID an, die einem lokalen Dienst entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSid">
      <summary>Gibt eine lokale SID an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSystemSid">
      <summary>Gibt eine SID an, die dem lokalen System entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LogonIdsSid">
      <summary>Gibt eine SID an, die den Anmelde-IDs entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.MaxDefined">
      <summary>Gibt die maximal definierte SID in der <see cref="T:System.Security.Principal.WellKnownSidType" />-Enumeration an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkServiceSid">
      <summary>Gibt eine SID an, die einem Netzwerkdienst entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkSid">
      <summary>Gibt eine SID für ein Netzwerkkonto an.Diese SID wird dem Prozess eines Tokens bei der Anmeldung über ein Netzwerk hinzugefügt.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NTAuthoritySid">
      <summary>Gibt eine SID für die Windows NT-Zertifizierungsstelle an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NtlmAuthenticationSid">
      <summary>Gibt eine SID an, die beim Authentifizieren des Clients durch das Microsoft NTLM-Authentifizierungspaket vorhanden war.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NullSid">
      <summary>Gibt eine NULL-SID an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid">
      <summary>Gibt eine SID an, die beim Authentifizieren des Benutzer über eine Gesamtstruktur mit aktivierter selektiver Authentifizierungsoption vorhanden war.Wenn diese SID vorhanden ist, kann <see cref="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid" /> nicht vorhanden sein.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ProxySid">
      <summary>Gibt eine Proxy-SID an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RemoteLogonIdSid">
      <summary>Gibt eine SID an, die Remoteanmeldungen entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RestrictedCodeSid">
      <summary>Gibt eine SID für beschränkten Code an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SChannelAuthenticationSid">
      <summary>Gibt eine SID an, die beim Authentifizieren des Clients durch das Secure Channel-Authentifizierungspaket (SSL/TLS) vorhanden war.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SelfSid">
      <summary>Gibt eine SID für sich selbst an.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ServiceSid">
      <summary>Gibt eine SID für einen Dienst an.Diese SID wird dem Prozess eines Tokens bei der Anmeldung als Dienst hinzugefügt.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.TerminalServerSid">
      <summary>Gibt eine SID an, die einem Terminalserverkonto entspricht.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid">
      <summary>Gibt eine SID an, die bei der Authentifizierung des Benutzers aus der Gesamtstruktur oder über eine Vertrauensstellung vorhanden war, für die die selektive Authentifizierungsoption nicht aktiviert ist.Wenn diese SID vorhanden ist, kann <see cref="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid" /> nicht vorhanden sein.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WinBuiltinTerminalServerLicenseServersSid">
      <summary>Gibt an, dass eine SID in einem Server vorhanden ist, der Terminal Server-Lizenzen ausgeben kann.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WorldSid">
      <summary>Gibt eine SID an, die allen entspricht.</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsBuiltInRole">
      <summary>Gibt allgemeine Rollen an, die mit <see cref="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.String)" /> verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.AccountOperator">
      <summary>Kontooperatoren verwalten die Benutzerkonten auf einem Computer oder in einer Domäne.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Administrator">
      <summary>Administratoren haben vollständigen und uneingeschränkten Zugriff auf den Computer oder die Domäne.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.BackupOperator">
      <summary>Backupoperatoren können Sicherheitsbeschränkungen lediglich für das Sichern oder Wiederherstellen von Dateien überschreiben.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Guest">
      <summary>Für Gäste gelten umfassendere Einschränkungen als für Benutzer.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PowerUser">
      <summary>Hauptbenutzer verfügen über die umfassendsten Administratorberechtigungen mit einigen Einschränkungen.Daher können Hauptbenutzer neben zertifizierten Anwendungen auch ältere Anwendungen ausführen.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PrintOperator">
      <summary>Druckoperatoren können die Druckersteuerung übernehmen.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Replicator">
      <summary>Replikationsdienste unterstützen die Replikation von Dateien in einer Domäne.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.SystemOperator">
      <summary>Systemoperatoren verwalten einen bestimmten Computer.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.User">
      <summary>Versehentliche oder beabsichtigte systemweite Änderungen durch Benutzer sind nicht möglich.Somit können Benutzer zertifizierte Anwendungen ausführen, jedoch nur wenige ältere Anwendungen.</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsIdentity">
      <summary>Stellt einen Windows-Benutzer dar.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.WindowsIdentity" />-Klasse für den Benutzer, der durch das angegebene Windows-Kontotoken dargestellt wird.</summary>
      <param name="userToken">Das Kontotoken für den Benutzer, für den der Code ausgeführt wird. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.WindowsIdentity" />-Klasse für den Benutzer, der durch das angegebene Windows-Kontotoken und den angegebenen Authentifizierungstyp dargestellt wird.</summary>
      <param name="userToken">Das Kontotoken für den Benutzer, für den der Code ausgeführt wird. </param>
      <param name="type">(Nur für Informationszwecke.) Der zur Identifizierung des Benutzers verwendete Authentifizierungstyp.Weitere Informationen finden Sie in den Hinweisen.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.AccessToken">
      <summary>[SICHERHEITSRELEVANT] Ruft dieses <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> für diese <see cref="T:System.Security.Principal.WindowsIdentity" />-Instanz ab. </summary>
      <returns>Gibt einen Wert vom Typ <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> zurück.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose">
      <summary>Gibt alle vom <see cref="T:System.Security.Principal.WindowsIdentity" /> verwendeten Ressourcen frei. </summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.Security.Principal.WindowsIdentity" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei. </summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetAnonymous">
      <summary>Gibt ein <see cref="T:System.Security.Principal.WindowsIdentity" />-Objekt zurück, das Sie als Sentinelwert im Code verwenden können, um einen anonymen Benutzer darzustellen.Der Eigenschaftswert stellt nicht die integrierte anonyme Identität dar, die vom Windows-Betriebssystem verwendet wird.</summary>
      <returns>Ein Objekt, das einen anonymen Benutzer darstellt.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent">
      <summary>Gibt ein <see cref="T:System.Security.Principal.WindowsIdentity" />-Objekt zurück, das den aktuellen Windows-Benutzer darstellt.</summary>
      <returns>Ein Objekt, das den aktuellen Benutzer darstellt.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Boolean)">
      <summary>Gibt ein <see cref="T:System.Security.Principal.WindowsIdentity" />-Objekt zurück, das eine Windows-Identität für den Thread oder den Prozess darstellt, je nach Wert des <paramref name="ifImpersonating" />-Parameters.</summary>
      <returns>Ein Objekt, das einen Windows-Benutzer darstellt.</returns>
      <param name="ifImpersonating">true, um nur die <see cref="T:System.Security.Principal.WindowsIdentity" /> zurückzugeben, wenn der Thread gerade einen Identitätswechsel ausführt. false, um die <see cref="T:System.Security.Principal.WindowsIdentity" /> des Threads zurückzugeben, wenn er einen Identitätswechsel ausführt, oder die <see cref="T:System.Security.Principal.WindowsIdentity" /> des Prozesses, wenn der Thread gerade keinen Identitätswechsel ausführt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Security.Principal.TokenAccessLevels)">
      <summary>Gibt ein <see cref="T:System.Security.Principal.WindowsIdentity" />-Objekt zurück, das den aktuellen Windows-Benutzer darstellt, der die angegebene gewünschte Tokenzugriffsebene verwendet.</summary>
      <returns>Ein Objekt, das den aktuellen Benutzer darstellt.</returns>
      <param name="desiredAccess">Eine bitweise Kombination der Enumerationswerte. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Groups">
      <summary>Ruft die Gruppen ab, zu denen der aktuelle Windows-Benutzer gehört.</summary>
      <returns>Ein Objekt, das die Gruppen darstellt, zu denen der Windows-Benutzer gehört.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.ImpersonationLevel">
      <summary>Ruft die Identitätswechselebene für den Benutzer ab.</summary>
      <returns>Einer der Enumerationswerte, der die Personifikationsebene angibt. </returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsAnonymous">
      <summary>Ruft einen Wert ab, der angibt, ob das Benutzerkonto vom System als anonymes Konto identifiziert wird.</summary>
      <returns>true, wenn das Benutzerkonto ein anonymes Konto ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsGuest">
      <summary>Ruft einen Wert ab, der angibt, ob das Benutzerkonto vom System als <see cref="F:System.Security.Principal.WindowsAccountType.Guest" />-Konto identifiziert wird.</summary>
      <returns>true, wenn das Benutzerkonto ein <see cref="F:System.Security.Principal.WindowsAccountType.Guest" />-Konto ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsSystem">
      <summary>Ruft einen Wert ab, der angibt, ob das Benutzerkonto vom System als <see cref="F:System.Security.Principal.WindowsAccountType.System" />-Konto identifiziert wird.</summary>
      <returns>true, wenn das Benutzerkonto ein <see cref="F:System.Security.Principal.WindowsAccountType.System" />-Konto ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Owner">
      <summary>Ruft die Sicherheits-ID für den Tokenbesitzer ab.</summary>
      <returns>Ein Objekt für den Tokenbesitzer.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)">
      <summary>Führt die angegebene Aktion als imitierte Windows-Identität aus.Statt einen imitierten Methodenaufruf zu verwenden und die Funktion in <see cref="T:System.Security.Principal.WindowsImpersonationContext" /> auszuführen, können Sie <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> verwenden und die Funktion direkt als Parameter angeben.</summary>
      <param name="safeAccessTokenHandle">Das SafeAccessTokenHandle der imitierten Windows-Identität.</param>
      <param name="action">Die auszuführende System.Action. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated``1(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Func{``0})">
      <summary>Führt die angegebene Funktion als imitierte Windows-Identität aus.Statt einen imitierten Methodenaufruf zu verwenden und die Funktion in <see cref="T:System.Security.Principal.WindowsImpersonationContext" /> auszuführen, können Sie <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> verwenden und die Funktion direkt als Parameter angeben.</summary>
      <returns>Gibt das Ergebnis der Funktion zurück.</returns>
      <param name="safeAccessTokenHandle">Das SafeAccessTokenHandle der imitierten Windows-Identität.</param>
      <param name="func">Die auszuführende System.Func.</param>
      <typeparam name="T">Der Typ des Objekts, das von der Funktion verwendet und zurückgegeben wird.</typeparam>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.User">
      <summary>Ruft die Sicherheits-ID für den Benutzer ab.</summary>
      <returns>Ein Objekt für den Benutzer.</returns>
    </member>
    <member name="T:System.Security.Principal.WindowsPrincipal">
      <summary>Ermöglicht es Code, die Windows-Gruppenmitgliedschaft eines Windows-Benutzers zu überprüfen.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.#ctor(System.Security.Principal.WindowsIdentity)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Principal.WindowsPrincipal" />-Klasse unter Verwendung des angegebenen <see cref="T:System.Security.Principal.WindowsIdentity" />-Objekts.</summary>
      <param name="ntIdentity">Das Objekt, aus dem die neue Instanz von <see cref="T:System.Security.Principal.WindowsPrincipal" /> erstellt werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ntIdentity" /> ist null. </exception>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Int32)">
      <summary>Bestimmt, ob der aktuelle Prinzipal zur Windows-Benutzergruppe mit der angegebenen relativen ID (RID) gehört.</summary>
      <returns>true, wenn der aktuelle Prinzipal Mitglied der angegebenen Windows-Benutzergruppe ist, d. h. in einer bestimmten Rolle, andernfalls false.</returns>
      <param name="rid">Die RID der Windows-Benutzergruppe, in der der Status der Mitgliedschaft des Prinzipals überprüft werden soll. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.SecurityIdentifier)">
      <summary>Bestimmt, ob der aktuelle Prinzipal zur Windows-Benutzergruppe mit der angegebenen Sicherheits-ID (SID) gehört.</summary>
      <returns>true, wenn der aktuelle Prinzipal Mitglied der angegebenen Windows-Benutzergruppe ist, andernfalls false.</returns>
      <param name="sid">Ein <see cref="T:System.Security.Principal.SecurityIdentifier" />, der eine Windows-Benutzergruppe eindeutig identifiziert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sid" /> ist null.</exception>
      <exception cref="T:System.Security.SecurityException">Windows hat einen Win32-Fehler zurückgegeben.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.WindowsBuiltInRole)">
      <summary>Bestimmt, ob der aktuelle Prinzipal zu der Windows-Benutzergruppe mit der angegebenen <see cref="T:System.Security.Principal.WindowsBuiltInRole" /> gehört.</summary>
      <returns>true, wenn der aktuelle Prinzipal Mitglied der angegebenen Windows-Benutzergruppe ist, andernfalls false.</returns>
      <param name="role">Einer der <see cref="T:System.Security.Principal.WindowsBuiltInRole" />-Werte. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="role" /> ist kein gültiger <see cref="T:System.Security.Principal.WindowsBuiltInRole" />-Wert.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
  </members>
</doc>