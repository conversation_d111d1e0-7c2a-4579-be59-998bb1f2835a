IronSoftware.Drawing is an free and open-source library originally developed by Iron Software that replaces System.Drawing.Common in .NET projects.

Cross Platform Compatibility
========================
* C#, F#, and VB.NET
* .NET 8, .NET 7, .NET 6, .NET 5, .NET Core, Standard, and Framework
* Console, Web, and Desktop Apps
* Windows, macOs, Linux, Docker, Azure, and AWS
* Microsoft Visual Studio or Jetbrains ReSharper & Rider

Documentation and Examples
========================
Visit our GitHub repo for code examples using all Bitmaps, Colors, Rectangles, and Fonts. See: https://github.com/iron-software/IronSoftware.System.Drawing
