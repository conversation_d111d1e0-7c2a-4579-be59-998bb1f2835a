MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L +'��        � " 0  t         ʓ       �    @                       �          `�                           u�  O    �  �                   �     ��  8                                                             H           .text   �s       t                    `.rsrc   �   �      v              @  @.reloc      �      z              @  B                ��      H     �/  �&      XV  h<                                          &(  
  *  0 9      ~  �
," r  p�  (  
o  
s  
�   ~  + *   0       ~  
+ *" �  *0       (  rC  p~  o  

+ *  0       (  rW  p~  o  

+ *  0       (  rc  p~  o  

+ *  0       (  r�  p~  o  

+ *  0       (  r�  p~  o  

+ *  0       (  r�  p~  o  

+ *  0       (  r�  p~  o  

+ *  0 �      s  

o  
  o  
o  
+;o  
t   o  
�
	,  o  
o  
X[�% o   
-��u  ,o!  
 �+ *    Gc     0 t    s  
}  }   s  }	   ("  
}  s#  
{	  {  {	  {  (  
($  
�, 
 {	  {  {	  {  (  {  {	  |  {	  |  	(   s%  
{	  {  {	  {  (  o&  
 o'  
 {	  {  {	  {  (  {	  {(  
}  {	  {)  
}  �  s*  
o+  
 s,  
o-  
 o.  
o  
 o/  
  � r�  po0  
(1  
(2  
  � *A         A  V        0 C      r�  p(3  

, "33�A[ "33�A[ r p"  �BZi"  �BZis4  
+ * 0 :      r�  p(3  

, "33�A[(   "33�A[(   s5  
+ *  0 *      ~  r p�8  �8  (6  
 o7  
&+ *  0       0r p+r1 p
+ *  0 $   	   (8  

, o9  
 NNVV *0 '   
   l(:  
k
��, l(;  
k++ * 0 '   
   l(:  
k
��, l(<  
k++ *"(  
 *0 �      s=  
%rE pr1 po>  
 %rM pr1 po>  
 %rU pr1 po>  
 %r] pr1 po>  
 %re pr1 po>  
 %rm pr1 po>  
 %ru pr1 po>  
 %r� pr1 po>  
 %r� pr1 po>  
 %r� pr1 po>  
 %r� pr1 po>  
 %r� pr1 po>  
 %r� pr1 po>  
 %r� pr1 po>  
 %r� pr1 po>  
 %r� pr1 po>  
 %r� pr1 po>  
 %r pr1 po>  
 %r pr1 po>  
 %r pr po>  
 %r) pr po>  
 %r1 pr po>  
 %r9 pr po>  
 %rI pr po>  
 %rQ pr po>  
 �  *0 i       (?  

rY po@  
(1  
(2  
 (A  
oB  
 r} poC  
(1  
(2  
 �(    � r� po0  
(1  
(2  
  � *        LM    0 z      r� poC  
(1  
(D  
s  

sE  
%r' poF  
 %rA pr� p(G  
oH  
 %oI  
 (J  
 �  (K  
 (L  
~M  
r� pr�  poN  
(G  
sO  
oP  
(Q  
,P (L  
~M  
r�  poN  
r� p(G  
sO  
oP  
r� pr�  poN  
r� p(R  
(2  
  (Q  
, (   r� p(2  
 �i  r p(2  
 
~M  
r�  poN  
sO  
oP  

	(Q  
9   ~M  
rc  poN  
sO  
oP  

	(S  
9�   ra p(2  
 	~T  
oU  
u'  -+(V  
 ~W  
~X  
sY  
oP  
		(S  


9f   	~Z  
oU  
u(  �94   o[  
(\  


#        �9�    	o]  
(^  
(_  
(`  
#      �?ZXi(a  
(b  
#       @[Xi(  &(   d(K  
 (   r� p(2  
 ~M  
rW  poN  
sO  
oP  
(S  
,~T  
oc  
+,, t'  oV  
 r� p(2  
 d(K  
 (    +
 r� p(2  
   +
 (     +
 rq p(2  
   +
 r� p(2  
   + (   r� p(2  
 �   8�   r+ p(2  
 	~d  
oU  
u,  -+(e  
 ~W  
~f  
sY  
og  
 oh  
+do  
t&   ~i  
oj  
uD  ,�D  +_,' rS po]  
(k  
(1  
(2  
 +
 o   
-��u  ,o!  
 �-+(]  
(k  
rC  poN  
(3  
  9�    ~M  
r�  poN  
sO  
oP  
!!(S  
,!~d  
"oc  
+##9�    "t,  oe  
 r� p(2  
 d(K  
 ~M  
rW  poN  
sO  
oP  
$$(S  
,$~T  
%oc  
+&&,, %t'  oV  
 r� p(2  
 d(K  
 (    +
 r� p(2  
    +
 (      �' r� p'o0  
(1  
(2  
  � *  A4     �  q   &                [  \        0 e   
   ~M  
r�  poN  
sO  
oP  

(S  
,~T  
oc  
+, t'  oV  
 r p(2  
  +
 rG p(2  
  *"(  
 *"(  
 *"(  
 *0 q     sl  

om  
on  
oo  
op  
Yoq  
Ykor  
os  
Yot  
Yk
{	  {  "  �BZ(u  
	{	  {  "  �BZ(u  
{  ov  
k{  ow  
k[[��		,! {	  [}  {	  }   + {	  Z}  {	  }   op  
k{	  {  Y"   @[Xos  
k{	  {  Y"   @[Xox  
oy  
 ox  
oz  
 ox  
o{  
 ox  
{  {	  {  {	  {  o|  
 *   BSJB         v4.0.30319     l   �
  #~    $  #Strings    (  �  #US �!     #GUID   �!  �  #Blob         W�	   �3      H      	      '   |               	   
                  
        y      �7
 7
 �
 x   � �� j� �� �� �� � �
 
 M� 4�
 �	 �
 d7
 �
� ��
 ��
 On
 �n Z  �� w	'
 � �� )� �� M � � �� �# �� 

�
 {

 � B� P�� �   �
�
�    b� �� �� �� ��g �   J� �� 		� K	� S	� ?� /� �# 2� 6�
 �
 � �� �� �� � w� �� �� �	# wv  �v  �v     m          �
p
M      c	�M      ��M       M    "   M    �� "�1 � �Q�� �Q�� � K� �
� �� = �P     ��	  \     ��� �     �
K �     �� �     �� �     ��� !    �:
� <!    ��	� d!    ��� �!    �� � �!    �W
� �!    � � � x"    � O� $    � �	 d$    � 
 �$    � 2 �$    � _ %    � �  8%    � Q- l%    � _- �%    ��	  �%    ��	2 h'    � �6     � � n
<     � � CB �'    � �K" �-    � (R$ .    ��	 & &.    ��	 & /.    ��	 & 8.    �  [&    s   �   �   7   K   �
   �
   �   K   �
   �
   K   �
   �
   K   �
   K   �
   �   K   �
   J   v   �   �   
   �    �    �   y   |   e
   �   �   �   n   �   �   �	 �	  �	  �	
 ) �	 1 �	 9 �	 A �	 I �	 Q �	 Y �	 a �	 i �	 q �	 y �	 � �	 � �	 � �	 � �	  � �	 q. q7 � �	= � �N � �	 � z � N
e ��	k � 7p � At � � z � � z � e~ � > 6� � �	 �`� � �	 � �� � ��  7 �  g � ��	� � �� ��	 � 7	� � �� � O �� ��
� ��� ��� 	�	�  �	��
 O"��� �1�F>��>�q	> �	  =� �K� G� �+P��V� P� �����	 �m ��
���
 �1 )U����1�1��� ����	�1[�1����
�1��9� 1�9� 1�����	A� A7IE17#Y)Q�
QAQ�Q�
1�/a� a�
 61�8i�	k a��1[DYG� !�	d�l� �q	Az !�
z !�
z 	�
z !�z !�z ��wAz �
z �	})s�)��)��)��  �  �) � �.  q.  z.  �. # �. + �. 3 �. ; �. C �. K �. S �. [ �. c �. k . s C { `C � �C � �I � �� � �� � �& E J U � � � +8C\KS    �c  *h  m  �m  >
m  �	m  �m  � m  [
m              	      
  	   
     ��  1 n
  3 C �                            ��                ��               �#               ��
               �2               ��           �        �        �  �     K  �     �
  �	     �
  �     v  �
        �     "  �     M       �
       �	  8)     �	           <>c__DisplayClass2_0 <Print>b__0 <>c__DisplayClass2_1 Item1 CS$<>8__locals1 ValueTuple`2 Dictionary`2 Item2 <Module> System.Drawing.Drawing2D GetDPI get_OK MOUSEEVENTF_LEFTDOWN MOUSEEVENTF_LEFTUP get_X ZEBRA_PAPER_ROLL_ORIENTATION_DICTIONARY get_Y mscorlib get_Algebraic System.Collections.Generic get_CurrentThread Add get_Kind PrinterResolutionKind IsNullOrWhiteSpace set_SmoothingMode set_InterpolationMode set_PixelOffsetMode add_PrintPage DrawImage image get_Message language Invoke IDisposable RuntimeTypeHandle GetTypeFromHandle get_BoundingRectangle Single FromFile Console get_Name get_TwoLetterISOLanguageName set_FileName set_PrinterName printerName WriteLine get_Halftone TreeScope ControlType RotateFlipType getDitherType get_CurrentUICulture set_CurrentUICulture get_Culture set_Culture resourceCulture WindowsBase Dispose Truncate EditorBrowsableState CompilerGeneratedAttribute GuidAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute set_UseShellExecute get_Value TryGetValue GetCurrentPropertyValue value ZebraPrinterSetting.exe get_PaperSize set_PaperSize getPaperSize System.Threading Ceiling System.Runtime.Versioning GetString System.Drawing.Printing ZebraPrinterSetting System.Drawing Math imagePath get_Width width CeilIfDecimal FloorIfDecimal System.ComponentModel printer_util FindAll user32.dll Program System get_Bottom rm resourceMan Boolean Min Main System.Windows.Automation RangeValuePatternInformation AutomationElementInformation GetPaperRollOrientation paperRollOrientation GetDocumentOrientation printDocumentOrientation orientation System.Globalization System.Reflection PrinterResolutionCollection AutomationElementCollection PropertyCondition Exception PrinterResolution ClickOkButton RadioButton InvokePattern RangeValuePattern SelectionItemPattern AutomationPattern TryGetCurrentPattern dwExtraInfo CultureInfo ProcessStartInfo Sleep RotateFlip get_Top number Slider sender get_ResourceManager PrintPageEventHandler System.CodeDom.Compiler set_PrintController StandardPrintController PrinterHelper Floor IEnumerator GetEnumerator .ctor .cctor ZebraPrinterSetting.Resources.de.Designer.cs ZebraPrinterSetting.Resources.en.Designer.cs get_Graphics System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices get_Printing_Preferences ZebraPrinterSetting.Resources System.Resources ZebraPrinterSetting.Resources.ja.resources ZebraPrinterSetting.Resources.de.resources ZebraPrinterSetting.Resources.zh.resources ZebraPrinterSetting.Resources.pl.resources ZebraPrinterSetting.Resources.en.resources ZebraPrinterSetting.Resources.ko.resources ZebraPrinterSetting.Resources.fr.resources ZebraPrinterSetting.Resources.es.resources ZebraPrinterSetting.Resources.it.resources ZebraPrinterSetting.Resources.pt.resources ZebraPrinterSetting.Resources.ru.resources DebuggingModes UIAutomationTypes PropertyConditionFlags dwFlags get_PageSettings set_DefaultPageSettings get_PrinterSettings PrintPageEventArgs args Margins ConvertDimensions System.Collections get_Graphic_Options get_PrinterResolutions cButtons SetCursorPos Process set_Arguments System.Windows Concat Format Rect Object Select get_Left get_Right get_Height height sizeUnit unit UIAutomationClient AutomationElement get_RootElement PrintDocument get_Current mouse_event Print Start FindFirst MoveNext mainWindow dx dy get_Assembly GetExecutingAssembly get_Apply RotateImageIfNecessary op_Equality op_Inequality IsSelectedProperty NameProperty ControlTypeProperty AutomationProperty   AZ e b r a P r i n t e r S e t t i n g . R e s o u r c e s . i t  A l g e b r a i c  A p p l y  G r a p h i c   O p t i o n s  G r a p h i c s  H a l f t o n e  O K  )P r i n t i n g   P r e f e r e n c e s  #P r i n t P a g e   E r r o r :    m m  
c u s t o m  { 0 } x { 1 }  p o r t r a i t  l a n d s c a p e  4 x 2  2 x 4  3 x 2  2 x 3  1 x 3  3 x 1  
4 x 1 . 2 5  
1 . 2 5 x 4  3 . 5 x 1 . 5  1 . 5 x 3 . 5  1 x 2  2 x 1  5 7 x 3 2  3 2 x 5 7  6 2 x 2 9  3 6 x 8 9  5 9 . 9 x 3 8  
1 0 1 x 6 2  9 5 x 5 5  
7 0 x 1 5 0  4 x 6  6 x 4  2 . 1 x 4 . 1  4 x 1  1 x 4  #S y s t e m   L a n g u a g e :    L a n g u a g e   c o d e :    KE r r o r   w h i l e   g e t t i n g   s y s t e m   l a n g u a g e :    =Z e b r a P r i n t e r S e t t i n g . R e s o u r c e s .  r u n d l l 3 2 . e x e  Ap r i n t u i . d l l , P r i n t U I E n t r y   / e   / n   "  "     1P r i n t i n g   P r e f e r e n c e s . . .    UZ P L   P r i n t i n g   P r e f e r e n c e s   w i n d o w   n o t   f o u n d .  MZ P L   P r i n t i n g   P r e f e r e n c e s   w i n d o w   f o u n d .  5G r a p h i c   O p t i o n s   t a b   f o u n d .  3C l i c k e d   o n   s l i d e r   c u r s o r .  /C l i c k e d   ' A p p l y '   b u t t o n . u' A p p l y '   b u t t o n   n o t   f o u n d   o r   d o e s n ' t   s u p p o r t   I n v o k e P a t t e r n . UV a l u e P a t t e r n   n o t   s u p p o r t e d   f o r   t h e   s l i d e r .  3S l i d e r   e l e m e n t   n o t   f o u n d .  /G r a p h i c s   t a b   n o t   f o u n d .  'G r a p h i c s   t a b   f o u n d .  7S e l e c t e d   D i t h e r i n g   o p t i o n :    CS e l e c t e d   ' H a l f t o n e '   r a d i o   b u t t o n . ME r r o r   w h i l e   c h a n g i n g   p r i n t e r   s e t t i n g :    )C l i c k e d   ' O K '   b u t t o n . o' O K '   b u t t o n   n o t   f o u n d   o r   d o e s n ' t   s u p p o r t   I n v o k e P a t t e r n .  l��KI�J�������         YQQ ����  �� ��U U	eimq  ��  i    ��    	uy}�� ��  �� y}   �� ��  e    ��  	}   a   �� 

U��  U  �� UO(Q������������
��������������i��q������  ��  ����   ���� 
 ���� ����	 ���� �	 �	�
 ��  ��  
  ��  �� �	 ���� ����
����   y  ��   � � � �!
 ���z\V4���?_�
:1�8V�6N5      QUa��  Q U   	  �� }  ��      					 U ��Q �� Q U         TWrapNonExceptionThrows      pc_usb_info      ! Copyright © PhoneCheck 2023  ) $8096c939-dd84-4209-a7be-571b787d4126   1.0.0.0  M .NETFramework,Version=v4.7.2 TFrameworkDisplayName.NET Framework 4.7.2A 3System.Resources.Tools.StronglyTypedResourceBuilder17.0.0.0           �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y    G r a p h i c   O p t i o n s    G r a p h i c s )   H a l f t o n e 4   O K =   (P r i n t i n g   P r e f e r e n c e s F   
AlgebraicoAplicarOpciones Gráficas	GráficosTramadoAceptarPreferencias de impresión de  �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y 
   G r a p h i c   O p t i o n s    G r a p h i c s ,   H a l f t o n e 8   O K E   (P r i n t i n g   P r e f e r e n c e s I   Algébrique	AppliquerOptions Graphiques
GraphiquesDemi-teinteOKOptions d’impression      �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y    G r a p h i c   O p t i o n s    G r a p h i c s &   H a l f t o n e /   O K :   (P r i n t i n g   P r e f e r e n c e s >   	AlgebricoApplicaOpzioni GraficheGrafici	MezzitoniOKPreferenze stampa -    �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y    G r a p h i c   O p t i o n s    G r a p h i c s #   H a l f t o n e -   O K 7   (P r i n t i n g   P r e f e r e n c e s ;   	AlgebraicApplyGraphic OptionsGraphicsHalftoneOKPrinting Preferences      �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y 
   G r a p h i c   O p t i o n s    G r a p h i c s *   H a l f t o n e 4   O K =   (P r i n t i n g   P r e f e r e n c e s A   AlgebraischÜbernehmenGrafikoptionenGrafikenHalbtonOKDruckeinstellungen für       ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y    G r a p h i c   O p t i o n s    G r a p h i c s 9   H a l f t o n e M   O K d   (P r i n t i n g   P r e f e r e n c e s h   	代数(A)	適用(A)!グラフィックオプショングラフィックハーフトーン(H)OK印刷設定 �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y    G r a p h i c   O p t i o n s    G r a p h i c s (   H a l f t o n e 3   O K A   (P r i n t i n g   P r e f e r e n c e s I   	대수(A)	적용(A)그래픽 옵션	그래픽하프톤(H)확인인쇄 기본 설정�  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y    G r a p h i c   O p t i o n s    G r a p h i c s )   H a l f t o n e 2   O K ?   (P r i n t i n g   P r e f e r e n c e s C   AlgebraicznyZastosujOpcje GraficzneGrafikaPółcienieOKPreferencje drukowania:   �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y    G r a p h i c   O p t i o n s    G r a p h i c s )   H a l f t o n e 4   O K @   (P r i n t i n g   P r e f e r e n c e s D   
AlgébricoAplicarOpções Gráficas	Gráficos
Meios-tonsOKPreferências de Impressão de   8  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y    G r a p h i c   O p t i o n s 2   G r a p h i c s ]   H a l f t o n e m   O K }   (P r i n t i n g   P r e f e r e n c e s �   АлгебраическоеПрименить)Графические параметрыГрафикаПолутонOK Настройка печати:    �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP6Gv�asY ��	qrHЗ�P��PS��}^   s   |      I       &   �  A l g e b r a i c     
A p p l y    G r a p h i c   O p t i o n s    G r a p h i c s -   H a l f t o n e 5   O K C   (P r i n t i n g   P r e f e r e n c e s K   代数调色法(A)	应用(A)图形选项图形半色调(H)确定打印首选项     ﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ZebraPrinterSetting.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class en {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal en() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ZebraPrinterSetting.Resources.en", typeof(en).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Algebraic.
        /// </summary>
        internal static string Algebraic {
            get {
                return ResourceManager.GetString("Algebraic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply.
        /// </summary>
        internal static string Apply {
            get {
                return ResourceManager.GetString("Apply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Graphic Options.
        /// </summary>
        internal static string Graphic_Options {
            get {
                return ResourceManager.GetString("Graphic Options", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Graphics.
        /// </summary>
        internal static string Graphics {
            get {
                return ResourceManager.GetString("Graphics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Halftone.
        /// </summary>
        internal static string Halftone {
            get {
                return ResourceManager.GetString("Halftone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        internal static string OK {
            get {
                return ResourceManager.GetString("OK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printing Preferences.
        /// </summary>
        internal static string Printing_Preferences {
            get {
                return ResourceManager.GetString("Printing Preferences", resourceCulture);
            }
        }
    }
}
       &  ﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ZebraPrinterSetting.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class de {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal de() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ZebraPrinterSetting.Resources.de", typeof(de).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Algebraisch.
        /// </summary>
        internal static string Algebraic {
            get {
                return ResourceManager.GetString("Algebraic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Übernehmen.
        /// </summary>
        internal static string Apply {
            get {
                return ResourceManager.GetString("Apply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Grafikoptionen.
        /// </summary>
        internal static string Graphic_Options {
            get {
                return ResourceManager.GetString("Graphic Options", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Grafiken.
        /// </summary>
        internal static string Graphics {
            get {
                return ResourceManager.GetString("Graphics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Halbton.
        /// </summary>
        internal static string Halftone {
            get {
                return ResourceManager.GetString("Halftone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        internal static string OK {
            get {
                return ResourceManager.GetString("OK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Druckeinstellungen für.
        /// </summary>
        internal static string Printing_Preferences {
            get {
                return ResourceManager.GetString("Printing Preferences", resourceCulture);
            }
        }
    }
}
          �@�       }   ��  �t                             RSDS�0��&[D�G&�w�   C:\Users\<USER>\Desktop3\sources\zebra_printer_setting\zebra_printer\obj\Debug\ZebraPrinterSetting.pdb ��          ��                          ��            _CorExeMain mscoree.dll        �%  @                                                                     �                  0  �                   H   X�  `          `4   V S _ V E R S I O N _ I N F O     ���                 ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       ��   S t r i n g F i l e I n f o   �   0 0 0 0 0 4 b 0      C o m m e n t s       "   C o m p a n y N a m e         @   F i l e D e s c r i p t i o n     p c _ u s b _ i n f o   0   F i l e V e r s i o n     1 . 0 . 0 . 0   P   I n t e r n a l N a m e   Z e b r a P r i n t e r S e t t i n g . e x e   \   L e g a l C o p y r i g h t   C o p y r i g h t   �   P h o n e C h e c k   2 0 2 3   *   L e g a l T r a d e m a r k s         X   O r i g i n a l F i l e n a m e   Z e b r a P r i n t e r S e t t i n g . e x e   8   P r o d u c t N a m e     p c _ u s b _ i n f o   4   P r o d u c t V e r s i o n   1 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   1 . 0 . 0 . 0                                                                            �     �3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      