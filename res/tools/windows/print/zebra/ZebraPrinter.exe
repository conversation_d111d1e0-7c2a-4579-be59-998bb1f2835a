MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L ��Ä        � " 0  "         A       `    @                       �          `�                           �@  O    `  �                   �     @  8                                                             H           .text   !       "                    `.rsrc   �   `      $              @  @.reloc      �      (              @  B                �@      H     )                                                        0 �      s  

o  
  o  
o  
+;o  
t   o  
�
	,  o  
o  
X[�% o  
-��u  ,o  
 �+ *    Gc     0 t    s  
}  }   s  }   (  
}  s  
{  {  {  {  (  
(  
�, 
 {  {  {  {  (  {  {  |  {  |  	(   s  
{  {  {  {  (  o  
 o  
 {  {  {  {  (  {  {   
}  {  {!  
}  �  s"  
o#  
 s$  
o%  
 o&  
o  
 o'  
  � r  po(  
()  
(*  
  � *A         A  V       0 C      r%  p(+  

, "33�A[ "33�A[ r+  p"  �BZi"  �BZis,  
+ * 0 :      r%  p(+  

, "33�A[(   "33�A[(   s-  
+ *  0 *      ~  r9  p�&  �&  (.  
 o/  
&+ *  0       0rI  p+r[  p
+ *  0 $      (0  

, o1  
 NNVV *0 '      l(2  
k
��, l(3  
k++ * 0 '      l(2  
k
��, l(4  
k++ *"(5  
 *0 �      s6  
%ro  pr[  po7  
 %rw  pr[  po7  
 %r  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r�  pr[  po7  
 %r pr[  po7  
 %r pr[  po7  
 %r pr[  po7  
 %r+ pr[  po7  
 %r9 pr[  po7  
 %rE prI  po7  
 %rS prI  po7  
 %r[ prI  po7  
 %rc prI  po7  
 %rs prI  po7  
 %r{ prI  po7  
 �  *0 �   	   �i�, r� p(*  
 8�   �o8  


	rT p(+  
-r^ p(+  
-*+N�i��, rl p(*  
 +>�(
   +3�i��, r� p(*  
 +(   +
r� p(*  
 + *   0 9   
   (  
�, r p(*  
  + rg p�)  (.  
(*  
  *   0 �      �i�, r� p(*  
 +h�
��(9  
(:  
�(9  
(:  

�� 	(   r@ p(*  
  � rx po(  
()  
(*  
  � *    I f   "(5  
 *"(5  
 *"(5  
 * 0 q     s;  

o<  
o=  
o>  
o?  
Yo@  
YkoA  
oB  
YoC  
Yk
{  {  "  �BZ(D  
	{  {  "  �BZ(D  
{  oE  
k{  oF  
k[[��		,! {  [}  {  }   + {  Z}  {  }   o?  
k{  {  Y"   @[XoB  
k{  {  Y"   @[XoG  
oH  
 oG  
oI  
 oG  
oJ  
 oG  
{  {  {  {  {  oK  
 *   BSJB         v4.0.30319     l     #~  p  �  #Strings    l  �  #US      #GUID   $  �  #Blob         W	   �3      /               K                       �      
� w� >_ �   f9 �9 �9 ^9 *9 C9 }9 Rr 0r �9 �� g� ` � 
 � +/
 q ��
 �
 � S � g�
 �
 x �
 �
 K K  
  ��
 �
 �
 � �� ��
 � +� M � �$ ��
 V
 .| 
 @| 
 V|     s          vA      �vA       A    "   A   1 � � D� �� ~� = �P     � � � �     � �� �"    � �� �"    � �  #    � �� X#    � �� |#    � �� �#    � Y� �#    � g� $    �E  $    �K� �%    � �� �&    � J�  �&    � �� t'    �E  }'    �E  �'    �E  �'    �  �    �   �   0   D   �   �      D   �   �   D   �   �   D   �   D   �   ~   D   �   �   �   �   �      �      �   �	 E  E  E
 ) E 1 E 9 E A E I E Q E Y E a E i E q E y E � E � E � � � B* � 7/ � �4 � � 8 � � > � � > � �B � � � �\ � E 	b � E � �g � �m  7 z  m ~ E� � f� E � �� � �� � � � �� 	Y� )�� 	�� � E�  E� 	`�  �� 	�� � �� AA�A%� E  E  � � 	� Q�!1'� E>� �F� �K� :> � n> � w> � �> � �> � �> A�P� :> � �> � RVa*\a<caRjatq.  �.  �.  . # . + -. 3 -. ; -. C . K 3. S -. [ -. c U. k . s �� { �� { � F � � � � � � /s � �                            {�                �           <>c__DisplayClass2_0 <Print>b__0 <>c__DisplayClass2_1 Item1 CS$<>8__locals1 Int32 ValueTuple`2 Dictionary`2 Item2 <Module> System.Drawing.Drawing2D GetDPI get_X ZEBRA_PAPER_ROLL_ORIENTATION_DICTIONARY get_Y mscorlib System.Collections.Generic Add get_Kind PrinterResolutionKind IsNullOrWhiteSpace set_SmoothingMode set_InterpolationMode set_PixelOffsetMode add_PrintPage DrawImage image get_Message IDisposable Single FromFile Console set_PrinterName printerName WriteLine RotateFlipType get_InvariantCulture Dispose Parse Truncate CompilerGeneratedAttribute GuidAttribute DebuggableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute TryGetValue ZebraPrinter.exe get_PaperSize set_PaperSize getPaperSize Ceiling System.Runtime.Versioning String System.Drawing.Printing System.Drawing Math imagePath get_Width width HandleDpiCheck CeilIfDecimal FloorIfDecimal printer_util Program System get_Bottom Min Main HandlePrintOperation GetPaperRollOrientation paperRollOrientation GetDocumentOrientation printDocumentOrientation orientation System.Globalization System.Reflection PrinterResolutionCollection Exception PrinterResolution CultureInfo RotateFlip get_Top number IFormatProvider sender PrintPageEventHandler set_PrintController StandardPrintController PrinterHelper ZebraPrinter ToLower Floor IEnumerator GetEnumerator .ctor .cctor get_Graphics System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices DebuggingModes get_PageSettings set_DefaultPageSettings get_PrinterSettings PrintPageEventArgs args Margins ConvertDimensions System.Collections get_PrinterResolutions Concat Format Object get_Left get_Right get_Height height sizeUnit unit PrintDocument get_Current Print MoveNext RotateImageIfNecessary op_Equality op_Inequality   #P r i n t P a g e   E r r o r :    m m  
c u s t o m  { 0 } x { 1 }  p o r t r a i t  l a n d s c a p e  4 x 2  2 x 4  3 x 2  2 x 3  1 x 3  3 x 1  
4 x 1 . 2 5  
1 . 2 5 x 4  3 . 5 x 1 . 5  1 . 5 x 3 . 5  1 x 2  2 x 1  5 7 x 3 2  3 2 x 5 7  6 2 x 2 9  3 6 x 8 9  5 9 . 9 x 3 8  
1 0 1 x 6 2  9 5 x 5 5  
7 0 x 1 5 0  4 x 6  6 x 4  2 . 1 x 4 . 1  4 x 1  1 x 4  ��U s a g e :   - d p i   [ P r i n t e r N a m e ]   o r   - p r i n t   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ W i d t h ]   [ H e i g h t ]   [ S i z e U n i t ]   [ O r i e n t a t i o n ] 	- d p i 
- p r i n t uI n v a l i d   a r g u m e n t s .   U s a g e   f o r   D P I   c h e c k :   - d p i   [ P r i n t e r N a m e ] ��I n v a l i d   a r g u m e n t s .   U s a g e   f o r   p r i n t :   - p r i n t   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ W i d t h ]   [ H e i g h t ]   [ S i z e U n i t ]   [ O r i e n t a t i o n ] II n v a l i d   c o m m a n d .   U s e   - d p i   o r   - p r i n t . [C u s t o m   D P I   s e t t i n g   n o t   f o u n d   f o r   t h e   p r i n t e r .  D P I   o f   { 0 } :   { 1 }  ��U s a g e   f o r   p r i n t :   - p r i n t   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ W i d t h ]   [ H e i g h t ]   [ S i z e U n i t ]   [ O r i e n t a t i o n ] 7I m a g e   p r i n t e d   s u c c e s s f u l l y .  -E r r o r   w h i l e   p r i n t i n g :      ����oD�i��7�}t       	IMQU  }  M    ��    	Y]ae m  i ]a   �� ��  I    i  	a   E   �� 

e  �� ��
yi   ]  i   �� �� �� ��	 m�z\V4���?_�
:Em 	  i a  m     u        TWrapNonExceptionThrows      pc_usb_info      ! Copyright © PhoneCheck 2023  ) $8096c939-dd84-4209-a7be-571b787d4126   1.0.0.0  M .NETFramework,Version=v4.7.2 TFrameworkDisplayName.NET Framework 4.7.2        �"�       p   L@  L"                             RSDS3if��uH�n�n���   D:\IdeaProjects\Desktop3\sources\zebra_printer\zebra_printer\obj\Debug\ZebraPrinter.pdb �@          �@                          �@            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                 �                  0  �                   H   X`  H          H4   V S _ V E R S I O N _ I N F O     ���                 ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       ��   S t r i n g F i l e I n f o   �   0 0 0 0 0 4 b 0      C o m m e n t s       "   C o m p a n y N a m e         @   F i l e D e s c r i p t i o n     p c _ u s b _ i n f o   0   F i l e V e r s i o n     1 . 0 . 0 . 0   B   I n t e r n a l N a m e   Z e b r a P r i n t e r . e x e     \   L e g a l C o p y r i g h t   C o p y r i g h t   �   P h o n e C h e c k   2 0 2 3   *   L e g a l T r a d e m a r k s         J   O r i g i n a l F i l e n a m e   Z e b r a P r i n t e r . e x e     8   P r o d u c t N a m e     p c _ u s b _ i n f o   4   P r o d u c t V e r s i o n   1 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   1 . 0 . 0 . 0                                                                                                    @     1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      