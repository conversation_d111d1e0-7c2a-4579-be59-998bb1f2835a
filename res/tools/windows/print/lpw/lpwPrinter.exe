MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L ���        � " 0  <         �[       `    @                       �          `�                           n[  O    `  �                   �     �Z  8                                                             H           .text   �;       <                    `.rsrc   �   `      >              @  @.reloc      �      B              @  B                �[      H     �0  �)                                                      {  *"}  *{  *"}  *{  *"}  *�(  
  (   (   (   *   0 )     s  
}   ~  �  s  
(  ++ *   0 �      s  
%r  pr  pr/  ps  o  
 %r9  prO  pr/  ps  o  
 %rg  pr�  pr�  ps  o  
 %r�  pr�  pr�  ps  o  
 %r�  pr�  pr�  ps  o  
 %r	 pr pr�  ps  o  
 %r9 prO prO ps  o  
 %ra prw pr� ps  o  
 %r� pr� pr/  ps  o  
 %r� pr�  pr�  ps  o  
 %r� pr�  pr�  ps  o  
 %r pr�  pr�  ps  o  
 %r7 prU prg ps  o  
 %rq pr pr�  ps  o  
 %r� pr pr�  ps  o  
 %r� pr� prO ps  o  
 �  * 0       r� p(  

o  
+ *   0 �      r� p(  

o  
�, r7 ps  
zo  
o  
o  
(  
(  
o  
o  
o  
(  
(  
o  
o  
o  
o  

	s   
+ * 0       
 o!  
+>o"  
t  
 	o#  
�,	 	o#  

 	o$  
�,	 	o$  
  o%  
-��u  ,o&  
 �k5
k��+," r� p�2  �2  ('  
((  
 �_ r( p�  %�2  �%�2  �%�6  �%�6  �()  
((  
 �	 r� p	o*  
(+  
((  
 � *     JX        ��   0 �     (,  
r� p(,  
(-  
(  
r� p(.  
((  
  o!  
+o"  
t   rg p(.  
((  
  o%  
-��u  
	,	o&  
 ��9�     o!  
8�   o"  
t   (/  
-1ro po0  
,"o  (1  
-o2  
o  o0  
-4r{ po0  
,"o  (1  
-o2  
o  o0  
++,& o#  
ko$  
k	V	V
ݫ    o%  
:I����u  
	,	o&  
 � r� p(3  
, N"33�A[VN"33�A[V N"  �BZ	N"  �BZ	VVNN(  -NN(  +, NN	V	V r� pNiNis4  

+ 
*    6 +a      � �H    "(  
 *   0 �      s5  
%o6  
 
 o7  
o8  
+Fo"  
t   o9  
-o:  
1o;  
�+
	, o:  
o;  
X[݆    o%  
-��u  ,o&  
 �o  
r� po<  
,	 ,  +Go  
r� po<  
,	 ,  +(o  
r� po<  
,	 �   +	 �   + *    Rn     0 �    s  
}   s  }   (  {=  
}
  {>  
}  {?  
{
  (  
{  (  (  (@  
	(  }  {  (  }  {  |
  |  (   sA  
%oB  
o6  
 |
  |  oB  
oC  
(
  sD  
%oE  
 %oF  
 	oB  
oG  
oH  

r� p
�6  (.  
((  
 (/  
-(  
(I  
,

1
�+

,; r� p�6  
�6  ('  
((  
 	oB  
oG  
oJ  
oK  
  	oL  
 �   sM  
oN  
 sO  
oP  
 oQ  
  � r� po*  
(+  
((  
  � * A         �  �       0 6      r� p(3  

, +r� p(3  
, "33�A[+ + *  0 s   	   kZi
kZi  sR  
(S  

 	(T  
oU  
 	oV  
 	oW  
 	oX  
 	oY  
  �	,	o&  
 �kkoZ  
 + *    ! 4U     0 �   
   o[  
o\  
 
 & sR  

8�    +l o]  

#�A`��"�?(^  
lZ#bX9���?(_  
lZX#�v��/�?(`  
lZXi �   2(T  
+(a  
ob  
  Xo\  
�-� Xo[  
�:i���+ *  0 �      (c  

(d  
iise  
 (S  

 	(T  
oU  
 	oV  
 	oW  
 	oX  
 	of  
  �	,	o&  
 �(g  
oh  
 si  
%oj  
 (k  
& �,o&  
 �*      # 7Z       n�     0       2+
+ *  0 �   
   NN(  
o[  
ko\  
k(  (/  
�
	, o  
r� p(3  
-+ 333	��++, ol  
 NNVV *"(  
 *  0 �      �i�, r� p((  
 8�   �o  


	r� p(3  
-r� p(3  
-*+N�i��, r� p((  
 +>�(   +3�i��, rI p((  
 +(   +
rL	 p((  
 + *   0 9      (  
�, r�	 p((  
  + r�	 p�6  ('  
((  
  *   0 :     �i�, r
 p((  
 8  �(
  �, r�
 p((  
 8�   �
���
�� �(/  
-�	(m  
+

, 	 �4  %r� p�%�%r� p�%�%r p�%�%r. p�%	�%rN p�%	�%
rl p�%�%r� p�%
r� p�K  (.  
�(n  
((  
 	(   r� p((  
  � r� po*  
(+  
((  
  � *      ] �  "(  
 *"(  
 *Jo  {  o<  
*"(  
 *"(  
 * 0 i     so  

op  
oq  
o#  
or  
Yos  
Yko$  
ot  
You  
Yk
{
  (v  
	{  (v  
{  o[  
k{  o\  
k[[��		, [}  }
   + Z}
  }   or  
k{
  Y"   @[Xot  
k{  Y"   @[Xow  
oV  
 ow  
oW  
 ow  
oX  
 ow  
{  {
  {  of  
 {  {  

,% {  	{
  {  r&
 p(    *   BSJB         v4.0.30319     l   �
  #~    �  #Strings    �  H
  #US �$     #GUID   �$    #Blob         W�		
   �3      L   
          5   w                                      �      ��	 g�	 ��	 
   ;� �� �� N� � 3� R� '�	 ��	 �� mV �\ l $ ��	 ��	 �	
 � ��	 � \ �
�c    �	0 � �\ S\ n\ ]� e� � �
� �� �	� �	� �
� �
� � \ }� ^ $
 L
 C
 � K\
 ;
 � �� �\ �\ �\ �\ � \ }\c    ��c �   d
� �� 	� ,	� 4	� �? ��  �  �  \�   ��  �?
 ��	
 ~�	 e� s\ �\    �          �m	A      D	m	A  
    T	m	A      Km	A       A     �  y     �  y 	      A    I   A 
  1 q
4 �< r< X< 2<?V�� BV�� B?V�� FV�� F (J �M �M FP y UP     ���  X     ��  a     ���  i     ��  r     ���  z     ��  �     ��	Y �     � '` �     ��	f �"    � � �"    � j	 d#    � Cu �$    � } �&    ��	  �&    � � � �'    � -� �)    � T
� *    � �� �*    � (�! d+    � {�" $,    � ��* @,    � \�, �,    ��	 0 �,    � ��0 �-    � !� 1 �-    � ��2 (/    ��	 3 1/    ��	 3 :/    � , �3 M/    ��	 4 V/    ��	 4 `/    � = �4    �   �   �   2   �   �   2   p         p   �
   �   �   �   �   �   �
   \         �   2   �   R   \   (   �   �   0   -
   @
   
   �   k   �   �   P   �   �   � �   �   �   k   �   �   �   �
      �
   �   �   5	 �	  �	  �	
 ) �	 1 �	 9 �	 A �	 I �	 Q �	 Y �	 a �	 i �	 q �	 y �	 � �	 � �	 � �	  � �	  �	6 I�<  �	  ?] Y�i irs q�	 � g� yS� ��� ��� ��� �y	�  �	� � �	� � !� � �� � �� � Cs � � ��� �<� ��� � q� ��� ��� ����
����
�� �� �s � �	&� �	 �  � P=��	� � �C� � � 
� ��
I s p � t � x�|	�	 	�
�� z
��	 N �� 
��3� ����S���	�
���	6 	8���	 		�	- �	�!W�)��!��!��!�!
!ao�� �� 96)� >)� >)� >)�BB!�V1F� �	]!ac97n�tA�	 A� I=|��Y�����9�	�1�
���9�� 9�� 9�� 9c� a��1�	�      (  ,  �   { .  �.  �.  . # . + 0. 3 0. ; 0. C . K 6. S 0. [ 0. c X. k �. s �@ { A { A � �` { a { a � �� { � { � � �� { � { � { #{ $� �C{ & c w � � -N��"K������    -�  ��  ��                  - V � �               b	             �               �\               �               �t          	  
  ) Q      <>c__DisplayClass1_0 <>c__DisplayClass14_0 <FindBySize>b__0 <Print>b__0 <>c__DisplayClass1_1 IEnumerable`1 List`1 Item1 CS$<>8__locals1 Int32 Func`2 Item2 ValueTuple`3 Item3 <Module> get_B System.Drawing.Drawing2D LANDSCAPE get_G GetDPI System.IO get_R PORTRAIT get_X get_Y value__ mscorlib System.Collections.Generic Add IsPaperSizeSupported <TapeName>k__BackingField <LabelName>k__BackingField <Size>k__BackingField get_Kind PrinterResolutionKind IsNullOrWhiteSpace set_PaperSource source set_SmoothingMode set_InterpolationMode set_PixelOffsetMode showPrintedPage add_PrintPage dpiAdjustedImage FromImage DrawImage image get_Message Enumerable IDisposable Single FromFile Console fileName get_TapeName set_TapeName tapeName get_LabelName set_LabelName labelName get_PaperName set_PrinterName printerName ConvertToMonochrome WriteLine Combine set_Landscape rollType RotateFlipType System.Core get_InvariantCulture Capture Dispose TryParse DebuggerBrowsableState get_White CompilerGeneratedAttribute GuidAttribute DebuggableAttribute DebuggerBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute TupleElementNamesAttribute CompilationRelaxationsAttribute AssemblyProductAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute set_UseShellExecute get_Value value Save lpwPrinter.exe get_Size set_Size IsValidSize BrotherLabelSize labelSize get_PaperSize set_PaperSize GetORCreatePaperSize FindBySize size get_Png System.Drawing.Imaging System.Runtime.Versioning dimensionString ToString System.Drawing.Printing System.Drawing Match Math imagePath GetFolderPath get_Width availablePrintableWidth width ResizeImageForDpi targetDpi get_Black HandleDpiCheck original GetPixel SetPixel Program get_Item System get_Bottom Enum Boolean SaveImageToDesktopAndOpen Min Main HandlePrintOperation GetOrientation orientation System.Globalization System.Reflection PaperSourceCollection PaperSizeCollection PrinterResolutionCollection GroupCollection ArgumentException PrinterResolution SetResolution StringComparison CultureInfo ProcessStartInfo Bitmap RotateFlip get_Top Group System.Linq Clear IFormatProvider SpecialFolder sender PrintPageEventHandler set_PrintController StandardPrintController LabelSizeHelper PrinterHelper lpwPrinter lpw_printer ToLower Color IEnumerator GetEnumerator .ctor .cctor get_Graphics System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices get_PaperSources DebuggingModes labelWidthInInches labelHeightInInches ConvertToInches NumberStyles AllSizes get_PaperSizes paperSizes get_PageSettings set_DefaultPageSettings get_PrinterSettings PrintPageEventArgs args Equals Contains Margins ParseDimensions System.Text.RegularExpressions System.Collections RegexOptions get_PrinterResolutions get_Groups get_Success Process Concat ImageFormat PixelFormat Object get_Left get_Right get_Height availablePrintableHeight height sizeUnit unit FirstOrDefault Environment PrintDocument get_Current Print get_Count Start MoveNext Regex paperTray RotateImageIfNecessary op_Equality IsNullOrEmpty     1 x 2   o r   2 x 1  5 4 m m   x   2 9 m m  	2 9 m m  1 x 3   o r   3 x 1  2 9 m m   x   9 0 m m  !1 . 2 5 x 4   o r   4 x 1 . 2 5  3 8 m m   x   9 0 m m  	3 8 m m  %1 . 5 x 3 . 5   o r   3 . 5 x 1 . 5  2 x 3   o r   3 x 2  6 2 m m   x   7 5 m m  	6 2 m m  2 x 4   o r   4 x 2  6 2 m m   x   1 0 0 m m  3 x 4   o r   4 x 3  3 8 m m   x   2  4 x 6   o r   6 x 4  1 0 2 m m   x   1 5 2 m m  1 0 2 m m  2 9 x 6 2   o r   6 2 x 2 9  6 2 m m   x   2 9 m m  3 2 x 5 7   o r   5 7 x 3 2  3 6 x 8 9   o r   8 9 x 3 6  %3 8 x 5 9 . 9   o r   5 9 . 9 x 3 8  5 4 x 7 0   o r   7 0 x 5 4  2 9 m m   x   2  	5 4 m m  5 5 x 9 5   o r   9 5 x 5 5  !6 2 x 1 0 1   o r   1 0 1 x 6 2  !7 0 x 1 5 0   o r   1 5 0 x 7 0  5 0 m m   x   3  Q( \ d + ( \ . \ d + ) ? ) x ( \ d + ( \ . \ d + ) ? ) ( i n c h | m m | c m ) $  ��I n v a l i d   d i m e n s i o n   f o r m a t .   E x p e c t e d   f o r m a t   i s   l i k e   ' 2 x 3 i n c h '   o r   ' 2 9 x 6 0 m m ' . [P a p e r   s i z e   { 0 }   x   { 1 }   i s   s u p p o r t e d   b y   p r i n t e r .  ��P a p e r   s i z e   { 0 }   x   { 1 }   i n c h e s   e x c e e d s   t h e   m a x i m u m   s u p p o r t e d   s i z e   o f   { 2 }   x   { 3 } .  7E r r o r   c h e c k i n g   p a p e r   s i z e :    x  gA l l   a v a i l a b l e   p a p e r   s i z e s   m e n t i o n   b e l o w   a n d   b l s   { 0 }  { 0 }  l a b e l  	t a p e  m m  
C u s t o m  z t 4 1 1  z d 6 2 0  4 2 0  EN o   o f   a v a i l a b l e   p a p e r   s o u r c e s :   { 0 }  ��G o i n g   t o   s e t   p a p e r   s o u r c e   { 0 }   f r o m   a v a i l a b l e   n u m b e r   o f   { 1 }   s o u r c e s .  #P r i n t P a g e   E r r o r :    	i n c h  l a n d s c a p e  ��U s a g e :   - d p i   [ P r i n t e r N a m e ]   o r   - l p w   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ S i z e ]   [ O r i e n t a t i o n ]   [ P a p e r T r a y ]   [ R o l l T y p e ]   [ s h o w P r i n t e d P a g e ] 	- d p i 	- l p w uI n v a l i d   a r g u m e n t s .   U s a g e   f o r   D P I   c h e c k :   - d p i   [ P r i n t e r N a m e ] �I n v a l i d   a r g u m e n t s .   U s a g e   f o r   p r i n t :   - l p w   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ S i z e ]   [ O r i e n t a t i o n ]   [ P a p e r T r a y ]   [ R o l l T y p e ]   [ s h o w P r i n t e d P a g e ] EI n v a l i d   c o m m a n d .   U s e   - d p i   o r   - l p w . [C u s t o m   D P I   s e t t i n g   n o t   f o u n d   f o r   t h e   p r i n t e r .  D P I   o f   { 0 } :   { 1 }  ��U s a g e   f o r   p r i n t :   - l p w   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ S i z e ]   [ O r i e n t a t i o n ]   [ P a p e r T r a y ]   [ R o l l T y p e ]   [ s h o w P r i n t e d P a g e ] ��U n a b l e   t o   p a r s e   l a b e l   s i z e .   I t   s h o u l d   b e   l i k e   [ W x H   ( i n c h | m m ) ]   [ 3 . 0 0 x 4 . 0 0 i n c h ]  uG o i n g   t o   p r i n t   i m a g e   w i t h   f o l l o w i n g   p a r a m s   
 P r i n t e r   N a m e :      
 I m a g e   P a t h :      
 S i z e :      
 O r i e n t a t i o n :      
 P a p e r   T r a y :      
 R o l l   T y p e :      
  1S h o w   P r i n t e d   P a g e :   { 0 }   
  7I m a g e   p r i n t e d   s u c c e s s f u l l y .  -E r r o r   w h i l e   p r i n t i n g :    P r i n t e d P a g e . p n g    ����0O��|Ɇ�        M ��  �� �� 
E  U	 U��  U]  �� ��    �� ��]	  
imqu  i        
imqimm    ��  	ai}q  ��  �� !$(����m��]u  ��  a  e m  �� ���� �� �� �� �� ��
������ � ����  �� �� � �	 �
 �� 	�������� ��   ��
���� � 
 ��  � � �%�!     �)u  ��m   ��  m   ���z\V4���?_�
:       P r i n t e d P a g e . p n g E ��$    
 ] e me 
   ���� ����
 ��   ��   ��(         TWrapNonExceptionThrows      pc_usb_info      ! Copyright © PhoneCheck 2023  ) $8096c939-dd84-4209-a7be-571b787d4126   1.0.0.0  M .NETFramework,Version=v4.7.2 TFrameworkDisplayName.NET Framework 4.7.2           widthheightunit         r>��       j   [  =                             RSDS�����A���ߐ�w   D:\IdeaProjects\Desktop3\sources\lpw_printer\lpw_printer\obj\Debug\lpwPrinter.pdb �[          �[                          �[            _CorExeMain mscoree.dll       �%  @                                                                             �                  0  �                   H   X`  @          @4   V S _ V E R S I O N _ I N F O     ���                 ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       ��   S t r i n g F i l e I n f o   |   0 0 0 0 0 4 b 0      C o m m e n t s       "   C o m p a n y N a m e         @   F i l e D e s c r i p t i o n     p c _ u s b _ i n f o   0   F i l e V e r s i o n     1 . 0 . 0 . 0   >   I n t e r n a l N a m e   l p w P r i n t e r . e x e     \   L e g a l C o p y r i g h t   C o p y r i g h t   �   P h o n e C h e c k   2 0 2 3   *   L e g a l T r a d e m a r k s         F   O r i g i n a l F i l e n a m e   l p w P r i n t e r . e x e     8   P r o d u c t N a m e     p c _ u s b _ i n f o   4   P r o d u c t V e r s i o n   1 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   1 . 0 . 0 . 0                                                                                                            P     �;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      