MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L ��\        � " 0  
         ^)       @    @                       �          `�                           )  O    @  �                   `     �'                                                               H           .text   d	       
                    `.rsrc   �   @                    @  @.reloc      `                    @  B                @)      H     �                                                         " (   *   0 D      r  p(  

��,. ~  
�o  
s  
	(  &�o  
(  & *"(  
 *   BSJB         v4.0.30319     l   l  #~  �  �  #Strings    �     #US �     #GUID   �  @  #Blob         W	    �3                                               �      5 � i M �   �   �  � U n �  } ` [ ` �  � � � �`
 �M F              �6A   V�
 D     � �  G      � � �N  P     � S  \     � Y  �     �@     .    �   ;    �	 @  @  @
 ) @ 1 @ 9 @ A @ I @ Q @ Y @ a @ i @ q @ y @ � H " � 1) � 3 , � @0 � @   ? .  ] .  f .  � . # � . + � . 3 � . ; � . C � . K � . S � . [ � . c � . k � . s �  �      � �               6             6                 6        <Module> SW_RESTORE mscorlib ShowWindowAsync hWnd get_MainWindowHandle GetProcessesByName GuidAttribute DebuggableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute Presenter.exe HandleRef System.Runtime.Versioning user32.dll Program System FocusInterCom Main System.Reflection Zero Presenter .ctor IntPtr System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices DebuggingModes args Process Object SetForegroundWindow nCmdShow   I n t e r c o m   ����"�BK�/F��       I I   �z\V4��	    E            TWrapNonExceptionThrows      	Presenter       Copyright ©  2019  ) $6e649b99-7731-4645-b6e2-b7b077da1afa   1.0.0.0  M .NETFramework,Version=v4.5.2 TFrameworkDisplayName.NET Framework 4.5.2    ��\         �'  �	  RSDS��y#��@�&++���   c:\users\<USER>\documents\visual studio 2015\Projects\Presenter\Presenter\obj\Debug\Presenter.pdb                                                                                                                                                                    4)          N)                          @)            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                  �   P  �                  8  �                   �                     h  �                   �  �@            4   V S _ V E R S I O N _ I N F O     ���                 ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       �|   S t r i n g F i l e I n f o   X   0 0 0 0 0 4 b 0      C o m m e n t s       "   C o m p a n y N a m e         < 
  F i l e D e s c r i p t i o n     P r e s e n t e r   0   F i l e V e r s i o n     1 . 0 . 0 . 0   <   I n t e r n a l N a m e   P r e s e n t e r . e x e   H   L e g a l C o p y r i g h t   C o p y r i g h t   �     2 0 1 9   *   L e g a l T r a d e m a r k s         D   O r i g i n a l F i l e n a m e   P r e s e n t e r . e x e   4 
  P r o d u c t N a m e     P r e s e n t e r   4   P r o d u c t V e r s i o n   1 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   1 . 0 . 0 . 0   �C  �          ﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>

<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity version="1.0.0.0" name="MyApplication.app"/>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>                                                                                                 `9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      