MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L  #d�        � 0           �7       @    @                       �          `�                           �7  O    @  �                   `     7  8                                                             H           .text   �                           `.rsrc   �   @                    @  @.reloc      `                     @  B                �7      H     �#       	  �5  p                                          0 �       }  (  
  (   �  s  
(  
 �  s  
(  
 ~  ~  s  
(  
 ~  s  
%o  
 % �   	  s  
o  
 }  {  o  
{  o   
 *6 r  p(!  
 * 0 6      ("  
�
,
 r!  p(!  
  ("  
�,
 rE  p(!  
  *
 *   0 +      ,{  �+
, {  o#  
  ($  
 * 0 �        s%  
}  (&  
 {  o  
 {  s  
o'  
 {  rk  po(  
 {   �   �  s  
o  
 {  o)  
 {  �  s*  
o+  
 "  �@"  PAs,  
(-  
 (.  
  �   �  s  
(/  
 (  
{  o   
 (0  
 ry  p((  
 (1  
 r�  po2  
 (3  
 *"(4  
 *.r�  p�  *� �(5  
�  �(5  
�  ��  (6  
 (7  
 s  (8  
 *&(4  
  * 0 9      ~  �
," r�  p�  (9  
o:  
s;  
�   ~  + *   0       ~  
+ *" �  *0       ~	  
+ *"(<  
 *Vs  (=  
t  �	  *  BSJB         v4.0.30319     l     #~  �  �  #Strings    $  �   #US      #GUID      �  #Blob         W�	   �3      6      	      	   =                                   �� �� �V �   �e pe Qe �e �e �e  e �i fi 4e [
 o .� #�
 �o ��
 9o S�
 No 8� t �V K� �� �P � �� � ; ��
 �o
 �o #� U�
 � o >�� w   � �
 (o a �
 �o  �
 �o
 S o
 �o )�
 "o � � m � �e � ;              A      �Q   � �Q  	    �	Q  
   0	� 	   A �  	 �
 �
 �  �  ? P     �I  �     � u !    � C& F!    � - L!    � �   �!    � �  �"    �I  �"    �O�  �"    � 4 �"    �I 	 �"    ��:	 0#    �� ?	 G#    �� D	 P#    ��J
 g#    �I 
 p#    �O� 
    �   Y   �   Y   �   Y   �   ]   	 I  I  I
 ) I 1 I 9 I A I I I Q I Y I a I i I q I y I � I � I � I � I � I  � I 	I& P, I& � �3 !I: � .@ � IG �O 9I: +V b] A. c I� j � t Y�  � �  � I 1 .@ �  e aI& ~ iI� q�� qO � � 4V � r � �� L ? � I � � �� ��� ��� � � ��� � I� I �2 � ) � �.  ^.  g.  �. # �. + �. 3 �. ; �. C �. K �. S �. [ �. c �. k �. s �I � �� � E� � @� � @� � @� � � { @o z � � �       �O  � T  �Y       
     �                            � %                � o    I  �       � �               � �               � �    I  �       � �           �  �      �     panel1 Form1 ToInt32 <Module> SizeF mscorlib Add Synchronized defaultInstance set_AutoScaleMode IDisposable RuntimeTypeHandle GetTypeFromHandle Console DockStyle set_Name WriteLine Type get_Culture set_Culture resourceCulture ApplicationSettingsBase Dispose EditorBrowsableState get_WindowState FormWindowState STAThreadAttribute CompilerGeneratedAttribute GuidAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute value Intercom.exe set_Size set_ClientSize Form1_Resize add_Resize System.Runtime.Versioning Form1_FormClosing add_FormClosing disposing System.Drawing set_Dock System.ComponentModel Panel ContainerControl navigationUrl Program System Intercom Form resourceMan Main Application set_Location System.Configuration System.Globalization System.Reflection ControlCollection set_StartPosition FormStartPosition Run CultureInfo CefSharp sender get_ResourceManager FormClosingEventHandler PaintEventHandler System.CodeDom.Compiler IContainer ChromiumWebBrowser browser .ctor .cctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Intercom.Form1.resources Intercom.Properties.Resources.resources DebuggingModes Intercom.Properties EnableVisualStyles Settings FormClosingEventArgs PaintEventArgs args get_Controls System.Windows.Forms CefSharp.WinForms set_AutoScaleDimensions paramters components Object get_Default SetCompatibleTextRenderingDefault InitializeComponent Panel1_Paint add_Paint Point Convert SuspendLayout ResumeLayout set_Text IRequestContext set_TabIndex set_MaximizeBox pointx get_Assembly pointy     I n t e r C o m   C l o s e d  #I n t e r C o m   R e s t o r e d  %I n t e r C o m   M i n i m i z e d  
p a n e l 1  F o r m 1  %P h o n e C h e c k   S u p p o r t   ;I n t e r c o m . P r o p e r t i e s . R e s o u r c e s     �jfV���@�CcrD��<         y  �� ��  �� �� �� ��  �� ��   �� ��  �� �� ��     Aqq ����  �� ��u �����z\V4��@Ķ�"A8�?_�
:EIMqu U Y ]   q  u u   q u         TWrapNonExceptionThrows     
 Intercom       Copyright ©  2019  ) $83e0f3b2-872b-4a62-8076-aa6792e82405   1.0.0.0  M .NETFramework,Version=v4.7.2 TFrameworkDisplayName.NET Framework 4.7.2   @ 3System.Resources.Tools.StronglyTypedResourceBuilder4.0.0.0  Y KMicrosoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator11.0.0.0           �   ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet           PADPADP�   �   ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet           PADPADP�       �ZN�       a   <7  <                             RSDSŋ��j�O��?}o�m�   C:\Users\<USER>\Source\Repos\Intercom\Intercom\obj\x86\Debug\Intercom.pdb �7          �7                          �7            _CorExeMain mscoree.dll        �%  @                              �   P  �                  8  �                   �                     h  �                   �  �@            4   V S _ V E R S I O N _ I N F O     ���                 ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       �|   S t r i n g F i l e I n f o   X   0 0 0 0 0 4 b 0      C o m m e n t s       "   C o m p a n y N a m e         : 	  F i l e D e s c r i p t i o n     I n t e r c o m     0   F i l e V e r s i o n     1 . 0 . 0 . 0   : 
  I n t e r n a l N a m e   I n t e r c o m . e x e     H   L e g a l C o p y r i g h t   C o p y r i g h t   �     2 0 1 9   *   L e g a l T r a d e m a r k s         B 
  O r i g i n a l F i l e n a m e   I n t e r c o m . e x e     2 	  P r o d u c t N a m e     I n t e r c o m     4   P r o d u c t V e r s i o n   1 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   1 . 0 . 0 . 0   �C  �          ﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>

<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity version="1.0.0.0" name="MyApplication.app"/>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>                                                                                           0     �7                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      