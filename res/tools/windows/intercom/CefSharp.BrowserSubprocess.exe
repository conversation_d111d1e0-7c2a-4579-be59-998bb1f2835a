MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L �(�\        � "0           �2       @    @                       �     $  `�                           02  O    @  �
                   `     �0                                                               H           .text   �                           `.rsrc   �
   @                    @  @.reloc      `      $              @  B                d2      H     d"                 x0  �                                   0 �     s  
(  
r  p(  
}  %r  p(  
,;r1  p(  
(  
}  rU  p(  
,(  
�  s  
o  
&ry  p(  
,>r�  p(  
-	s  
+
{  s  

		o  
�,o   
�(!  
*    � 	�     0 -      }   ("  
}   }  {   (  +*($  
*2{  (  *  0 �     {  
,O {  (%  
o&  
�&�  �  ('  
o(  
()  
-<%
}  }  |  (  +�]{  |  �  %
}  (+  
�  (,  
��}  |  (-  
��}  |  (.  
*            ��   6|  (/  
*  BSJB         v4.0.30319     l   (  #~  �  �  #Strings    `
  �   #US      #GUID     �  #Blob         W	
   �3      '                  /                                lI �I m i   �~ O~ 0~ �~ �~ �~ �~ �* ~ �k �= k
 �� �� 
k �I �I RI k � I �I �k �
 �� E k  Wk S k ^} �} wk �} �� � k � Ok    Y         c�A       A    !   ]    � � G� �� � � > �P     � r� $!    � $�  ]!    �  ]!    �  e!    �    t!    ��  T"    ��       x   �    �  a 	     
 )  1  9  A  I  Q  Y  a  i  q  y  �  �  � � � �   �  � m2 � 
6 � �@ � :F � [K �U [ 	�a � �@ � m !x � �� )2 � �� � @� � g� �  1� � 1 �� � � � � � � � � ; 96� � �� � E � �   .  �.  �.  �. # �. + �. 3 �. ; �. C �. K . S %. [ �. c Q. k �. s a@ { �c � �� � �� � �& � �   #   % �  I  �      � �             � b     I  �       � �    I  �       �                � k        G � U �      <>c__DisplayClass0_0 <Main>b__0 <AwaitParentProcessExit>d__1 <>u__1 IEnumerable`1 Int32 <Module> mscorlib System.Collections.Generic parentProcessId GetProcessById AwaitUnsafeOnCompleted get_IsCompleted IDisposable IAsyncStateMachine SetStateMachine stateMachine ValueType CefSharp.BrowserSubprocess.Core Dispose Parse Create <>1__state CompilerGeneratedAttribute DebuggableAttribute ComVisibleAttribute AssemblyTitleAttribute AsyncStateMachineAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute DebuggerHiddenAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute AssemblyCopyrightAttribute CLSCompliantAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute GetArgumentValue CefSharp.BrowserSubprocess.exe System.Runtime.Versioning String Task Program System Main Action System.Reflection SetException Run CefSharp AsyncVoidMethodBuilder <>t__builder IRenderProcessHandler CommandLineArgsParser TaskAwaiter GetAwaiter .ctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices DebuggingModes args System.Threading.Tasks CefSharp.Internals TaskCreationOptions WcfEnabledSubProcess ExecuteProcess CefSharp.RenderProcess CefSharp.BrowserSubprocess Object WaitForExit AwaitParentProcessExit GetResult SetResult Environment HasArgument Start EnableHighDPISupport MoveNext StartNew Delay get_Factory TaskFactory op_Equality op_Inequality  
- - t y p e !c r a s h p a d - h a n d l e r #- - h o s t - p r o c e s s - i d #- - c e f s h a r p e x i t s u b r e n d e r e r  - - w c f - e n a b l e d  �h�=/��B�g� J�        M aEII  	 u  	 u  ��  ������
 Eu Eu    U  U0 
ei �� ��  e  
0 
e  i�z\V4��@Ķ�"A8�� $  �  �      $  RSA1     ����cʎi]K�Z�f4�HۚA���P��u���Z�b`e���w�3��  l!9���锣%���;c�j}�� �V8�?ُJ3����� �׿�{r�7'����mf{�R�q�~O»�х�Ue         TWrapNonExceptionThrows       CefSharp.BrowserSubprocess       The CefSharp Authors  
 CefSharp  + &Copyright © 2019 The CefSharp Authors   
73.1.130.0  M .NETFramework,Version=v4.5.2 TFrameworkDisplayName.NET Framework 4.5.2D ?CefSharp.BrowserSubprocess.Program+<AwaitParentProcessExit>d__1        ��.- <�gMa��,@h6B"�I	x�r��4 )r� �ZOXBY���,;�,�
u
v�yk��w�)��)__�=U��Z����82"�G��ְX_�Yn}�N�e){���/V)�}*w@�ݟ
    �(�\         1    RSDSR�(	�C��`r�v   C:\projects\cefsharp\CefSharp.BrowserSubprocess\obj\x86\Release\CefSharp.BrowserSubprocess.pdb                                                                                                                                                                      X2          r2                          d2            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                              �   P  �                  8  �                   �                     h  �                   �  �@  �          �4   V S _ V E R S I O N _ I N F O     ���    I   �  I   � ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       �N   S t r i n g F i l e I n f o   *   0 0 0 0 0 4 b 0      C o m m e n t s       J   C o m p a n y N a m e     T h e   C e f S h a r p   A u t h o r s     ^   F i l e D e s c r i p t i o n     C e f S h a r p . B r o w s e r S u b p r o c e s s     6   F i l e V e r s i o n     7 3 . 1 . 1 3 0 . 0     ^   I n t e r n a l N a m e   C e f S h a r p . B r o w s e r S u b p r o c e s s . e x e     p &  L e g a l C o p y r i g h t   C o p y r i g h t   �   2 0 1 9   T h e   C e f S h a r p   A u t h o r s   *   L e g a l T r a d e m a r k s         f   O r i g i n a l F i l e n a m e   C e f S h a r p . B r o w s e r S u b p r o c e s s . e x e     2 	  P r o d u c t N a m e     C e f S h a r p     :   P r o d u c t V e r s i o n   7 3 . 1 . 1 3 0 . 0     >   A s s e m b l y   V e r s i o n   7 3 . 1 . 1 3 0 . 0     �D  �          <?xml version="1.0" encoding="utf-8"?>

<asmv1:assembly
    manifestVersion="1.0"
    xmlns="urn:schemas-microsoft-com:asm.v1"
    xmlns:asmv1="urn:schemas-microsoft-com:asm.v1"
    xmlns:asmv2="urn:schemas-microsoft-com:asm.v2"
    xmlns:asmv3="urn:schemas-microsoft-com:asm.v3"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <assemblyIdentity version="73.1.130.0" name="CefSharp.BrowserSubprocess.app" />
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <!-- UAC Manifest Options
        If you want to change the Windows User Account Control level replace the 
        requestedExecutionLevel node with one of the following.
        <requestedExecutionLevel  level="asInvoker" uiAccess="false" />
        <requestedExecutionLevel  level="requireAdministrator" uiAccess="false" />
        <requestedExecutionLevel  level="highestAvailable" uiAccess="false" />

        Specifying requestedExecutionLevel node will disable file and registry virtualization.
        If you want to utilize File and Registry Virtualization for backward 
        compatibility then delete the requestedExecutionLevel node.-->
        <requestedExecutionLevel level="asInvoker" uiAccess="false" />
      </requestedPrivileges>
    </security>
  </trustInfo>

  <asmv3:application>
    <asmv3:windowsSettings xmlns="http://schemas.microsoft.com/SMI/2005/WindowsSettings">
      <dpiAware>true/PM</dpiAware>
    </asmv3:windowsSettings>
  </asmv3:application>

  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!-- A list of all Windows versions that this application is designed to work with. 
      Windows will automatically select the most compatible environment.-->

      <!-- Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}" />
      <!-- Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}" />
      <!-- Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}" />
      <!-- Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}" />
    </application>
  </compatibility>
</asmv1:assembly>
                                                                                                                                        0     �2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      