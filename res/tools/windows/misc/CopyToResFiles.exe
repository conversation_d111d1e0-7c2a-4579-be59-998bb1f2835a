MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L [7��        � " 0           f.       @    @                       �          `�                           .  O    @  �                   `     t-  8                                                             H           .text   l                           `.rsrc   �   @                    @  @.reloc      `                    @  B                H.      H     �"  �
                                                      0 �     �  %r  p�%r  p�
(  �, (  
o  
o  
s  

	o  
o  
 	o  
o  
 	o  
o  
 	o  
o  
 	o  
r  po  
 	o  
�o  
 	o  
&(  
  8B    r  p(  
r)  p�(  
(  
,T (  
 +/�	 	�  %|�o   

��(!  
  X�i2�("  
   ݿ   
 r  p(  
rC  p(#  
($  
�, r  p(  
rC  p(#  
(%  
& �  %�  %(&  


('  
�%r  p�%��%r�  p�%��((  
�r  p(  
r�  p(#  
()  
 
o*  
()  
 
o*  
(+  
  �  *      � �%�  0       (,  

s-  
    o.  
+ *"(/  
 * BSJB         v4.0.30319     l     #~  t  �  #Strings      ,  #US 0	     #GUID   @	  �  #Blob         G 	    �3                   /                 q      �y ?y G �   .� �� �� &� �� � E� Z � Z y� `� �
 �G �� � � �� �� ��
 � G
 
G � ~ $  ,� v$  $   @  � � � �              ��A   P     � ��  T"    � 1�  ~"    �A     �	 A  A  A
 ) A 1 A 9 A A A I A Q A Y A a A i A q A y A � �2 � � 7 � � < � A � �@ � �  � ] � X � 5 � 7  � � � /E � I � g N � �S � �Z � �_ � 
e � kl � � s � �x � �Z � p~ � P� � �< � �� � �� � [ < � � s � $� � A� � � � � A .  � .  � .  � . # � . + . 3 . ; . C � . K . S . [ . c &. k P. s ] � �                             � .                � �       IEnumerable`1 ConsoleApp4 <Module> System.IO mscorlib set_Verb System.Collections.Generic get_Message GetEnvironmentVariable File IsInRole WindowsBuiltInRole Console get_MainModule ProcessModule get_FileName set_FileName DateTime WriteLine Delete GuidAttribute DebuggableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute set_UseShellExecute ConsoleApp4.exe System.Runtime.Versioning ToString System.Security.Principal WindowsPrincipal Program System Main System.Reflection Exception get_StartInfo ProcessStartInfo DirectoryInfo Char IsAdministrator .ctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices DebuggingModes CopyToResFiles ReadAllLines AppendAllLines args GetCurrentProcess set_Arguments Exists Concat Object Split Exit Environment GetCurrent Start set_RedirectStandardOutput get_Now set_CreateNoWindow Copy CreateDirectory WindowsIdentity   r u n a s  L o c a l A p p D a t a  / P h o n e C h e c k /  ;/ P h o n e C h e c k / C o p y F i l e s E r r o r L o g  7:   E r r o r   C o p y i n g   F i l e s   S R C :      D E S T :    a/ P h o n e C h e c k / C o p y F i l e s E r r o r L o g / C o p y T o R e s F i l e s . t x t     �$'WvK���3v�.       EIM  E  ]    a            u  M 	 yQU  Q Q ���z\V4��           TWrapNonExceptionThrows      ConsoleApp4       Copyright ©  2021  ) $ff48ac98-c8e2-404e-b644-0f67fa3262e2   1.0.0.0  M .NETFramework,Version=v4.7.2 TFrameworkDisplayName.NET Framework 4.7.2     ��       h   �-  �                             RSDS�tJ��I��O�3���   C:\Users\<USER>\source\repos\ConsoleApp4\ConsoleApp4\obj\Debug\ConsoleApp4.pdb <.          V.                          H.            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                          �   P  �                  8  �                   �                     h  �                   �  �@  ,          ,4   V S _ V E R S I O N _ I N F O     ���                 ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       ��   S t r i n g F i l e I n f o   h   0 0 0 0 0 4 b 0      C o m m e n t s       "   C o m p a n y N a m e         @   F i l e D e s c r i p t i o n     C o n s o l e A p p 4   0   F i l e V e r s i o n     1 . 0 . 0 . 0   @   I n t e r n a l N a m e   C o n s o l e A p p 4 . e x e   H   L e g a l C o p y r i g h t   C o p y r i g h t   �     2 0 2 1   *   L e g a l T r a d e m a r k s         H   O r i g i n a l F i l e n a m e   C o n s o l e A p p 4 . e x e   8   P r o d u c t N a m e     C o n s o l e A p p 4   4   P r o d u c t V e r s i o n   1 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   1 . 0 . 0 . 0   �C  �          ﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>

<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity version="1.0.0.0" name="MyApplication.app"/>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>                                                                                 h>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      