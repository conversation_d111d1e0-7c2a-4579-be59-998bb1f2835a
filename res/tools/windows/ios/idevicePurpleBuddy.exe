MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� �=�g � [  � & (    N     �        @                       �    �  `                                             �  t   �  �   P  �           �  �                           �A  (                   �  (                          .text   �                       `  `.data      0      $              @  �.rdata  �   @      &              @  @.pdata  �   P      4              @  @.xdata  $   `      8              @  @.bss    �   p                      �  �.idata  t   �      <              @  �.CRT    `    �      H              @  �.tls        �      J              @  �.rsrc   �   �      L              @  �.reloc  �    �      R              @  B/4      �   �      T              @  B/19     +�   �   �   Z              @  B/31     �#   �  $                 @  B/45     6!   �  "   D             @  B/57     �
        f             @  B/70              r             @  B/81        0     v             @  B/97     �   P     �             @  B/113    �   p     �             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H��5  1��    H��5  �    H��5  �    H��4  f�8MZuHcP<HЁ8PE  tfH�?5  �
�_  � ��tC�   �I  �  H��5  ���  H��5  ���d  H�}4  �8tP1�H��(Ð�   �  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�5  �l
  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H�u5  L��^  H��^  H�
�^  � ��^  H�5  D�H��^  H�D$ �]  �H��8��    ATUWVSH�� H�o4  H�-�q  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5<4  1�����V  �����  �^     ����L  ���e  H�a3  H� H��tE1��   1����  H�
a4  �Cq  H��3  H�
����H��}  �  ��]  �{Hc�H��H����  L�%�]  H�Ņ��F  H��1�I��  H�pH���  I��H�D I�H��H���K  H9�u�H�H�    H�-]]  �  H��2  L�B]  �
L]  H� L� H�7]  �  �
]  �]  ����   � ]  ��ttH�� [^_]A\�f�     H�5�2  �   ���������   �  ��������H��2  H�
�2  �Y  �   �������1�H�����f�     ��  ��\  H�� [^_]A\�f.�     H��2  H�
�2  �   ��  �7���f�H����������	  �H��(H��1  �    ������H��(� H��(H��1  �     �z�����H��(� H��(�  H���H��(Ð�����������H�
	   �����@ Ð��������������H��(H�
�+  ��  H�
�+  ��  H�
�+  ��  H�
�+  H��(��  �     VSH��(H��H�
�+  H���  �   ��o  H���  H���=  H���M  1��  �ff.�     f�VSH��hH��+  H�5�+  fHn�fHn�H�5�+  fl�H��+  fHn�D$0fHn�L�L$(H�D$(    fl�D$@�   ��uIH�L$(H��t?H�T$0H�\$0H�t$P��   H��uH��H9�tH�L$(H��   H��t�   �@ 1�H��h[^�ff.�     @ UWVSH��(H�=0+  H�5(+  H��H��@ �   �&  I��H��H��������t�H��H����������������%Jm  ���%:m  ���%*m  ���%m  ���%
m  ���%�l  ���%
o  ���     H��(H��  H� H��t"D  ��H��  H�PH�@H��  H��u�H��(�fD  VSH��(H��.  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^�S��� 1�fD  D�@��J�<� L��u��fD  �JY  ��t�D  �6Y     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H��-  �8t�    ��t��tN�   H��([^�f�H��x  H�5�x  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�<+  Hc�H����    H� *  �DA �y�qH�q�   ��  �DD$0I��H��*  �|$(H��I���t$ �  �t$@|$P1�DD$`H��x[^ÐH��)  ��    H��)  ��    H��)  �s���@ H�*  �c���@ H��)  �S���H�3*  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�  A�   �   H�
2*  I���  H�t$(�   ��  H��H��I���
  �x  ��    WVSH��PHc5&W  H�˅��  H�W  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H��V  H��H��H�H�x �     �#  �WA�0   H�H��V  H�T$ H�L��i  H���}   �D$D�P����t�P���u�_V  H��P[^_� ��H�L$ H�T$8A�@   �   DD�H5V  H�KI��H�S�i  ��u���h  H�
S)  ���d���@ 1��!���H��U  �WH�
�(  L�D�>���H��H�
�(  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%�U  E��tH�e[^_A\A]A^A_]�fD  �~U     �9	  H�H��H��   H����  L�-*  H�$*  �NU      H)�H�D$0H�CU  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5�)  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
(  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  ��S  ������H�5�f  1�H�}�D  H��S  H�D� E��t
H�PH�HI����A��H��(D;%fS  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5P'  �s�;H��L�>H���Z����>L9�r��������H�
&  �����H�
�%  ���������H��XH�eR  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
)R  �  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H��%  Hc�H��� 1ҹ   �  H���>  H���  H��Q  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �D  H���@����   �   �+  �f�     1ҹ   �  H��t*H�������   ���i���f�     �   ���T����   �   ��
  �@����   �   ��
  �,����   �   �
  �����������ATUWVSH�� L�%�P  L����b  H�P  H��t6H�-c  H�=�b  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%�b  WVSH�� �+P  ��H�օ�u
1�H�� [^_ú   �   �9
  H��H��t3H�pH�5P  �8H���[b  H��O  H��H��O  H�C�Xb  묃��멐VSH��(��O  �˅�u1�H��([^�D  H�5�O  H���b  H�
�O  H��t'1��H��H��tH���9�H�Au�H��tH�B�	  H����a  1�H��([^� H�AO  ��ff.�     @ SH�� ����   w0��tL�O  ����   �O     �   H�� [�f�     ��u��N  ��t��<�����f.�     ��N  ��uf��N  ��u�H��N  H��t�    H��H�[��  H��u�H�
�N  H��N      ��N      ��`  �l����k����   H�� [������f�     H�
iN  ��`  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H����  H��w{H��!  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���f  ��u�H��H�� [^_��    1�H��H�� [^_� H�i!  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H��   1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L��   1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H�)   1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H��  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L�Y  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������H��(i��  ��\  1�H��(Ð��������H��8E1�L�D$ I��H��1��  H��8Ð�VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H���  ���   ����  �[  � ��Z  H� H��  H� H�M��t	A�$��  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���P  ���   ����  ��  � ���  H� H��  H� H�M��t	A�$�s  1�H�� [^_]A\�fD  SH�� H����  ���    HD�H�� [�f�H�	  �8 t1�Ð�  ff.�     SH�� �˹   �  A��H��  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H�%  ��~   H�W  �j   H�C  �V   H�/  H��(�f.�     H��(H��  ��>   H�  �*   H�  �   H��  H��(Ð����������%"[  ���%"[  ���%"[  ���     �%�Z  ���%�Z  ���%�Z  ���     �%zZ  ���%zZ  ���%zZ  ���%zZ  ���%zZ  ���%zZ  ���%zZ  ���%zZ  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���%�Y  ���     �%Y  ���%Y  ���%�X  ���     �%�X  ���%�X  ���%�X  ���%�X  ���%rX  ���%rX  ���%RX  ���%BX  ���%2X  ���%"X  ���%X  ���%X  ���%�W  ���%�W  ���%�W  ���%�W  ��AWAVAUATUWVSH��8��H���6���H�D$     H�D$(    ���  E1�   L�5!  Hc�H��L�,�    D�;A��-�   �{duy�{ us�   ������9��H�L$(L�����������   H�L$(H�T$ L�/  ��������   �   �TX  A� ���H�  H������H�L$(��������WL��H���������z���A��-u�{utIH�g  H���b�����uN�d/M��tA�<$ t
���L���D  ����1�H��8[^_]A\A]A^A_À{ t��1�H�L$(�$������/���H�
E  �X����f���M��t�L��H�
�  �����M���H�
�  �.����   �kW  H������H�T$(H�L$ �T�������������������������������P- @           ��������                                                                                                                                �- @           ��������        ����                           ����            ' @           0' @           �' @           pq @   xq @    ) @   `) @   �' @   �( @   @( @   �' @   �0 @   �0 @   �0 @      �p  �0 @   �0 @   PDT PST �( @   �( @                                                                                                                                                                                                                                                           idevicePurpleBuddy      Show information about device setup screen availability . PhoneCheck 2.0 Developed By AaterAli  Hello GuessedCountry    WebKitAcceleratedDrawingEnabled HSA2UpgradeMiniBuddy3Ran setupMigratorVersion  com.apple.PurpleBuddy --debug --udid     No device found with udid %s, is it plugged in?
        No device found, is it plugged in? ideviceinfo  ERROR: Could not connect to lockdownd, error code %d
 [connected]               @ @                            � @   � @   lp @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ����<�������\���l���|���L���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      ������������������������������k���        runtime error %d
               00 @           @0 @           `- @              @           �L @           �L @           �A @           �0 @            � @           �p @           hp @           dp @           `p @            p @           �p @           @p @           Hp @            � @           � @           � @           (� @           pp @            0 @           �p @           � @           � @           Pp @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                                                                                                                            `    .  `  0  y  `  �  �  `  �  �  $`  �  
  D`    $  d`  0  <  l`  @  A  p`  P  �  t`  �  �  |`  �  �  �`  �  �  �`  0  j  �`  p  �  �`  �  �  �`       �`    ?  �`  @  �  �`  �  �  �`  �  �  �`  �  �  a  �  Y  a  `  �  a  �  -  $a  0  n  <a  p  |  Da  �  =   Ha  @   �   Pa  �   !  `a   !  �!  la  �!  �"  xa  �"  �"  �a  �"  0#  �a  0#  �#  �a  �#  P$  �a  P$  �$  �a  �$  %  �a  %  F%  �a  P%  �%  �a  �%  �&  �a  �&  '  �a  '  .'  �a  0'  x'  �a  �'  �'  �a  �'  �'  �a  �'  :(  �a  @(  �(  �a  �(  �(  �a  �(  �(  �a  �(  )   b   )  U)  b  `)  �)  b  �)  �)  b  �+  M-  �`  P-  U-   b                                                                                                                                                                                                                                                                                                                                                                      B   b  
 
20`pP�	 B  �*     �  �  �  �  	 B  �*     �    �     B         B   B0`   �0`   B0`pP  	 b0`
p	P����   B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   B   b   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ��          P�  �  (�          ��  P�  ��          ��  ��  ��          �  ��  ��          �  �  Ё          0�  ��  �          ��  �  x�          ܊  ��  ��          �  �  ��          <�  �  �          `�  0�                      @�      P�      `�      |�      ��      ��              ԅ      �      �      �      0�      H�      f�      n�      |�      ��              ��      ��              ��      Ά      ؆      ��              �              ��      �               �      .�      <�      J�      T�      n�      ��      ��      ��      ��      ԇ      �       �      �      2�      :�      B�              L�      ^�      n�      |�      ��      ��      ��      ƈ              Έ      ؈      �              �      ��      �      �               �              @�      P�      `�      |�      ��      ��              ԅ      �      �      �      0�      H�      f�      n�      |�      ��              ��      ��              ��      Ά      ؆      ��              �              ��      �               �      .�      <�      J�      T�      n�      ��      ��      ��      ��      ԇ      �       �      �      2�      :�      B�              L�      ^�      n�      |�      ��      ��      ��      ƈ              Έ      ؈      �              �      ��      �      �               �              f idevice_free  k idevice_new   m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_get_value   DeleteCriticalSection =EnterCriticalSection  tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf  � fflush  � fwrite  � puts  � strcmp  � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset     plist_dict_get_item    �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  �  �  �  �  �  �  �  �  KERNEL32.dll    (�  (�  api-ms-win-crt-environment-l1-1-0.dll   <�  <�  <�  <�  api-ms-win-crt-heap-l1-1-0.dll  P�  api-ms-win-crt-math-l1-1-0.dll  d�  d�  api-ms-win-crt-private-l1-1-0.dll   x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  ��  api-ms-win-crt-string-l1-1-0.dll    ��  ��  ��  ��  api-ms-win-crt-time-l1-1-0.dll  Ȁ  libplist-2.0.dll                                                                                                                                                        0 @                    @                   @ @    @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                                h�   0  0    �P�`�p�������������������ȠР���� �   @  H   �������� �0�@�P�`�p�����������Х�� �� �0�@�P�`�p��������� �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                            ,               @   $                      <    �&       P @   �      �+ @   �                      ,    �6       0 @   �                           6=                           8C                       ,    �C         @                              FE                       ,    �E        @   �                           �M                           !N                       ,    �O       � @   �                       ,    �R       � @                              iS                       ,    �S       � @   =                      ,    Fk       0 @   L                           ;n                       ,    �n       � @   �                      ,    �~       @  @   b                          w�                            �                       ,    Њ       �" @   �                          ��                       ,    @�       �& @                          ,    �       ' @                          ,    ��       0' @   H                       ,    b�       �' @   2                           �                       ,    ��       �' @                                                                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(p @   argv B  	 p @   �  �  envp C  	p @   Jargret E�   mainret F�   	p @   managedapp G�   	p @   has_cctor H�   	p @   startinfo IR  	p @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 p @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(p @   	Q	 p @   	X	p @   	w 	p @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   :   �  GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden �  �  �           9  char {   long long unsigned int long long int short unsigned int int long int unsigned int _iobuf !
   _Placeholder #    !	FILE /�   unsigned char double float long double long unsigned int {   signed char short int 	plist_t Y  �   '`  IDEVICE_E_SUCCESS IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y 	idevice_error_t 0�    2 �    	idevice_t 3�  x  �   �  �   $�  LOCKDOWN_E_SUCCESS LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ 	lockdownd_error_t P�  =  R)�  =  	lockdownd_client_t S#�  �  _Float16 __bf16 j  plist_dict_get_item �
�    �  �   lockdownd_get_value ��  S  �  �  �  S   �  "exit � l  �    lockdownd_client_free ��  �  �   sleep .�   �  �    fflush s�   �  �     �  idevice_free �`  �  �   fprintf ��   	  �  �   __acrt_iob_func ]�  2	  �    lockdownd_client_new_with_handshake ��  r	  �  r	  �   �  printf ��   �	  �   idevice_new �`  �	  �	  �   �  #idevice_set_debug_level l�	  �    strcmp ?�   �	  �  �   IsValueAvailable z�   � @   �       �  /  z)�  �  �  
domain z=�  �  �  
key zQ�      node }
�  ��extrakeys   ��$I @   ?       �
  
i ��   0  (  h   
data ��  z  v  ] @   �  x @   �    ; @     R�RQ�QX�XY��  %�  "  &�    PrintHello q� @   C       ��  /  q$�  �  �  6  q7�  �  �  � @     �  R	x@ @    � @   	  �  R1 � @   �  � @   l  �  Rt  � @   �  �  Rs  � @   X  R0  PurpleBuddyCrawler a� @   H       ��  /  a,�  �  �  6  a?�  �  �  x   
isdata g
�   �  �  � @   �  u  R2 � @   �	  �  Rs Qu Xt  � @   "  Rs Qv    main "�   �+ @   �      �=  
argc "�     �  
argv "�  3  +  /  $�  ��'ldret %�  �~6  &�  ��
ret '`  e  Y  
i (	�   �  �  
udid )�  �  �  (node *
�   �+ @   .  , @   �	  �
  R1 , @   �	  �
  R��Q|  9, @   2	  �
  Q��X	cA @    L, @   	  �
  R2 a, @   �    Q	pA @   X � k, @   �  {, @   �	  C  Rs Q~  �, @   �	  h  Rs Q	�@ @    �, @   i  �  R  v ]  u  �, @   �	  �  R��Q0  - @     �  R	@A @    - @   w	  �  R	A @   Q|  *- @       R	�A @    5- @   	  "  R1 =- @   �  M- @   �   )print_usage 
i  argc �   argv *�   *=  P @   8       �  ]    	  R      ` @     �  R	 @ @    l @     �  R	@ @    x @     �  R	R@ @    +� @     R	a@ @     ,puts __builtin_puts 	 -__main __main  ]     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 E  -  0 @   �       �  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	0p @   atexit ��   �  Y   __main 5� @          ��  � @   �   	__do_global_ctors  p @   j       �  
nptrs "�   C  =  
i #�   [  W  � @   j  R	0 @     	__do_global_dtors 0 @   :       �[  p [  	 0 @    	   �   X	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 -    �  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
V  �   �,  __uninitialized  __initializing __initialized  V  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	0 @   �  	0 @   =  
"	Hp @   [  	@p @    �    
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ;  #  K  _dowildcard  �   	 0 @   int  }   <
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �    @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �     @          � �    v
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 v  ^  �  _newmode �   	Pp @   int  �   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �   @   �       	  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	lp @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�A @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�A @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	hp @   __mingw_initltsdyn_force ��   	dp @   __mingw_initltssuo_force ��   	`p @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@   @   /       �}  
n  �  v  r  
�  *M  �  �  
y  ;d  �  �  5 @   �   __tlregdtor m�   � @          ��  func m  R __dyn_tls_init L@  	  n  �  �  *M  y  ;d  pfunc N
$  ps O
�    �  @ @   �       ��  �  �  �  �  �  �     �  �  �  �  p @    p @   +       L�  �  $     �  5  3  �  A  =  �  T  P  �  h  d   � @   �    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  +
  _commode �   	pp @   int  w   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  e
  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 A	  )	  � @   �       �
  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   � @   �       �0  pexcept 0  �  �  type 
�  �  �  = @   b  �  R2 f @   7  Q	C @   Xs Yt w �ww(�ww0�w  5   �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  � @          ]  _fpreset 	� @          � �    L  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  �  __mingw_app_type �   	�p @   int  G   z  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 3  i  � @   =      �  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	�p @   	�  maxSections �%  	�p @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �� @   ]      ��  4was_init �%  	�p @   5mSecs �%      !�  U @   �   �4  �  �  �  6�   
�  .    
�  �  g  
�  �  �  
�  /  )  

  S  G  
  �  �  "E  �   �  
F  �  �  
[  	  	  i @   `  R	hD @   Xu w t     � @   � @          �;  �  ?	  =	  �  J	  H	  �  Y	  W	    � @   � @          �  c	  a	  �  n	  l	  �  }	  {	  � @   �  Ru    !  � @   �   ��  �  �	  �	  �  �	  �	  �  �	  �	  7  � @   �   �  �	  �	  �  �	  �	  �  �	  �	  � @   �  Ru      w @   w @   
       �w  �  �	  �	  �  �	  �	  �  �	  �	    w @   w @   
       �  �	  �	  �  �	  �	  �  
  
   @   �  Ru      � @   � @          �   �  
  
  �  "
   
  �  1
  /
    � @   � @          �  ;
  9
  �  F
  D
  �  U
  S
  � @   �  Ru    "$  �   �  
)  c
  ]
  83  �   
4  }
  {
    � @   � @   
       s�  �
  �
  �  �
  �
  �  �
  �
    � @   � @   
       �  �
  �
  �  �
  �
  �  �
  �
   @   �  Rt      
  @   `    R	8D @    - @   `  R	 D @      9�  � @   X       �|  
�  �
  �
  :�  �� @   
  Yu    @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �` @   b      �`  &addr ��  �
  �
  b �:  ��h �g  )    i �%  Z  T  >@ @   P       �  new_protect �
u  s  q  
t @   
  �  Ys  ~ @    
  � @   `  R	�C @     
� @   �
  �  Rs  � @   n
  
 @   E
    Q��X0 
� @   `  >  R	�C @    � @   `  R	�C @   Qs   ?__report_error T� @   i       �/  &msg T    {  @argp ��   �X
 @     �  R2 
6 @   /  �  R	`C @   Q1XK 
E @       R2 
S @   �
  !  Qs Xt  Y @   �
   Afwrite __builtin_fwrite   �   R  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  0 @   L       o  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�p @   
__setusermatherr ��  �   __mingw_setusermatherr �p @          �P  f ,�  �  �  | @   �  R�R  __mingw_raise_matherr �0 @   >       �typ !�   �  �  name 2�  �  �  a1 ?w   �  �  a2 Jw   �  �  rslt 
w   � ex 0  �@i @   R�@   �    d  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 d
  L
    _fmode �   	�p @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �
  � @   �      W  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	�p @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   � @   �      ��  'exception_data �-�      old_handler �
	  [  K  action ��   �  �  reset_fpu ��   &
  
  
� @   �
  �  R8Q0 ( @   �  R�R 
7 @   �
  �  R4Q0 M @   �  R4 
� @   �
    R8Q0 
� @   �
  7  R8Q1 
� @   �
  S  R;Q0 � @   f  R; � @   y  R8 
  @   �
  �  R;Q1 
  @   �
  �  R4Q1 
3  @   �
  �  R8Q1 )8  @   �
   �	   �
     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  @  @   b      �  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 q @   __mingwthr_cs_init �   	�p @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	�p @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �! @   �       �n  	hDllHandle z�  
  g
  	reason {<  �
  �
  	reserved |S  }  e   %" @   K       �  
keyp �&�  �  �  
t �-�       D" @   �  
k" @   C  R	 q @     !n  �! @   �! @          �  �  " @   )
   "n  " @     �E  #  �  �" @   )
    u" @   6  
�" @   e  R	 q @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�    ! @   �       �d	  	key A(<    
  
prev_key C�  8  2  
cur_key D�  W  O  P! @   �  B	  Rt  �! @   �  
�! @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �  @   o       �$
  	key *%<  ~  t  	dtor *1.  �  �  
new_key ,$
  �  �  �  @   �  �	  R1QH �  @   �  
  Rt  
! @   �  Rt   �  &n  @  @   p       ��      '�  x  @          �
  �      |  @     �  @     (�  @   Rt   Z  @   �  �
  R|  )�  @   �  R	 q @      �    q  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 9  !  \  _CRT_MT �   	00 @   int  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	Aq @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	@q @    �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  w  �" @   �      �  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  �  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  �  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  �  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   �  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �% @   �       �7
  i �(�   6  2  �  �	`  �  �	  I  E  importDesc �@  g  e  �  �^
  importsStartRVA �	I  w  o  �  �% @   	�  ��  �  �  �  �  �  	�  �% @    �  �  �  �  �  �  �  �  �  �      M  $& @   $& @   J       �q  �  �  f  }  �  �  �    �  �        
_IsNonwritableInCurrentImage �  P% @   �       ��  pTarget �%`  ,  $  �  �	`  rvaTarget �
�  Q  O  �  �^
  [  Y  �  P% @   �  �/  �  �  �  �  �  	�  `% @    �  �  �  �  �  g  c  �  x  v      M  �% @   �% @   I       �q  �  �  f  }  �  �  �  �  �  �  �  �    
_GetPEImageBase �`  % @   6       �0  �  �	`  	�  % @   �  �	�  �  �  �  �  	�   % @    �  �  �  �  �  �  �  �  �  �       
_FindPESectionExec y^
  �$ @   s       �%  eNo y�   �  �  �  {	`  �  |	  �  �  �  }^
  �  �  �  ~�   �  �  	�  �$ @   l  �	�  l  �  �  �  	�  �$ @    |  �  |  �  �     �  �           
__mingw_GetSectionCount g�   P$ @   7       ��  �  i	`  �  j	      	�  P$ @   Q  m	�  Q  �  �  �  	�  `$ @    a  �  a  �  �  )  %  �  :  8       
__mingw_GetSectionForAddress Y^
  �# @   �       �  p Y&s  L  D  �  [	`  rva \
�  q  o  �  �# @   +  _�  �  +  �  �  �  	�  �# @    ;  �  ;  �  �  }  y  �  �  �      	M  	$ @   F  c
q  �  �  f  F  }  �  �  �  �  �  �  �  �     
_FindPESectionByName :^
  0# @   �       �M  pName :#�  �  �  �  <	`  �  =	  
    �  >^
      �  ?�   !    �  E# @      F  �     �  �  �  �  U# @    U# @          �  �  �  ,  *  �  6  4     &?# @   �  -  Rt  '�# @   z  Rs Qt X8  _FindPESection $^
  �  �  $`  (rva $-�  �  &	  �  '^
  �  (�    _ValidateImageBase   �  �  `  pDOSHeader �  �  	  pOptHeader v   )�  �" @   ,       �~  �  D  @  �  V  R  �  �  	�  �" @      �  j  d    �  �  �  �  �  �  �     *M  �" @   P       �f  �  �  +q  Q}  �  �  �  �  �  �  �  �    �    Y  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 t  \    _MINGW_INSTALL_DEBUG_MATHERR �   	@0 @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 $    �& @          U  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char short int DWORD ��   float signed char double long double _Float16 __bf16 Sleep      sleep .�   �& @          �seconds "�   .  *   ' @   k  	R�R
�   �     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  ' @          �  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	P0 @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  ' @          �_File *�  N  H  _Format J�  g  a  _ArgList Z�   �  z  )' @   �  R0Q�RX�QY0w �X   �   J  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  0' @   H       <  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	`0 @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  0' @   H       �_Format .�  �  �  
ap 
�   �Xret 	  �  �  \' @   �  �  R1 q' @   �  R0Xs Y0w t    �   �  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �' @   2       �  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	p0 @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  �' @   2       �_File )�  �  �  _Format I�  �  �  
ap 
�   �hret 	    �  �' @   �  R0Q�RX�QY0w �   �      	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  [  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	xq @   local__winitenv 
`  	pq @   '  
	�0 @     
	�0 @    �   �   "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �' @         �  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	 1 @   �      �   __imp_at_quick_exit g)  	�0 @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	�0 @   
initial_tzname1 }
V  	�0 @   
initial_tznames ~�  	�0 @   
initial_timezone 
%  	�0 @   
initial_daylight �  	�0 @   __imp_tzname ��  	�0 @   __imp_timezone ��  	�0 @   __imp_daylight ��  	�0 @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�0 @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�0 @   �	  __imp__amsg_exit �V  	�0 @   F  __imp__get_output_format �\
  	�0 @   -
  __imp_tzset ��  	�0 @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�0 @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf    ) @   5       ��
  file �!�
      fmt �6�  8  2  
ap ��   �h+ret �  S  Q  P) @   �
  R4Q�RX�QY0w �  ,tzset "`) @   6       �  -  d) @   d) @   -       �r) @   +  ~) @     �) @       ._tzset �/_get_output_format nF  �' @          �0_amsg_exit i�( @   .       ��  ret i  _  [  ) @   z  �  R2 ) @   Q  �  Q	�D @   Xs  ) @   <  R�  at_quick_exit �  �( @          �   func ]*�  r  n  1�( @   �   _onexit ��  �( @          �p  func V%�  �  �  �( @   �  Rs   __wgetmainargs J  @( @   j       �[  _Argc J"�  �  �  _Argv J5I  �  �  _Env JGI  �  �   �  JQ       !  Jl�	  � `( @   0  p( @   	  &  R	v  $0.# u( @   �  ~( @   �  �( @   �  �( @   U   __getmainargs >  �' @   j       �E  _Argc >!�  %    _Argv >1S  >  8  _Env >@S  W  Q   �  >J  p  j  !  >e�	  � �' @   �   ( @   �    R	v  $0.# ( @   �  ( @   �  ( @   u  -( @   U   2  �) @   6       ��) @   +  �) @     �) @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   ( 
  I ~   I  H}  $ >   !I  .?:;9'I<  H }  	 :;9I  
4 :!;9I�B   :!;9I�B  .?:;9'I<  
 :!;9I�B  H}  >!!I:;9!  ( !    :;9I   <  7 I     .?:!;9!'I@z  4 :!;9I  U  .?:!;9!'@z  4 :!;9I  I �~   :!;!9I   1�B  %U  & I  :;9   
 :;9I8  !   ".?:;9'�<  #.?:;9'<  $  %I  &! I/  '4 :;9I
  (4 :;9I  ).:;9'   *.1@z  +H}�  ,. ?<n:;  -. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   %  4 :;9I?  $ >   $ >  %   :;9I  .?:;9'<   I  .?:;9'I@z   :;9I�B  H}  	I ~    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                   5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �    ^   �
        [  t  �  
�   �   �  �  �  �        "   
 	P @   L��	�u	I� �g=s=Y �Y��Nh9
vX�� X	L
X"^�"YX   J"ZX"�[Xf/CX�o�	�	�
L�  	�+ @   "���� �@( Y	t<J� <
g�  <J�	� X	� �� <Y�y( aX$ ��- �) �
KX Xs<�Y(.t  T���
��	U
Y�X� �Y #    K   �
      v  �  �  �  �  �  �  �       	0 @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      _  w  �  �  �  �  �    
    6     .   �
      m  �  �  �  R     .   �
        #  J  T   	  @    6     .   �
      �  �  �  �      K   �
      E  ]  �  �  �  �  �  �  �  �   	 @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      5  M  t    6     .   �
      �  �  	  	  �     <   �
      o	  �	  �	  �	  �	  �	  �	   	� @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      0
  H
  o
  z
   	� @   		3 6     .   �
      �
  �
    #  |    s   �
      �  �  �  �  �  �        "  *  7  @  H  T  e  n  w    	� @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	� @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      �  �  
  )
  7
  E
   	0 @   
L�Z
�KTYZ
g=yuX 6     .   �
      �
  �
  �
  �
  �    U   �
      8  P  w  	�  �  �  �  �  �  �  �  �   	� @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      E  ]  �  �  �  �  �  �  �  �  �  �  �     	@  @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      j  �  �  �  6     .   �
        (  O  c  G    K   �
      �  �  �    "  +  5  A  K  S   	�" @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< 6     .   �
      �  �  �  �  q     A   �
      T  l  �  �  �  �  �  �   	�& @   Kfg n     A   �
      0  H  q  �  �  �  �  �   	' @   K
�<.Y �     A   �
        .  W  r  �  �  �  �   	0' @   g?	YT�Y	 X� <uX �     A   �
      �    :  U  d  s  |  �   	�' @   KU	\fp	\;Y	Y W     O   �
      �  �    5  `  l  t  �  �  �  �  |    h   �
      �    ;  V  �  �  �  �  �  �  �  �  �  �  �     	�' @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	�( @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                              ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �         P  P @   8       D0o  $   P  � @   C       A�A�D@   4   P  � @   �       A�A�D��A�A�      ,   P  � @   H       A�A�A �A(�DP l   P  �+ @   �      B�B�B �B(�A0�A8�A@�AH�	D�+
HA�@A�8A�0A�(B� B�B�B�A       ���� x �         �  0 @   :       D0u  4   �  p @   j       A�A�D@@
A�A�H       �  � @             ���� x �             @             ���� x �      $   @   @   /       D0R
JN    L   @  @ @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       @  � @             ���� x �      <   �  � @   �       A�A�D�P�
���
���A�A�B    ���� x �         @  � @             ���� x �      $   p  � @   i       A�A�DP   <   p  ` @   b      A�A�A �Dp�
 A�A�A�D   \   p  � @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         P  0 @   >       D`y     P  p @             ���� x �      4   �  � @   �      A�D0}
A�Mf
A�I     ���� x �      L   �  @  @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �  �  @   o       A�A�A �D@U
 A�A�A�A    D   �   ! @   �       A�A�D@R
A�A�FR
A�A�D      4   �  �! @   �       A�D0p
A�J�
A�A      ���� x �           �" @   ,            �" @   P       L     0# @   �       A�A�A �D@~
 A�A�A�HI A�A�A�         �# @   �            P$ @   7            �$ @   s            % @   6            P% @   �            �% @   �          ���� x �         @  �& @          D0R     ���� x �         x  ' @          D@Y     ���� x �      ,   �  0' @   H       A�A�D`A�A�   ���� x �         �  �' @   2       DPm     ���� x �         0	  �' @          L   0	  �' @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   0	  @( @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   0	  �( @          A�D0WA�    0	  �( @             0	  �( @   .       A�D0   0	   ) @   5       DPp     0	  `) @   6       D0q     0	  �) @   6       D0q                                                                                                                                                                                                                                                                                                                                  Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion idevice_private client device lockdownd_client_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection _DoWildCard _StartInfo                                                                                                                                                                                                                                                C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> idevicepurplebuddy.c C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/plist ../include/libimobiledevice idevicepurplebuddy.c idevicepurplebuddy.c stdio.h plist.h libimobiledevice.h lockdown.h stdlib.h unistd.h string.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/sleep.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include sleep.c sleep.c minwindef.h synchapi.h unistd.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                                                                                                                                      �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 p @   ���
 p @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� e            � @    ZRZ��R�     � @    ZQZ��Q�     � @    ZXZ��X�        X @    

s � #P3%�
s � #P3%#�s � #H3%#�%
s � #P3%�     ] @    P%P     � @    RCT     � @    QCS     � @    RHS     � @    QHV   � @   P         �+ @    R�V���R���V         �+ @    Q�U���Q���U            �+ @    _	��_|P��	����	����P��P              �+ @    1�gT��T��t���T��T��1���T    �+ @    0���0�   T @   4�]  �   T @   4�R  � 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
�B @   ���
`B @   ���
�B @   ���
�B @   ���
C @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ����b     ��U  ��2�  ����b     ��U  ��1�  ����b     ��U  ��1�  ����b     ��U  �	�	4�  �	�	��b     �	�	U  �	�	4�  �	�	��b     �	�	U  �	�	8�  �	�	��b     �	�	U  �	�	8�  �	�	��b     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
�>f     �
�
T  �
�
4�  �
�
�>f     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� �               
R
�R� T                RQ�R�        QX�Q�        X�`�X� )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                                                                                                                                              X         Z���� ���� ���� ���� ���� ���� ���� ?         I @    "? � @    9 P @   ��+ @   � S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����                                         .file   a   ��  gcrtexe.c              �                                �              �   �                        �   �                        �   �                        %  @                        @  �                        `             k  �                        �  `                        �                           �  �                        �  0          �  �                    envp           argv            argc    (                          p                        '  �          9                           ^                          �             �  p                        �  �                        �  �                          �                    mainret            "  0                        8                           N  P                        d  @                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  �     +                 .file   �   ��  g    �                m  P                           �  �          �  �          �  �      main    �      .text   P     �               .data                            .bss    0                        .rdata         �                .xdata  t      0                 .pdata  l      0                    �  �     �                   �  �                           �  �                          �  �&  
   >  f                 �  �     �                    �  �     i                   �  0      @                    �  \      C                      9     �                          7                       �     g                    )        +                     4  P     8  
             .text   �      .idata$7L	      .idata$5@      .idata$4      .idata$6�      .text   �      .idata$7H	      .idata$58      .idata$4      .idata$6�      .text          .idata$7D	      .idata$50      .idata$4      .idata$6|      .text         .idata$7@	      .idata$5(      .idata$4       .idata$6`      .text         .idata$7<	      .idata$5       .idata$4�       .idata$6P      .text         .idata$78	      .idata$5      .idata$4�       .idata$6@      .text          .idata$7\      .idata$50      .idata$4      .idata$6 	      .file   �   ��  ggccmain.c             �  0                       p.0                   p            0                    __main  �          1  0       .text   0     �                .data                          .bss    0                       .xdata  �                       .pdata  �      $   	                 �  �6  
   a                   �       ?                    �  1     5                     �  p      0                      �     '                     -     �                     )  P     +                     4  �     �                .file   �   ��  gnatstart.c        .text                           .data                          .bss    @                           �  6=  
     
                 �  X	     �                     �  �                             �     V   
                   V                                                     )  �     +                 .file     ��  gwildcard.c        .text                           .data                           .bss    P                            �  8C  
   �                    �  
     .                     �  �                             K     :                      #     �                     )  �     +                 .file   -  ��  gdllargv.c         _setargv                        .text                          .data   0                        .bss    P                        .xdata  �                       .pdata  �                          �  �C  
   �                   �  <
     :                     �  �      0                      �     V                      �     �                     )  �     +                     4       0                .file   A  ��  g_newmode.c        .text                          .data   0                        .bss    P                           �  FE  
   �                    �  v
     .                     �                              �     :                      ^     �                     )       +                 .file   y  ��  gtlssup.c              =                             L  @          [                      __xd_a  P       __xd_z  X           r  �      .text        �                .data   0                        .bss    `                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  �E  
   �  6                 �  �
     �                    �  f                        �  0     0                      	                          n                            �     �                     )  @     +                     4  @     �                .file   �  ��  gxncommod.c        .text   �                       .data   0                        .bss    p                           �  �M  
   �                    �  �     .                     �  `                            +
     :                      �     �                     )  p     +                 .file   �  ��  gcinitexe.c        .text   �                       .data   0                        .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  !N  
   {                   �  �     a                     �  �                            e
     :                      �     �                     )  �     +                 .file   �  ��  gmerr.c            _matherr�                       .text   �     �                .data   0                        .bss    �                        .rdata        @               .xdata  �                       .pdata  �                          �  �O  
   6  
                 �  
                         �  }     �                    �  �     0                      �
     �                      )	     �                     )  �     +                     4  �     X                .file   �  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   0                        .bss    �                        .xdata                        .pdata                           �  �R  
   �                    �       -                     �  �     0                      ]     X                      �	     �                     )   	     +                     4  @     0                .file   �  ��  gmingw_helpers.    .text   �                       .data   0                        .bss    �                           �  iS  
   �                    �  L     .                     �                               �     :                      �
     �                     )  0	     +                 .file   '  ��  gpseudo-reloc.c        ~  �                           �  `	          �  �       the_secs�           �  �
          �  �           �  P                          `                    .text   �     =  &             .data   0                        .bss    �                       .rdata  `     [                .xdata       0                 .pdata       $   	                 �  �S  
   K  �                 �  z     �                    �       �  
                 �        0                    �  �      W                       �     �                     �     	                       3     O                    )  `	     +                     4  p     �                .file   G  ��  gusermatherr.c         2  0                           H  �           V  p      .text   0     L                .data   0                        .bss    �                       .xdata  <                      .pdata  8                         �  Fk  
   �                   �  R                         �  �     r                     �  P     0                      o     �                      �     �                     )  �	     +                     4  P     P                .file   [  ��  gxtxtmode.c        .text   �                       .data   0                        .bss    �                           �  ;n  
   �                    �  d     .                     �  �                                 :                      L
     �                     )  �	     +                 .file   }  ��  gcrt_handler.c         m  �                       .text   �     �               .data   0                        .bss    �                       .xdata  H                      .rdata  �     (   
             .pdata  P                         �  �n  
   �                   �  �     ~                    �  �     _                    �  �     0                      W     �  
                   �                            �
                         )  �	     +                     4  �     P                .file   �  ��  gtlsthrd.c             �  @                           �             �  �           �  �          �  �           �               �      .text   @     b  "             .data   0                        .bss    �      H                 .xdata  P     0                 .pdata  \     0                    �  �~  
   �
  A                 �       a                    �  [
     �                    �  �     0                    �  �                             �     x                     �     %                    )   
     +                     4  �     (               .file   �  ��  gtlsmcrt.c         .text   �                       .data   0                       .bss    @                           �  w�  
   �                    �  q     .                     �                               \     :                      !     �                     )  P
     +                 .file   �  ��  g    $            .text   �                       .data   @                        .bss    @                          �   �  
   �                    �  �     0                     �                               �     :                      �     �                     )  �
     +                 .file     ��  gpesect.c              8  �                           K  �          Z  0          o  �          �  P          �  �          �            �  P          �  �      .text   �     �  	             .data   @                        .bss    P                       .xdata  �     ,                 .pdata  �     l                    �  Њ  
   �  �                 �  �     �                    �  &     �                    �  @     0                    �  
     �                       �     K                     �     T                       w     �                     )  �
     +                     4       (               .text   �     2                 .data   @                        .bss    P                       .text   �                       .data   @                        .bss    P                           )  �
     +                 .file     ��  gmingw_matherr.    .text   �                       .data   @                       .bss    p                           �  ��  
   �                    �  Y     .                     �  p                                 :                      \     �                     )       +                 .file   5  ��  gsleep.c           sleep   �                       .text   �                     .data   P                        .bss    p                       .xdata  �                      .pdata  �                         �  @�  
   �  	                 �  �     �                     �                            �  �     0                      U     u   	                        �                     )  @     +                     4  @     8                .file   S  ��  gucrt_vfprintf.    vfprintf                       .text                        .data   P                      .bss    p                       .xdata  �                      .pdata                           �  �  
   �                   �       8                    �  <     X                     �  �     0                      �     r   	                   �     �                     )  p     +                     4  x     8                .file   q  ��  gucrt_printf.c     printf  0                       .text   0     H                .data   `                      .bss    p                       .xdata  �                      .pdata                           �  ��  
   �  
                 �  J     l                    �  �     -                     �  �     0                      <     �   	                   �     �                     )  �     +                     4  �     H                .file   �  ��  gucrt_fprintf.c    fprintf �                       .text   �     2                .data   p                      .bss    p                       .xdata  �                      .pdata                           �  b�  
   �                   �  �     b                    �  �     F                     �        0                      �     �   	                   �     �                     )  �     +                     4  �     8                .file   �  ��  g__initenv.c             p            x      .text   �                       .data   �                      .bss    p                          �  �  
   �                   �        �                     �  P                            [     [                      �                         )        +                 .file   �  ��  gucrtbase_compa        %  �                           8  �          F  @      _onexit �          U  �          c  �                        �  �          �         tzset   `          �  �                    _tzset  �          �  �           �  �           �  �           �  �           �  �       .text   �       "             .data   �      x   
             .bss    �                       .xdata  �     P                 .pdata  (     l                .rdata  �                          �  ��  
   �  Y                 �  �                           �       |                    �  p     0                      �     �                     �                            �     `                    )  0     +                     4  0	     �               .text   �      .data         .bss    �      .idata$7,      .idata$5      .idata$4�      .idata$6�      .text   �      .data         .bss    �      .idata$70      .idata$5      .idata$4�      .idata$6�      .text   �      .data         .bss    �      .idata$74      .idata$5      .idata$4�      .idata$6	      .text   �      .data         .bss    �      .idata$78      .idata$5       .idata$4�      .idata$6	      .file     ��  gfake              hname   �      fthunk        .text                           .data                          .bss    �                       .idata$2�                      .idata$4�      .idata$5      .file   %  ��  gfake              .text                           .data                          .bss    �                       .idata$4                       .idata$5(                      .idata$7<                      .text          .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text         .data         .bss    �      .idata$7       .idata$5�      .idata$4�      .idata$6�      .text         .data         .bss    �      .idata$7      .idata$5�      .idata$4�      .idata$6�      .file   3  ��  gfake              hname   �      fthunk  �      .text                           .data                          .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   y  ��  gfake              .text                           .data                          .bss    �                       .idata$4�                      .idata$5                       .idata$7     !                 .text          .data         .bss    �      .idata$7�
      .idata$5�      .idata$4x      .idata$6L      .text   (      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6^      .text   0      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6n      .text   8      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6|      .text   @      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text   H      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text   P      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text   X      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   x      fthunk  �      .text   `                       .data                          .bss    �                       .idata$2�                      .idata$4x      .idata$5�      .file     ��  gfake              .text   `                       .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7�
                       .text   `      .data         .bss    �      .idata$7T
      .idata$5      .idata$4�      .idata$6       .text   h      .data         .bss    �      .idata$7X
      .idata$5      .idata$4�      .idata$6.      .text   p      .data         .bss    �      .idata$7\
      .idata$5       .idata$4�      .idata$6<      .text   x      .data         .bss    �      .idata$7`
      .idata$5(      .idata$4       .idata$6J      .text   �      .data         .bss    �      .idata$7d
      .idata$50      .idata$4      .idata$6T      .text   �      .data         .bss    �      .idata$7h
      .idata$58      .idata$4      .idata$6n      .text   �      .data         .bss    �      .idata$7l
      .idata$5@      .idata$4      .idata$6�      .text   �      .data         .bss    �      .idata$7p
      .idata$5H      .idata$4       .idata$6�      .text   �      .data         .bss    �      .idata$7t
      .idata$5P      .idata$4(      .idata$6�      .text   �      .data         .bss    �      .idata$7x
      .idata$5X      .idata$40      .idata$6�      .text   �      .data         .bss    �      .idata$7|
      .idata$5`      .idata$48      .idata$6�      .text   �      .data         .bss    �      .idata$7�
      .idata$5h      .idata$4@      .idata$6�      .text   �      .data         .bss    �      .idata$7�
      .idata$5p      .idata$4H      .idata$6       .text   �      .data         .bss    �      .idata$7�
      .idata$5x      .idata$4P      .idata$6      .text   �      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4X      .idata$62      .text   �      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4`      .idata$6:      .text   �      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4h      .idata$6B      .file     ��  gfake              hname   �      fthunk        .text   �                       .data                          .bss    �                       .idata$2x                      .idata$4�      .idata$5      .file   6  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4p                      .idata$5�                      .idata$7�
     "                 .text   �      .data         .bss    �      .idata$7(
      .idata$5�      .idata$4�      .idata$6�      .text   �      .data         .bss    �      .idata$7,
      .idata$5       .idata$4�      .idata$6      .file   D  ��  gfake              hname   �      fthunk  �      .text                           .data                          .bss    �                       .idata$2d                      .idata$4�      .idata$5�      .file   Y  ��  gfake              .text                           .data                          .bss    �                       .idata$4�                      .idata$5                      .idata$70
     "                 .text          .data         .bss    �      .idata$7
      .idata$5�      .idata$4�      .idata$6�      .file   g  ��  gfake              hname   �      fthunk  �      .text                          .data                          .bss    �                       .idata$2P                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text                          .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7
                      .text         .data         .bss    �      .idata$7�	      .idata$5�      .idata$4�      .idata$6�      .text         .data         .bss    �      .idata$7�	      .idata$5�      .idata$4�      .idata$6�      .text          .data         .bss    �      .idata$7�	      .idata$5�      .idata$4�      .idata$6�      .text   (      .data         .bss    �      .idata$7�	      .idata$5�      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   0                       .data                          .bss    �                       .idata$2<                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   0                       .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7�	                      .text   0      .data         .bss    �      .idata$7�	      .idata$5�      .idata$4�      .idata$6�      .text   8      .data         .bss    �      .idata$7�	      .idata$5�      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   @                       .data                          .bss    �                       .idata$2(                      .idata$4�      .idata$5�      .file     ��  gfake              .text   @                       .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7�	     &                 .text   @      .data         .bss    �      .idata$7�	      .idata$5�      .idata$4p      .idata$6�      .text   H      .data         .bss    �      .idata$7�	      .idata$5�      .idata$4h      .idata$6|      .text   P      .data         .bss    �      .idata$7�	      .idata$5�      .idata$4`      .idata$6n      .text   X      .data         .bss    �      .idata$7�	      .idata$5�      .idata$4X      .idata$6f      .text   `      .data         .bss    �      .idata$7�	      .idata$5x      .idata$4P      .idata$6H      .text   h      .data         .bss    �      .idata$7|	      .idata$5p      .idata$4H      .idata$60      .text   p      .data         .bss    �      .idata$7x	      .idata$5h      .idata$4@      .idata$6      .text   x      .data         .bss    �      .idata$7t	      .idata$5`      .idata$48      .idata$6      .text   �      .data         .bss    �      .idata$7p	      .idata$5X      .idata$40      .idata$6�      .text   �      .data         .bss    �      .idata$7l	      .idata$5P      .idata$4(      .idata$6�      .file   +  ��  gfake              hname   (      fthunk  P      .text   �                       .data                          .bss    �                       .idata$2                      .idata$4(      .idata$5P      .file   9  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4x                      .idata$5�                      .idata$7�	     
                 .file   Z  ��  gcygming-crtend        	  P                       .text   �                       .data                          .bss    �                           �  P                         �                             �  �                         #	  h                         )  `     +                 .idata$2        .idata$5      .idata$4�       .idata$2�       .idata$50      .idata$4      .idata$4       .idata$5H      .idata$7P	      .idata$4      .idata$58      .idata$7`      .rsrc       
    __xc_z             0	  �          O	  �          Z	  @          s	  �          	  �	          �	  �          �	              �	  x          �	  �           �	  `          �	  d           !
  `          =
  �          _
            k
  �          x
  �          �
            �
  �           �
      	        �
            �
  �          �
  �       __xl_a  0              x          
  `          0  �      _cexit  x          D  `  ��       \     ��       u  <          �              �            �      ��       �     ��         0             P      __xl_d  @           0  x      _tls_end   	        U  0      __tzname�          k  @          x            �             �  �           �  0           �  �          �  �          �      	    memcpy  �          �  0          
  �      puts    X          *
  `          P
  �       malloc  (      _CRT_MT 0           i
  P          u
  �          �
              �
  �          �
  �          �
            �
     ��       �
  �            `          &  p          :  X          _  h           y  �          �  8          �  �           �  
          �  0          �  �            `       fflush  H             H          /  (           b             o  P           �  p          �  �          �  �          �  �      abort   �          �  P            �           $  P       __dll__     ��       4      ��       I  h          T  P	          s            �  �          �  @           �  �          �            �  @          �            
  P           9             K  P          W     ��       m             �  0
      calloc            �  �          �  @          �  �           �               �      Sleep   X          #  �      _commodep           4            A  �          N  `          \  �          p  <           �  �           �             �  �      __xi_z  (           �            �                            �          7  0          B             `  X          {  l       signal  �          �             �  H           �              �         strncmp           �  `          �  p            �            �           ?  @          Y  �          z      ��       �  @          �  8          �  �
          �               �            �          (  �          G  �           \  �          �            �     ��       �  h          �  8          �  �          �  �            p            �          )  �           V              u  P           �     ��       �              �  �           �  0          �  �           �            �  (      __xl_z  H       __end__                �          #  x          E         strcmp             S  x      __xi_a             a  �          p  �          |  h      __xc_a              �  �          �     ��       �  P           �     ��   _fmode  �           �  �          �  �                                   )  `          :  p           H  �          ]             n  �          �  H          �  �	          �  `          �  �          �        __xl_c  8           �     	          �            �          *  h          :  d           S              _  �
          �  �          �  �          �  �	      _newmodeP           �  �      fwrite  P          �  �          �  �            �                ��       4      ��       E  �          R  @           h  (          u  �      exit    �          �     ��       �  �          �      ��       �  p          �  @      _exit   �          �  `                          �      strlen            )  P          8  �          D             i  �            �          �  (          �  0          �  x             �          (  �           9             M  0          \  �           u  P           �  8          �  �           �  p      free               �  �       �  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame print_usage.isra.0 PrintHello IsValueAvailable PurpleBuddyCrawler .text.startup .xdata.startup .pdata.startup idevicepurplebuddy.c __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force idevice_new __imp_calloc __imp___p__fmode __imp___p___argc __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __rt_psrelocs_start __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler TlsGetValue __imp_strcmp __bss_start__ __imp___C_specific_handler ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection __C_specific_handler lockdownd_get_value .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf ___crt_xp_end__ __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp_idevice_new __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname _tls_used __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ .refptr._newmode __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ idevice_set_debug_level __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func __native_startup_state ___crt_xc_start__ lockdownd_client_free ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_lockdownd_get_value .refptr.__mingw_initltsdyn_force __rt_psrelocs_size .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp___p___wargv __imp_strlen __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __p__wenviron _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_plist_dict_get_item _head_libplist_2_0_dll __IAT_start__ __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit __DTOR_LIST__ __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp_TlsGetValue __imp___p__wenviron __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_VirtualProtect idevice_free ___tls_end__ .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ __imp_fflush __minor_image_version__ __imp___set_app_type __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv plist_dict_get_item .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_daylight __p___wargv __mingw_app_type 