MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� Am�g R �  � & ( @   ~     �        @                            |  `                                             �  $   �  �   �  $             �                           �e  (                   8�                             .text   h>      @                 `  `.data   p   P      F              @  �.rdata  P   `      J              @  @.pdata  $   �      ^              @  @.xdata  �   �      b              @  @.bss    �   �                      �  �.idata  $   �      f              @  �.CRT    `    �      x              @  �.tls        �      z              @  �.rsrc   �   �      |              @  �.reloc  �          �              @  B/4      �        �              @  B/19     �       �              @  B/31     �)   0  *   �             @  B/45     �8   `  :   �             @  B/57     �   �     �             @  B/70     �   �                  @  B/81     �   �     
             @  B/97     �'   �  (   &             @  B/113    �        N             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H�[  1��    H�[  �    H�	[  �    H�lZ  f�8MZuHcP<HЁ8PE  tfH��Z  �
��  � ��tC�   ��5  �$5  H�m[  ���5  H�=[  ���  H��Y  �8tP1�H��(Ð�   �5  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
[  �  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H��Z  L�֎  H�׎  H�
؎  � ���  H��Z  D�H���  H�D$ �1  �H��8��    ATUWVSH�� H��Y  H�-p�  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5�Y  1�����V  �����  ��     ����L  ���e  H��X  H� H��tE1��   1����H  H�
�Y  �ˢ  H�$Y  H�
����H��
4  �0  �ҍ  �{Hc�H��H���p4  L�%��  H�Ņ��F  H��1�I���2  H�pH���C4  I��H�D I�H��H����3  H9�u�H�H�    H�-]�  �  H�!X  L�B�  �
L�  H� L� H�7�  �4  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�5YX  �   ���������   ��0  ��������H�MX  H�
6X  ��2  �   �������1�H�����f�     �2  ���  H�� [^_]A\�f.�     H�X  H�
X  �   �2  �7���f�H����������2  �H��(H�5W  �    ������H��(� H��(H�W  �     �z�����H��(� H��(��/  H���H��(Ð�����������H�
	   �����@ Ð��������������AW��  AVAUATUWVS��!  H)�L��$  H��H��M��H����  H�D$`    L�D$`��  ���N  H��$�   H��H���0  H���0  ���   /I��t�/   fB�?I��H��$�  L��H���0  H���0  ���  /H��t�/   f�D
 H��L�D$`I�L��H���:  �D$H    E1��D$4����M��L�|$8H�L$@�L$LH��$   �:.uI�z uCI��K� H�H��u�L���K  �D$H����  �D$4H�ĸ  [^_]A\A]A^A_��    �:.�G  H�D$8f��H�D$h    HǄ$�       H�D$p�$�   �$�   �/  H��	fD  � -�:   �0  H��H��u�H�D$`�.   J� H���0  H��H�D$@L�l H��tA�   H�J  �I/  ����  H��L���&/  H��$   L�D$hH���9  L�t$hM����  I��   L�-�I  L�=�I  H��uZ�   I�6H��I  H����.  ����  H��I  H���.  ���f  � @  f�D$v@ I�\6H��H����   L��H���{.  ���  L��H���h.  ��t�H��I  H���U.  ���U  H��I  H���>.  ���^  H��I  H���'.  ��u�H�
�  �'.  H�
�I  H�T �+  H��$�  H�����  ���
  D�ʈ  E����  L�t$h�D$4    I�\6H��H���8���fD  �\$vL��f�� ���
  f�� @�  f�� ��x  L�D$`�s���fD  �z.������z �Y��������    I�6�/  L�t$hH��$�   ����D  H�kH  H���1-  ����   �A� ���fD�\$v�����    L���X
  �D$4    ���� I�6�.  L�t$hf�D$x�H����     I�6�.  L�t$hH��H����&�.H��H��?H��H)�H��$�   �
���f.�     H���,  H��L��D�p�Mc��,  �D$LA�Mc�BƄ4�   �H���fD  H��G  H���Q,  ���  � 0  f�D$v����f�H��H�
G  �A)  L�D$`�����    H��$   tH��$  H���5-  H���d���H��$   L�L$XA�   H���  �Å��  ���6����   �Ӝ  I��A��H�6G  H���)  L�D$`����@ H���-  L��$  I��H��H��$   �]���D�
R�  L�D$`�D$4E���I���H��$   H���l  L�D$`�/���f.�     H�sF  H���)+  ��tdH�hF  H���+  ���v  �   f�\$v�g����    H��$   H���  ���� H���`,  �����L���  L�D$`�����    f�D$v����H�fF  H���+  I��H���t  H�
X�  L�t$TL��$�  �*  �
\�  H�E  L�D ��H�
�D  HE�H�
EF  1��i'  L�t$ H�T$XA�   H��$   M���D$T    �a
  H��$   ��t8�@ M���   L���x*  L�t$ M��H��H�T$XA�   \$T�!
  ��u
D�D$TE��u�H��$   H�T$X�
  L���*  ;�$�   uf���  ����   �D$H�D$4    ����H��D  H���)  ��������T����   ���  I��H�D  H����&  �D$4�����c����   �_�  A�    �   H�
-E  I���)  L�D$`�����   �0�  I��H��D  H���n&  H�T$XH��$   �<	  L�D$`�����H��$   H����  �+���ff.�     f�WVSH��0H��L�D$(H��H�D$(    H����  ��uiH�t$(H�=�D  H�H��H��u�O�    �{ u!H�H��H��t6H��H���)  H��t�;.tـ;.u�{.t&H�H���
�     H��u�H��0[^_��     �{ t����     SH��0H��L�D$(H��H�D$(    H���  ��uOH�D$(�   H�H��u�;�    �y uH�H��H��t!�9.t�9.t#�.(  H�D$(H�H��H��u�H��0[�fD  �y.u׀y t���f�AVAUATUWVSH��pL�D$(H��H�D$(    �  ����   L�l$(�D$@ H�D$0    H�D$8    I�] H����   L��H�l$PL�t$0E1�H�=]C  @ A�	   H��H���'  ��u[H���'  H��vMH�S	H��A�   �'  L��H���D$` ��&  ��~'L���/(  H��&  H��L��I���&  f�     H�^H��H��u�L����  L��H��p[^_]A\A]A^�f�     E1��� �   E1��z�  I��H�|B  H���#  �fD  AT�@  UWVS�   H)�L��L�L$8H��H��A�   �\  A�ą���   H��A  H���b&  H��H����   �D$4    H�\$@H�l$4�$f.�     D�D$4E��t/I���   H���.&  H�l$ H�T$8I��H��A�   ��  ��t�H����%  H�T$8H����  D��H��@  [^_]A\ù   ���  I��H��A  H����"  A������ʹ   �a�  I��H��A  H���"  H�T$8H���r  ��AWAVAUATUWVSH��X  L�D$@H��H��H�D$@    �$  A�ą��F  H�L$@H�|$H�   L�l$PH�|$8H�H��t�8.uC�x u=H�H��H��u��  H��H����  D��H��X  [^_]A\A]A^A_�f�     �8.u�x.u
�x t��    H�D$ �   L��I��L��@  �,!  L�D$8L��H��H�D$H    �k  ��u^L�|$HM��tTI�H��t9M�wH�=4?  f�H���$  ��uI�H�,?  ��#  ��t1I�NI��H��u�L���4  L��H����  H�L$@�
����    L���  H��L������H�L$@������   A��������  I��H��?  H����   �����ff.�     AWAVAUATUWVSH��X  H��$�  H�
�?  ��#  H��$�  L�D$HH��?  H�D$H    �W  Hc���9  L�|$HI�H����  I�w1�L�%�?  H��I���     A�   L���#  ���� I�M I��H��u߅��_  Hc�H��H��H�D$8�+$  I���fD  H�H��H��t6A�   L��H���"  ��u�H��H��D�u�w"  H�^�I�D� Ic�H��u�L��H�t$P�  M�M �   H��L�A?  �!  H��$�  H������H��H���i  H�D$ I��L��$P  L��>  �   L����  H��H��$P  �a#  1����  H���!  H�
�>  ���#  �l$(H��L��>  �t$ I���   �  H��$�  I��L���a����Å�uiH�
�>  A��H���  L����"  ��H��X  [^_]A\A]A^A_� �   �}�  A�   �   H�
)>  I���!  H�L$H�  �����뱹   �G�  A�    �   H�
m>  I���!  ��~H�\$8L��L�H�H���b"  H9�u�L���U"  몹   ���  A�   �   H�
v=  I���6!  냹   �ё  A�   �   H�
�=  I���!  ��~�H�t$8L��L�H�H����!  H9�u��D  UWVSH��8D��D��H��H��H�
k=  � "  �l$(H��H�ى|$ I��L�X=  �  �H��8[^_]Ð���������%z�  ���%j�  ���%Z�  ���%J�  ���%:�  ���%*�  ���%�  ���%
�  ���%��  ���%�  ���%ڎ  ���%ʎ  ���%��  ���%��  ���%��  ���%��  ���%z�  ���%j�  ��H��(H�E+  H� H��t"D  ��H�/+  H�PH�@H� +  H��u�H��(�fD  VSH��(H��D  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^���� 1�fD  D�@��J�<� L��u��fD  ��y  ��t�D  ��y     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H��C  �8t�    ��t��tN�   H��([^�f�H�A�  H�5:�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H��?  Hc�H����    H��>  �DA �y�qH�q�   �  �DD$0I��H�*?  �|$(H��I���t$ ��  �t$@|$P1�DD$`H��x[^ÐH��=  ��    H�I>  ��    H�>  �s���@ H�y>  �c���@ H�A>  �S���H��>  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(��  A�   �   H�
�>  I���  H�t$(�   �  H��H��I���-  �h  ��    WVSH��PHc5�w  H�˅��  H��w  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H�Uw  H��H��H�H�x �     �#  �WA�0   H�H�'w  H�T$ H�L�o�  H���}   �D$D�P����t�P���u��v  H��P[^_� ��H�L$ H�T$8A�@   �   DD�H�v  H�KI��H�S��  ��u��ʊ  H�
�=  ���d���@ 1��!���H��v  �WH�
X=  L�D�>���H��H�
$=  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%4v  E��tH�e[^_A\A]A^A_]�fD  �v     �9	  H�H��H��   H����  L�-�?  H��?  ��u      H)�H�D$0H��u  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5n?  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
d<  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  �>t  ������H�5��  1�H�}�D  H�!t  H�D� E��t
H�PH�HI����A��H��(D;%�s  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5 =  �s�;H��L�>H���Z����>L9�r��������H�
}:  �����H�
9:  ���������H��XH��r  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
�r  �  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H�\:  Hc�H��� 1ҹ   ��  H���>  H���  H�Zr  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �4  H���@����   �   �  �f�     1ҹ   �  H��t*H�������   ���i���f�     �   ���T����   �   ��  �@����   �   �  �,����   �   �  �����������ATUWVSH�� L�%/q  L���ބ  H��p  H��t6H�-�  H�=Ԅ  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%��  WVSH�� ��p  ��H�օ�u
1�H�� [^_ú   �   �I  H��H��t3H�pH�5�p  �8H���;�  H�\p  H��H�Rp  H�C�@�  묃��멐VSH��(�<p  �˅�u1�H��([^�D  H�59p  H����  H�
	p  H��t'1��H��H��tH���9�H�Au�H��tH�B�  H���̃  1�H��([^� H��o  ��ff.�     @ SH�� ����   w0��tL��o  ����   ��o     �   H�� [�f�     ��u�mo  ��t��<�����f.�     �Ro  ��uf�Ho  ��u�H�4o  H��t�    H��H�[��  H��u�H�
0o  H�o      �o      �ł  �l����k����   H�� [������f�     H�
�n  ���  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���  H��w{H��7  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���6  ��u�H��H�� [^_��    1�H��H�� [^_� H�97  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H��6  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L�y6  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H��5  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H��5  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L�)5  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������AUATUWVS��D�Ɖ�L��)�)։�������   �� A�ՙA��D���u�D��)șA��E��~lHc�A��A�L�׉څ�~Yf�     M�1��f�     M��A��D�2A)�9�D��AL҃�Lc�N��M�M�M�9�u�A��I��E9�tD��뱐[^_]A\A]�A���u���ff.�     @ WVSH��0H�t$XL�L$hH��H�T$XL�D$`H�t$(�p  �   H� H�8��
  H�a1  I��H���&  �   ��
  I��H��H���^
  �   ��
  �
   H����
  �H��0[^_�ff.�      AWAVAUATUWVSH��H�v  H�=c  H��$�   H��M��H��$�   �=   �D$<��L��$�   �D$8�;  �J  I��H���  H��I��H)�M�4$M����   1�L�D$0I�\$ E1�D��$�   �L$(�����A���*Hc�H��L��P9S�tV�D$(   L�3H�� A��M��tfI��L��H���  ��u�L���  H9���   H��uE��u��u�D���fD  H�PH9S�u��@9C��   DD$(�D$(��     �L$(L�D$0��uKA����ux��$�   ���  �6  ���.  �      �?   H��H[^_]A\A]A^A_�fD  D�
  E��t�H��$�   �8:t�I����H�
0  �����@ L�D$0Ic�H��L�k��uRM��teD��  E��tH��$�   �8:��   H�{ �l  H��$�   �-  �8:�Z����:   �U���@ �U���wM����   L�hg  H��$�    tH��$�   D�8H�S�CH�������1��
���f�     H��H�D$(�  L�D$(H�������fD  H��$�   �8:�����H��H�
�.  ���������I����H�
�.  �����%������d���HcT$8H��$�   D�t$<H��A��D�5�  H��f  H���3����
�  ��t)H��$�   �8:tH��H�
�.  �E����[  ���D$81�H�{ tB�A  �D$8�;  H��$�   �8:�
��������k�����D$<�  �����������C��     AWAVAUATUWVSH��HD��$�   A��H��M��M��M���y  D�
�  ��  E����  �����  ��e  ����  A�E <-�  D�r  E����  <+��  H��e      ����   �H  �����B  ����f.�     �^e      �<  D9���  Hc�H�t� �>-��  H��,  H�  A����  A����  �5�  ����5  �=�  ���t%��I��A�؉������)�����  ����)���  ����  H�=�  ����Z���M��tkHc�  H9|� t]<-�r  A���w  <:�k  ��L���	  H�������D$ M��L��H��L��$�   �����Ã����  H�=-  �L���L�=  <:�,  ��-��   ��L���!	  H���  �PM��t��Wu	��;��  ��:��   � ��  ��  �}  f���c     ��     1�E1�H�
8+  �
w  ��c  �������v  A�E <-�����A��I�������@ �F����  �-   L���o  H���0����=7  �t
�=*  ���  L�~H���-   L�=  � �8  �-   L���&  H����   �x:�%���H�c      � ��  ��  L�=�b  H��*  ��H��  ��  �s�     ��  �����D  A���<+�#��������    �5v  H�3*  �=m  H�n  ���uP���t�=j  �H  �����B  �����������H��H[^_]A\A]A^A_�f���H�5Fb  �,  �   �ԉ�)�A��I���)��&����  �L���L�=�  <:������ u��  �
�  ��tA�} :��   ��  �?   �q���fD  A����2����
�  ���t
�=�  ��  H�~H�=�  <-������~ ������5`  ��H�)  �h  H�U  ��������A�؉�I��L$<�[����L$<)�)�9  ������     ��H�
�(  �����G���H��H�=  1������ ��   ��  ����  D9�|e��  H��(  H��  ��tA�} :t�W   H�
b)  ������  W   A�} :������:   �I����|  �S����q  �����H�H�D� H�j  �D$     M��L��H��L��$�   �������H��'  H�<  ������x:�\������0  A9�~Hc�H�D� H�0`  �:���H��'  H��  �  ��tA�} :t��H�
�(  �������  A�} :�
����0����     H��8E1��D$(    H�D$     ����H��8�ff.�      H��8H�D$`�D$(   H�D$ ����H��8�H��8H�D$`�D$(   H�D$ �e���H��8�H��8E1�L�D$ I��H��1��o  H��8Ð�H��HH�D$hL�L$hM��I��H�D$(H�ʹ   H�D$     H�D$8�D  H��HÐ������VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H����  ���   �����  �  � ��  H� H��  H� H�M��t	A�$�C  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���  ���   ����X  �+  � ��2  H� H��/  H� H�M��t	A�$��  1�H�� [^_]A\�fD  SH�� H���  ���    HD�H�� [�f�H�)'  �8 t1�Ð��  ff.�     SH�� �˹   �?  A��H�&  H���m�����   ��  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8�  H��H�ff.�     H��(H�E&  ��   H�g
  �z   H�S
  �f   H�?
  H��(�f.�     H��(H�&  ��N   H�'
  �:   H�
  �&   H��  H��(Ð����������%r  ���     �%�q  ���%�q  ���%�q  ���%�q  ���%zq  ���%zq  ���%zq  ���%zq  ���%zq  ���%zq  ���%�p  ���%�p  ���%�p  ���%�p  ���%�p  ���%�p  ���%�p  ���%�p  ���%�p  ���%�p  ���%�p  ���     �%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���%�o  ���     �%:o  ���%:o  ���%:o  ���%:o  ���%:o  ���     �%�n  ���     �%�n  ���%�n  ���%�n  ���%�n  ���%�n  ���%�n  ���%�n  ���     �%Bn  ���%Bn  ���%Bn  ���     �%
n  ���%
n  ���%�m  ���%�m  ���%�m  ���%�m  ���%�m  ���%�m  ���%�m  ���%zm  ���%jm  ���%Zm  ���%Jm  ���     AWAVAUATUWVSH���  ��H�����������  H�D$@    �$   L��$�   H�5-  L��L�-�  E1�E1��H��D$8    H�=n  H�5p  H�D$H    H�D$P    f�M��I��H���H�D$     ����A����,  A��dA��wJJc�H�����W     �H��#  L�0A�> u��   �n  A�(   �   H�
�  I���O���A�   �  H��#  ��W     ��W     L�(�Y���H�r#  �tW     L�(�@����nW     �1����cW     �"���H�;#  L�8A�? �����   �sm  A�*   �   H�
  I�������]���H��"  �W     L�(������   �!��������D$8   ����H��"  Hc H�L� ��V  H���  ����  �|$8H�L$@L��D�T$<����D�@�����D�T$<��D�T$8��  H�L$@H�T$HL�.  ����D�T$8������  H�L$HH�t$XE1�D�T$8H�O  I��L�L$XH���I���D�T$8���!  1�H�T$XL�D$`D�T$8H�L$`H�L$@����H�L$X������E1���D�T$8L�D$X�
  H�L$HH�/  I��D�T$8H�������D�T$8����  H�L$HD�T$8�����1�H�L$@L�D$PH�T$PH�T$X����D�T$8����  H�L$XH��tD�T$8����1�D�T$8H�D$X�=hU   ��   �=SU   H�L$P�u  �=MU   ��   �=HU   D�T$8uD����H�L$@�b���E1�D��H���  [^_]A\A]A^A_�H��  H�
�  �5���A�   ��L��T  M��L����������c  H�
�  �g���H�L$P돋�T  �T  �����D�T$<H�T$`H�
�T  ��i  D�T$<�������H�sT  �   ��j  H��  H��I��������l���H�L$PD�T$8����D�T$8�����E1�H��H�
�  D�T$8����H�T$PE1�H�
�  �}���H�T$PE1�H�
�  �i����=�S   D�T$8��   H�
�  D�T$8����H�L$PD�T$8����E1�H��L��D�T$8������=�S   D�T$8u{H�L$P�`������������S  �S  ������   ��i  A�!   �   H�
�  I����������H�
  D�T$8������=HS   D�T$8�N���H�L$P�����H�L$PL��D�T$8�)���H�
�  L���Z���H�L$PD�T$8�����������   H���6i  I��I��H�  H���q���H�L$H�����H�L$@�
���D�T$8����M����   L��H�
8  �����D�T$8�����   ��h  A��H�\  H������H�L$@����D�T$8�Q����   ��h  A�0   �   H�
  I�������H�L$P�����H�L$@�w���D�T$8����H�
�  �����D�T$8������������������������������������0N @           ��������                                                                                                                                                                                                                                                                                                                                                                                                                                8e @                   d       >e @                   h       Ce @                  u       He @                   l       Me @                   n       Ue @                  f       \e @                   e       de @                   k                                       `N @           ��������        ����                           ������������    (i @   ?                     ����             B @           @B @           �B @           �B @           �� @   �� @   pD @   �D @   C @   @D @   �C @    C @   8R @   <R @   @R @      �p  TR @   PR @   PDT PST  D @    D @                                                                                                                                                                                                                                                                                                                                                                                                                           Copy Move       ERROR: Could not read device directory '%s'
 .synced    Failed to read information for '%s'. Skipping...
 st_size st_ifmt S_IFREG S_IFDIR S_IFLNK S_IFBLK S_IFCHR S_IFIFO S_IFSOCK st_nlink st_mtime LinkTarget Link: %s
       Unable to open device file '%s' (%d). Skipping...
 wb   Unable to open local file '%s'. Skipping...
 %s: %s
    File size mismatch. Skipping...
 panic-full Failed to read directory %s
 powerlog_      Failed to open device file: %s
 Failed to open local file: %s
 %s/%s Read Data ... /    Failed to read root directory
 ProxiedDevice- No Watch directories found.
 /%s No watch files found!
 TEMP      %s\latest_powerlog_%ld_%d.PLSQL Success! [_%ld_%d]
     Failed to move watch logs file.
  COPYRIGHT PHONECHECK LLC 2025 %s - Ver 2.0
   ERROR: UDID argument must not be empty!
        ERROR: filter argument must not be empty!
 dhu:p:nlwr:f:e:k     ERROR: missing target directory.
       ERROR: Directory '%s' does not exist.
  No device found with udid %s.
 No device found. idevicecrashreport      ERROR: Could not connect to lockdownd, error code %d
 com.apple.crashreportmover        ERROR: Could not start service %s: %s
  com.apple.crashreportcopymobile Deleted Directory %s
 . /Panic /Retired SUCCESS_NO_PANIC ERROR_PANIC    ERROR: Failed to get crash reports from device.
 Files Copied!. debug help udid list network filter extract keep    �������������������������s���d�������������K������(��������������������    �& @                            � @   � @   �� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  (�������t�����������������Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      ��������������������0�������p���0������                        %s:     P O S I X L Y _ C O R R E C T           unknown option -- %s            unknown option -- %c                            option doesn't take an argument -- %.*s         ambiguous option -- %.*s                        option requires an argument -- %s                               option requires an argument -- %c                               runtime error %d
               PQ @           �Q @           @N @              @           Pr @           Pr @           �e @           �Q @           8� @           �� @           �� @           �� @           �� @            � @           � @           p� @           x� @            � @           � @           � @           (� @           �� @           @Q @           � @            . @           �' @           �� @           �� @           |Q @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                                                                                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P  �  t�  �  H  ��  P  �  ��  �    ��     @   ��  @   �!  Ȑ  �!  �$  ��  �$  7%  ��  �%  
&   �  &  z&  (�  �&  �&  4�  �&  �&  8�  �&  �&  <�  �&  a'  D�  p'  s'  P�  �'  x(  T�  �(  �(  l�  �(  �(  p�   )  b*  |�  p*  �-  ��  �-  .  ��  .  .  ��   .  �/  ��  �/  P0  ��  P0  �0  đ  �0  A1  Б  P1  B2  ܑ  P2  |2  �  �2  �2  �  �2  m3  �  p3  �3  ��  �3  '4  ��  04  �4   �  �4  �4  �  �4  y5  �  �5  F6  �  �6  Q7  �  `7  �7   �  �7  x;  ,�  �;  �A  D�  �A  �A  \�  �A   B  d�   B   B  l�   B  >B  t�  @B  yB  |�  �B  �B  ��  �B  C  ��  C  C  ��   C  �C  ��  �C  �C  ��   D  D  ��   D  5D  Ē  @D  nD  Ȓ  pD  �D  В  �D  �D  ؒ  �D  &E  ��  �G  )N  �  0N  5N  �                                                                                                                                                                                                                                  B   b  
 
20`pP�	 B  �F     �  �   .  �  	 B  �F     �     .     B        
 �0`pP
��	�� R0`p R0 �
0	`pP��� 0
`	pP�  
 K 0`
p	P����
 � 0`
p	P���� b0`pP  
 9 0`
p	P���� B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   0`pP�� R0`p	 �0`
p	P����  	 �0`
p	P����   b   b   b   b   �   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                         �          `�  8�  ��          ��  д  �          ��  0�  (�          �  H�  H�          $�  h�  h�          \�  ��  ��          ��  ��  ��          ��  ��  в          �  �  `�          l�  ��  ��          ��  �  ��          ܿ  �  (�           �  H�                      X�      l�      ��      ��      ��      ��      ̷      �      ��      �      �      8�      T�      l�      ��      ��      Ը      �              �      �      4�      N�      ^�      z�      ��      ��      ��      ƹ      ع              �      �              ��      �      �              "�      ,�      6�              @�      P�      Z�      b�              l�              ��      ��      ��      ��      ��              ��      κ      ܺ      �      ��      �      &�      <�      J�      R�      t�      ��      ��      ��      һ      ڻ      �              �      ��      �      �      6�      R�      l�      v�      ~�      ��      ��              ��      ��      ��      ��      ��      ʼ              Լ      �      �      ��      �              �              X�      l�      ��      ��      ��      ��      ̷      �      ��      �      �      8�      T�      l�      ��      ��      Ը      �              �      �      4�      N�      ^�      z�      ��      ��      ��      ƹ      ع              �      �              ��      �      �              "�      ,�      6�              @�      P�      Z�      b�              l�              ��      ��      ��      ��      ��              ��      κ      ܺ      �      ��      �      &�      <�      J�      R�      t�      ��      ��      ��      һ      ڻ      �              �      ��      �      �      6�      R�      l�      v�      ~�      ��      ��              ��      ��      ��      ��      ��      ʼ              Լ      �      �      ��      �              �               afc_client_free    afc_client_new     afc_dictionary_free    afc_file_close    	 afc_file_open 
 afc_file_read  afc_get_file_info  afc_read_directory     afc_remove_path   f idevice_free  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_service_descriptor_free � lockdownd_start_service   � lockdownd_strerror    2service_client_new    DeleteCriticalSection =EnterCriticalSection  KGetEnvironmentVariableW tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery  W atoi  Y atoll  __p__environ   __p__wenviron  getenv   _mkdir  & _stat64 H remove   _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy  |strchr  }strrchr ~strstr   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf   __stdio_common_vsprintf � fclose  � fopen � fputc � fwrite  � puts  6 _strdup � strcmp  � strcpy  � strlen  � strncmp � strncpy 	 __daylight   __timezone   __tzname  7 _time64 < _tzset   rand   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  �  �  �  �  �  �  �  �  �  KERNEL32.dll    (�  (�  api-ms-win-crt-convert-l1-1-0.dll   <�  <�  <�  api-ms-win-crt-environment-l1-1-0.dll   P�  P�  P�  api-ms-win-crt-filesystem-l1-1-0.dll    d�  d�  d�  d�  api-ms-win-crt-heap-l1-1-0.dll  x�  api-ms-win-crt-math-l1-1-0.dll  ��  ��  ��  ��  ��  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  api-ms-win-crt-string-l1-1-0.dll    ܰ  ܰ  ܰ  ܰ  ܰ  api-ms-win-crt-time-l1-1-0.dll  �  api-ms-win-crt-utility-l1-1-0.dll                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       0 @                    @                   �& @   �& @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          @     H�   P  D    � �@�`�������� �p�������С����� ���� �(�0�@�H�X�`�   `  L   �����������������Ъ�� �� �0�@�P�`�p�����������Ы�� �� �0�@� �     � �8�@�                                                                                                                                                                                                                                                                                                                                                    ,               @   $                      <    �&       P @   �      �G @   �                      ,    bc       �% @   �                           �i                           �o                       ,    Rp       �& @                              �q                       ,    ]r       �& @   �                           $z                           �z                       ,    )|       �' @   �                       ,    _       �( @                              �                       ,    ��       �( @   =                      ,    ӗ       �- @   L                           Ț                       ,    P�        . @   �                      ,    1�       �/ @   b                          �                           ��                       ,    ]�       P2 @   �                      ,    /�       �6 @   �                          k�                       ,    	�        B @                          ,    ��       @B @   9                       ,    K�       �B @   H                       ,    ��       �B @   2                           ��                       ,    B�       C @                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   �<   �  2GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden   �  U          9  char !{   size_t #,�   long long unsigned int long long int short unsigned int int long int __time64_t {#�   time_t ��   unsigned int _iobuf !
D  _Placeholder #D    3FILE /  unsigned char double float long double 	�   long unsigned int _ino_t +�   _dev_t 3
  &optind �   &optarg �  	{   !�  short int option  ><  name @A   has_arg A�   flag B�  val C�    !�  	�   "A  
  G�  no_argument  required_argument optional_argument  	�  "�  4signed char _Float16 __bf16 'JOB_OBJECT_NET_RATE_CONTROL_FLAGS 
  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	�  (tagCOINITBASE 
  ��  COINITBASE_MULTITHREADED   'VARENUM 
  		V  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � uint8_t 
$S  uint16_t 
&�   uint32_t 
(
  uint64_t 
*0�   _stat64 8S
f  st_dev T�   st_ino U�  st_mode V�   st_nlink W�  st_uid X�  
st_gid Y�  st_rdev Z�  st_size [�   st_atime \�    st_mtime ]�   (st_ctime ^�   0 �   '.  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 0f  �  2 R  �  idevice_t 3i  	F  (idevice_options 
  9�  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  �   
$�
  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ lockdownd_error_t 
P�  +  
R)�
  +  lockdownd_client_t 
S#  	�
  lockdownd_service_descriptor 
`j  port 
af   ssl_enabled 
b
V  identifier 
c�   lockdownd_service_descriptor_t 
e.�  	  �   "g  SERVICE_E_SUCCESS  SERVICE_E_INVALID_ARG SERVICE_E_MUX_ERROR }SERVICE_E_SSL_ERROR |SERVICE_E_START_SERVICE_ERROR {SERVICE_E_NOT_ENOUGH_DATA zSERVICE_E_TIMEOUT ySERVICE_E_UNKNOWN_ERROR �~ service_error_t +�  �  -'�  �  service_client_t .!�  	  �   &5  AFC_E_SUCCESS  AFC_E_UNKNOWN_ERROR AFC_E_OP_HEADER_INVALID AFC_E_NO_RESOURCES AFC_E_READ_ERROR AFC_E_WRITE_ERROR AFC_E_UNKNOWN_PACKET_TYPE AFC_E_INVALID_ARG AFC_E_OBJECT_NOT_FOUND AFC_E_OBJECT_IS_DIR 	AFC_E_PERM_DENIED 
AFC_E_SERVICE_NOT_CONNECTED AFC_E_OP_TIMEOUT AFC_E_TOO_MUCH_DATA 
AFC_E_END_OF_DATA AFC_E_OP_NOT_SUPPORTED AFC_E_OBJECT_EXISTS AFC_E_OBJECT_BUSY AFC_E_NO_SPACE_LEFT AFC_E_OP_WOULD_BLOCK AFC_E_IO_ERROR AFC_E_OP_INTERRUPTED AFC_E_OP_IN_PROGRESS AFC_E_INTERNAL_ERROR AFC_E_MUX_ERROR AFC_E_NO_MEM AFC_E_NOT_ENOUGH_DATA  AFC_E_DIR_NOT_EMPTY !AFC_E_FORCE_SIGNED_TYPE  afc_error_t D�  
  G�  AFC_FOPEN_RDONLY AFC_FOPEN_RW AFC_FOPEN_WRONLY AFC_FOPEN_WR AFC_FOPEN_APPEND AFC_FOPEN_RDAPPEND  afc_file_mode_t NI  D  ]#�  D  afc_client_t ^  	�  5target_directory ?
A  	0� @   extract_raw_crash_reports @�   	P� @   keep_crash_reports A�   	L� @   LIST_MODE B�   	H� @   WBAT_MODE C�   	D� @   ClEAN_MODE D�   	@� @   PRINT_MODE E�   	<� @   PANIC_FOUND F�   	8� @   mkdir �     A   remove ��   6  A   atoi ��   N  A   atoll �(�   g  A   strrchr ]�  �  A  �    strchr D�  �  A  �    memset 5D  �  D  �   �    _stat64 a�   �  A  �   	�  afc_client_free �
5  
  �   afc_client_new n
5  8  W  j  8   	�  lockdownd_service_descriptor_free 
>�
  r  j   service_client_new Ag  �  W  j  �   	�  lockdownd_client_free 
��
  �  �
   lockdownd_strerror 
G
A  �  �
   lockdownd_start_service 
��
  (  �
  A  (   	j  idevice_free �.  L  W   lockdownd_client_new_with_handshake 
��
  �  W  �  A   	�
  idevice_new_with_options �.  �  �  A  n   	W  getopt_long M�   �  �   �  A    �   	�  	<  6idevice_set_debug_level l-  �    getenv ��  G  A   _time64 ��   a  a   	�   printf ��   �  A   7rand ��   malloc D  �  �    afc_remove_path 
5  �  �  A   afc_get_file_info �
5  �  �  A  �   	�  snprintf G�   +  �  �   A   fclose j�   E  E   	F  "E  afc_file_read �
5  �  �  �  �  w  �   	w  fwrite ��   �  �  �   �   J   afc_file_close �
5  �  �  �   fopen E  �  F  F   afc_file_open �
5  $  �  A  �  $   	�  afc_dictionary_free r
5  P  �   strcpy =�  n  �  A   strdup l�  �  A   8free �  D   strncpy Y	�  �  �  A  �    strlen @�   �  A   strncmp V�   �  A  A  �    fprintf ��     J  F   __acrt_iob_func ]E  ?  
   strcmp ?�   ]  A  A   strstr `�  {  A  A   afc_read_directory �
5  �  �  A  �   main m�   �G @   �      �Y"  argc m�   �  �  argv m�    �  
device vW  ��|
lockdownd w�
  ��|
afc x�  ��|device_error z.  6  *  lockdownd_error {�
  �  l  #�  |5  �  �  udid ~A  -  +  use_network �   =  ;  #W  �A  M  K  custom_path_search �A  c  [  panic_path_root �A  �  �  panic_path_PanicDir �A  �  �  panic_path_RetiredDir �A  �  �  sysdiagnose_path_dDir �A      c ��   J  <  
longopts �Y"  ��}
service �!j  ��|
svcmove 	�  ��|service_error 
g  �  �  )b;  �K @    E  �2  {;  �  �  $E  *�;  ��|
�K @   �  Q��|   �G @   �<  )H @   �  o  Rs Qv Xu Y| w 0 oH @     �  R2 �H @   �<  �  R	c @   Q1X( 
I @     �  R2 'I @   �<  �  R	8c @   Q1X* OI @       R1 �I @   �  ?  R��|Q~ X	�0��|�0)( 	�# �I @   L  e  Q��|X	�c @    J @   �  �  Qs Xt  4J @   r  �  X��| @J @   =  qJ @   �  �  Qs Xt  �J @   �  �J @   
  �  X��| �J @   =  K @   �  K @   -  KK @   f  @  R	�b @   Q	�b @    eK @   v1  ^  Q} Y  yK @   �<  }  R	)e @    �K @     �  R2 �K @   �  �  Q	�c @   Xs  �K @   �"  L @   c0  �  R	�d @   X0 #L @   c0     R	�d @   X0 7L @   c0  2   R	�d @   X0 ZL @   �<  Q   R	�d @    |L @   �/  n   R} X0 �L @     �   R2 �L @   �<  �   R	xc @   Q1X! �L @   �<  �   R	�d @    M @   �(  �   Q}  &M @   f  !  R	�d @   Q}  <M @   �  JM @     /!  R2 _M @   �  Z!  Q	hd @   Xs Yt  iM @   �  sM @   -  �M @   f  �!  R	�c @   Q~  �M @     �!  R2 �M @   �  �!  Q	d @   Xs  �M @   -  �M @     �!  R2 �M @   �<  #"  R	�d @   Q1X0 �M @   �  	N @   -  
N @   �<  R	�c @     <  i"  +�    9�  a�"  ,p  �  size a4�   now a?�   ,  H�   :tmp_dir bA   process_powerlog_and_cleanup �   �! @   �      ��(  afc /�  �  �  
root_list �  ��scount 	�   �  �  proxied_list �    �  idx 	�   .  $  
proxied_dir_path $
�(  ��slatest_powerlog '�  _  U  �  1
�(  ��wp  6
�(  ��{now 9	�   �  �  #  :�   �  �  ;�  ;�   �#   "  1$  i �   �  �  
~" @   �  Q| X>  �" @   J       �$  i �   �  �  �" @   �  {$  Rs Q| X> 
�" @   n  Rs   �$ @   "       �$  i *�   �  �  �$ @   �   S$ @           �$  i F�   �  �  n$ @   �   )i"  �# @    2  ;�%  �"      �"  "     �"  1  /  w"  C  A  $2  -�"  R  P  �# @   -  y%  R	vb @    
�# @     Rs Q
 X	�b @   w t w(v    <�;  o# @    o# @          9�%  �;  a  _  
w# @   G  R0  " @   �<  &  R	�a @    9" @   {  E&  R� Q	b @   X��s �" @   �  _&  R��s # @   )  w&  R  # @     �&  Rt Q
 X	[b @    /# @   {-  �&  R� Qt  _# @     �&  R| Q
 X	�a @   Yt w s  o# @   �  # @   �  �# @   >+  :'  R� Q| Xs  �# @   f  e'  R	�b @   Qt Xv  �# @   �  }'  R}  $ @     �'  R2 $ @   �<  �'  R	>b @   Q1XL '$ @   )  9$ @     �'  R2 S$ @   �<  (  R	�b @   Q1X  {$ @   �  #(  R}  �$ @     :(  R2 �$ @   �<  c(  R	b @   Q1XN �$ @     z(  R2 
�$ @   �<  R	_b @   Q1XF  {   �(  .�   � afc_delete_directory_recursive ��   @  @   �      �>+  afc �1�  w  o  path �BA  �  �  
list ��  ��{err �5  �  �  
fullpath �
�(  ��{�   �*  i ��   �  �  $  g  ��  ��{is_dir ��   <  8  ;! @   A       �)  j ��   T  R  X! @   ?  �)  Qu  
k! @   ?  Q	�` @     ! @     %*  R} Q
 X	�a @   Yv  -! @   �  K*  Rt Q} X��{ �! @   )  c*  R  �! @   �  �*  Rt Q}  �! @   )  �*  R  
�! @   �(  Rt Q}    l  @   {  �*  Rt Qv X��{ �  @   )  �  @   �  +  Rt Qv  �! @     +  R2 
�! @   �  Q	�a @   Xv   move_file_from_device ��     @          �j-  afc �(�  j  b   �  �9A  �  �   p  �WA  �  �  
handle ��  ��_err �5  �  �  output �E  2  *  �  �w  ��_
buffer �j-  ��_L @   �  9,  Rt Qs X1Y��_ f @   �  ^,  Rv Q	#a @    � @   �  �,  Rs Q1Yu  � @   O  �,  Rt Xs Y
 w v  � @   +  �,  Ru  � @   �  �,  Rt  � @     �,  R2   @   �  -  Q	�a @   Xs    @     0-  R2 1  @   �  U-  Q	�a @   Xv  
>  @   �  Rt   S  {-  .�   � get_latest_powerlog_file ��  � @   :      ��/  afc �-�  X  T  proxied_dir �>A  x  p  
list ��  ��~latest_file ��  �  �  
latest_date �
�/  ���   /  i ��   �  �  �   �.  
date_str ��/  ��m @   �  x.  Rs  � @   �  �.  Rv Qs	X@ � @   ?  �.  Rv Q~  � @   �  �.  R|  � @   n  
� @   P  R~ Qv   
a @   �  Rs Qu X9   @   {  ?/  R�RQs X��~ � @   )  W/  R}   @     n/  R2 
 @   �  Q	�a @   Xs   {   �/  +�    /List_Directories �P @   �       �c0  path ��  �  �  afc �1�  	  	   �  �C5  :	  4	  
list �	�  �h�   @0  k ��   f	  V	  � @   �<   
q @   {  R�QQ�RX�h  /List_PanicLogs f� @   �       �11  path f�  �	  �	  afc f/�  �	  �	   �  fA5  	
  
  
list j	�  �X�   1  k r�   '
  %
  
 @   ]  Rs Qu   
� @   {  R�QQ�RX�X  =print_usage K
v1  argc K�   argv K*�  is_error K4�    >afc_client_copy_and_remove_crash_reports y�   P @   C      �b;  %afc B�  E
  5
  %device_directory SA  �
  �
  %host_directory qA  �
  �
  ?W  y�A      @�  {5  A  +  k |�   �  �  res }�        crash_report_count ~�   d  R  handle �  ��Vsource_filename ��(  ��Wtarget_filename ��(  ��[list �	�  ��V0device_directory_length ��   host_directory_length ��   �  �  h   s:  Ag  �
�  ��Vstbuf ��  ��Vcurrent_pos �	�  
  �  p �	�  "
  
  i ��   R
  6
  �   �3  newlen ��   �
  �
  � @   �  �3  Rs  
� @   �  R} Qs X~  $ &  �   s6  output 
E    �
  �  !
w  ��Vbytes_total "
w  6  2  
data #j-  ��_k @   ]  V4  Ru Q� � @   �  �4  R� Qu X1Y��V � @     �4  R2 � @   �  �4  Q	�` @   Xu Ys  � @   �  �4  Rv Q	#a @    � @   �   @   f  5  R	Ua @    ? @   O  A5  R� X} Y
 w ~  ` @   �  d5  R} Q1Y   @   O  �5  Rt X} Y
 w ~  � @   �  �5  R�  � @   +  �5  R  ! @     �5  R2 ; @   �<  6  R	`a @   Q1X  P @     6  R2 b @   �  >6  Q	(a @   Xv  t @   �  W6  R�  
� @   �  R� Qu   Bb;  b @    b @          �	�6  {;  M  K  *�;  ��_
s @   �  Rv Q��_  � @   P  �6  Ru ��V" � @   �   7  Q:  @   g  7  Rs Q. 7 @   �  B7  Q	=` @   X7 J @   P  `7  R} Qs  _ @   �  �7  R� Qu X��V � @   ?  �7  Rs Q	�` @    � @   ?  �7  Rs Q	�` @    � @   ?  �7  Rs Q}    @   ?  8  Rs Q   @   ?  18  Rs Q	�` @    * @   ?  V8  Rs Q	�` @    A @   ?  {8  Rs Q	�` @    Q @   �  b @   f  �8  R	�` @    � @   )  �8  R~   @   N  7 @   ?  �8  Rs Q	�` @    y @   6  � @   N   @   ?  09  Rs Q	�` @    ? @   f  U9  R	H` @   Qu  � @     m9  Rv  � @   v1  �9  R� Qu Xv Y�  @   �  �9  R� Qu  ? @   ?  �9  Rs Q	�` @    R @   ?  :  Rs Q	�` @    � @   �  !:  R� Qu  � @     9:  Rv  � @   )  Q:  R~  
� @   ?  Rs Q	�` @     � @   {  �:  Rt Qs X��V � @   P  �:  Ru Qs  � @   �  �:  Ru  � @   P  �:  Rv Q|  � @   �  ;  Rv  m @   )  ` @   )  );  Rs  � @     @;  R2 
	 @   �  Q	` @   Xs   Cfile_exists H�   �;  Dpath H$A  0tst J�   Etime "�   �;  _Time /�;   	�   Fi"  �$ @   G       �m<  w"  `  Z  �"  �  |  �"  �  �  �"  �  �  -�"  �  �  % @   -  7<  R	vb @    
.% @     Rs Qt X	�b @   w u w(v   G�/DiagnosticLogs/sysdiagnose H__main __main 1fwrite __builtin_fwrite 1puts __builtin_puts  ]   
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �% @   �         char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	`� @   atexit ��   �  Y   __main 5�& @          ��  �& @   �   	__do_global_ctors  & @   j       �  
nptrs "�     �  
i #�       ]& @   j  R	�% @     	__do_global_dtors �% @   :       �[  p [  	 Q @    	   �   A  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  w  =  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
�  �   �,  __uninitialized  __initializing __initialized  �  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	4Q @   �  	0Q @   =  
"	x� @   [  	p� @    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _dowildcard  �   	@Q @   int  }   %  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 <  $  �& @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   �& @          � �    _  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  #  _newmode �   	�� @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 w  _  �& @   �       ]  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	�� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�e @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�e @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	�� @   __mingw_initltsdyn_force ��   	�� @   __mingw_initltssuo_force ��   	�� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@  �& @   /       �}  
�  �  6  2  
  *M  H  D  
   ;d  Z  V  �& @   �   __tlregdtor m�   p' @          ��  func m  R __dyn_tls_init L@  	  �  �    *M     ;d  pfunc N
$  ps O
�    �  �& @   �       ��  p  h  �  �  �  �  �  �  �  �  �  ' @    ' @   +       L�  �  �  �  �  �  �  �    �  �      �  (  $   U' @   �    �    t  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 e  M  s  _commode �   	�� @   int  w   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  	  �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  �' @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   �' @   �       �0  pexcept 0  O  I  type 
�  q  e  �' @   b  �  R2 ( @   7  Q	g @   Xs Yt w �ww(�ww0�w  5   �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 `
  H
  �( @          �  _fpreset 	�( @          � �    5  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  �  __mingw_app_type �   	�� @   int  G   c  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �( @   =      7  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0  �I  the_secs ��  	Ƞ @   	�  maxSections �%  	Ġ @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �p* @   ]      ��  4was_init �%  	�� @   5mSecs �%  �  �  !�  �* @   x  �4  �  �  �  6x  
�  �  �  
�  W  '  
�  n  V  
�  �  �  

      
  P  @  "E  �  �  
F  �  �  
[  �  �  	, @   `  R	hh @   Xu w t     �+ @   �+ @          �;  �  �  �  �  
    �        �+ @   �+ @          �  #  !  �  .  ,  �  =  ;  �+ @   �  Ru    !  b, @   �  ��  �  G  E  �  R  P  �  a  _  7  b, @   �  �  k  i  �  v  t  �  �  �  n, @   �  Ru      - @   - @   
       �w  �  �  �  �  �  �  �  �  �    - @   - @   
       �  �  �  �  �  �  �  �  �  - @   �  Ru      0- @   0- @          �   �  �  �  �  �  �  �  �  �    0- @   0- @          �  �  �  �      �      8- @   �  Ru    "$  �  �  
)  #    83  �  
4  =  ;    �- @   �- @   
       s�  G  E  �  R  P  �  a  _    �- @   �- @   
       �  k  i  �  v  t  �  �  �  �- @   �  Rt      
�- @   `    R	8h @    �- @   `  R	 h @      9�  �, @   X       �|  
�  �  �  :�  ���, @   
  Yu   �* @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable � ) @   b      �`  &addr ��  �  �  b �:  ��h �g  �  �  i �%      >�) @   P       �  new_protect �
u  3  1  
* @   
  �  Ys  * @    
  ,* @   `  R	�g @     
`) @   �
  �  Rs  �) @   n
  
�) @   E
    Q��X0 
R* @   `  >  R	�g @    b* @   `  R	�g @   Qs   ?__report_error T�( @   i       �/  &msg T  ?  ;  @argp ��   �X
�( @     �  R2 
�( @   /  �  R	`g @   Q1XK 
�( @       R2 
�( @   �
  !  Qs Xt  �( @   �
   Afwrite __builtin_fwrite   �   ;  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �- @   L       �  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	Р @   
__setusermatherr ��  �   __mingw_setusermatherr �. @          �P  f ,�  Z  V  . @   �  R�R  __mingw_raise_matherr ��- @   >       �typ !�   n  h  name 2�  �  �  a1 ?w   �  �  a2 Jw   �  �  rslt 
w   � ex 0  �@	. @   R�@   �    M  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  e   _fmode �   	� @   int  �   {  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 e  M   . @   �      �   char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�    ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	    z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��    . @   �      ��  'exception_data �-�  �  �  old_handler �
	      action ��   l  R  reset_fpu ��   �  �  
|. @   �
  �  R8Q0 (�. @   �  R�R 
�. @   �
  �  R4Q0 �. @   �  R4 
</ @   �
    R8Q0 
U/ @   �
  7  R8Q1 
l/ @   �
  S  R;Q0 �/ @   f  R; �/ @   y  R8 
�/ @   �
  �  R;Q1 
�/ @   �
  �  R4Q1 
�/ @   �
  �  R8Q1 )�/ @   �
   �	   �
   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 v  ^  �/ @   b      ,"  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	 � @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  P1 @   �       �n  	hDllHandle z�  ?  '  	reason {<  �  �  	reserved |S  =  %   �1 @   K       �  
keyp �&�  �  �  
t �-�  �  �  �1 @   �  
2 @   C  R	 � @     !n  �1 @   �1 @          �  �  �1 @   )
   "n  �1 @   �  �E  #�  �  %2 @   )
    2 @   6  
=2 @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   �0 @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�      �0 @   �  B	  Rt  #1 @   �  
,1 @   �  Rt   ___w64_mingwthr_add_key_dtor *�   P0 @   o       �$
  	key *%<  >  4  	dtor *1.  r  d  
new_key ,$
  �  �  0 @   �  �	  R1QH �0 @   �  
  Rt  
�0 @   �  Rt   �  &n  �/ @   p       ��  �  �  '�  0 @          �
  �  �  �  0 @     !0 @     (30 @   Rt   �/ @   �  �
  R|  )P0 @   �  R	 � @      �    Z  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �$  _CRT_MT �   	PQ @   int  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 7    �$  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	a� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	`� @    �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  P2 @   �      %  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  6  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  N  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  i  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  i  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "N  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #N  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   6  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �5 @   �       �7
  i �(�   �  �  ^  �	`  D  �	  	    importDesc �@  '  %  -  �^
  importsStartRVA �	I  7  /  �  �5 @   	�  ��  �  �  �  �  �  	�  �5 @    �  �  �  �  �  v  r  �  �  �      M  �5 @   �5 @   J       �q  �  �  f  }  �  �  �  �  �  �  �  �    
_IsNonwritableInCurrentImage �  �4 @   �       ��  pTarget �%`  �  �  ^  �	`  rvaTarget �
�      -  �^
      �  �4 @   o  �/  �  o  �  �  �  	�   5 @      �    �  �  '  #  �  8  6      M  $5 @   $5 @   I       �q  D  B  f  }  N  L  �  X  V  �  b  `    
_GetPEImageBase �`  �4 @   6       �0  ^  �	`  	�  �4 @   T  �	�  T  �  �  �  	�  �4 @    d  �  d  �  �  o  k  �  �  ~       
_FindPESectionExec y^
  04 @   s       �%  eNo y�   �  �  ^  {	`  D  |	  �  �  -  }^
  �  �  x  ~�   �  �  	�  04 @   9  �	�  9  �  �  �  	�  A4 @    I  �  I  �  �  �  �  �  �  �       
__mingw_GetSectionCount g�   �3 @   7       ��  ^  i	`  D  j	  �  �  	�  �3 @     m	�    �  �  �  	�   4 @    .  �  .  �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
  p3 @   �       �  p Y&s      ^  [	`  rva \
�  1  /  �  p3 @   �  _�  �  �  �  �  �  	�  �3 @      �    �  �  =  9  �  N  L      	M  �3 @     c
q  Z  X  f    }  f  b  �  �  �  �  �  �     
_FindPESectionByName :^
  �2 @   �       �M  pName :#�  �  �  ^  <	`  D  =	  �  �  -  >^
  �  �  x  ?�   �  �  �  �2 @   �  F  �  �  �  �  �  �  �2 @    �2 @          �  �  �  �  �  �  �  �     &�2 @   �  -  Rt  'J3 @   z  Rs Qt X8  _FindPESection $^
  �  ^  $`  (rva $-�  D  &	  -  '^
  x  (�    _ValidateImageBase   �  ^  `  pDOSHeader �  D  	  pOptHeader v   )�  P2 @   ,       �~  �       �      �  �  	�  Y2 @    �  �  *  $  �  �  �  D  @  �  Q  O     *M  �2 @   P       �f  ]  Y  +q  Q}  p  l  �  �  �  �  �  �    8   B  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �6 @   �      c*  
__gnuc_va_list �   #__builtin_va_list �   char �   
va_list w   
size_t #,�   long long unsigned int long long int 
wchar_t b
  short unsigned int int long int 	�   6  	#  unsigned int long unsigned int unsigned char double float long double 	�  	6  optind #  optopt #  opterr #  optarg 6  option  >*  name @/   has_arg A#  flag B@  val C#   �  	�   /  $E  G~  no_argument  required_argument optional_argument  _iobuf 0!
  _ptr %6   _cnt &	#  _base '6  _flag (	#  _file )	#  _charbuf *	#   _bufsiz +	#  $_tmpfname ,6  ( 
FILE /~  
DWORD �U  signed char short int WCHAR 1�   E  	E  	S  LPWSTR 5X  LPCWSTR 9]  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS E  �i  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  %tagCOINITBASE E  ��  COINITBASE_MULTITHREADED   VARENUM E  		+  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � �  	�Q @   �  	|Q @   �  	xQ @   &__mingw_optreset D#  	�� @   �  	�� @   
place f6  	pQ @   
nonopt_start i#  	hQ @   
nonopt_end j#  	dQ @   �   �  �   ! �  
recargchar m�  	 j @   
recargstring n�  	�i @   �   :  �    *  
ambig o:  	�i @   �   f  �   ' V  
noarg pf  	�i @   �   �  �    �  
illoptchar q�  	Pi @   
illoptstring r�  	0i @   vfprintf )#  �  �  4  �    	  �  fprintf "#  	  �  4   __acrt_iob_func ]�  @	  E   '__p___argv ��  strncmp 
V#  w	  /  /  �    strlen 
@�   �	  /   strchr 
D6  �	  /  #   GetEnvironmentVariableW ?  �	  q  b     getopt_long_only O#   B @           ��
  �  ,#  �  �  �  ,+�
       �  ,>/      �  -�
  0  ,  idx --@  � B @     R�RQ�QX�XY�Yw � w(5  	;  	*  getopt_long M#  �A @           �q  �  #  F  B  �  &�
  \  X  �  9/  r  n  �   �
  �  �  idx  -@  � �A @     R�RQ�QX�XY�Yw � w(1  getopt #  �A @   "       �  �  #  �  �  �  !�
  �  �  �  4/  �  �  �A @     R�RQ�QX�XY0w 0w(0  (getopt_internal C#  �; @   (      ��  �  C#  �  �  �  C*�
  "      �  C=/  Z   P   �  D�
  �   ~   idx D*@  � )flags D3#  �   �    oli F6  �   �    optchar G#  (!  !  *�  G#  [!  U!  +posixly_correct H
#  	`Q @   ,start g�< @      C
  Rt Qu Xs Yv  = @   �	  [
  R}  1= @   �  �
  Rv Q} X~ Y�  o= @   �	  �
  R} Qs  �= @   �	  �
  R	i @   Q0X0 !> @   �	  �
  R} Q- j> @   �	  	  R} Q- j? @      '  Ru Yv  5@ @      T  R���Qt Xs Yv  ^@ @   �  y  R	Pi @   Qs  �@ @   �  �  R	 j @   QW $A @   �  �  Rv Q} X~ Y� w 0 �A @   �  R	 j @   Qs   -parse_long_options �#  �7 @   �      �   �  �"�
  w!  s!  �  �5/  �!  �!  �  ��
  �!  �!  idx �*@  �!  �!  .�  �3#  � current_argv �6  �!  �!  has_equal �6  �!  �!  current_argv_len �	�   E"  ="  i �#  h"  b"  ambiguous �	#  �"  "  match �#  �"  �"  F8 @   �	  /  Ru Q= �8 @   S	  S  Ru Q~ Xt  �8 @   w	  k  R~  �9 @   �  �  R	�i @   Qt Xu  m: @   w	  �  Ru  �: @   �  �  R	0i @   Qu  �: @   �  �  R	�i @   Qt Xu  ; @   �  R	�i @   Qu   !permute_args ��6 @   �       ��  panonopt_start �#  #  #  panonopt_end �&#  ;#  3#  opt_end �8#  ^#  X#  �  ��
  |#  t#  cstart �#  �#  �#  cyclelen �#  �#  �#  i �#  �#  �#  j �#  �#  �#  ncycle �#  $  $  nnonopts �&#  $  
$  nopts �0#  5$  /$  pos �7#  Y$  Q$  swap �6  w$  s$  /�  �6 @    �  ��  �$  �$  �  �$  �$  0�  1�  �$  �$     2gcd �#  �  a �	#  b �#  3c �#   !warnx ~`7 @   �       ��  fmt ~/  �$  �$  
ap ��   �X4�  �7 @    �7 @   W       �  %   %    %  
%  5�7 @   @	  �7 @   	  u  R2 �7 @   �  �  Q	 i @   Xu  �7 @   	  �  R2 �7 @   �  �  Qs Xt  �7 @   	  �  R2 �7 @   "  R:   6_vwarnx u"  fmt u/  ap u!�    7fputc __builtin_fputc 
  �    �   GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  �3  _MINGW_INSTALL_DEBUG_MATHERR �   	�Q @   int  �   �   GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   B @          �3  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	�Q @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	   B @          �_File *�  /%  )%  _Format J�  H%  B%  _ArgList Z�   a%  [%  9B @   �  R0Q�RX�QY0w �X   �   *"  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @B @   9       p4  __gnuc_va_list �   __builtin_va_list �   char 	�   va_list w   size_t #,�   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(B  G  
threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   j  o  �   y   �   j  �   t  __imp_snprintf �  	�Q @   P  __stdio_common_vsprintf �  �  �   j  �   t  $  �    snprintf G  @B @   9       �__stream +o  �%  �%  __n <�   �%  �%  __format [y  �%  �%  ap 
�   �hret   �%  �%  tB @   �  R2Q�RX�QY�Xw 0w(�   �   c#  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  l  �B @   H       �4  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	�Q @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �B @   H       �_Format .�  �%  �%  
ap 
�   �Xret 	  �%  �%  �B @   �  �  R1 �B @   �  R0Xs Y0w t    �   �$  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 f  N  �B @   2       �5  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�Q @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  �B @   2       �_File )�  &  
&  _Format I�  ,&  &&  
ap 
�   �hret 	  A&  ?&  �B @   �  R0Q�RX�QY0w �   �   1&  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 K  3  6  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	�� @   local__winitenv 
`  	�� @   '  
	�Q @     
	�Q @    �   �&  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 h  P  C @         p6  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	`R @   �      �   __imp_at_quick_exit g)  	XR @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	TR @   
initial_tzname1 }
V  	PR @   
initial_tznames ~�  	@R @   
initial_timezone 
%  	<R @   
initial_daylight �  	8R @   __imp_tzname ��  	0R @   __imp_timezone ��  	(R @   __imp_daylight ��  	 R @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	R @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	R @   �	  __imp__amsg_exit �V  	R @   F  __imp__get_output_format �\
  	 R @   -
  __imp_tzset ��  	�Q @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�Q @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   pD @   5       ��
  file �!�
  Y&  S&  fmt �6�  x&  r&  
ap ��   �h+ret �  �&  �&  �D @   �
  R4Q�RX�QY0w �  ,tzset "�D @   6       �  -  �D @   �D @   -       ��D @   +  �D @     �D @       ._tzset �/_get_output_format nF  C @          �0_amsg_exit i@D @   .       ��  ret i  �&  �&  QD @   z  �  R2 cD @   Q  �  Q	`j @   Xs  nD @   <  R�  at_quick_exit �   D @          �   func ]*�  �&  �&  15D @   �   _onexit ��   D @          �p  func V%�  �&  �&  
D @   �  Rs   __wgetmainargs J  �C @   j       �[  _Argc J"�  �&  �&  _Argv J5I  '  '  _Env JGI  ''  !'   �  JQ  F'  @'  !�  Jl�	  � �C @   0  �C @   	  &  R	v  $0.# �C @   �  �C @   �  �C @   �  �C @   U   __getmainargs >   C @   j       �E  _Argc >!�  e'  _'  _Argv >1S  ~'  x'  _Env >@S  �'  �'   �  >J  �'  �'  !�  >e�	  � @C @   �  PC @   �    R	v  $0.# UC @   �  ^C @   �  iC @   u  }C @   U   2  �D @   6       �E @   +  E @     E @                                                                                                                                                                                                                                                                                                                                
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  H}  (    I  ( 
  4 :!;9I�B  .?:;9'I<  H }  	 !I  
H}   :;9I  
 :;9I8  
4 :!;9I  $ >  .?:;9'I<  4 :!;9I   :!;9I�B   1�B  U  (   4 :!;9I�B  >!I:;9   :;9I  .?:!;9'I@z  4 :!;9I  :;9   :;9I   <     I      :!;9I�B  !& I  "7 I  #4 :!;9I�B  $U  % :!;!� 9I�B  &4 :!;9I?<  '>!!I:;9  (>!!I:;9  )1R�BUX!YW  *4 1  +! I/  , :!;!�9I  -4 1�B  .! I/  /.?:!;9!'@z  04 :!;9I  1. ?<n:!;!   2%U  3   4&   54 :;9I?  6.?:;9'<  7. ?:;9'I<  8.?:;9'<  9.?:;9'   :4 :;9I  ;.?:;9I<  <1R�BXYW  =.:;9'   >.:;9'I@z  ? :;9I�B  @4 :;9I�B  A4 :;9I  B1R�BXYW  C.:;9'I   D :;9I  E.:;9'I   F.1@z  G6   H. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   I ~  (   H}  $ >   I   :!;9I�B  4 :!;9I�B  
 :;9I8  	 !I  
4 :!;9I  & I  (   
 :;9I  H}  .?:;9'I<   :!;9I�B  4 :!;9I?<  4 G  I  ! I/   :!;9I�B   1�B   :!;9I   :!;9I  .?:!;9!'I@z   :!;9I  :;9  7 I  >!!I:;9  .?:!;9!'I<      4 :!;9I�B  !.:!;9!'@z  "%  # I  $>I:;9  %>I:;9  &4 :;9I?  '. ?:;9'I<  (.:;9'I@z  ) :;9I�B  *4 :;9I�B  +4 :;9I  ,
 :;9  -.:;9'I@z  . :;9I  /1R�BUXYW  0U  14 1�B  2.:;9'I   34 :;9I  41R�BXYW  5H }  6.:;9'   7. ?<n:;   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}    I  $ >   !I  I ~  
 :!;9I8   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!     7 I  
%   I   <  'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|          5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �    �   �
        [  t  �  �   �   �  �  �  �  �    	      (  8  K  V  `  f  k  t  }  �    	P @   � �w	�	�XY�/ �� </  �J/ �� </ �J   �b� �a<
� �<UX X f    J t�X��! �~��U
H��GM]�=�
]
X��XU X  ��KX �  <� q�
 .� �� y�   X J�	 �� �J �� �� �L3 ��~
 �� QJ 	2 N� X J �7X�[
�
��! � �)X �& �	�t� <[��� �" Z/�! eK�*4t/�� ��o�� # �  ����� �� <Y�k�
�
�M��H< �L ��!Xmtȅ
H�� ��=�XzXz� X � X �
w +Z
��Y%�WK%U3 J% X]!���Z�� � �� �� <Y
z�� �  <Yj� �� <Y!�2"w@T	?�<Y P  ., � c   < J[ �/ XL X� z <P z�  XI w��[@T	?�<Y P  X X < � c   J J[% XZ �   J J X% w�" fK��	KV=�	 X� 
WY
 
 ! J �A[vX
�
 K �h)J�dZ XK�!�!e=� u   � X�m�
~Y
; f� <Yj�/j>jVhfX=��=��XXz �l X
Yl�l �N���
iX �� <Yf
3 �� <Y�H./HVg�X= � ] S
[Z S <
Y
 X e   J J XY��.+ c ( �
���
�
Y
/ J# X ]"  <! �K6 �) I "  J �
��
��J
fY
e f� <Y4%�04�t[	;�	 � �  �	� � <
��$ d   J��g
 $ �   <
�
 K$ :L$J V" LX$ :  X@T[v/	�$2<�N["�}8?��(XXt/&[?�[	 JL ���
 �  <Y�t
;t �  <Y  �= ;K& W  X
�
@. �  <Y
. �  <Y  �= ;K& W U7��U�t[�?�X     	�G @   �.���yfq:<u<�u[t��K.K ���X
�e.	�g �  <Y	�u��=yXu�=
X
�X�sX	�� �  <Y]u�=VX�U5�t@ Xf�	 
� X�  X�	 Ys/n!��!�Y��"/�Y"�"W/Y
=
sY#f�����Z/�Y��X�Y��xTf��.Y��~�<�}f	/��  �$ �~.h
�z�Xw�z
f ��t �t <�
� X�� Y=>&�f<�Z �t, �! �  <Y
�X�Zl��Y]� tX < f � <Y	��
����	� �� <Y�
� � �  <Y��
�~��
 #    K   �
      �  �    2  <  F  N  [  d  n   	�% @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �  �       F  Q  Y  f  o  z  6     .   �
      �  �      R     .   �
      m  �  �  �   	�& @    6     .   �
      
  "  I  T      K   �
      �  �  �    
      )  3  ;   	�& @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �  �  �  �  6     .   �
      6	  N	  u	  �	  �     <   �
      �	  �	  
  +
  2
  9
  @
   	�' @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �
  �
  �
  �
   	�( @   		3 6     .   �
      6  N  u  �  |    s   �
      �  �  "  =  L  [  d  n  z  �  �  �  �  �  �  �  �  �    	�( @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	p* @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      1
  I
  p
  �
  �
  �
   	�- @   
L�Z
�KTYZ
g=yuX 6     .   �
      �
    7  B  �    U   �
      �  �  �  	�        $  .  ?  L  U   	 . @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      �  �  �          +  5  =  J  U  ^  r   	�/ @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      �  �      6     .   �
      r  �  �  �  G    K   �
      !  9  `  {  �  �  �  �  �  �   	P2 @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< ]	    n   �
          G  b  k  t  }  �  �  �  �  �  �  �  �  �  �    	�6 @   ��/x<_//a</�< 9zXL  X�  J"�
y<	�8	�M 
A	t>> w<  G  J�
= <    TXpfuV"Ys	 X � � <Y �f <Y	 �X < f	X� �bur�p<�Vrt��X <� = I=   Ju�.	p�<d�Xk
��% j�  <% J JZ �N �
��
Y
 ��<�J�</X�J�
�
� Xf <�� ��f^�<KY ��� ��8<Z�	t��2K;�/
���X� � �P<	X�X�XJ�w� ��	��+̟^X!&��w�!!��
A�
tvf
�( � f�X�
 ��	������f���
���f	�fY�-0��
�N  �� �, X t5 t�� � ��
�0�t<J<t, �
�. ��3 J- �����~�
�	�Xt	fxfK  �gX�	M
J
�' I� �� ��'tXu
�. �!��
�nJfgt	u	;u��P�	K
<
ew
cu	^]h��
Q�X�...�X<ugit0,�,Z� �J<t
�gu ��
g �~�	K;Xf X � J+ ��
zf=sg
y�f*Je.	
�!t�f<�
e� Ju� ����k� X
.�
�	�
	X�<��z��	 �u�g
t
J
�
.L
h	XL
h 6     .   �
      <  T  |  �  n     A   �
      �    .  I  Y  i  r  |   	 B @   K
�<.Y �     A   �
      �  �    1  A  Q  Z  d   	@B @   KU	\f�X�Y	Y �     A   �
      �  �  �    %  3  <  F   	�B @   g?	YT�Y	 X� <uX �     A   �
      �  �  �  �  	    !  +   	�B @   KU	\fp	\;Y	Y W     O   �
        �  �  �        &  /  :  F  |    h   �
      �  �  �  �  &  8  J  Q  Z  d  m  u  �  �  �  �   	C @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	@D @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                    ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �      l   P  P @   C      B�G�B �B(�A0�A8�A@�AH�	H�*
HA�@A�8A�0A�(B� B�B�B�H    <   P  � @   �       A�A�A �DP�
 A�A�A�I   ,   P  P @   �       A�D@s
A�G     d   P  � @   :      B�B�B �A(�A0�A8�A@�D��
@A�8A�0A�(A� B�B�B�J      L   P    @          B�F�A �A(�A0�H� �
0A�(A� A�A�B�Al   P  @  @   �      B�B�B �B(�A0�A8�A@�AH�	G�w
HA�@A�8A�0A�(B� B�B�B�J     l   P  �! @   �      B�B�B �B(�A0�A8�A@�AH�	G�
�
HA�@A�8A�0A�(B� B�B�B�D    D   P  �$ @   G       A�A�A �A(�D`z(A� A�A�A�      l   P  �G @   �      B�B�B �B(�A0�A8�A@�AH�	G��
HA�@A�8A�0A�(B� B�B�B�A       ���� x �         �  �% @   :       D0u  4   �  & @   j       A�A�D@@
A�A�H       �  �& @             ���� x �            �& @             ���� x �      $   P  �& @   /       D0R
JN    L   P  �& @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       P  p' @             ���� x �      <   �  �' @   �       A�A�D�P�
���
���A�A�B    ���� x �         P  �( @             ���� x �      $   �  �( @   i       A�A�DP   <   �   ) @   b      A�A�A �Dp�
 A�A�A�D   \   �  p* @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         `  �- @   >       D`y     `  . @             ���� x �      4   �   . @   �      A�D0}
A�Mf
A�I     ���� x �      L      �/ @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <      P0 @   o       A�A�A �D@U
 A�A�A�A    D      �0 @   �       A�A�D@R
A�A�FR
A�A�D      4      P1 @   �       A�D0p
A�J�
A�A      ���� x �         (	  P2 @   ,          (	  �2 @   P       L   (	  �2 @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       (	  p3 @   �          (	  �3 @   7          (	  04 @   s          (	  �4 @   6          (	  �4 @   �          (	  �5 @   �          ���� x �      T   P
  �6 @   �       B�B�A �A(�A0�A8��
�0A�(A� A�B�B�A      <   P
  `7 @   �       A�A�A �DPw A�A�A�      l   P
  �7 @   �      B�B�B �B(�A0�A8�A@�AH�	D�e
HA�@A�8A�0A�(B� B�B�B�G    l   P
  �; @   (      B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�C       P
  �A @   "       D@]     P
  �A @           D@[     P
   B @           D@[     ���� x �         @   B @          D@Y     ���� x �         x  @B @   9       DPt     ���� x �      ,   �  �B @   H       A�A�D`A�A�   ���� x �         �  �B @   2       DPm     ���� x �         0
  C @          L   0
   C @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   0
  �C @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   0
   D @          A�D0WA�    0
   D @             0
  @D @   .       A�D0   0
  pD @   5       DPp     0
  �D @   6       D0q     0
  �D @   6       D0q                                                                                                                                                                                                                                                                                                                                  Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion randomValue lockdownd_client_private afc_client_private filename_filter fileinfo local_destination device_file_path service_client_private afc_error generate_path bytes_read idevice_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection options short_too nargv nargc long_options _DoWildCard _StartInfo                                                              C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools idevicecrashreport.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/sys ../include/libimobiledevice idevicecrashreport.c idevicecrashreport.c time.h corecrt.h stdio.h types.h getopt.h winnt.h combaseapi.h wtypes.h stdint.h _mingw_stat64.h libimobiledevice.h lockdown.h service.h afc.h io.h stdlib.h string.h stat.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:/M/B/src/mingw-w64/mingw-w64-crt/misc/getopt.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include getopt.c getopt.c vadefs.h corecrt.h getopt.h stdio.h minwindef.h winnt.h combaseapi.h wtypes.h string.h processenv.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_snprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_snprintf.c ucrt_snprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                                                                                                                                                                                                                                 �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� %
             �G @    R�S��S��S      �G @    Q�V��V��V            H @    �0���P��0��	�	0��
�P��P                      H @    �0���P��P��P��0��	�	0��
�
P�
�0���P��S��0�             H @    �0���P��0��	�	0��
�
0��
�0���0�  H @    0�  H @    0�  H @    0�        H @     
�b @   ���]��]��]    H @    �
�d @   ���
�d @   � 	   H @    �
�d @   ���
�d @   � 
   H @    �
�d @   ���
�d @   �    H @    ��c   ���c                 H @     0�5P��P��Z��Z����|�	�	Z     ;J @    P&S  �K @   	0� @        �! @    "R"��         D" @    $0�$�U��U��U        �" @    P�]��]��]          �" @    0�4V4E^E�V��V           2# @    ,P,8S8<R��P��S      z# @    PeT��T      �# @    PWV��V   D" @   $0�  �" @   0�  �$ @   0�  S$ @   0�    �# @    P$V  �# @   $T  �# @   $
 �  �# @   $S   �# @   	P  o# @   0�         @  @    +R+�T���R���T         @  @    +Q+�V���Q���V         o  @    $P$)\��P��P         w  @    !0�!,sx3%�,4sx3%#�49sp3%#�`�sx3%�     |! @    0�$A1�   ;! @   0�           @    +R+�T���R���T           @    +Q+_S_��Q���S           @    'X'dVd��X���V               O @    P7\7ZPv�P��P��\��\        i @    PuU��P��U     � @    #R#��R�         � @    #Q#LSL��Q���S           @    @0�@�\��P��\��0�     @    60���0�       P @    R P ��R�       P @    Q R ��Q�       P @    
X
!�X�!*P             u @    0�!sx3%�!)sx3%#�).sp3%#�.Bsx3%�BJsx3%#�JOsp3%#�Uisx3%�       � @    R"P"��R�       � @    Q"R"��Q�       � @    X#�X�#AP   � @   0�                P @    ERE�T��� ��� ��T��� ��T���            P @    EQE�S���Q���S���Q�             P @    AXA�\���X���\���X���\���X�     P @    EYE��                       � @    P�	�
P�
�
S��P��S�
�
P�
�
0��
�
P�
�
0���P��S             
 @    @0�@K| 3%�KO| 3%#�Oo|x3%#���| 3%���0���
| 3%���| 3%�           i @    �	����	����0���P����V��0���	��                 i @    �0�����V����V����V��0�����V����V��0�����V            
 @    @R@o��V����V��R��
��V����V      � @    	P P ,R      @    P R                         m @     0� ctx3%�cltx3%#�luth3%#�u�tx3%���tx3%#���th3%#���tx3%���tx3%���tx3%���tx3%���0���tx3%��
�
tx3%�   � @   ^           � @    !P!�_��_��P��_    : @    0�SS  b @   V       �$ @    RCSCG�R�       �$ @    QDTDG�Q�       �$ @    XEUEG�X�       �$ @    YFVFG�Y�   !% @   P 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
�f @   ���
`f @   ���
�f @   ���
�f @   ���
g @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  �����     ��U  ��2�  �����     ��U  ��1�  �����     ��U  ��1�  �����     ��U  �	�	4�  �	�	���     �	�	U  �	�	4�  �	�	���     �	�	U  �	�	8�  �	�	���     �	�	U  �	�	8�  �	�	���     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
�˒     �
�
T  �
�
4�  �
�
�˒     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � ;            ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�             �	�
R�
�\��R��\���R���\           �	�
Q�
�V��Q��V���Q���V           �	�
X�
�]��X��]��]         �	�
Y�
�^���Y���^               �	�
�(���(��_���(��_���(��_       �
�
P�
�p���P��p���P             �
�
P�
�S��-���S��S��S     ��
0��
�
P��0�     ��R��	�      ��Q��	�         ��X��\���X���	\     ��Y��	�     ��U��	U               ��P��X����������P������	���	�	��         ��T��T��P��	T      ��0���_��_                  ��0�����������R��1�������0���	0��	�	0�            ��	����_��_��	����	_�	�	_        WRW��R���R          Q�S���Q���S        WXW��X���X          QYQ�U���Y���U          MW\WgQg�\��|���\   ?�P  ?W0�      Mg0�g�R��r���R��0�  6M]       �V���Q�R���V       �T���X�Q���T       MW\W�Q��Q��\     gsZ��Z       %T%%P%,Q,6]��T     %V%/P��V        ,Q,/]/6Q��Q       ��R��S���R�  ��T    ��R��S T                RQ�R�        QX�Q�        X�`�X� [                !R!3Q39�R�        Q3X39�Q�        X3Y39�X�   49P )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                              X         Z���� ���� ���� ���� ���� ���� ����         1 @    $g���
�
�  @    
�� � @    ������	 � @    iy� u @    O[i  @    

6D� 8 @    
-� w  @    9i� |  @    
'd� D" @    M b# @    $&J �K @     P @   �!�G @   �
 S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����          6��                                                                                                                                                                                                                                                                                                                                       .file   a   ��  gcrtexe.c              �                                �              �                            �   0                        �   @                        %  �
                        @                          `             k  �                        �  �                        �  �
                        �                          �  0          �                       envp           argv            argc    (                          �                        '  �          9  p                        ^  �                        �             �  �
                        �                           �  `                          �
                    mainret            "  �                        8  �                        N  �                        d  �                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  P     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  �     +                 .file     ��  g    �                m  P                           �  L           �  �          �  8           �  P
          �  �
          �               @          #  �          @  �      main    �7          N  D           X  0                        n  <           y  @           �  P           �  @                    .text   P     �  �             .data                          .bss    0      $                 .rdata         �               .xdata  t      �                 .pdata  l      `                    �  �7     �  f                 �                            �  �                          �  �&  
   �<  �                �  �     �                    �  �     )
  W                 �  0      @                    �  \                           9     �                          �                       �     �                    )  �     +                     4  P     H               .text   @      .idata$7\
      .idata$5�      .idata$4�      .idata$6�      .text   H      .idata$7X
      .idata$5�      .idata$4�      .idata$6�      .text   P      .idata$7T
      .idata$5�      .idata$4�      .idata$6�      .text   X      .idata$7P
      .idata$5�      .idata$4�      .idata$6�      .text   `      .idata$7L
      .idata$5�      .idata$4�      .idata$6l      .text   h      .idata$7H
      .idata$5�      .idata$4x      .idata$6T      .text   p      .idata$7D
      .idata$5�      .idata$4p      .idata$68      .text   x      .idata$7@
      .idata$5�      .idata$4h      .idata$6      .text   �      .idata$7<
      .idata$5�      .idata$4`      .idata$6      .text   �      .idata$78
      .idata$5x      .idata$4X      .idata$6�      .text   �      .idata$74
      .idata$5p      .idata$4P      .idata$6�      .text   �      .idata$70
      .idata$5h      .idata$4H      .idata$6�      .text   �      .idata$7,
      .idata$5`      .idata$4@      .idata$6�      .text   �      .idata$7(
      .idata$5X      .idata$48      .idata$6�      .text   �      .idata$7$
      .idata$5P      .idata$40      .idata$6�      .text   �      .idata$7 
      .idata$5H      .idata$4(      .idata$6�      .text   �      .idata$7
      .idata$5@      .idata$4       .idata$6l      .text   �      .idata$7
      .idata$58      .idata$4      .idata$6X      .file   ,  ��  ggccmain.c             �  �                       p.0                              �
                    __main  �          6  `       .text   �     �                .data                         .bss    `                       .xdata                         .pdata  �      $   	                 �  bc  
   a                   �  
     ?                    �  �     5                     �  p      0                           '                     �     �                     )  �     +                     4  �     �                .file   B  ��  gnatstart.c        .text   �                       .data   0                      .bss    p                           �  �i  
     
                 �  A     �                     �  �                             =     V   
                   �                            w                         )  
     +                 .file   V  ��  gwildcard.c        .text   �                       .data   @                      .bss    �                            �  �o  
   �                    �  �     .                     �  �                             �     :                      �     �                     )  @
     +                 .file   r  ��  gdllargv.c         _setargv�                       .text   �                      .data   P                       .bss    �                        .xdata  8                      .pdata  �                          �  Rp  
   �                   �  %     :                     �  �      0                      �     V                      $     �                     )  p
     +                     4        0                .file   �  ��  g_newmode.c        .text   �                       .data   P                       .bss    �                           �  �q  
   �                    �  _     .                     �                              #     :                      �     �                     )  �
     +                 .file   �  ��  gtlssup.c              B  �                           Q  �          `  �
                    __xd_a  P       __xd_z  X           w  p      .text   �     �                .data   P                       .bss    �                       .xdata  <                      .pdata       $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  ]r  
   �  6                 �  �     �                    �  &                        �  0     0                      ]                          �                            _     �                     )  �
     +                     4  P     �                .file   �  ��  gxncommod.c        .text   �                       .data   P                       .bss    �                           �  $z  
   �                    �  t     .                     �  `                            s     :                      M     �                     )        +                 .file   �  ��  gcinitexe.c        .text   �                       .data   P                       .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �z  
   {                   �  �     a                     �  �                            �     :                      �     �                     )  0     +                 .file     ��  gmerr.c            _matherr�                       .text   �     �                .data   P                       .bss    �                        .rdata        @               .xdata  T                      .pdata  ,                         �  )|  
   6  
                 �                           �  =     �                    �  �     0                      �     �                      �	     �                     )  `     +                     4  �     X                .file   +  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   P                       .bss    �                        .xdata  l                      .pdata  8                         �  _  
   �                    �       -                     �  �     0                      �     X                      H
     �                     )  �     +                     4  P     0                .file   ?  ��  gmingw_helpers.    .text   �                       .data   P                       .bss    �                           �  �  
   �                    �  5     .                     �                               �     :                      �
     �                     )  �     +                 .file   l  ��  gpseudo-reloc.c        �  �                           �             �  �       the_secs�           �  p          �  �           �  �
                        
  �
                    .text   �     =  &             .data   P                       .bss    �                       .rdata  `     [                .xdata  p     0                 .pdata  D     $   	                 �  ��  
   K  �                 �  c     �                    �  �     �  
                 �        0                    �  l     W                       7     �                          	                       �     O                    )  �     +                     4  �     �                .file   �  ��  gusermatherr.c         7  �                           M  �           [        .text   �     L                .data   P                       .bss    �                       .xdata  �                      .pdata  h                         �  ӗ  
   �                   �  ;                         �  J     r                     �  P     0                      �     �                      �     �                     )        +                     4  `     P                .file   �  ��  gxtxtmode.c        .text                           .data   P                       .bss    �                           �  Ț  
   �                    �  M     .                     �  �                            e      :                      �
     �                     )  P     +                 .file   �  ��  gcrt_handler.c         r                          .text         �               .data   P                       .bss    �                       .xdata  �                      .rdata  �     (   
             .pdata  �                         �  P�  
   �                   �  {     ~                    �  �     _                    �  �     0                      �      �  
                                               M                         )  �     +                     4  �     P                .file   �  ��  gtlsthrd.c             �  �                           �             �             �  P           �            �  �             P!      .text   �     b  "             .data   P                       .bss          H                 .xdata  �     0                 .pdata  �     0                    �  1�  
   �
  A                 �  �     a                    �       �                    �  �     0                    �  �                            ,"     x                     ^     %                    )  �     +                     4        (               .file   �  ��  gtlsmcrt.c         .text   P"                       .data   P                      .bss    `                           �  �  
   �                    �  Z     .                     �                               �$     :                      �     �                     )  �     +                 .file     ��  g    )            .text   P"                       .data   `                       .bss    `                          �  ��  
   �                    �  �     0                     �                               �$     :                           �                     )       +                 .file   H  ��  gpesect.c              =  P"                           P  �"          _  �"          t  p#          �  �#          �  0$          �  �$          �  �$          �  �%      .text   P"     �  	             .data   `                       .bss    p                       .xdata  �     ,                 .pdata  �     l                    �  ]�  
   �  �                 �  �     �                    �  �     �                    �  @     0                    �  �     �                       %     K                     -     T                       �     �                     )  @     +                     4  (	     (               .text   P&     2                 .data   `                       .bss    p                       .text   �&                       .data   `                       .bss    p                           )  p     +                 .file   |  ��  ggetopt.c              	  �&                       warnx   `'          	  �'      place   p      ambig   �	          +	  0	      noarg   �	          8	  �	          E	  �+          U	  `          g	  d          r	  h          	  P	          �	   
      getopt  �1          �	  �1          �	   2      .text   �&     �  t             .data   `     $                .bss    �                      .xdata       d                 .pdata  (     T                .rdata   	     B                    �  /�  
   <  �                 �  B     �                    �  �     ?                    �  p     0                    �  �                            c*     a	                     �     +                       �     .                    )  �     +                     4  P
     �               .file   �  ��  gmingw_matherr.    .text    2                       .data   �                      .bss    �                           �  k�  
   �                    �  �      .                     �  �                            �3     :                      �     �                     )  �     +                 .file   �  ��  gucrt_vfprintf.    vfprintf 2                       .text    2                     .data   �                     .bss    �                       .xdata  t                      .pdata  |                         �  	�  
   �                   �  �      8                    �  %     X                     �  �     0                      �3     r   	                   �     �                     )        +                     4  @     8                .file   �  ��  gucrt_snprintf.    snprintf@2                       .text   @2     9                .data   �                     .bss    �                       .xdata  |                      .pdata  �                         �  ��  
   �                   �  *"     9                    �  u%     _                     �  �     0                      p4     �   	                   �     �                     )  0     +                     4  x     8                .file   �  ��  gucrt_printf.c     printf  �2                       .text   �2     H                .data   �                     .bss    �                       .xdata  �                      .pdata  �                         �  K�  
   �  
                 �  c#     l                    �  �%     -                     �        0                      �4     �   	                   l     �                     )  `     +                     4  �     H                .file     ��  gucrt_fprintf.c    fprintf �2                       .text   �2     2                .data   �                     .bss    �                       .xdata  �                      .pdata  �                         �  ��  
   �                   �  �$     b                    �  &     F                     �  P     0                      �5     �   	                   N     �                     )  �     +                     4  �     8                .file     ��  g__initenv.c           �	  �          �	  �      .text   3                       .data   �                     .bss    �                          �  ��  
   �                   �  1&     �                     �  �                            6     [                      3                         )  �     +                 .file   X  ��  gucrtbase_compa        �	  3                           �	   3          �	  �3      _onexit  4          
   4          
  P                        4
  @4          ?
  p4      tzset   �4          M
                       _tzset  �4          i
  8          z
  <          �
  @          �
  T          �
  P      .text   3       "             .data   �     x   
             .bss    �                       .xdata  �     P                 .pdata  �     l                .rdata  `
                          �  B�  
   �  Y                 �  �&                          �  G&     |                    �  �     0                      p6     �                     �                            P     `                    )  �     +                     4  0
     �               .text   05      .data   p      .bss    �      .idata$7�      .idata$5H      .idata$4(      .idata$6
      .file   f  ��  gfake              hname   (      fthunk  H      .text   @5                       .data   p                       .bss    �                       .idata$2�                      .idata$4(      .idata$5H      .file   �  ��  gfake              .text   @5                       .data   p                       .bss    �                       .idata$40                      .idata$5P                      .idata$7      "                 .text   @5      .data   p      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�      .text   H5      .data   p      .bss    �      .idata$7�      .idata$5       .idata$4       .idata$6�      .text   P5      .data   p      .bss    �      .idata$7�      .idata$5(      .idata$4      .idata$6�      .text   X5      .data   p      .bss    �      .idata$7�      .idata$50      .idata$4      .idata$6�      .text   `5      .data   p      .bss    �      .idata$7�      .idata$58      .idata$4      .idata$6
      .file   �  ��  gfake              hname   �      fthunk        .text   `5                       .data   p                       .bss    �                       .idata$2�                      .idata$4�      .idata$5      .file   �  ��  gfake              .text   `5                       .data   p                       .bss    �                       .idata$4                       .idata$5@                      .idata$7�                      .text   `5      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   h5      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   p5      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   x5      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �5      .data   p      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�      .text   �5      .data   p      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   �5                       .data   p                       .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   F  ��  gfake              .text   �5                       .data   p                       .bss    �                       .idata$4�                      .idata$5                      .idata$7�     !                 .text   �5      .data   p      .bss    �      .idata$7@      .idata$5�      .idata$4`      .idata$6�      .text   �5      .data   p      .bss    �      .idata$7D      .idata$5�      .idata$4h      .idata$6�      .text   �5      .data   p      .bss    �      .idata$7H      .idata$5�      .idata$4p      .idata$6      .text   �5      .data   p      .bss    �      .idata$7L      .idata$5�      .idata$4x      .idata$6      .text   �5      .data   p      .bss    �      .idata$7P      .idata$5�      .idata$4�      .idata$66      .text   �5      .data   p      .bss    �      .idata$7T      .idata$5�      .idata$4�      .idata$6R      .text   �5      .data   p      .bss    �      .idata$7X      .idata$5�      .idata$4�      .idata$6l      .text   �5      .data   p      .bss    �      .idata$7\      .idata$5�      .idata$4�      .idata$6v      .text   �5      .data   p      .bss    �      .idata$7`      .idata$5�      .idata$4�      .idata$6~      .text   �5      .data   p      .bss    �      .idata$7d      .idata$5�      .idata$4�      .idata$6�      .text   �5      .data   p      .bss    �      .idata$7h      .idata$5�      .idata$4�      .idata$6�      .file   T  ��  gfake              hname   `      fthunk  �      .text   �5                       .data   p                       .bss    �                       .idata$2�                      .idata$4`      .idata$5�      .file   �  ��  gfake              .text   �5                       .data   p                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7l                       .text   �5      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �5      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text    6      .data   p      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�
      .text   6      .data   p      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�
      .text   6      .data   p      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�
      .text   6      .data   p      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6      .text    6      .data   p      .bss    �      .idata$7�      .idata$5       .idata$4       .idata$6&      .text   (6      .data   p      .bss    �      .idata$7�      .idata$5(      .idata$4      .idata$6<      .text   06      .data   p      .bss    �      .idata$7�      .idata$50      .idata$4      .idata$6J      .text   86      .data   p      .bss    �      .idata$7�      .idata$58      .idata$4      .idata$6R      .text   @6      .data   p      .bss    �      .idata$7       .idata$5@      .idata$4       .idata$6t      .text   H6      .data   p      .bss    �      .idata$7      .idata$5H      .idata$4(      .idata$6�      .text   P6      .data   p      .bss    �      .idata$7      .idata$5P      .idata$40      .idata$6�      .text   X6      .data   p      .bss    �      .idata$7      .idata$5X      .idata$48      .idata$6�      .text   `6      .data   p      .bss    �      .idata$7      .idata$5`      .idata$4@      .idata$6�      .text   h6      .data   p      .bss    �      .idata$7      .idata$5h      .idata$4H      .idata$6�      .text   p6      .data   p      .bss    �      .idata$7      .idata$5p      .idata$4P      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   �6                       .data   p                       .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file     ��  gfake              .text   �6                       .data   p                       .bss    �                       .idata$4X                      .idata$5x                      .idata$7     "                 .text   �6      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �6      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �6      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �6      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �6      .data   p      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .file   &  ��  gfake              hname   �      fthunk  �      .text   �6                       .data   p                       .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   ;  ��  gfake              .text   �6                       .data   p                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�     "                 .text   �6      .data   p      .bss    �      .idata$7|      .idata$5�      .idata$4�      .idata$6l
      .file   I  ��  gfake              hname   �      fthunk  �      .text   �6                       .data   p                       .bss    �                       .idata$2x                      .idata$4�      .idata$5�      .file   s  ��  gfake              .text   �6                       .data   p                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�                      .text   �6      .data   p      .bss    �      .idata$7L      .idata$5�      .idata$4h      .idata$6@
      .text   �6      .data   p      .bss    �      .idata$7P      .idata$5�      .idata$4p      .idata$6P
      .text   �6      .data   p      .bss    �      .idata$7T      .idata$5�      .idata$4x      .idata$6Z
      .text   �6      .data   p      .bss    �      .idata$7X      .idata$5�      .idata$4�      .idata$6b
      .file   �  ��  gfake              hname   h      fthunk  �      .text   �6                       .data   p                       .bss    �                       .idata$2d                      .idata$4h      .idata$5�      .file   �  ��  gfake              .text   �6                       .data   p                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7\                      .text   �6      .data   p      .bss    �      .idata$7      .idata$5h      .idata$4H      .idata$6"
      .text   �6      .data   p      .bss    �      .idata$7      .idata$5p      .idata$4P      .idata$6,
      .text   �6      .data   p      .bss    �      .idata$7       .idata$5x      .idata$4X      .idata$66
      .file   �  ��  gfake              hname   H      fthunk  h      .text    7                       .data   p                       .bss    �                       .idata$2P                      .idata$4H      .idata$5h      .file   �  ��  gfake              .text    7                       .data   p                       .bss    �                       .idata$4`                      .idata$5�                      .idata$7$     %                 .text    7      .data   p      .bss    �      .idata$7�
      .idata$5H      .idata$4(      .idata$6�	      .text   7      .data   p      .bss    �      .idata$7�
      .idata$5P      .idata$40      .idata$6
      .text   7      .data   p      .bss    �      .idata$7�
      .idata$5X      .idata$48      .idata$6
      .file   �  ��  gfake              hname   (      fthunk  H      .text    7                       .data   p                       .bss    �                       .idata$2<                      .idata$4(      .idata$5H      .file   �  ��  gfake              .text    7                       .data   p                       .bss    �                       .idata$4@                      .idata$5`                      .idata$7�
     &                 .text    7      .data   p      .bss    �      .idata$7�
      .idata$50      .idata$4      .idata$6�	      .text   (7      .data   p      .bss    �      .idata$7�
      .idata$58      .idata$4      .idata$6�	      .file   
  ��  gfake              hname         fthunk  0      .text   07                       .data   p                       .bss    �                       .idata$2(                      .idata$4      .idata$50      .file   h  ��  gfake              .text   07                       .data   p                       .bss    �                       .idata$4                       .idata$5@                      .idata$7�
     "                 .text   07      .data   p      .bss    �      .idata$7�
      .idata$5       .idata$4       .idata$6�	      .text   87      .data   p      .bss    �      .idata$7�
      .idata$5      .idata$4�      .idata$6�	      .text   @7      .data   p      .bss    �      .idata$7�
      .idata$5      .idata$4�      .idata$6�	      .text   H7      .data   p      .bss    �      .idata$7�
      .idata$5      .idata$4�      .idata$6�	      .text   P7      .data   p      .bss    �      .idata$7�
      .idata$5       .idata$4�      .idata$6�	      .text   X7      .data   p      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6z	      .text   `7      .data   p      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6^	      .text   h7      .data   p      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6N	      .text   p7      .data   p      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$64	      .text   x7      .data   p      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6	      .text   �7      .data   p      .bss    �      .idata$7|
      .idata$5�      .idata$4�      .idata$6	      .file   v  ��  gfake              hname   �      fthunk  �      .text   �7                       .data   p                       .bss    �                       .idata$2                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   �7                       .data   p                       .bss    �                       .idata$4                      .idata$5(                      .idata$7�
     
                 .file   �  ��  gcygming-crtend        �
  0>                       .text   �7                       .data   p                       .bss    �                           �  0>                         �  �                          �                           �
  H>                         )        +                 .idata$2        .idata$58      .idata$4      .idata$4�      .idata$5�      .idata$7`
      .rsrc       
    __xc_z         strcpy  p5          �
  P          �
  @5            �5            `          +  �
          G  �          e  H          x              �  X>          �  (          �  �          �  �           �  P7                       1  �          >  �          O  �          `  0          m      	        |  �          �  X6          �  �      __xl_a  0           �  h7          �  @          �  P          

  �          
  �      _cexit  6      getenv  7          B
  `  ��       Z
     ��       s
  �          �
              �
  �          �
      ��       �
     ��          0             �      __xl_d  @           .  X      _tls_end   	        S  �
      __tznameP5          i  �          u  @          �  07          �  �          �             �            �  0           �  P          �  �
                	    memcpy  �6            �          2  X          ?            M        puts    �5          ^  �
          �  �           �  X          �  0      malloc  �6      remove  �6      _CRT_MT P      optarg  �          �  @7          �  �          �              �  �            H            P          >  (          M     ��       e  �          �  �          �  �          �  �          �  8          �  �           �  �      opterr  �                        X          1  �          E  �          s   7          �            �  �           �  H          �  X          �  87          �  <             0           ,  8          9  P           K  �          f  p            @          �  H          �  �6          �  �          �  P          �  �          �  `      abort   `6            �
          @  �          T  P           d  �      __dll__     ��       �      ��       �  �5          �  `
          �  �          �  x7            �          #  �          0  �          ;  �6          I  �          X  �
          h  0          s  x           �  0          �     ��       �  4      strrchr �6          �  �      calloc  �6          
  �            �          0  �          :  �5          R  X          ^  �           �  �          �  P          �  P      Sleep   H7          �         _commode�           �  �          �  p          �  �            @>            �           C  �      _stat64 �6          W  d           �            �         optind  |          �  �      __xi_z  (           �  p          �  �          �  0          �             
  @          +  $      strstr  �6          _  �5      rand    05          j  p          �  �          �  �          �  �       signal  p6          �  �5          �  x           �              �  h      strncmp �5            @>            �      strncpy �5          *  �
          J  p          W  �           �  8          �  (           �  0          �      ��       �  �
            �          2            c             u  �          �  �          �             �            �  `          �            �     ��         �      strdup  `5          +  7          9  p7          Q  86          p   6          �  `7          �  h      fopen   �5          �  �          �  �           �                �               ��       (              7  �          G             x  h          �  �          �  x      fclose  �5          �  8          �  @          �        __xl_z  H       __end__              �  �          �               `      strcmp  h5          (  X>      __xi_a             6  P6          E            Q  X7      __xc_a              f  �          }     ��       �  P           �  P           �     ��       �  �      _fmode  �                         P          ,  x          E  �6          V             g  �          x  �          �  6          �             �  �          �  (          �  �
            x            �          &  �5          1            F  �      fputc   �5      __xl_c  8           S     	    optopt  x          `  �
          �             �             �  H          �  �          �  �           �  @          �  l          *  �          B  H5          M  \      _newmode�           {  H6      fwrite  �5      _time64 X5          �             �  �          �  �          �      ��       �      ��       �  �          �  0          �  P&          	   p              �5          ,   P      exit    h6          >      ��       Z       ��       r   P          �   8      atoi     7          �          _exit   06          �   �          �   �          �   6      strlen  x5          �   �          �   (6          !  �
          )!  `      atoll   (7          =!  �      strchr  �6          O!  �7          e!  @6          �!  �          �!  �          �!            �!  �           "  P          '"  �          8"  h          P"  �          \"  p      mkdir   �6          j"  �          y"             �"  P           �"  �5          �"             �"   6      free    �6          �"  �       �"  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame afc_client_copy_and_remove_crash_reports keep_crash_reports List_PanicLogs PANIC_FOUND List_Directories get_latest_powerlog_file move_file_from_device afc_delete_directory_recursive process_powerlog_and_cleanup generate_path WBAT_MODE .rdata$.refptr.optarg PRINT_MODE ClEAN_MODE extract_raw_crash_reports .rdata$.refptr.optind .text.startup .xdata.startup .pdata.startup idevicecrashreport.c __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names permute_args parse_long_options illoptstring recargstring getopt_internal posixly_correct.0 nonopt_end nonopt_start illoptchar recargchar getopt_long getopt_long_only local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp_GetEnvironmentVariableW __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone __imp_idevice_new_with_options _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode __imp___p___argc __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __rt_psrelocs_start afc_client_free __imp_lockdownd_service_descriptor_free __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ __imp_fputc .refptr.optind VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free __imp_getenv __imp_strncpy .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler lockdownd_service_descriptor_free .refptr.optarg TlsGetValue __imp_strcmp __bss_start__ __imp___C_specific_handler __imp_rand ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit afc_dictionary_free __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force lockdownd_strerror __imp_afc_file_open VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a target_directory __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection __imp_afc_read_directory service_client_new __imp_afc_dictionary_free __C_specific_handler afc_file_open __imp_afc_file_close __mingw_optreset .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf ___crt_xp_end__ __imp_lockdownd_start_service __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_strdup __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT __imp_atoi _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname __imp_strcpy __imp_lockdownd_strerror _tls_used __stdio_common_vsprintf __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ lockdownd_start_service .refptr._newmode afc_file_read __data_end__ __imp_fwrite __CTOR_LIST__ _head_lib64_libapi_ms_win_crt_utility_l1_1_0_a __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ idevice_set_debug_level __imp_strstr __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __lib64_libapi_ms_win_crt_filesystem_l1_1_0_a_iname __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection afc_file_close _tls_index __acrt_iob_func __native_startup_state ___crt_xc_start__ lockdownd_client_free ___CTOR_LIST__ __imp_snprintf .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_atoll _head_lib64_libapi_ms_win_crt_convert_l1_1_0_a .refptr.__mingw_initltsdyn_force __rt_psrelocs_size .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp___p___wargv __imp_strlen __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __p__wenviron GetEnvironmentVariableW _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit afc_remove_path _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __lib64_libapi_ms_win_crt_utility_l1_1_0_a_iname __imp_mkdir afc_client_new __imp_afc_remove_path __IAT_start__ __imp_afc_client_new __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit __DTOR_LIST__ __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ _head_lib64_libapi_ms_win_crt_filesystem_l1_1_0_a __subsystem__ __imp___stdio_common_vsprintf __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __imp_remove afc_read_directory __p___argc __imp_VirtualProtect idevice_free ___tls_end__ __lib64_libapi_ms_win_crt_convert_l1_1_0_a_iname .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __imp_fclose __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_strchr __imp__time64 ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ __minor_image_version__ __imp___set_app_type __imp_afc_client_free __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR __imp_afc_file_read afc_get_file_info DeleteCriticalSection _initialize_wide_environment __imp_service_client_new __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv __imp_afc_get_file_info __imp_fopen __imp__stat64 .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_daylight __p___wargv __mingw_app_type 