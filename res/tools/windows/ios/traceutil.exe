MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� �vh . �  � & ( 4   n     �        @                           �  `                                             �     �  �   �  `             �                           �e  (                   h�  (                          .text   �3      4                 `  `.data       P      :              @  �.rdata      `      <              @  @.pdata  `   �      N              @  @.xdata  �   �      R              @  @.bss    �   �                      �  �.idata     �      V              @  �.CRT    `    �      h              @  �.tls        �      j              @  �.rsrc   �   �      l              @  �.reloc  �          r              @  B/4               t              @  B/19     U�      �   z              @  B/31     �(      *   v             @  B/45     21   P  2   �             @  B/57        �     �             @  B/70     �   �     �             @  B/81     �   �     �             @  B/97     �&   �  (                @  B/113             *             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H��Y  1��    H��Y  �    H��Y  �    H�Y  f�8MZuHcP<HЁ8PE  tfH�OY  �
��  � ��tC�   ��.  �.  H�
Z  ����-  H��Y  ���  H��X  �8tP1�H��(Ð�   �~.  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�Y  �   1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H��Y  L�֎  H�׎  H�
؎  � ���  H�!Y  D�H���  H�D$ �*  �H��8��    ATUWVSH�� H�X  H�-��  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5LX  1�����V  �����  ��     ����L  ���e  H�qW  H� H��tE1��   1����X  H�
qX  �ۢ  H��W  H�
����H���,  �@  �ҍ  �{Hc�H��H���@-  L�%��  H�Ņ��F  H��1�I���+  H�pH���-  I��H�D I�H��H����,  H9�u�H�H�    H�-]�  ��  H��V  L�B�  �
L�  H� L� H�7�  �B-  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�5�V  �   ���������   ��)  ��������H��V  H�
�V  ��+  �   �������1�H�����f�     �c+  ���  H�� [^_]A\�f.�     H��V  H�
�V  �   �w+  �7���f�H����������+  �H��(H��U  �    ������H��(� H��(H��U  �     �z�����H��(� H��(��(  H���H��(Ð�����������H�
	   �����@ Ð��������������SH�� H��H�I8�?+  H�K0�6+  H�K@�-+  H�CHH��tH�H�+  H�CHH��+  H�KH�+  H��H�� [��*  f�     H�OK  ��t��H�:K  H�?K  ��HF��ff.�     ��wH�XK  ��Hc�H���f�     H�5K  ��     H�K  ��     H��J  ��     H��J  ��     H��J  ��     H��J  ��     H��J  ��     WVSH�� L��L�Ή�I��H��H��L��J  �(  H�V�H�KA��L��J  H�� [^_�n%  ff.�      1�H��tH��t#�H��tH��t�Q��	��f.�     ��@ ��D  ��ff.�     f���ff.�     ���ff.�     �H��H�D$ �  L�L$<�D$<    ��  ��u�D$<��tH��HÐH���������    ATUWVSH��@H��H��L��M��tj1�L�d$<�A��H�T M��H���D$ �  A)��D$<    �z  ��u&�D$<��tH�H9�r�H��H��@[^_]A\��     H������H��@[^_]A\�1���f.�     WVSH��0H��D��H�T$,H��A�   �P���H��uI�D$,��>���D�H���j(  H�H��H��t&I��H������H��H��H9�uH��0[^_�H��1(  H���������     WVSH�� H��H�JH���(  H��H��tI��H��H����'  �3 H��H�� [^_�H�
�H  �'  ���    SH��@H�D$8    ��  H�
�H  H���  H��H  H��I����  H�
5�  H���%  ��uqH�T$3L�L$4�D$ �  H�
�  A�   �D$4    ��  ��u�|$4tr�   ��  A�   �   H�
hH  I���E&  H���M  �H��@[�fD  �   ��  A�   �   H�
H  I���&  H���  �H��@[�@ H�
y�  H�T$8�o  ��u;�   ���  H�L$8H���  H�L$8��  H����  �H��@[�f.�     �   �]�  A�   �   H�
�G  I���%  H���  �:���ff.�      AVAUATUWVSH��  H�/Users/<USER>/L��L��L��H�Desktop/H�T$HH�Work/PhoH�D$PH�neCheck/H�T$XH�DevelopmH�D$`H�ent_ProjH�T$hH�ects/NewH�D$pH�/libimobH�T$xH�iledevicH��$�   H�e/tools/H��$�   H�collect_H��$�   H�foo.tar H��$�   H�ct_test/H��$�   H��$�   �r  �Å��@  H��F  ���%  I��H����  �)  H�
�F  H���  H�F  H��I���(  H���?  H���Y  H���
  H�
v�  H���f
  ���V  H�T$2L�L$4�D$ �  H�
E�  A�   �D$4    �*
  ���r  �|$4�g  �|$2�\  H�
�  H�T$8H�D$8    �
  ���:  H�
RF  H�|$3�q#  H��$�   �c�    �l$3@���1  H�
��  H��A�<   HǄ$�       �h���I��H���,  H��$�   M��   H����"  H����#  H�
m�  A�   H������H��t�L����"  L�d$ M��H��L��E  �   �  H���]#  L���#  H���  H�
�E  �"  �E�    �   �=�  A�   �   H�
nD  I���c"  H���k  L���K"  L���C#  �H�İ  [^_]A\A]A^ù   ��  A�   �   H�
�D  I���fD  �   �ř  A�    �   H�
KD  I����!  럐H����  H��D  H��I����  ������H���  H�fD  H��I����  H�������H���  H�MD  H��I���  H���������     �   �-�  A�   �   H�
�C  I���S!  ���4!  L���<"  ������    �   ��  A�   �   H�
TC  I������ �   �Ř  D��H��C  H����  ����D  �   ���  A�   �   H�
�C  I����   �����fD  AWAVAUATUWVSH���   H��H���a  L�IH��$�   �   L�UB  H���   H����   D�KL��$�   �
   L�>B  L���  D���  �C(E��u9L��C  M��M��L��H�K@H�|C  H��HDʃ�wzH��C  Hc�H���f����_  �P�L�
=A  ��H�?A  L�(C  LF�L�%#C  H�5"C  ��     H�
3C  ��  �H���   [^_]A\A]A^A_��    L�5A  �H�S0H�C8H�=�B  L�=�@  D�L�L$pH��L�D$@HD�H��L�D$(I��HD�E��L�t$xL��B  H�T$HME�H��$�   H�t$hD�\$`L��L�d$XH�t$PH�t$8H�D$0H�t$ H��$�   H�
�B  L��$�   M��H��$�   �  H�SHH��t6H�L�BH�t$0I��L�d$ L��H�
�B  H��HD�M��LD�H�D$(�[  �
   �  �����@ H�K@L��A  L�%�A  L�
�?  H��uH�
�A  H�5�A  L�5�?  �����L�5�?  �����@ L�5�?  ����@ L�5�?  ����@ H�
�A  H���   [^_]A\A]A^A_�)  �L�5r?  �t���@ L�5[?  �d���@ H��t����fD  �ff.�     @ AWAVAUATUWVSH��XI��H��H��e�  �P   �  H��H����  A�G	H�L$H�A�G7H�D$H��  E�gmE�ouI���   �o A���    F�oHN�@ �F$A�GD�F(A�Gkf�D$(A�Gy�D$<��  H�����H���   ��  1��f�     H9�tH��H��A���    u�L���   H��H�T$0��  H�T$0H��H����  I��H���  � H�~8�|$(K�7H�T$(H�O�  H�T$(H��H����  I��H���U  �; H�^0I�L$L�M�4?�l  H��H����  M��L��H���"  B�# H�^@I9�rH�FH    H��H��X[^_]A\A]A^A_�D�t$<I9�sڹ   �  H�FHH��H����   I�MI���  K�,'H��H����   H��M��H��H���  B�/ H�{I�NM�M��  H��H��taM��L��H���y  B�7 H�} �_��� �   1��[�  A�   �   H�
�?  I���  �0���@ �   A��   1��p���H�
K=  ��  �H�
==  ��  H�nH�a���H�
(=  ��  H�N8�  H�N0�
  H�N@�  H��1���  �����H�
�<  1��  ����H�
�<  �  ����H�
�<  �}  �{���H�
�<  �l  �6���ff.�     @ UWVSH��H���  H�
�>  H���  H��<  H��I���  ���  �f  H��>  H��I���  Hc��L  H��>  H��I���j  �<   �0  H��>  H��I���N  H�
�{  H���  ����   H���'  H�T$,H�
�{  A�   ����H��t2�   ���  A�   �   H�
E>  I����  �H��H[^_]� �\$,H���  H�
5{  I��H��H���_���H9�urH����   H���  �>H���b  H���H  �F��	ǉ�H���t�   ��  A�   �   H�
N;  I���C  H��H��H[^_]�C   �   ��  A�   �   H�
�=  I���  H��H��H[^_]��   1�1�H����  H����  H�
]z  I��H��H������H9�t"�   ��  A�   �   H�
M=  I���L�D$0��H��H�D$0    �  H���p  H�L$0H�;=  �  H��H���s  H�D$8    H�l$8H���U  H�\$8H���  H�=  H����  ��t<�   ��  I��H��<  H���  H�L$8H��t��  H�L$0�  �%����H��H�|$+��  �8�H�t$8H������H��H�������d   �  H��tH���Q���H���  H�
*y  A�   H���T���H����   �|$+��   H�
 y  H��A�<   H�D$8    ����H��H���u����   ��  A�"   �   H�
`<  I���8  �_��� �H��H���/��� ��H���!���D  �>H��H������f.�     �   ���  A�   �   H�
�;  I����  �����fD  H��:  �����   �q�  A�   �   H�
�;  I���  �   �%  ������%��  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%ҋ  ���%:�  ���%ڎ  ���%ʎ  ���%��  ���%��  ���%��  ���%��  ���%z�  ���%j�  ��H��(H�'  H� H��t"D  ��H��&  H�PH�@H��&  H��u�H��(�fD  VSH��(H�@  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^���� 1�fD  D�@��J�<� L��u��fD  ��v  ��t�D  ��v     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H�#?  �8t�    ��t��tN�   H��([^�f�H�1�  H�5*�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�,<  Hc�H����    H�;  �DA �y�qH�q�   �  �DD$0I��H��;  �|$(H��I���t$ �  �t$@|$P1�DD$`H��x[^ÐH��:  ��    H��:  ��    H��:  �s���@ H�	;  �c���@ H��:  �S���H�#;  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�  A�   �   H�
";  I����  H�t$(�   �{  H��H��I����  �@  ��    WVSH��PHc5�t  H�˅��  H��t  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H�Et  H��H��H�H�x �     �#  �WA�0   H�H�t  H�T$ H�L�o�  H���}   �D$D�P����t�P���u��s  H��P[^_� ��H�L$ H�T$8A�@   �   DD�H�s  H�KI��H�S��  ��u��ʇ  H�
C:  ���d���@ 1��!���H�zs  �WH�
�9  L�D�>���H��H�
�9  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%$s  E��tH�e[^_A\A]A^A_]�fD  ��r     �9	  H�H��H��   H����  L�-{;  H��;  ��r      H)�H�D$0H��r  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5�:  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
�8  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  �.q  ������H�5��  1�H�}�D  H�q  H�D� E��t
H�PH�HI����A��H��(D;%�p  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5�8  �s�;H��L�>H���Z����>L9�r��������H�

7  �����H�
�6  ���������H��XH��o  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
�o  �T  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H��6  Hc�H��� 1ҹ   ��
  H���>  H���  H�Jo  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �y
  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �
  H���@����   �   ��  �f�     1ҹ   ��  H��t*H�������   ���i���f�     �   ���T����   �   �  �@����   �   �  �,����   �   �}  �����������ATUWVSH�� L�%n  L����  H��m  H��t6H�-�  H�=ԁ  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%��  WVSH�� ��m  ��H�օ�u
1�H�� [^_ú   �   �	  H��H��t3H�pH�5~m  �8H���C�  H�Lm  H��H�Bm  H�C�@�  묃��멐VSH��(�,m  �˅�u1�H��([^�D  H�5)m  H�����  H�
�l  H��t'1��H��H��tH���9�H�Au�H��tH�B�m  H���̀  1�H��([^� H��l  ��ff.�     @ SH�� ����   w0��tL��l  ����   �|l     �   H�� [�f�     ��u�]l  ��t��<�����f.�     �Bl  ��uf�8l  ��u�H�$l  H��t�    H��H�[�
  H��u�H�
 l  H��k      ��k      ��  �l����k����   H�� [������f�     H�
�k  ��  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���i  H��w{H�T3  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���  ��u�H��H�� [^_��    1�H��H�� [^_� H��2  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H�I2  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L�	2  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H��1  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H�I1  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L��0  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������H��(��u1�H��(Ð��Hi��MbH��&�U{  1�H��(Ð�����AWAVAUATUWVSH��8H��H��t#�k  �D$,H�Ń�~H�H��H��:XXXXt!�   �    �����H��8[^_]A\A]A^A_�f�zXXu�1���u� H���ǅ�t	�|�Xt��Lc�N�t.D  9|$,~W�]�N�<.)�L�%J.  L��    �  I����H�Hi�C!�A��A��H�� ���D)�k�>)�Hc�A�A�G�I9�u�A��  A�@   ��  H����  ����<����,  �8�s����$�����������������H��8E1�L�D$ I��H��1��o  H��8Ð�H��HH�D$hL�L$hM��I��H�D$(H�ʹ   H�D$     H�D$8�D  H��HÐ������VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H���   ���   �����  �  � ��  H� H��w  H� H�M��t	A�$�3  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���  ���   ����X  �+  � ��2  H� H��  H� H�M��t	A�$��  1�H�� [^_]A\�fD  SH�� H���  ���    HD�H�� [�f�H��,  �8 t1�Ð��  ff.�     SH�� �˹   �?  A��H��+  H���m�����   ��  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8�  H��H�ff.�     H��(H�,  ��   H�7  �z   H�#  �f   H�  H��(�f.�     H��(H��+  ��N   H��  �:   H��  �&   H��  H��(Ð����������%"y  ���     �%�x  ���%�x  ���%�x  ���%�x  ���%�x  ���     �%�x  ���%�x  ���%�x  ���     �%x  ���%x  ���%x  ���%x  ���%x  ���%x  ���%x  ���%x  ���%x  ���%x  ���%x  ���%x  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%Bv  ���%Bv  ���%v  ���%v  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���     �%�u  ���%�u  ���%ju  ���     �%Ju  ���%:u  ���%*u  ���%u  ���%
u  ���%�t  ���%�t  ���%�t  ���%�t  ���%�t  ��AUATUWVSH��(��H���J������N  ��tH�OH��#  ��������i  �   1�H�
�_  �	������\  1������H�
�_  L��#  H��_  ��������l  H�
�_  L��_  H��#  ��������  H�o_  H�
x_  L�Y_  �d����ƅ��Y  H�
C_  H�4_  �?���H�='_   �$  9���  Hc�H��#  L�$�L�,�    L����������  H��#  L�����������  H��#  L���������t!H��#  L�����������  ��^     �����9���  �]���H�
�^  ����H�
�^  ����H�
�^  ����H�
�^  �����8H�W�   �����   ��t  A�   �   H�
,"  I�������   ��H��([^_]A\A]ù   �Qt  A�   �   H�
"  I���w���H�
^  �C���빹   �t  A�.   �   H�
L"  I���D���H�
�]  �����H�
�]  �����H�
�]  ������k���H�W!  H�
n!  �����S����   ��s  A�'   �   H�
�!  I�������H�
o]  ����H�
k]  ����������9��������������������   �\s  A�   �   H�
"  I������������   �2s  A�   �   H�
�!  I���X���H�
�\  ���������J�L/�U����������   ��r  M��H��!  H�������d����������������{�����������������������C @           ��������                                �C @           ��������        ����                           ����             ; @            ; @           `; @           �; @           �� @   �� @   P= @   �= @   �; @    = @   p< @    < @   �P @   �P @   �P @      �p  Q @    Q @   PDT PST  = @   �< @                                                                                                                                                                                                                                           [37m [32m [31m NOTICE INFO DEBUG USER_ACTION ERROR FAULT UNKNOWN ������̴��ܴ���������������������������������������������������������%Y-%m-%d %H:%M:%S .%06d malloc PidList Request Could not send request
 Could not receive first byte
 Could not receive response
    Could not create temporary file
 wb     Could not open temporary file
 CreateArchive SizeLimit AgeLimit StartTime Invalid first byte received
 Generating File....  Invalid magic byte: %d
 Error receiving data
 tar -xf %s -C %s Success! [35m [36m [0m  unknown Invalid SyslogEntry Failed to format timestamp    %s%s%s%s %s%s%s{%s%s%s}[%s%u%s] <%s%s%s>: %s%s%s  [%s%s%s - %s%s%s] $�������������������������������������������������������������Խ��Ľ��Data length is too short
 NULL StartActivity MessageFilter Pid StreamFlags  Error receiving length_length
 Error receiving length
  Error receiving response data
 Status RequestSuccessful Invalid status: %s
 No status in response
 Device Disconnected! 
       Error receiving syslog entry data
 COPYRIGHT PHONECHECK LLC 2025 %s - Ver 2.0.1
 -u No device found
 ideviceostrace     Could not connect to lockdownd
 com.apple.os_trace_relay        Could not start os_trace_relay service
 Could not create property list service client
 Could not get service client
 pidlist collect syslog syslogcolor Unknown command: %s
 Provide Correct Args!
             �) @                            � @   � @   �� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ����L�������l���|�������\���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
       ��� ��� ��� ��� ������� �����������{���                        XXXXXX                          abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789  runtime error %d
               0P @           @P @           �C @              @            q @            q @           `e @           �P @            � @           �� @           �� @           �� @           �� @            � @           � @           p� @           x� @            � @           � @           � @           (� @           �� @            P @           � @           01 @           �* @           �� @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P  �  t�  �  �  |�  �  h  ��  p  �  ��  �  �  ��       ��      ��     $  ��  0  i  ��  p  �  ��     x  ��  �  �  Đ  �  B  А  P  �  ؐ     �   �  �   �   �  �   �#  �   $  ,(  $�  �(  )  H�   )  �)  P�  �)  �)  \�  �)  �)  `�  �)  �)  d�  �)  q*  l�  �*  �*  x�  �*  �+  |�  �+  �+  ��  �+  	,  ��  ,  r-  ��  �-  �0  ��  �0  1  ȑ   1  ,1  Б  01  �2  ԑ  �2  `3  ܑ  `3  �3  �  �3  Q4  ��  `4  R5  �  `5  �5  �  �5  �5  �  �5  }6  �  �6   7   �   7  77  $�  @7  �7  (�  �7  �7  ,�   8  �8  0�  �8  V9  4�  �9  �9  8�  �9  �:  @�   ;  ;  X�   ;  Y;  `�  `;  �;  h�  �;  �;  t�  �;  �;  |�   <  j<  ��  p<  �<  ��  �<  �<  ��   =  =  ��   =  N=  ��  P=  �=  ��  �=  �=  ��  �=  >  Ē  0@  �C  4�  �C  �C  ̒                                                                                                                                                                      B   b  
 
20`pP�	 B  p?     �  �  01  �  	 B  p?     �    01     B         20       20`p             �  
 
r0`pP� R0`p 20`p r0	 V 
0	`pP���  
  0`
p	P����   	 �0`
p	P����   �0`pP   B0`pP��   B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   B  	 b0`
p	P����   b   �   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                     @�          �  h�  ��          (�  ش  ��          p�  �  �          ��  @�  (�          ��  P�  @�          ܾ  h�  P�          �  x�  x�          <�  ��  ��          d�  ��  ��          ؿ  е  P�          ,�  x�  ��          X�  �  س          ��   �  �          ��  8�   �          ��  H�                      ��      ��      ��      ̷      �      �      0�      L�      p�      ��      ��      �      �              0�              H�      `�      x�      ��      ��      ��      ڹ      �      �      �              �              �      *�              :�              D�      T�      ^�      f�              p�      ��              ��      ��              ��      ��      ̺      ں      �      ��      �      ,�      :�      D�      L�      n�      ��      ��      ��      ̻      Ի      ܻ      �      �              ��      �      �      *�      D�      `�      z�      ��      ��      ��      ��      ��              ��      ��      ȼ              Ҽ      �      �      ��      
�      �               �              (�      @�      \�      l�      ��      ��      ��      ��              ��      ��      ��      ̷      �      �      0�      L�      p�      ��      ��      �      �              0�              H�      `�      x�      ��      ��      ��      ڹ      �      �      �              �              �      *�              :�              D�      T�      ^�      f�              p�      ��              ��      ��              ��      ��      ̺      ں      �      ��      �      ,�      :�      D�      L�      n�      ��      ��      ��      ̻      Ի      ܻ      �      �              ��      �      �      *�      D�      `�      z�      ��      ��      ��      ��      ��              ��      ��      ȼ              Ҽ      �      �      ��      
�      �               �              (�      @�      \�      l�      ��      ��      ��      ��              f idevice_free  k idevice_new   m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_service_descriptor_free � lockdownd_start_service   property_list_service_client_free 	property_list_service_client_new  property_list_service_get_service_client  
property_list_service_receive_plist   property_list_service_send_xml_plist  8service_receive_with_timeout   plist_print_to_stream DeleteCriticalSection =EnterCriticalSection  tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery  W atoi   __p__environ   __p__wenviron + _unlink  _set_new_mode  calloc   free   malloc  
 __setusermatherr  # _fdopen  __C_specific_handler  xmemcpy   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit # _errno  % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  c perror  g signal  j system   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf   __stdio_common_vsprintf  _close  s _sopen  � fclose  � fwrite  � putchar � puts  � strcmp  � strlen  � strncmp 	 __daylight   __timezone   __tzname  ' _localtime64  < _tzset  Q strftime   rand   plist_dict_get_item    plist_dict_insert_item     plist_free     plist_from_bin    , plist_get_string_val  8 plist_new_dict    ; plist_new_string  = plist_new_uint     �   �   �   �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  libimobiledevice-glue-1.0.dll   (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  KERNEL32.dll    <�  api-ms-win-crt-convert-l1-1-0.dll   P�  P�  api-ms-win-crt-environment-l1-1-0.dll   d�  api-ms-win-crt-filesystem-l1-1-0.dll    x�  x�  x�  x�  api-ms-win-crt-heap-l1-1-0.dll  ��  ��  api-ms-win-crt-math-l1-1-0.dll  ��  ��  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  api-ms-win-crt-stdio-l1-1-0.dll ܰ  ܰ  ܰ  api-ms-win-crt-string-l1-1-0.dll    �  �  �  �  �  �  api-ms-win-crt-time-l1-1-0.dll  �  api-ms-win-crt-utility-l1-1-0.dll   �  �  �  �  �  �  �  �  libplist-2.0.dll                                                                                                                                                                                                                                                            0 @                    @                   �) @   �) @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          @     ȣ   P  0    �P�`�p�����������������ȠРؠ������ `  H   `��������� �0�@�P�`�p�����������Щ�� �� �0�@�P�`�p����������� �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                            ,               @   $                      <    �&       P @   �      0@ @   t                      ,    1f       �( @   �                           �l                           �r                       ,    !s       �) @                              �t                       ,    ,u       �) @   �                           �|                           }}                       ,    �~       �* @   �                       ,    .�       �+ @                              ł                       ,    W�       �+ @   =                      ,    ��       �0 @   L                           ��                       ,    �       01 @   �                      ,     �       �2 @   b                          Ӹ                           \�                       ,    ,�       `5 @   �                          ��                       ,    ��       �9 @   *                       ,    ��       �9 @   "                      ,    ��        ; @                          ,    4�        ; @   9                       ,    ��       `; @   H                       ,    ��       �; @   2                           .�                       ,    ��       �; @                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   �?   �  >GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden �  �  �          9  
char {   	size_t #,�   
long long unsigned int 	ssize_t -#�   
long long int 
short unsigned int 
int 
long int 	__time64_t {#�   �   	time_t ��     
unsigned int 
long double 
signed char 	uint8_t $j  U  
unsigned char 
short int 	uint16_t &�   	uint32_t ('  	uint64_t *0�   	useconds_t T'  
long unsigned int 	plist_t Y�  ?�   ||  PLIST_ERR_SUCCESS  PLIST_ERR_INVALID_ARG PLIST_ERR_FORMAT ~PLIST_ERR_PARSE }PLIST_ERR_NO_MEM |PLIST_ERR_UNKNOWN �~ 	plist_err_t ��  �   'X  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y 	idevice_error_t 0�  )�  2 |  *�  	idevice_t 3�  p  {   �  �   �  �   $�  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ 	lockdownd_error_t P�  )&  R)�  *&  	lockdownd_client_t S#�  �  +lockdownd_service_descriptor `:	  
port a�   
ssl_enabled b
U  
identifier c�   	lockdownd_service_descriptor_t e.a	  �  �   	"7
  SERVICE_E_SUCCESS  SERVICE_E_INVALID_ARG SERVICE_E_MUX_ERROR }SERVICE_E_SSL_ERROR |SERVICE_E_START_SERVICE_ERROR {SERVICE_E_NOT_ENOUGH_DATA zSERVICE_E_TIMEOUT ySERVICE_E_UNKNOWN_ERROR �~ 	service_error_t 	+f	  )n  	-'[
  *n  	service_client_t 	.!y
  O
  �   
#�  PROPERTY_LIST_SERVICE_E_SUCCESS  PROPERTY_LIST_SERVICE_E_INVALID_ARG PROPERTY_LIST_SERVICE_E_PLIST_ERROR ~PROPERTY_LIST_SERVICE_E_MUX_ERROR }PROPERTY_LIST_SERVICE_E_SSL_ERROR |PROPERTY_LIST_SERVICE_E_RECEIVE_TIMEOUT {PROPERTY_LIST_SERVICE_E_NOT_ENOUGH_DATA zPROPERTY_LIST_SERVICE_E_UNKNOWN_ERROR �~ 	property_list_service_error_t 
,~
  	property_list_service_private 
.5  @property_list_service_client_private 	property_list_service_client_t 
/(X  �  
float p  f  A
double 
_Float16 
__bf16 5JOB_OBJECT_NET_RATE_CONTROL_FLAGS '  �c
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  {   s
  �     �  �  BtagCOINITBASE '  ��
  COINITBASE_MULTITHREADED   5VARENUM '  
	?  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � +_iobuf !
f  
_Placeholder #�    	FILE /?  +tm $d
  
tm_sec e	�    
tm_min f	�   
tm_hour g	�   
tm_mday h	�   
tm_mon i	�   
tm_year j	�   
tm_wday k	�   
tm_yday l	�   
tm_isdst m	�     s   device �  	X� @   C  �  	P� @    service  :	  	H� @    plist_client  1  	@� @    service_client `
  	8� @    useColor $�   	0� @   '  *  NOTICE  INFO DEBUG USER_ACTION ERROR_LOG FAULT_LOG  	SyslogLogLevel 1�  64K  
category 5�   
subsystem 6�   	SyslogLabel 7  6P:�  
pid ;�   D�  <s  
level =  (
image_name >�  0
filename ?�  8
message @�  @
label A�  H K  	SyslogEntry B_  atoi ��     �   property_list_service_client_free 
J�  ;  1   property_list_service_get_service_client 
��  {  1  {   `
  lockdownd_service_descriptor_free >�  �  :	   property_list_service_client_new 
?�  �  �  :	  �   1  lockdownd_client_free ��    �   lockdownd_start_service ��  S  �  �  S   :	  idevice_free �X  w  �   lockdownd_client_new_with_handshake ��  �  �  �  �   �  ,idevice_set_debug_level l�  �    idevice_new �X      �   �  usleep 4�   #  �   Eexit � 7  �    strcmp ?�   U  �  �   $plist_get_string_val �
~  �  x
   plist_dict_get_item �
�  �  �  �   plist_from_bin �|  �  �  �  �   �  _localtime64 ��  �  �   s    printf ��     �  - system �   9  �   fwrite ��   b  k  �   �   g   f  b  fprintf ��   �  g  �  - fclose j�   �  b   plist_new_uint �
�  �  �   unlink ��   �  �   close J�   �  �    fdopen 	b    �   �   mkstemp ��   4  �   ,plist_print_to_stream ;]  �  b   property_list_service_receive_plist 
��  �  1  �   ,plist_free �
�  �   __acrt_iob_func ]b  �  '   property_list_service_send_xml_plist 
W�    1  �   $plist_dict_insert_item �
?  �  �  �   plist_new_string �
�  b  �   Fplist_new_dict �
�  memcpy 2�  �  �  f  �    $perror ��  �   $free �  �   malloc �  �  �    service_receive_with_timeout 	�7
  #  `
  �  �  #  '   �  snprintf G�   O  �  �   �  - strftime ��   y  �  �   �  ~     y  7main ��   0@ @   t      �[  %argc ��   �  �  %argv �x
  
    udid �	�  8  0  commandIndex ��   t  \  G  !pid �
�    &�  L  pid 
�   �  �  �A @   [  {C @   �   F@ @   (?  d@ @   7  x  Q	)d @    @ @   �  �  R	X� @    �@ @   �  �  R0 �@ @   w  �  Q	P� @   X	=d @    �@ @       Q	pd @   X	H� @    �@ @   �  %  X	@� @    	A @   ;  D  Q	8� @    =A @   7  i  R| Q	e @    TA @   7  �  R| Q	
e @    kA @   7  �  R| Q	e @    ~A @   7  �  R| Q	e @    �A @     �A @   �  �A @   �  �A @   X  �A @   �  #  R2 B @   7?  L  R	,d @   Q1X@ 'B @   �  c  R2 AB @   7?  �  R	Pd @   Q1XO MB @   X  ZB @   �  �  R2 tB @   7?  �  R	�d @   Q1X. �B @   �  �B @   �  �B @   X  �B @     -  R	d @   Q	�c @    �B @   �  D  R2 �B @   7?  n  R	�d @   Q1X' �B @   �  �B @   X  C @   5  C @   �  �  R2 6C @   7?  �  R	=e @   Q1XF FC @   �  �  R2 `C @   7?    R	�d @   Q1XM lC @     �C @   �  9  R2 �C @   l  Q	(e @   X|   8syslog_stream @ $ @   ,      ��%  %pid @�   	  �  �  B
�  T  J  .c  O�%  ��length_length T�  �  ~  length_data V�%  �  �  ?  `�  �  �  response_data g�  	    response_plist n
�  �@status_node r
�  )  %  &s     status t�  �Hc& @   U  �  Qv  �& @   7  �  Rs Q	~c @    �& @   �  �  R2 �& @   l  �  Q	�c @   Xs  �& @   �  �& @   �  Rs   &�  �!  start_byte �
U  ��data ��  �HS  �
�   C  ?  ]  ��%  ]  Y  " ,  �& @   �& @   
       ��   ,  s  q  �& @   f<  Rs   �& @   �%  �   Rt  �& @   *,  �   Rs  �& @   
  �   Rd ' @   �  !  Rt  ' @   �8  .!  Qu X1 O' @   �7  L!  Qv X< f' @   �  c!  R2 �' @   7?  �!  R	�c @   Q1X" ( @   �  �!  R2 !( @   7?  �!  R	�c @   Q1XF ,( @   #  R1  "�:  �$ @   �$ @           T"  �:  �  �   �:  % @   W  `["  �:  �  �  �:  �  �  W  �:     �    $ @   b  $ @   ?  �"  R	�b @    0$ @     �"  Rs Q	�` @    :$ @   �  �"  R
�� L$ @     �"  Rs Q	c @    T$ @   �  #  Rt  $ & f$ @     -#  Rs Q	c @    p$ @   �  E#  R< �$ @     j#  Rs Q	c @    �$ @   �  �#  Qs  �$ @   �  �#  Rs  �$ @   �8  �#  Q��X4 �$ @   �  �#  R2 �$ @   7?  �#  R	 c @   Q1XN �$ @   �  $  Rs  % @   �8  .$  Qt Xs  [% @   �  E$  R2 u% @   7?  n$  R	�` @   Q1XG 9�% @   �  �% @   �  �$  R2 �% @   7?  9�% @   �  �% @   �  �$  Rt  �% @   �  �$  Rs  �% @   �8  �$  Qt Xs  �% @   �  %  R2 (& @   �  5%  Rt Qu X�@ 0& @   �  M%  Rt  A& @   ~  l%  Q	wc @    �& @   �  �' @   �  �%  R2 �' @   7?  R	�c @   Q1XF  U  �%  �    U  �  7parse_syslog_entry ��%  �  @         ��+  %data �0�+  �  �  :?  �=�       ]  ��%  t  f  offset ��   �  �  rawtime �  ��F  �	�   �  �  image_name_size ��  !    message_size ��  [  Q  subsystem_size �  �  �  category_size �  �  �  filename_length 
�   i	  _	  "�:  ! @   ! @           �J'  �:  �	  �	   9<  *! @   �   ��'  Q<  �	  �	  0! @   �  R��  "�:  !! @   !! @           ��'  �:  �	  �	   �:  q! @   �   �'  �:  �	  �	   "�:  q! @   
q! @          (  �:  �	  �	   �7  �! @   �   �(  H�7  �7  �	  �	  �   �7  
  
  �! @   �  �! @   P?  �(  Ru Q��Xs  �# @   �  R	�` @      �7  �! @    �   @)  �7  ?
  ;
  �7  [
  U
  �   �7  �
  z
  " @   �  �(  Ru #" @   P?  #)  Rs Q��Xu  �# @   �  R	�` @      �7  7" @      �)  �7  �
  �
  �7  �
  �
    �7  �
  �
  <" @   �  �)  R| V" @   P?  �)  Rs Q~ X|  �# @   �  R	�` @      �7  �" @    &  0#*  �7      �7  &    &  �7  _  W  �" @   �  ;*  R} �" @   P?  b*  Ru Q | "X}  l# @   �  R	�` @      �7  �" @    C  4"+  �7  �  �  �7  �  �  C  �7  �  �  �" @   �  �*  R~ �" @   P?   +  Ru Q} X~  ^# @   �  R	�` @      
! @   �  5+  RP �" @   �  L+  R@ # @   �  c+  R2 7# @   7?  �+  R	�b @   Q1XI �# @   �  �+  R	�` @    �# @   �  �# @   �  �# @   �  �# @   �  �# @   �  R	�` @     e  Ifree_syslog_entry �*,  J]  �%�%   8print_syslog_entry c  @   �      ��.  :]  c&�%  �  �  time_buffer j
�.  ��microsecond_buffer q
�.  ��!level_color u�  !label_color v�  !process_color w�  !pid_color x�  reset_color y�  6  ,  �;  � @    �   |4-  �;  r  j   <  � @   �   u*[-  +<  �  �   ? @   O  �-  Rv QNX	�` @   Ys h @   (  �-  R} Q:X	�` @    � @   i?  �-  R	*b @    � @     =.  R	Hb @   Xv Y} w t w8t w� t w� | w� t w� ~ w�t w�t    @     v.  R	yb @   Q| Yt w | w0t    @   ~?  �.  R: '�  @   i?  R	b @     {   �.  �    {   �.  �   	 ;collect �P @   �      ��4  out ��  �  �  size_limit �(�  �  �  age_limit �=�  3
  %
  start_time �Q�  �
  v
  /�  �
�  �
  �
  <temp_file ��4  ��z#temp_fd �	�   %    #temp_fp �b  Z  N  first_byte &
{   ��z.�  '�  ��zret (X  �  �  .�  2
�  ��zcommand W
	5  ��{&�   71  magic_byte ?
{   ��zdata H�  ��{S  I
�   �  �  � @   �7  `0  Qt X< � @   9  �0  Rv Q1Y~  � @   �  �0  Rv  � @   �8  �0  Qu X1 � @   �  �0  R2 � @   l  �0  Q	�a @   Xv 8$8& � @   �  1  R2 � @   7?  R	�a @   Q1XE  ^ @     O1  R}  v @   �  t1  Rs Q	1a @    � @   b  � @   ?  �1  R	Wa @    � @     �1  Rs Q	�` @    � @   �  �1  Qs   @   �  
2  Q��zX1Y��zw 
� > @   ]  #2  Q��z W @   i?  B2  R	�a @    � @   �  Z2  R~   @   (  �2  Rt Q
 X	�a @   Y} w |   @     �2  Rt   @   �  �2  R}   @   �  �2  Rs  ' @   i?  �2  R	�a @    ; @   �  3  R2 U @   7?  ] @   �  63  Rs  e @   �  N3  R~  n @   �  f3  R}  � @   �  }3  R2 � @   �  �3  R2 � @   7?  �3  R	a @   Q1X  � @   �  �3  Rt  � @     �3  Rs Q	xa @    � @   �  4  Rv  
 @     84  Rs Q	ea @     @   �  P4  Ru  - @     u4  Rs Q	oa @    K @   �  �4  R2 e @   7?  �4  R	8a @   Q1XN l @   �  �4  Rs  t @   �  �4  R}  � @   �  R2  {   	5  �   l {   5  K�   � Lget_PIDLIST �� @   r      ��7  /�  �
�  �  �  0�  �
�  �h<buf �c
  �c0�  ��  �d#ret �X  �  �  � @   b  � @   ?  �5  R	�` @     @     �5  Rs Q	�` @     @   �  �5  Qs  C @   �  #6  Q�cX1Y�dw 
� Y @   �  :6  R2 s @   7?  c6  R	�` @   Q1XM | @   �  {6  Rs  � @   �  �6  R2 � @   7?  �6  R	�` @   Q1XG � @   �  �6  Rs  � @   ]  �6  Q�h � @   �  7  R1 � @   4  � @   �    @   �  47  Rs   @   �  K7  R2 5 @   7?  t7  R	�` @   Q1XK = @   �  Rs   try_decode ��  �7  1data �!�+  ?  �.�   2output ��   3recv_prefixed ��     @   x       ��8    �(`
      �  �7s
  E  =  endianity �D{   l  f  /?  ��  �  �  0c  �
�%  �\  @   �8  r8  Rt Q�\X4 > @   �  �8  Rs  T @   �8  �8  Rt Xs  o @   �   3recv_val ��   p @   �       ��9    �#`
  �  �  �  �1�  �  �  size �@�   4  (  #total_received ��   m  e  h   #received ��   �  �  M�9  � @   x   �:  �  �  :  �  �  �9  �  �  x   =:  �L(:  �  �  � @   �  Ru Qv s "Xt s Y| w 
�     recv_service_data �	�   4:    �,`
  �  �:�  ?  �I�   N�  ��  2ret �X   3read_uint8_le �U    @          �m:  O�  �&�+  R read_uint16_le �
�  �:  �  �(�+   read_uint32_le 
�  �:  �  (�+   read_uint32_le_windows t
�  �:  �  t0�+  ?  t?�   2value u�   ;format_timestamp fp @   B       ��;  �  f"�    
  F  f1�   4  ,  �  fE�  _  U  size fT�   �  �  � @   O  �;  Rs Qt X	�` @   Y�R '� @   (  R�X#Q�YCX	�` @   Y�Q  syslog_level_to_string X
�  <  1level X3   get_log_level_color F
�  9<  1level F0   Plocaltime &�  a<  Q_Time >a<   "   ,  P @   W       ��<  ,  �  �  a @   �  j @   �  s @   �  � @   �  � @   �  � @   �  '� @   �  R�R  <  � @   %       �=  +<  �  �   �;  � @   �       �D=  �;    �   �:  � @   ;       ��=  4�:  R�:  !    �:  K  =   �:    @          ��=  4�:  R m:   @          ��=  4�:  R �9  0 @   9       �P>  �9  �  �  :  �  �  :      =:  �l(:      N @   �  R�RQ�QX�XY�lw 
�  �7  � @   I       ��>  �7  4  ,  �7  ]  U  �7  �  ~  � @   �  �>  Rt � @   P?  �>  Rs Qu Xt  � @   �  R	�` @      ,  �  @          �(?  ,  �  �  '�  @   f<  R�R  R__main __main (fwrite __builtin_fwrite (memcpy __builtin_memcpy (puts __builtin_puts (putchar __builtin_putchar  ]   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �( @   �       �  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	`� @   atexit ��   �  Y   __main 5�) @          ��  �) @   �   	__do_global_ctors   ) @   j       �  
nptrs "�   �  �  
i #�   �  �  m) @   j  R	�( @     	__do_global_dtors �( @   :       �[  p [  	 P @    	   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �     char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
�  �   �,  __uninitialized  __initializing __initialized  �  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	P @   �  	P @   =  
"	x� @   [  	p� @    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  V  _dowildcard  �   	 P @   int  }   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  q  �) @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   �) @          � �    
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 %  
  �  _newmode �   	�� @   int  �   /
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �) @   �          char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	�� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�e @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	`e @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	�� @   __mingw_initltsdyn_force ��   	�� @   __mingw_initltssuo_force ��   	�� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@  �) @   /       �}  
�  �      
�  *M      
�  ;d  /  +  �) @   �   __tlregdtor m�   �* @          ��  func m  R __dyn_tls_init L@  	  �  �  �  *M  �  ;d  pfunc N
$  ps O
�    �  �) @   �       ��  E  =  �  m  e  �  �  �  �  �  �   * @     * @   +       L�  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   e* @   �    �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  6  _commode �   	�� @   int  w   D  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 9	  k	  p  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  �* @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   �* @   �       �0  pexcept 0  $    type 
�  F  :  �* @   b  �  R2 + @   7  Q	�f @   Xs Yt w �ww(�ww0�w  5   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  �+ @          h  _fpreset 	�+ @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 L  4  �  __mingw_app_type �   	�� @   int  G     'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �    �+ @   =      �  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	Ƞ @   	�  maxSections �%  	Ġ @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator ��- @   ]      ��  4was_init �%  	�� @   5mSecs �%  �  �  !�  . @   �  �4  �  �  �  6�  
�  �  �  
�  ,  �  
�  C  +  
�  �  �  

  �  �  
  %    "E  �  �  
F  r  j  
[  �  �  / @   `  R	h @   Xu w t     �. @   �. @          �;  �  �  �  �  �  �  �  �  �    �. @   �. @          �  �  �  �      �      �. @   �  Ru    !  r/ @   �  ��  �      �  '  %  �  6  4  7  r/ @   �  �  @  >  �  K  I  �  Z  X  ~/ @   �  Ru      '0 @   '0 @   
       �w  �  d  b  �  o  m  �  ~  |    '0 @   '0 @   
       �  �  �  �  �  �  �  �  �  /0 @   �  Ru      @0 @   @0 @          �   �  �  �  �  �  �  �  �  �    @0 @   @0 @          �  �  �  �  �  �  �  �  �  H0 @   �  Ru    "$  	  �  
)  �  �  83    
4        �0 @   �0 @   
       s�      �  '  %  �  6  4    �0 @   �0 @   
       �  @  >  �  K  I  �  Z  X  �0 @   �  Rt      
�0 @   `    R	�g @    �0 @   `  R	�g @      9�  �/ @   X       �|  
�  f  b  :�  ���/ @   
  Yu   �- @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �, @   b      �`  &addr ��  �  v  b �:  ��h �g  �  �  i �%  �  �  >�, @   P       �  new_protect �
u      
$- @   
  �  Ys  .- @    
  <- @   `  R	xg @     
p, @   �
  �  Rs  �, @   n
  
�, @   E
    Q��X0 
b- @   `  >  R	@g @    r- @   `  R	 g @   Qs   ?__report_error T�+ @   i       �/  &msg T      @argp ��   �X
�+ @     �  R2 
�+ @   /  �  R	 g @   Q1XK 
�+ @       R2 
, @   �
  !  Qs Xt  	, @   �
   Afwrite __builtin_fwrite   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 I
  1
  �0 @   L       z  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	Р @   
__setusermatherr ��  �   __mingw_setusermatherr � 1 @          �P  f ,�  /  +  ,1 @   �  R�R  __mingw_raise_matherr ��0 @   >       �typ !�   C  =  name 2�  [  W  a1 ?w   m  i  a2 Jw   �  }  rslt 
w   � ex 0  �@1 @   R�@   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �
  (   _fmode �   	� @   int  �     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  01 @   �      b   char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�    ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	    z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   01 @   �      ��  'exception_data �-�  �  �  old_handler �
	  �  �  action ��   A  '  reset_fpu ��   �  �  
�1 @   �
  �  R8Q0 (�1 @   �  R�R 
�1 @   �
  �  R4Q0 �1 @   �  R4 
L2 @   �
    R8Q0 
e2 @   �
  7  R8Q1 
|2 @   �
  S  R;Q0 �2 @   f  R; �2 @   y  R8 
�2 @   �
  �  R;Q1 
�2 @   �
  �  R4Q1 
�2 @   �
  �  R8Q1 )�2 @   �
   �	   �
   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �2 @   b      �!  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	 � @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  `4 @   �       �n  	hDllHandle z�    �  	reason {<  �  {  	reserved |S    �   �4 @   K       �  
keyp �&�    y  
t �-�  �  �  �4 @   �  
5 @   C  R	 � @     !n  �4 @   �4 @          �  �  �4 @   )
   "n  �4 @   +  �E  #+  �  55 @   )
    %5 @   6  
M5 @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   �3 @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�  �  �   4 @   �  B	  Rt  34 @   �  
<4 @   �  Rt   ___w64_mingwthr_add_key_dtor *�   `3 @   o       �$
  	key *%<    	  	dtor *1.  G  9  
new_key ,$
  �    �3 @   �  �	  R1QH �3 @   �  
  Rt  
�3 @   �  Rt   �  &n  �2 @   p       ��  �  �  '�  (3 @          �
  �  �  �  ,3 @     13 @     (C3 @   Rt   
3 @   �  �
  R|  )`3 @   �  R	 � @      �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  g$  _CRT_MT �   	0P @   int  �    *  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  l  �$  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	a� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	`� @    �   Z  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 >  &  `5 @   �      �$  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<    e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  7  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  R  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  R  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "7  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #7  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
     � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �8 @   �       �7
  i �(�   �  �  G  �	`  -  �	  �  �  importDesc �@  �  �    �^
  importsStartRVA �	I        �  �8 @   	�  ��  �  �  �  �  �  	�  �8 @    �  �  �  �  �  K   G   �  \   Z       M  �8 @   �8 @   J       �q  h   f   f  }  t   p   �  �   �   �  �   �     
_IsNonwritableInCurrentImage �   8 @   �       ��  pTarget �%`  �   �   G  �	`  rvaTarget �
�  �   �     �^
  �   �   �   8 @   �  �/  �  �  �  �  �  	�  8 @    �  �  �  �  �  �   �   �  
!  !      M  48 @   48 @   I       �q  !  !  f  }  #!  !!  �  -!  +!  �  7!  5!    
_GetPEImageBase �`  �7 @   6       �0  G  �	`  	�  �7 @   �  �	�  �  �  �  �  	�  �7 @    �  �  �  �  �  D!  @!  �  U!  S!       
_FindPESectionExec y^
  @7 @   s       �%  eNo y�   c!  _!  G  {	`  -  |	  t!  r!    }^
  ~!  |!  a  ~�   �!  �!  	�  @7 @   �  �	�  �  �  �  �  	�  Q7 @    �  �  �  �  �  �!  �!  �  �!  �!       
__mingw_GetSectionCount g�    7 @   7       ��  G  i	`  -  j	  �!  �!  	�   7 @   z  m	�  z  �  �  �  	�  7 @    �  �  �  �  �  �!  �!  �  �!  �!       
__mingw_GetSectionForAddress Y^
  �6 @   �       �  p Y&s  �!  �!  G  [	`  rva \
�  "  "  �  �6 @   T  _�  �  T  �  �  �  	�  �6 @    d  �  d  �  �  "  "  �  #"  !"      	M  �6 @   o  c
q  /"  -"  f  o  }  ;"  7"  �  Y"  W"  �  c"  a"     
_FindPESectionByName :^
  �5 @   �       �M  pName :#�  v"  l"  G  <	`  -  =	  �"  �"    >^
  �"  �"  a  ?�   �"  �"  �  �5 @   I  F  �  I  �  �  �  �  6 @    6 @          �  �  �  �"  �"  �  �"  �"     &�5 @   �  -  Rt  'Z6 @   z  Rs Qt X8  _FindPESection $^
  �  G  $`  (rva $-�  -  &	    '^
  a  (�    _ValidateImageBase   �  G  `  pDOSHeader �  -  	  pOptHeader v   )�  `5 @   ,       �~  �  �"  �"  �  �"  �"  �  �  	�  i5 @    B  �  �"  �"  B  �  �  #  #  �  &#  $#     *M  �5 @   P       �f  2#  .#  +q  Q}  E#  A#  �  d#  b#  �  n#  j#    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 #    &*  _MINGW_INSTALL_DEBUG_MATHERR �   	@P @   int  �     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �9 @   *       `*  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char DWORD ��   float signed char short int double long double _Float16 __bf16 useconds_t T�   Sleep �  	   usleep �   �9 @   *       �us k  �#  �#  �9 @   ~  	R�R�����Mb&%      �  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �9 @   "      �*  char w   size_t #,�   long long unsigned int long long int short unsigned int int long int w   �   unsigned int long unsigned int unsigned char double float long double short int _sopen $�   {  {  �   �   
    rand ��   _errno ��   memcmp 1�   �  �  �  �    �  strlen @�   �  {   mkstemp ��   �9 @   "      ��  template_name �   �#  �#  i 	�   j �    $  �#  fd �   
$  $  len �   $  $  index �   5$  -$  letters 	  	�h @   	�9 @   �  �  Rt  : @   �  �: @   �  	�: @   V  �  Rt Q
�X@Y
� �: @   �      	  �   > �   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   ; @          @,  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	PP @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	   ; @          �_File *�  a$  [$  _Format J�  z$  t$  _ArgList Z�   �$  �$  ; @   �  R0Q�RX�QY0w �X   �   !  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   ; @   9       �,  __gnuc_va_list �   __builtin_va_list �   char 	�   va_list w   size_t #,�   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(B  G  
threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   j  o  �   y   �   j  �   t  __imp_snprintf �  	`P @   P  __stdio_common_vsprintf �  �  �   j  �   t  $  �    snprintf G   ; @   9       �__stream +o  �$  �$  __n <�   �$  �$  __format [y  �$  �$  ap 
�   �hret    %  �$  T; @   �  R2Q�RX�QY�Xw 0w(�   �   M"  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  k  `; @   H       8-  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	pP @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  `; @   H       �_Format .�  %  %  
ap 
�   �Xret 	  -%  +%  �; @   �  �  R1 �; @   �  R0Xs Y0w t    �   �#  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 e  M  �; @   2       �-  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�P @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  �; @   2       �_File )�  E%  ?%  _Format I�  ^%  X%  
ap 
�   �hret 	  s%  q%  �; @   �  R0Q�RX�QY0w �   �   %  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 J  2  W.  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	�� @   local__winitenv 
`  	�� @   '  
	�P @     
	�P @    �   �%  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 g  O  �; @         �.  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	Q @   �      �   __imp_at_quick_exit g)  	Q @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	Q @   
initial_tzname1 }
V  	 Q @   
initial_tznames ~�  	�P @   
initial_timezone 
%  	�P @   
initial_daylight �  	�P @   __imp_tzname ��  	�P @   __imp_timezone ��  	�P @   __imp_daylight ��  	�P @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�P @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�P @   �	  __imp__amsg_exit �V  	�P @   F  __imp__get_output_format �\
  	�P @   -
  __imp_tzset ��  	�P @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�P @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   P= @   5       ��
  file �!�
  �%  �%  fmt �6�  �%  �%  
ap ��   �h+ret �  �%  �%  �= @   �
  R4Q�RX�QY0w �  ,tzset "�= @   6       �  -  �= @   �= @   -       ��= @   +  �= @     �= @       ._tzset �/_get_output_format nF  �; @          �0_amsg_exit i = @   .       ��  ret i  �%  �%  1= @   z  �  R2 C= @   Q  �  Q	 i @   Xs  N= @   <  R�  at_quick_exit �   = @          �   func ]*�  �%  �%  1= @   �   _onexit ��  �< @          �p  func V%�  �%  �%  �< @   �  Rs   __wgetmainargs J  p< @   j       �[  _Argc J"�  &  &  _Argv J5I  :&  4&  _Env JGI  Y&  S&   j  JQ  x&  r&  !v  Jl�	  � �< @   0  �< @   	  &  R	v  $0.# �< @   �  �< @   �  �< @   �  �< @   U   __getmainargs >   < @   j       �E  _Argc >!�  �&  �&  _Argv >1S  �&  �&  _Env >@S  �&  �&   j  >J  �&  �&  !v  >e�	  �  < @   �  0< @   �    R	v  $0.# 5< @   �  >< @   �  I< @   u  ]< @   U   2  �= @   6       ��= @   +  �= @     �= @                                                                                                                                                                                                                                                                                                                                                                                                                                                  
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  H}   I  ( 
  (   H }   1�B   !I  	 :;9I  

 :;9I8  .?:;9'I<  H}  
$ >  4 :!;9I�B  .?:;9'I<  4 :!;9I  1R�BUX!YW  4 1�B  .1@z  (   U   :!;9I  4 :!;9I�B   :!;9I�B  .?:!;9'I !   :!;9I�B  >!I:;9  I  & I  7 I  ! I/   4 :!;9I?  !4 :!;9I  "1R�BX!YW  #4 :!;9I�B  $.?:;9'<  % :!;9I�B  &U  'H}�  (. ?<n:!;!   ) :;9I  * <  +:;9  ,.?:;9'<  -   .4 :!;9I  /4 :!;9I�B  04 :!;9I  1 :!;9I  24 :!;9I  3.?:!;9!	'I@z  4 1  5>!!I:;9  6:!;9!	  7.?:!;9'I@z  8.?:!;9!'@z  9H }�  : :!;9I�B  ;.?:!;9!'@z  <4 :!;9!
I  =4 1  >%U  ?   @ <  A&   B>I:;9  C4 :;9I?  D
 :;9I8  E.?:;9'�<  F. ?:;9'I<  G  H 1  I.?:;9'   J :;9I  K! I/  L.?:;9@z  M1R�BUXYW  N4 :;9I  O :;9I  P.:;9'I   Q :;9I  R. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   %  4 :;9I?  $ >   $ >   :;9I  %  .?:;9'<   I  .?:;9'I@z   :;9I�B  H}  	I ~   $ >   I  I ~   !I  4 :!;!9I�B  H }  & I  .?:!;9'I<  	H}  
%   :;9I  .?:;9'I<  
   . ?:;9'I<  . ?:;9'I<  &   .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I  I  ! I/    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}    I  $ >   !I  I ~  
 :!;9I8   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!     7 I  
%   I   <  'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                                                                                                                                                                5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �    �   �
        W  p  �  �  �  �           *  3  ;  C  V  a  k  �  �  �  �  �  �  �  �  �  �    	P @   �[?JY��J	Y�	J��=WX7 	� @   �}	�N:��'vz�z�"y� x� ww�	tx�|Z�{Zy�<<<tY#X XG 	� @   0 X f = f3 e J <2 z�B U 10h( 00�" >.� =Q�K��Y4 J" JNcF��F�W	�t
<vX��Y4 J" JM	<
_=f  	z�yX   x<5O�uO
	kOS	Af Xk�.
 �==]	+ � <^f 	�m6u6=6I=X=\�K�  	z �	�Y
�Ys= X� <[, �Q��Y" J	u �  <Y�X	dt �  <Y�XtX, Q �X <[��X	u� �  <Y�].
]�
>]]yX
>�]y<
h�X/��=�Ys= X� <Y���, �	���Y" �9 �	�,
��, X	����X�����un�	 Jf�����	�J �  <Y���(�	JX � Y� �  <Y< �� <z� �� <Z	� �� <Z	q. �  <Yu�(� � � �� <Y� �  <Y-�/-=�	� X��(* tL<: Z* r6 j? zJ
�}�f
m@	�� ��}.� �I H 
t t < X < X 
J < 
� 
J < 
X t 
X J	UJ	Y);	 I f t <�q.zJ�}��t X6 q�}t� � �"�	�(t�	X��}f�,��
X�E/��Eg�)XX=�
�} �N�~J �X/
�} ��&�~GNf(�~X�J Yt0'J Y�/��
�|�
�|(JJ�0L f0 t�0 �	Y<0I��}tJ�X�}Y�=j�K �K�}���=j�K �K�}�X@�}J���Y �K	X�.3 d " X	[' �	K'=�}��J	<�}#�X�}J=��:YY! �	K�}�J"?�}<�\�Y  �	K	��Y	- f  <Y0 =��	�}t�.	���}JX
������<
� .Z	�Xyt	5Y�}X�X	�X	��<�0Ys= X� <Y �� <Y �� <Y �� <Z, ���	 f	g �  <g� ��
�|�J	��=	 X	X
�| � �= �3 � t .�9 /	aX �  <Y� <X 	�.� �  <Y6<	J���|.5��	��=	 X	Y � ]
��Y�=���	X � �	 K � � <Y
X X	Y�b  ���;=Z��}��k�	 J7 �����	� �  <Y �{�  . <9 ��9 =/ �|� f9 �  �  <	 y� � �  <Y " 	0@ @   ���� X ��	W	. ��
t �� �
�, �
�1��	<�	� �� �� �P �	�	�"
:>	�X
�����.
K	� �  <Yq� Xt.	�f �  <Y�	. �  <Y����X	/	$X �  <Y��	#X<	�� �  <Y	ZX �  <Y
�X	 �� <Y #    K   �
      %  =  d    �  �  �  �  �  �   	�( @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
        &  M  h  �  �  �  �  �  �  6     .   �
        4  [  f  R     .   �
      �  �  �     	�) @    6     .   �
      W  o  �  �      K   �
      �    3  N  W  `  j  v  �  �   	�) @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �  �  #	  .	  6     .   �
      �	  �	  �	  �	  �     <   �
      
  6
  ]
  x
  
  �
  �
   	�* @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �
  �
    )   	�+ @   		3 6     .   �
      �  �  �  �  |    s   �
      0  H  o  �  �  �  �  �  �  �  �  �  �  �  
  
  
  &
    	�+ @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	�- @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      ~
  �
  �
  �
  �
  �
   	�0 @   
L�Z
�KTYZ
g=yuX 6     .   �
      E  ]  �  �  �    U   �
      �  �  &  	A  O  ]  e  q  {  �  �  �   	01 @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      �    3  N  X  b  l  x  �  �  �  �  �  �   	�2 @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
        1  X  b  6     .   �
      �  �  �    G    K   �
      n  �  �  �  �  �  �  �  �     	`5 @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< 6     .   �
      [  s  �  �  �     E   �
      
  "  J  e  �  �  �  �  �   	�9 @   KM.tJh O    F   �
        #  K  f  p  z  �  �  �   	�9 @   �yC) X' X) J <&Y.�	� X	gXfh��% N �%  J f � �  f
�( W(K0 .' � <" � 	[	�	�  X n     A   �
      �    -  H  X  h  q  {   	 ; @   K
�<.Y �     A   �
      �  �    0  @  P  Y  c   	 ; @   KU	\f�X�Y	Y �     A   �
      �  �  �    $  2  ;  E   	`; @   g?	YT�Y	 X� <uX �     A   �
      �  �  �  �         *   	�; @   KU	\fp	\;Y	Y W     O   �
      ~  �  �  �        %  .  9  E  |    h   �
      �  �  �  �  %  7  I  P  Y  c  l  t  �  �  �  �   	�; @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	 = @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                  ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �      $   P  P @   W       A�D0LA�   P  � @   %          P  � @   �       <   P  p @   B       A�A�A �D@s A�A�A�          P  � @   ;          P    @             P   @             P    @          $   P  0 @   9       DPj
B       l   P  p @   �       B�A�A �A(�A0�DpW
0A�(A� A�A�B�IK
0A�(A� A�A�B�A       <   P    @   x       A�A�A �DP\
 A�A�A�A   <   P  � @   I       A�A�A �D@p
 A�A�A�A    <   P  � @   r      A�DP�
A�Gr
A�ED
A�Kd   P  P @   �      B�B�B �A(�A0�A8�A@�G�
@A�8A�0A�(A� B�B�B�A     �   P    @   �      B�B�B �B(�A0�A8�A@�AH�	G��
HA�@A�8A�0A�(B� B�B�B�Hv
HA�@A�8A�0A�(B� B�B�B�F        P  �  @          l   P  �  @         B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�A    t   P   $ @   ,      A�A�A �A(�Dp�
(A� A�A�A�D�
(A� A�A�A�Hl
(A� A�A�A�H     \   P  0@ @   t      B�B�A �A(�A0�A8�D`�
8A�0A�(A� A�B�B�A          ���� x �           �( @   :       D0u  4      ) @   j       A�A�D@@
A�A�H         �) @             ���� x �         �  �) @             ���� x �      $   �  �) @   /       D0R
JN    L   �  �) @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       �  �* @             ���� x �      <   x  �* @   �       A�A�D�P�
���
���A�A�B    ���� x �         �  �+ @             ���� x �      $      �+ @   i       A�A�DP   <      , @   b      A�A�A �Dp�
 A�A�A�D   \      �- @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         �  �0 @   >       D`y     �   1 @             ���� x �      4   0	  01 @   �      A�D0}
A�Mf
A�I     ���� x �      L   �	  �2 @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �	  `3 @   o       A�A�A �D@U
 A�A�A�A    D   �	  �3 @   �       A�A�D@R
A�A�FR
A�A�D      4   �	  `4 @   �       A�D0p
A�J�
A�A      ���� x �         �
  `5 @   ,          �
  �5 @   P       L   �
  �5 @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       �
  �6 @   �          �
   7 @   7          �
  @7 @   s          �
  �7 @   6          �
   8 @   �          �
  �8 @   �          ���� x �      $   �  �9 @   *       D0J
BY       ���� x �      l     �9 @   "      B�B�B �B(�A0�A8�A@�AH�	D�
HA�@A�8A�0A�(B� B�B�B�A         ���� x �         �   ; @          D@Y     ���� x �         �   ; @   9       DPt     ���� x �      ,   
  `; @   H       A�A�D`A�A�   ���� x �         P
  �; @   2       DPm     ���� x �         �
  �; @          L   �
   < @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �
  p< @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �
  �< @          A�D0WA�    �
   = @             �
   = @   .       A�D0   �
  P= @   5       DPp     �
  �= @   6       D0q     �
  �= @   6       D0q                                                                                                                                                                                                                                          Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion client lockdownd_client_private length microseconds data_size entry length_buf service_client_private request timestamp response bytes_received buffer idevice_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection _DoWildCard _StartInfo                                                                                                                                C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> ideviceostrace.c C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/sys C:/msys64/ucrt64/include/plist ../include/libimobiledevice C:/msys64/ucrt64/include/libimobiledevice-glue ideviceostrace.c ideviceostrace.c time.h corecrt.h stdint.h types.h plist.h libimobiledevice.h lockdown.h service.h property_list_service.h winnt.h combaseapi.h wtypes.h stdio.h stdlib.h unistd.h string.h io.h utils.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_usleep.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/sys mingw_usleep.c mingw_usleep.c minwindef.h types.h synchapi.h C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mkstemp.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include mkstemp.c mkstemp.c stdlib.h io.h string.h corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_snprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_snprintf.c ucrt_snprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                  �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� �                0@ @    R�S���R���S         0@ @    Q�U���Q���U        O@ @    $0�$/Q��0���Q                     O@ @    $1�$�V��v~���1���3���V��V��V��v���V��v~���V      �A @    
	��R��P              $ @    R�T���R���T���R���T���R�          $ @    P�S��S��S��R  �$ @   *����8$����@$!����H$!����!�           % @    PDTy�T��T��T    �% @    �U��U       �% @    P�T��T     D& @    P��P     �& @    
P��P    �& @    P$S  �& @   
S �$ @    ���          % @    5S��S��S��S��S     % @    :T��T��T                 % @    	0�	t ��� t��8$t ��!� 3
t��@$u !�3:t��@$t �
��!���0���U��
t��@$u !���t��@$t��H$!t �
��!���t��8$t ��!���t ���             �  @    %R%�_���R���_��R��_                   �  @    )Q)�V���Q���V���Q���Q��V���Q���V               
! @    
P
�T��T��T��R��P��T 		               ! @     0� 9�=�I7�II;�II?�IIC�IQD�QQE�Q[k�[[m�[[o�[[u�[cy�cc}�c�����U��| u "���| u "���} | "���]������| u "�������U    _! @    l)� ��8$� ��@$!� ��H$!?��!���)� ��8$� ��@$!� ��H$!?��!�           q! @    PZ� Z������ ����          q! @    �\��\��\��\��\    q! @    Z*� ��8$� ��@$!� ��H$!� ��!���*� ��8$� ��@$!� ��H$!� ��!�     y! @    R*� ��8$� ��@$!� ��H$!� ��!���*� ��8$� ��@$!� ��H$!� ��!�          y! @    0�,S/3r�3IS��0� ! @    	�    *! @    R��� !! @    7� q! @    � � 
 q! @   � �   �! @    %������        �! @    PU��P��U    �! @    )U��U      �! @    Q)������        " @    PS��P��S    7" @    $\��\    7" @    $^��^        ?" @    PS��P��S    �" @    "]��]          �" @    VQ" | "���V�� | "�        �" @    PU��P��U    �" @     ^n|^    �" @     ]n|]        �" @    PUfqPqtU                   @    :R:�S���R���S��R��S���R���S           � @    'T��T��
b @   ���T��T         � @    s(x�s(��s(��s(   � @   :P         P @    8R8�\���R���	\             P @    5Q5�V���Q���V���Q���V��	�Q�             P @    �X��U���X���U���X���U��	�X�             P @    �Y��T���Y���T���Y���T��	�Y�               a @    �0���P��S��S��0���S��0���	S           ` @    P1S��P��S��S            y @    
P
�^��^��^��P��^      @    7P��P     � @     P��P            � @    0�P�S��S��S��S     C @    Py�P           @    ReTeg�R�gxT           @    QfUfg�Q�gxU         @    X6S6x�X�        4 @    	P	+S3:P:;S             p @    RdUdh�R�h~U~��R���R             p @    QeVeh�Q�hV��Q���Q             p @    XcTch�X�h}T}��X���T        z @    0�SS^rSx|0�    � @    	p �����0	�� � @   3t s �     � @    	v s "�	&Q&3v s "� � @   3U   � @   P       p @    R#Y#B�R�         p @    Q=U=AYAB�Q�           p @    X#R#;S;Arm�AB�X�         p @    Y<T<Aq�AB�Y�         P @    RRSRVRVW�R�       � @    R$r�$%�R�     � @    R��R�       � @     Q &�Q�&;Q          � @    0�r ���r��8$r ��!�%
r��@$p !�&4r ���4:
r��@$p !�:;r��@$r��H$!r �
��!�     0 @    R9�R�     0 @    Q9�Q�     0 @    X9�X�   N @   P         � @    R:U:;�R�;IU         � @    Q9T9;�Q�;IT         � @    PS-P-0S       �  @    	R	
�R�
R 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
 f @   ���
 f @   ���
pf @   ���
Hf @   ���
�f @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ���X�     ��U  ��2�  ���X�     ��U  ��1�  ���X�     ��U  ��1�  ���X�     ��U  �	�	4�  �	�	�X�     �	�	U  �	�	4�  �	�	�X�     �	�	U  �	�	8�  �	�	�X�     �	�	U  �	�	8�  �	�	�X�     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
���     �
�
T  �
�
4�  �
�
���     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� �               R*�R� z                  RQTQ\�R�\�T  ��U   ��P       !+P+;��\���      fmvz�mmPmtp�t�P T                RQ�R�        QX�Q�        X�`�X� [                !R!3Q39�R�        Q3X39�Q�        X3Y39�X�   49P )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                            X         Z���� ���� ���� ���� ���� ���� ���� h        � @    5PW � @    2 h @    \\w�� � @    ����� � @    @�� ! @     5! @    << �! @    		.�� �! @    )�� +" @    0�� �" @    2���� �" @    
*x� % @    3������ M& @    cs��� �& @    77��� �A @    �� P @   �'0@ @   � S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����                                                                                                                                                                                                                                                                .file   a   ��  gcrtexe.c              �                                �              �   �	                        �   �	                        �   �	                        %  P	                        @  �	                        `             k  �
                        �  p
                        �  0	                        �  �
                        �  0          �  �
                    envp           argv            argc    (                          �
                        '  �          9  
                        ^   
                        �             �  �	                        �  �
                        �   
                          �	                    mainret            "  @
                        8  0
                        N  `
                        d  P
                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �
     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )        +                 .file     ��  gideviceostrace        m  P                           �  �          �  �          �  p          �  �          �             �            �               0      recv_valp                       %  �          0  �      collect P	          <             O  �          a  �          t         main    00      .text   P     �  �             .data                            .bss    0      0                 .xdata  t      �                 .pdata  l      �   6             .rdata         T  $                 �  00     t  P                 �  4                          �  D                         �  �&  
   �?  A                �  �     "                    �  �     �  ^                 �  0      @                    �  \      l                     9     �                          �                       �                         )  0     +                     4  P     �  &             .text   0      .idata$7      .idata$5�      .idata$4�      .idata$6	      .text   8      .idata$7       .idata$5�      .idata$4�      .idata$6�      .text   @      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text   H      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text   P      .idata$7�
      .idata$5�      .idata$4�      .idata$6p      .text   X      .idata$7�
      .idata$5�      .idata$4x      .idata$6L      .text   `      .idata$7�
      .idata$5�      .idata$4p      .idata$60      .text   h      .idata$7�
      .idata$5�      .idata$4h      .idata$6      .text   p      .idata$7�
      .idata$5�      .idata$4`      .idata$6�      .text   x      .idata$7�
      .idata$5�      .idata$4X      .idata$6�      .text   �      .idata$7�
      .idata$5x      .idata$4P      .idata$6�      .text   �      .idata$7�
      .idata$5p      .idata$4H      .idata$6�      .text   �      .idata$7�
      .idata$5h      .idata$4@      .idata$6�      .text   �      .idata$7$      .idata$5�      .idata$4�      .idata$60	      .text   �      .idata$7�      .idata$5�      .idata$4X      .idata$6�
      .text   �      .idata$7�      .idata$5x      .idata$4P      .idata$6�
      .text   �      .idata$7�      .idata$5p      .idata$4H      .idata$6�
      .text   �      .idata$7�      .idata$5h      .idata$4@      .idata$6�
      .text   �      .idata$7�      .idata$5`      .idata$48      .idata$6l
      .text   �      .idata$7�      .idata$5X      .idata$40      .idata$6\
      .text   �      .idata$7�      .idata$5P      .idata$4(      .idata$6@
      .text   �      .idata$7�      .idata$5H      .idata$4       .idata$6(
      .file   @  ��  ggccmain.c             �  �                       p.0                 �             �  @	                    __main  �          �  `       .text   �     �                .data                          .bss    `                       .xdata  H                      .pdata  P     $   	                 �  1f  
   a                   �  �
     ?                    �  �     5                     �  p      0                      �     '                     �     �                     )  `     +                     4       �                .file   V  ��  gnatstart.c        .text   �                       .data                          .bss    p                           �  �l  
     
                 �  �     �                     �  �                                   V   
                   �                            �                         )  �     +                 .file   j  ��  gwildcard.c        .text   �                       .data                           .bss    �                            �  �r  
   �                    �  �     .                     �  �                             V     :                      �     �                     )  �     +                 .file   �  ��  gdllargv.c         _setargv�                       .text   �                      .data   0                        .bss    �                        .xdata  `                      .pdata  t                         �  !s  
   �                   �  �     :                     �  �      0                      �     V                      q     �                     )  �     +                     4  �     0                .file   �  ��  g_newmode.c        .text   �                       .data   0                        .bss    �                           �  �t  
   �                    �  
     .                     �                              �     :                      
     �                     )        +                 .file   �  ��  gtlssup.c              �  �                           
  �             	                    __xd_a  P       __xd_z  X           0  �      .text   �     �                .data   0                        .bss    �                       .xdata  d                      .pdata  �     $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  `     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  ,u  
   �  6                 �  /
     �                    �  �                        �  0     0                                                 �                            �     �                     )  P     +                     4  �     �                .file   �  ��  gxncommod.c        .text   �                       .data   0                        .bss    �                           �  �|  
   �                    �       .                     �  `                            6     :                      �     �                     )  �     +                 .file     ��  gcinitexe.c        .text   �                       .data   0                        .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  }}  
   {                   �  D     a                     �  �                            p     :                      9	     �                     )  �     +                 .file   "  ��  gmerr.c            _matherr�                       .text   �     �                .data   0                        .bss    �                        .rdata  �     @               .xdata  |                      .pdata  �                         �  �~  
   6  
                 �  �                         �       �                    �  �     0                      �     �                      �	     �                     )  �     +                     4  x     X                .file   ?  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   0                        .bss    �                        .xdata  �                      .pdata  �                         �  .�  
   �                    �  �     -                     �  �     0                      h     X                      �
     �                     )  
     +                     4  �     0                .file   S  ��  gmingw_helpers.    .text   �                       .data   0                        .bss    �                           �  ł  
   �                    �  �     .                     �                               �     :                      4     �                     )  @
     +                 .file   �  ��  gpseudo-reloc.c        <  �                           K            a  �       the_secs�           m  �          �  �           �  `	                        �  p	                    .text   �     =  &             .data   0                        .bss    �                       .rdata        [                .xdata  �     0                 .pdata  �     $   	                 �  W�  
   K  �                 �       �                    �  �     �  
                 �        0                    �  �     W                       �     �                     �     	                       �     O                    )  p
     +                     4        �                .file   �  ��  gusermatherr.c         �  �                              �              !      .text   �      L                .data   0                        .bss    �                       .xdata  �                      .pdata  �                         �  ��  
   �                   �  �                         �       r                     �  P     0                      z     �                      1
     �                     )  �
     +                     4  �     P                .file   �  ��  gxtxtmode.c        .text   0!                       .data   0                        .bss    �                           �  ��  
   �                    �  �     .                     �  �                            (      :                      �
     �                     )  �
     +                 .file   �  ��  gcrt_handler.c         +  0!                       .text   0!     �               .data   0                        .bss    �                       .xdata  �                      .rdata  `     (   
             .pdata  �                         �  �  
   �                   �       ~                    �  �     _                    �  �     0                      b      �  
                                               �                         )        +                     4  0	     P                .file   �  ��  gtlsthrd.c             B  �"                           b             p             ~  `#          �            �  �#          �  `$      .text   �"     b  "             .data   0                        .bss          H                 .xdata  �     0                 .pdata       0                    �   �  
   �
  A                 �  �     a                    �  �     �                    �  �     0                    �                              �!     x                     �     %                    )  0     +                     4  �	     (               .file     ��  gtlsmcrt.c         .text   `%                       .data   0                       .bss    `                           �  Ӹ  
   �                    �  �     .                     �                               g$     :                      �     �                     )  `     +                 .file   $  ��  g    �            .text   `%                       .data   @                        .bss    `                          �  \�  
   �                    �  *     0                     �                               �$     :                      l     �                     )  �     +                 .file   \  ��  gpesect.c              �  `%                           	  �%            �%          -  �&          J   '          b  @'          u  �'          �   (          �  �(      .text   `%     �  	             .data   @                        .bss    p                       .xdata       ,                 .pdata  4     l                    �  ,�  
   �  �                 �  Z     �                    �  �     �                    �  @     0                    �  6     �                       �$     K                          T                       &     �                     )  �     +                     4  �
     (               .text   `)     2                 .data   @                        .bss    p                       .text   �)                       .data   @                        .bss    p                           )  �     +                 .file   p  ��  gmingw_matherr.    .text   �)                       .data   @                       .bss    �                           �  ��  
   �                    �  �     .                     �  p                            &*     :                           �                     )        +                 .file   �  ��  gmingw_usleep.c    usleep  �)                       .text   �)     *                .data   P                        .bss    �                       .xdata  8                      .pdata  �                         �  ��  
   �  	                 �       �                     �  �#                          �  �     0                      `*     �   
                   �                         )  P     +                     4  �     @                .file   �  ��  gmkstemp.c         mkstemp �)                           �  �      .text   �)     "               .data   P                        .bss    �                       .rdata  �     _                 .xdata  @                      .pdata  �                         �  ��  
                      �  �     ?                    �  �#     ~                     �  �     0                      �*     S  
                   �     �                     )  �     +                     4       �                .file   �  ��  gucrt_vfprintf.    vfprintf +                       .text    +                     .data   P                      .bss    �                       .xdata  X                      .pdata  �                         �  ��  
   �                   �  �     8                    �  O$     X                     �  �     0                      @,     r   	                   �     �                     )  �     +                     4  �     8                .file   �  ��  gucrt_snprintf.    snprintf +                       .text    +     9                .data   `                      .bss    �                       .xdata  `                      .pdata  �                         �  4�  
   �                   �  !     9                    �  �$     _                     �        0                      �,     �   	                   �     �                     )  �     +                     4  �     8                .file   	  ��  gucrt_printf.c     printf  `+                       .text   `+     H                .data   p                      .bss    �                       .xdata  h                      .pdata  �                         �  ��  
   �  
                 �  M"     l                    �  %     -                     �  P     0                      8-     �   	                   k     �                     )       +                     4  
     H                .file   '  ��  gucrt_fprintf.c    fprintf �+                       .text   �+     2                .data   �                      .bss    �                       .xdata  t                      .pdata  �                         �  ��  
   �                   �  �#     b                    �  3%     F                     �  �     0                      �-     �   	                   M     �                     )  @     +                     4  P
     8                .file   =  ��  g__initenv.c           �  �          �  �      .text   �+                       .data   �                      .bss    �                          �  .�  
   �                   �  %     �                     �  �                            W.     [                      2                         )  p     +                 .file   w  ��  gucrtbase_compa        �  �+                            	   ,          	  p,      _onexit �,          	   -          +	  �	                        P	   -          [	  P-      tzset   �-          i	  �	                    _tzset  �-          �	  �           �	  �           �	  �           �	            �	         .text   �+       "             .data   �      x   
             .bss    �                       .xdata  |     P                 .pdata  �     l                .rdata   	                          �  ��  
   �  Y                 �  �%                          �  y%     |                    �  �     0                      �.     �                     j                            O     `                    )  �     +                     4  �
     �               .text   .      .data          .bss    �      .idata$7�      .idata$58      .idata$4      .idata$6 
      .file   �  ��  gfake              hname         fthunk  8      .text    .                       .data                           .bss    �                       .idata$2                     .idata$4      .idata$58      .file   �  ��  gfake              .text    .                       .data                           .bss    �                       .idata$4                      .idata$5@                      .idata$7�     "                 .text    .      .data          .bss    �      .idata$7|      .idata$5       .idata$4�      .idata$6�      .text   (.      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�      .text   0.      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�      .text   8.      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�      .text   @.      .data          .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6

      .text   @.      .data          .bss    �      .idata$7�      .idata$5(      .idata$4       .idata$6
      .file   �  ��  gfake              hname   �      fthunk         .text   P.                       .data                           .bss    �                       .idata$2�                      .idata$4�      .idata$5       .file   �  ��  gfake              .text   P.                       .data                           .bss    �                       .idata$4                      .idata$50                      .idata$7�                      .text   P.      .data          .bss    �      .idata$7L      .idata$5�      .idata$4�      .idata$6�      .text   X.      .data          .bss    �      .idata$7P      .idata$5�      .idata$4�      .idata$6�      .text   `.      .data          .bss    �      .idata$7T      .idata$5�      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   p.                       .data                           .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   ^  ��  gfake              .text   p.                       .data                           .bss    �                       .idata$4�                      .idata$5�                      .idata$7X     !                 .text   p.      .data          .bss    �      .idata$7�      .idata$5x      .idata$4P      .idata$6�      .text   x.      .data          .bss    �      .idata$7       .idata$5�      .idata$4X      .idata$6      .text   �.      .data          .bss    �      .idata$7      .idata$5�      .idata$4`      .idata$6      .text   �.      .data          .bss    �      .idata$7      .idata$5�      .idata$4h      .idata$6*      .text   �.      .data          .bss    �      .idata$7      .idata$5�      .idata$4p      .idata$6D      .text   �.      .data          .bss    �      .idata$7      .idata$5�      .idata$4x      .idata$6`      .text   �.      .data          .bss    �      .idata$7      .idata$5�      .idata$4�      .idata$6z      .text   �.      .data          .bss    �      .idata$7      .idata$5�      .idata$4�      .idata$6�      .text   �.      .data          .bss    �      .idata$7      .idata$5�      .idata$4�      .idata$6�      .text   �.      .data          .bss    �      .idata$7       .idata$5�      .idata$4�      .idata$6�      .text   �.      .data          .bss    �      .idata$7$      .idata$5�      .idata$4�      .idata$6�      .text   �.      .data          .bss    �      .idata$7(      .idata$5�      .idata$4�      .idata$6�      .file   l  ��  gfake              hname   P      fthunk  x      .text   �.                       .data                           .bss    �                       .idata$2�                      .idata$4P      .idata$5x      .file     ��  gfake              .text   �.                       .data                           .bss    �                       .idata$4�                      .idata$5�                      .idata$7,                       .text   �.      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �.      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �.      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �.      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �.      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �.      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text    /      .data          .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6      .text   /      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6,      .text   /      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6:      .text   /      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6D      .text    /      .data          .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6L      .text   (/      .data          .bss    �      .idata$7�      .idata$5(      .idata$4       .idata$6n      .text   0/      .data          .bss    �      .idata$7�      .idata$50      .idata$4      .idata$6�      .text   8/      .data          .bss    �      .idata$7�      .idata$58      .idata$4      .idata$6�      .text   @/      .data          .bss    �      .idata$7�      .idata$5@      .idata$4      .idata$6�      .text   H/      .data          .bss    �      .idata$7�      .idata$5H      .idata$4       .idata$6�      .text   P/      .data          .bss    �      .idata$7�      .idata$5P      .idata$4(      .idata$6�      .text   X/      .data          .bss    �      .idata$7�      .idata$5X      .idata$40      .idata$6�      .text   `/      .data          .bss    �      .idata$7�      .idata$5`      .idata$48      .idata$6�      .text   h/      .data          .bss    �      .idata$7�      .idata$5h      .idata$4@      .idata$6�      .file     ��  gfake              hname   �      fthunk  �      .text   p/                       .data                           .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   0  ��  gfake              .text   p/                       .data                           .bss    �                       .idata$4H                      .idata$5p                      .idata$7�     "                 .text   p/      .data          .bss    �      .idata$7\      .idata$5�      .idata$4�      .idata$6�
      .text   x/      .data          .bss    �      .idata$7`      .idata$5�      .idata$4�      .idata$6�
      .file   >  ��  gfake              hname   �      fthunk  �      .text   �/                       .data                           .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   Z  ��  gfake              .text   �/                       .data                           .bss    �                       .idata$4�                      .idata$5�                      .idata$7d     "                 .text   �/      .data          .bss    �      .idata$74      .idata$5�      .idata$4x      .idata$6p
      .text   �/      .data          .bss    �      .idata$78      .idata$5�      .idata$4�      .idata$6�
      .file   h  ��  gfake              hname   x      fthunk  �      .text   �/                       .data                           .bss    �                       .idata$2�                      .idata$4x      .idata$5�      .file   �  ��  gfake              .text   �/                       .data                           .bss    �                       .idata$4�                      .idata$5�                      .idata$7<                      .text   �/      .data          .bss    �      .idata$7      .idata$5x      .idata$4P      .idata$6D
      .text   �/      .data          .bss    �      .idata$7      .idata$5�      .idata$4X      .idata$6T
      .text   �/      .data          .bss    �      .idata$7      .idata$5�      .idata$4`      .idata$6^
      .text   �/      .data          .bss    �      .idata$7      .idata$5�      .idata$4h      .idata$6f
      .file   �  ��  gfake              hname   P      fthunk  x      .text   �/                       .data                           .bss    �                       .idata$2x                      .idata$4P      .idata$5x      .file   �  ��  gfake              .text   �/                       .data                           .bss    �                       .idata$4p                      .idata$5�                      .idata$7                      .text   �/      .data          .bss    �      .idata$7�      .idata$5h      .idata$4@      .idata$6:
      .file   �  ��  gfake              hname   @      fthunk  h      .text   �/                       .data                           .bss    �                       .idata$2d                      .idata$4@      .idata$5h      .file   �  ��  gfake              .text   �/                       .data                           .bss    �                       .idata$4H                      .idata$5p                      .idata$7�     %                 .text   �/      .data          .bss    �      .idata$7�      .idata$5P      .idata$4(      .idata$6
      .text   �/      .data          .bss    �      .idata$7�      .idata$5X      .idata$40      .idata$6*
      .file   �  ��  gfake              hname   (      fthunk  P      .text   �/                       .data                           .bss    �                       .idata$2P                      .idata$4(      .idata$5P      .file     ��  gfake              .text   �/                       .data                           .bss    �                       .idata$48                      .idata$5`                      .idata$7�     &                 .text   �/      .data          .bss    �      .idata$7�      .idata$5@      .idata$4      .idata$6
      .file     ��  gfake              hname         fthunk  @      .text   �/                       .data                           .bss    �                       .idata$2<                      .idata$4      .idata$5@      .file   d  ��  gfake              .text   �/                       .data                           .bss    �                       .idata$4                       .idata$5H                      .idata$7�     "                 .text   �/      .data          .bss    �      .idata$7l      .idata$50      .idata$4      .idata$6
      .text   �/      .data          .bss    �      .idata$7h      .idata$5(      .idata$4       .idata$6�	      .text   �/      .data          .bss    �      .idata$7d      .idata$5       .idata$4�      .idata$6�	      .text   �/      .data          .bss    �      .idata$7`      .idata$5      .idata$4�      .idata$6�	      .text    0      .data          .bss    �      .idata$7\      .idata$5      .idata$4�      .idata$6�	      .text   0      .data          .bss    �      .idata$7X      .idata$5      .idata$4�      .idata$6�	      .text   0      .data          .bss    �      .idata$7T      .idata$5       .idata$4�      .idata$6�	      .text   0      .data          .bss    �      .idata$7P      .idata$5�      .idata$4�      .idata$6x	      .text    0      .data          .bss    �      .idata$7L      .idata$5�      .idata$4�      .idata$6`	      .text   (0      .data          .bss    �      .idata$7H      .idata$5�      .idata$4�      .idata$6H	      .file   r  ��  gfake              hname   �      fthunk  �      .text   00                       .data                           .bss    �                       .idata$2(                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   00                       .data                           .bss    �                       .idata$4                      .idata$58                      .idata$7p     
                 .file   �  ��  gcygming-crtend        �	  �3                       .text   00                       .data                           .bss    �                           �  �3                         �  �                          �  T                         �	  �3                         )  �     +                 .idata$2        .idata$5h      .idata$4@      .idata$2       .idata$5�      .idata$4�      .idata$2      .idata$5H      .idata$4       .idata$4�      .idata$5�      .idata$7      .idata$4�      .idata$5�      .idata$7(      .idata$4`      .idata$5�      .idata$7�      .rsrc       
    __xc_z         putchar �.          �	  h          
  �          6
             U
   .          `
  �          o
  �.          �
  H          �
  p          �
  P          �
              �
  �3          �
  �           �
  �            �            �           K   0          g  �	          �  �          �  �          �  �          �  �          �  �           �      	        �   
          �  @/            �       __xl_a  0           *  0          7  (          Z  �          �  P          �             �  �      _cexit  �.          �  �          �  `  ��       
     ��       !
  �          O
              e
  h          x
      ��       �
     ��       �
  0           �
  �      __xl_d  @           �
  @      _tls_end   	          @	      __tzname0.            �/          $  �          5             G  �           X  0           h  �	          �  X      _sopen  �.          �            �  �	          �      	    memcpy  x/          �  �      perror  X/          �  �
      puts    �.            @          )  p	          O  �          `  �           y  h      system  h/      malloc  �/      _CRT_MT 0           �  �/          �  �          �              �  �          �  8          �  �          �                         (     ��       @  �          ^  �          q  �
          �             �  �           �  �          �  �          �  �          �              <          9  �/          F  �	          _  �           x  �/          �  P           �             �  P           �            �  8.            p/             
      abort   H/          7  `	          a  �           u  �          �  �          �  P           �  �          �  �      __dll__     ��             ��         �.          (            G  X          w   0          �  @           �  �          �  �/          �  P
          �  p          �   	          �  @            �           -  p          ?            K     ��       a             y  d      calloc  �/          �  �          �  �.          �  �          �  �             @             �                       <  `      Sleep   �/          T  �
      _commode�           e             r  �            �3          �            �  x          �  x           �  �             (           *  �      __xi_z  (           6  (          E  �          ]             u             �  �	          �  �          �  �.      rand    .          �  
            �            �       signal  `/          )  p.          9             ]  x           t              �  x          �  h      strncmp `.          �  �3          �  `           �  �	      unlink  �/          �  `          �  �           "  8           1  �          F  <           u  �	          �      ��   client  P           �  P	          �  �          �  �      fdopen  �/            �          *  �          R  �          _  (          �  0          �  �          �  �
          �  �           �  p                         `          +     ��       >             ^  �/          l   /          �   /          �  0          �  P          �  P          �  �           
              ,  P           ;     ��       P              _  �           o  �          �  H          �  x          �        fclose  �.          �  h          �  �            H          .  �      __xl_z  H       __end__              ;  �          Z            |  X      useColor0           �        strcmp  P.          �  �3          �  h      __xi_a             �  8/          �            �  0      __xc_a              �  �          �     ��         P           $  d           V     ��       d  �          �  �      _fmode  �           �             �  X          �  �/          �            �  p
          �  �              �.                       &  �          9            K  �          �  �.          �  (          �  �      __xl_c  8           �     	        �  �          �  �	             0            0          #  �          0  �           I              U            h  ,          �  8      strftime@.          �  `          �  (.          �        _newmode�           
   0/      fwrite  �.             �          %   �
          4   x          J       ��       b       ��       s   �             `)          �   p           �   x.          �          exit    P/          �      ��       �       ��       �   8          
!  �      _errno  /      atoi    �/          &!         _exit   /          ?!  p           L!  0
          [!  �.      strlen  X.      device  X           r!  `
          �!  /          �!  0	          �!  (0          �!  (/          �!  x          "  �           "  �           O"             q"  �           �"  �          �"  �          �"  X          �"  @
          �"  �           �"  P       close   �.          #  �          #  �.          +#  �           :#  �.          F#  �      free    �/      service H           \#  �       m#  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame free_syslog_entry.part.0 get_log_level_color syslog_level_to_string format_timestamp read_uint32_le_windows read_uint32_le read_uint16_le read_uint8_le recv_service_data recv_prefixed try_decode get_PIDLIST print_syslog_entry free_syslog_entry parse_syslog_entry syslog_stream .text.startup .xdata.startup .pdata.startup __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names letters.0 local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 __imp_plist_get_string_val __imp_service_receive_with_timeout ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight plist_new_dict __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname __imp_plist_new_uint _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force idevice_new __imp_calloc __imp___p__fmode __imp___p___argc __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __imp_property_list_service_receive_plist property_list_service_client_new __rt_psrelocs_start __imp_lockdownd_service_descriptor_free plist_new_uint __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll property_list_service_client_free __imp__errno .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free .refptr._matherr property_list_service_receive_plist .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ plist_new_string __mingw_oldexcpt_handler lockdownd_service_descriptor_free TlsGetValue __imp_strcmp __bss_start__ __imp___C_specific_handler __imp_rand __imp_putchar ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp_fdopen __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection _localtime64 __C_specific_handler .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf __imp_property_list_service_get_service_client plist_free ___crt_xp_end__ __imp_property_list_service_send_xml_plist __imp_lockdownd_start_service __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_puts _set_new_mode .refptr.__xi_a __imp_plist_new_dict .refptr._CRT_MT __imp_atoi _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp_idevice_new __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname _tls_used __stdio_common_vsprintf __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a plist_client __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ lockdownd_start_service .refptr._newmode __data_end__ __imp_fwrite __CTOR_LIST__ _head_lib64_libapi_ms_win_crt_utility_l1_1_0_a __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ __imp_strftime idevice_set_debug_level __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __lib64_libapi_ms_win_crt_filesystem_l1_1_0_a_iname __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func _head_libimobiledevice_glue_1_0_dll __native_startup_state ___crt_xc_start__ lockdownd_client_free __imp_system ___CTOR_LIST__ __imp_snprintf .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a service_client plist_get_string_val _head_lib64_libapi_ms_win_crt_convert_l1_1_0_a .refptr.__mingw_initltsdyn_force __rt_psrelocs_size .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp___p___wargv __imp_property_list_service_client_free __imp_strlen libimobiledevice_glue_1_0_dll_iname service_receive_with_timeout __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __imp_plist_from_bin __file_alignment__ __imp_InitializeCriticalSection __p__wenviron _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_plist_dict_insert_item __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __lib64_libapi_ms_win_crt_utility_l1_1_0_a_iname __imp_plist_dict_get_item __imp_plist_new_string _head_libplist_2_0_dll __IAT_start__ plist_from_bin property_list_service_get_service_client __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp_perror __imp__onexit __DTOR_LIST__ __imp_unlink __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ _head_lib64_libapi_ms_win_crt_filesystem_l1_1_0_a __subsystem__ __imp___stdio_common_vsprintf plist_dict_insert_item __imp_TlsGetValue __imp___p__wenviron __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_VirtualProtect idevice_free ___tls_end__ __lib64_libapi_ms_win_crt_convert_l1_1_0_a_iname .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __imp_fclose __mingw_initltsdyn_force _dowildcard __imp__localtime64 __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname property_list_service_send_xml_plist __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_close ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ __minor_image_version__ __imp___set_app_type __imp_plist_print_to_stream __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv plist_dict_get_item __imp_property_list_service_client_new __imp_plist_free .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __imp__sopen __stdio_common_vfprintf __imp_daylight __p___wargv plist_print_to_stream __mingw_app_type 