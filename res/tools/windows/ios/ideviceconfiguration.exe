MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� c=�g �   � & ( ,   h     �        @                       �    ^�  `                                             �  �   �  �   p  �           �  �                           �Y  (                   ��                            .text   �+      ,                 `  `.data      @      2              @  �.rdata      P      4              @  @.pdata  �   p      J              @  @.xdata  @   �      N              @  @.bss    �   �                      �  �.idata  �   �      R              @  �.CRT    `    �      b              @  �.tls        �      d              @  �.rsrc   �   �      f              @  �.reloc  �    �      l              @  B/4      p   �      n              @  B/19     o�      �   t              @  B/31     �$   �  &   V             @  B/45     �*      ,   |             @  B/57     p   P     �             @  B/70     �   `     �             @  B/81     ]   p     �             @  B/97         �  "   �             @  B/113       �     �             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H�UM  1��    H�VM  �    H�YM  �    H��L  f�8MZuHcP<HЁ8PE  tfH��L  �
�  � ��tC�   �)  �T  H��M  ���<  H��M  ���
  H�=L  �8tP1�H��(Ð�   ��  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
aM  �  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H�5M  L��~  H��~  H�
�~  � ��~  H��L  D�H��~  H�D$ ��  �H��8��    ATUWVSH�� H�/L  H�-(�  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5�K  1�����V  �����  �~     ����L  ���e  H�!K  H� H��tE1��   1����X  H�
!L  ���  H�tK  H�
����H��]  �@
  ��}  �{Hc�H��H���  L�%�}  H�Ņ��F  H��1�I��  H�pH���  I��H�D I�H��H���+  H9�u�H�H�    H�-]}  ��  H�qJ  L�B}  �
L}  H� L� H�7}  �  �
}  �}  ����   � }  ��ttH�� [^_]A\�f�     H�5�J  �   ���������   �?  ��������H��J  H�
�J  �9  �   �������1�H�����f�     ��  ��|  H�� [^_]A\�f.�     H�iJ  H�
RJ  �   ��  �7���f�H�����������  �H��(H��I  �    ������H��(� H��(H�eI  �     �z�����H��(� H��(�'  H���H��(Ð�����������H�
	   �����@ Ð��������������SH�� H��H���X  L��;  H��;  H��I��H�� [��  f�SH�� H��H���(  L��;  H�k;  H��I��H�� [��  f�AVAUATUWVSH��0I��H�`;  H��M���%  H��H����   1�A�   H���  H���  E1�1�H�ى��  ����� nHc��  H��H����   E1����+D  A�D9�~Ic�I��A�   �   H��  H���H���  D9�uCI�} 1�A�4$H��0[^_]A\A]A^ù   �y�  I��H��:  H���w  H���G  �����H���b  �   �G�  D�t$ A��I��H��H��:  �=  ���ŏ  ���  H�
B:  H����  �   ��  I��H�>:  H���  둹   ��  A�   �   H�
_:  I����  H���  �_���ff.�      SH�� �/   H���~  H�
x:  H��H�PHE�H���D  H�
}:  �  H�
�:  �  H�
�:  �  H�
�:  �t  H�
-;  �h  H�
a;  �\  H�
�;  �P  H�
�;  �D  H�
=<  �8  H�
�<  �,  H�
�<  H�� [�  ff.�     ATUWVSH��0H�ˈT$.����D$/ ����   H��1�1�fD  8�uH��H���PH����u�H���A  H�D�H9�H�� L�fJ��    �  H��H��tYH�l$.H��H���  H��H��tXM��tW1�� L9�tK��  I��H��1�H�D��H����  H��H��u�H9�u=H��    H��H��0[^_]A\�1�1��c���1���A�H   H��;  H�
�;  �^�  A�L   H��;  H�
�;  �D�  � SH�� H���c  H�H��  H��t0�I����t" �J�H����v��
tA�I�����u�A�  H�� [�ff.�     @ WVSH��   H���&  H��H�|7  H�\$ H���?  H��H��u*�ED  H���`���H���  H����   H��H����   I��   H����  H��u�H����  H��H��   [^_Ð����������%Z�  ���%J�  ���%:�  ���%*�  ���%�  ���%
�  ���%��  ���%�  ���%ڊ  ���%ʊ  ���%��  ���%��  ���%��  ���%��  ���%z�  ���%j�  ���%Z�  ���%J�  ���%:�  ���%*�  ���%�  ���%
�  ���%��  ���%�  ��H��(H�&  H� H��t"D  ��H��%  H�PH�@H��%  H��u�H��(�fD  VSH��(H��B  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^���� 1�fD  D�@��J�<� L��u��fD  ��u  ��t�D  ��u     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H��A  �8t�    ��t��tN�   H��([^�f�H�1�  H�5*�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�L?  Hc�H����    H�0>  �DA �y�qH�q�   ��  �DD$0I��H��>  �|$(H��I���t$ ��  �t$@|$P1�DD$`H��x[^ÐH��=  ��    H��=  ��    H��=  �s���@ H�)>  �c���@ H��=  �S���H�C>  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(��  A�   �   H�
B>  I���2  H�t$(�   ��  H��H��I���
  �  ��    WVSH��PHc5vs  H�˅��  H�hs  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H�s  H��H��H�H�x �     �#  �WA�0   H�H��r  H�T$ H�L��  H���}   �D$D�P����t�P���u��r  H��P[^_� ��H�L$ H�T$8A�@   �   DD�H�r  H�KI��H�S���  ��u��r�  H�
c=  ���d���@ 1��!���H�Jr  �WH�
=  L�D�>���H��H�
�<  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%�q  E��tH�e[^_A\A]A^A_]�fD  ��q     �9	  H�H��H��   H����  L�-+>  H�4>  ��q      H)�H�D$0H��q  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5�=  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
<  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  ��o  ������H�5+�  1�H�}�D  H��o  H�D� E��t
H�PH�HI����A��H��(D;%�o  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5`;  �s�;H��L�>H���Z����>L9�r��������H�
-:  �����H�
�9  ���������H��XH��n  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
yn  ��  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H�:  Hc�H��� 1ҹ   �4  H���>  H���  H�n  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   ��  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �t  H���@����   �   �[  �f�     1ҹ   �D  H��t*H�������   ���i���f�     �   ���T����   �   �  �@����   �   ��
  �,����   �   ��
  �����������ATUWVSH�� L�%�l  L�����  H��l  H��t6H�-��  H�=|�  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%P�  WVSH�� �{l  ��H�օ�u
1�H�� [^_ú   �   �y
  H��H��t3H�pH�5^l  �8H����  H�,l  H��H�"l  H�C��  묃��멐VSH��(�l  �˅�u1�H��([^�D  H�5	l  H����  H�
�k  H��t'1��H��H��tH���9�H�Au�H��tH�B��	  H���t  1�H��([^� H��k  ��ff.�     @ SH�� ����   w0��tL�nk  ����   �\k     �   H�� [�f�     ��u�=k  ��t��<�����f.�     �"k  ��uf�k  ��u�H�k  H��t�    H��H�[�	  H��u�H�
 k  H��j      ��j      �u~  �l����k����   H�� [������f�     H�
�j  �[~  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���  H��w{H�6  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���N  ��u�H��H�� [^_��    1�H��H�� [^_� H�y5  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H��4  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L��4  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H�94  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H��3  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L�i3  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������H��8E1�L�D$ I��H��1��  H��8Ð�VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H���  ���   �����  �  � ��  H� H��w  H� H�M��t	A�$�C  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���  ���   ����`  �+  � ��2  H� H��  H� H�M��t	A�$��  1�H�� [^_]A\�fD  SH�� H���#  ���    HD�H�� [�f�H�91  �8 t1�Ð��  ff.�     SH�� �˹   �/  A��H�0  H���m�����   ��  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8�   H��H�ff.�     H��(H�U0  ��~   H��  �j   H��  �V   H��  H��(�f.�     H��(H�0  ��>   H��  �*   H�s  �   H�_  H��(Ð����������%*y  ���%*y  ���%*y  ���     �%�x  ���%�x  ���%�x  ���%�x  ���%�x  ���     �%:x  ���%:x  ���%:x  ���%:x  ���%:x  ���%:x  ���%:x  ���%:x  ���%:x  ���%:x  ���%:x  ���%:x  ���%:x  ���     �%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%"w  ���%bv  ���%bv  ���%bv  ���     �%2v  ���     �%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%ru  ���%bu  ���%Ru  ���%Bu  ���%2u  ���%"u  ���%u  ��AWAVAUATUWVSH���  ��H�������H�D$h    H�D$p    H�D$x    ����  H�D$H    E1�   L�-	$  H�D$@    H�D$8    H�D$0    H�D$X    �D$T    �D$P    �D$,����Hc�H�,�L�4�    D�} A��-��   �}d��   �} ��   �   �G�����9���D$,����K  ���B  �|$TH�L$xL������D�@��������	  H�L$xH�T$hL�$  ������Å���	  H�L$hL�D$pH�D$  ��������	  H�T$pH�L$xE1�L��$�   L��$�   �G����Å���	  H�L$pH��t�v����D$,���p  H�2(  A��Jc�H���fD  L��H���]���������A��-u
�}u�}   H��"  H���6�����uN�d6M��t@A�<$ t9�������H�k"  H���
�����uIJ�D6H�D$0H��t�8 ��   fD  H�1�������H���  [^_]A\A]A^A_À} t��x���H�"  H��������tYH�"  H��������uSJ�D6H�D$8H��t��8 t�J�D6�D$,   ��H�D$@J�D6H�D$H����1����D$,�
����D$P   �����H��!  H���5������8  H��!  H���������Y  H��!  H���������O  H�!  H����������E  H�l!  H����������;  H�a!  H�����������  H�V!  H���������G  J�D6H�D$XH��������8 ������D$,   ���0���H��$�   E1�H��$�   L��$�   ������Å��i  H��$�   ������   ����r  A��H�	#  ���H������H��$�   �I���H��$�   ����H�L$x�
�������J�D6H�D$0H��������8 �����J�D6�D$,   ��H�D$8J�D6H�D$@J�D6 H�D$H�d���H�L$0E1�E1�H��$�   L��$�   L��$�   D��$�   �����ƅ��`���H��$�   ��$�   H������H��H�-e!  H�������H��$�   H�����������  H��$�   ������H����>�����u������H�D$@H�5&!  H9�t!H�L$hH���������9  H�
!  �b���H�D$HH9�t!H�L$hH���������  H�
�   �7���H�L$hE1�1�H�=!  ����L�d$h�l$PL�d$8L���{���H��$�   A��H���(���A������  ��H����������u��=���H��$�   �	����+���H�L$01�1�H��$�   L��$�   H��$�   ��$�   �'����������H��$�   ��$�   H������H��H���]���H��$�   H���������B  H�
�  �I���H�D$@H�5�  H9�t!H�L$hH���[������C  H�
�  ����H�D$HH9�t!H�L$hH���`������  H�
�  �����H�L$h�R���E1�H�
   L�\$h�����H�L$8�4���D�D$PH��$�   H�����������  H�
�  ���������H��$�   E1�H��$�   L��$�   �����Å������H��$�   1�1�L�D$d�T$dH��$�   H��$�   �����H��$�   H�
�  ���������1�H��$�   H��$�   H��$�   �����Å�t�H��$�   ������   ����n  A��H��  ���H��������*���H�|$XE1�H��$�   L��$�   L��$�   H����������4���H��H�
/  �1��������H�D$@H�5  H9�t!H�L$hH���������  H�
�  �Z���H�D$HH9�t!H�L$hH����������  H�
�  �/���H�L$h1�E1�L�-  �����\$,D�=9!  D�53!  H�l$hH��$�   H��F   L���H�H��$�   �fD�D�w��x����,   H��H������H��H��t)H�������H�H���:���H��H���O���H�H��u�H���w���D�D$PH��$�   H������A����trD��H�
K  A�������A���V����\$,�����D$,   �i����D$,   �\����D$,   �O���H��  �D$,   H�D$HH��  H�D$@�*����\$,H�
�  ������K���H�  H���M����������H��  H���6����������H��  H���������/���H��  H������������H��  H��������������D$T   �����D$,   ����H�
  �K��������H�

  �:�������H�
�  �)��������H�
�  ���������H��$�   �����   ����k  L�D$0A��H��  H�����������M��t{L��H�
  �i�������[����   ��k  A�5   �   H�
�  I������H�L$h����H�L$x����뽹   �[k  A��H�!  H���Y���H�L$x������H�
�  �Q���놹   �$k  A�<   �   H�
r  I���"���H�L$pH��t�{���H�L$h����H�L$x�����9���H�
x  ������X���H�
�  ����������H�
�  ������������������������������������������������`; @           ��������                                                                                                                �; @           ��������        ����                           ����            �* @           �* @           + @           p� @   x� @   �, @   �, @   P+ @   �, @   �+ @   `+ @   �@ @   �@ @   �@ @      �p  �@ @   �@ @   PDT PST `, @   @, @                                                                                                                                                                                                                                                           Language com.apple.international Locale rb opening file failed: %s
 Could not open file '%s'
   The file '%s' is too large for processing.
 Could not allocate memory...
       Could not read in file '%s' (size %ld read %ld)
 Usage: %s [OPTIONS] COMMAND
   -u udid list [Get Wifi Profiles from Phone].    -u udid mdm [Get MDM Status from Phone].        -u udid CloudConfig [Get CloudConfig from Phone].       -u udid DeleteProfile ProfileIdentifier [Delete Profiles from Phone].   -u udid install path [install Wifi .configprofiles in Phone].   -u udid testprepare path [prepare device with predefined skipsetup].    -u udid supervise (make sure device is on HelloScreen & not already prepared!)  -u udid prepare skiplist(comma seprated) Language Locale [prepare iphone using skiplist and language].  -u udid all path.config path.skiplist Language Locale [perform both prepare and wifi]. -n connect to network device PhoneCheck 2.0.3 ideviceconfiguration.c idx < count idx == count - 1 en_US en --debug --udid install supervise prepare all wipedevice list mdm testprepare CloudConfig DeleteProfile -x --xml -h --help -n  No device found with udid %s, is it plugged in?
        No device found, is it plugged in? ideviceprovision     ERROR: Could not connect to lockdownd, error code %d
 com.apple.mobile.MCInstall        Could not start service "com.apple.mobile.MCInstall"
   Could not connect to "com.apple.mobile.MCInstall" on device
 Profile-Success Profile-Fail %d
 N Language-Success Language-Fail Locale-Success Locale-Fail Prepare-Success Prepare-Fail %d errorcode %d
 Could not install profile '%s', status code: 0x%x
 Prepare-here Prepare-Fail %s Could not get CloudConfiguration from device, status code: 0x%x
 [%s] Profile-deleted
  Could not get installed profiles from device, status code: 0x%x
        AccessibilityAppearance,All,Android,AppStore,Appearance,AppleID,Biometric,CloudStorage,DeviceToDeviceMigration,Diagnostics,DisplayTone,ExpressLanguage,HomeButtonSensitivity,IntendedUser,Keyboard,Language,Location,MessagingActivationUsingPhoneNumber,N/A,OnBoarding,Passcode,Payment,PreferredLanguage,Privacy,Region,Restore,RestoreCompleted,SIMSetup,Safety,ScreenSaver,ScreenTime,Siri,SoftwareUpdate,SpokenLanguage,TOS,TVHomeScreenSync,TVProviderSignIn,TVRoom,TapToSetup,TermsOfAddress,UpdateCompleted,WatchMigration,Welcome,WiFi,Zoom,iCloudStorage,iMessageAndFaceTime  ��������:��� ���<���D��������������            � @                            � @   � @   l� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  x���,�������L���\���l���<���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
       ��� ��� ��� ��� ������� �����������[���        runtime error %d
               0@ @           @@ @           p; @              @            d @            d @           �Y @           �@ @           �� @           �� @           h� @           d� @           `� @            � @           �� @           @� @           H� @            � @           � @           � @           (� @           p� @            @ @           �� @           0" @           � @           P� @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P  ~  t�  �  �  |�  �  B  ��  P    ��    -  ��  0  �  ��  �    ��  �    ��     �  �  �  �  �  �  �  ��  �  �  ��  �  q  �  �  �  �  �  �  �  �  �  ,�  �  	  0�    r  <�  �  �!  H�  �!  "  `�   "  ,"  h�  0"  �#  l�  �#  `$  t�  `$  �$  ��  �$  Q%  ��  `%  R&  ��  `&  �&  ��  �&  �&  ��  �&  }'  ��  �'   (  ��   (  7(  ��  @(  �(  ��  �(  �(  ā   )  �)  ȁ  �)  V*  ́  �*  �*  Ё  �*  +  ؁  +  B+  �  P+  S+  �  `+  �+  ��  �+  :,   �  @,  ^,  �  `,  u,  �  �,  �,  �  �,  �,  $�  �,  &-  ,�  0-  f-  4�  �/  Q;  Ȁ  `;  e;  <�                                                                                                                                                                                                                                                                                                                                              B   b  
 
20`pP�	 B  �.     �  �  0"  �  	 B  �.     �    0"     B         20 20 R
0	`pP��� 20
 
R0`pP� 20
 
$ 0`p  
 [ 0`
p	P���� B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   b   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                                                                                                     �          <�  ��  ��          ��  ��  �          ��  �  ��          Э   �   �          ��  (�  0�           �  8�  P�          ��  X�  ��          �   �  h�           �  p�  ��          T�  ��  ��          ��  Ȧ                       �      �      ,�      H�      `�      ��      ��      ħ      �      ��      �      0�      D�      X�      p�      ��      ��      ��              ̨      �      ��      �      (�      @�      ^�      f�      t�      ��              ��      ��              ��      Ʃ      Щ      ة              �              ��      �      �              "�      0�      >�      L�      V�      `�      z�      ��      ��      ��      ��      Ȫ      �      
�      �      &�      H�      P�      X�      b�              n�      ��      ��      ��      ��      ԫ      ޫ      �      �      ��      ��      �      �              �      "�      ,�      6�      @�              J�      X�      f�      r�              |�      ��      ��      ��      Ь      �               �      �      ,�      H�      `�      ��      ��      ħ      �      ��      �      0�      D�      X�      p�      ��      ��      ��              ̨      �      ��      �      (�      @�      ^�      f�      t�      ��              ��      ��              ��      Ʃ      Щ      ة              �              ��      �      �              "�      0�      >�      L�      V�      `�      z�      ��      ��      ��      ��      Ȫ      �      
�      �      &�      H�      P�      X�      b�              n�      ��      ��      ��      ��      ԫ      ޫ      �      �      ��      ��      �      �              �      "�      ,�      6�      @�              J�      X�      f�      r�              |�      ��      ��      ��      Ь      �              f idevice_free  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_service_descriptor_free � lockdownd_set_value   � lockdownd_start_service   � mcinstall_DeleteConfigs   � mcinstall_EraseDevice � mcinstall_GetCloudConfig  � mcinstall_Prepare � mcinstall_Wifi    � mcinstall_client_free � mcinstall_client_new  � mcinstall_copy    � mcinstall_get_status_code � mcinstall_is_mdm  DeleteCriticalSection =EnterCriticalSection  tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy  }strrchr  __p___argc   __p___argv   __p___wargv  _assert  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit # _errno  % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal  h strerror   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf  � fclose  � fgets � fopen � fread � fseek � ftell � fwrite  � puts  6 _strdup � strcmp  � strlen  � strncmp � strtok  	 __daylight   __timezone   __tzname  < _tzset   plist_array_append_item    plist_free    4 plist_new_array   6 plist_new_data    ; plist_new_string  L plist_to_xml   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  �  �  �  �  �  �  �  �  KERNEL32.dll    (�  (�  api-ms-win-crt-environment-l1-1-0.dll   <�  <�  <�  <�  api-ms-win-crt-heap-l1-1-0.dll  P�  api-ms-win-crt-math-l1-1-0.dll  d�  d�  d�  api-ms-win-crt-private-l1-1-0.dll   x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  ��  ��  ��  api-ms-win-crt-string-l1-1-0.dll    ��  ��  ��  ��  api-ms-win-crt-time-l1-1-0.dll  Ƞ  Ƞ  Ƞ  Ƞ  Ƞ  Ƞ  libplist-2.0.dll                                                                                                            0 @                    @                   � @   � @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          0     x�   @  0    �P�`�p�������������������ȠР���� �   P  H   ����������Ь�� �� �0�@�P�`�p�����������Э�� �� �0�@�P�`�p� �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                            ,               @   $                      <    �&       P @   �      �/ @   �                      ,    �T       � @   �                           L[                           Na                       ,    �a       � @                              \c                       ,    �c       � @   �                           �k                           7l                       ,    �m       � @   �                       ,    �p       � @                              q                       ,    r       � @   =                      ,    \�       �! @   L                           Q�                       ,    ٌ       0" @   �                      ,    ��       �# @   b                          ��                           �                       ,    �       `& @   �                          ��                       ,    V�       �* @                          ,    ��       �* @   H                       ,    ��       + @   2                           H�                       ,    ��       P+ @                                                                                                                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   P.   �  3GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden �  �  �          9  char 4{   size_t #,�   long long unsigned int ssize_t -#�   long long int short unsigned int int long int unsigned int $_iobuf !
2  _Placeholder #2    52  FILE /  unsigned char double float long double 	�   long unsigned int short int 	{   �  	�   signed char 	F  _Float16 __bf16 	�  uint8_t $F  uint16_t &�   uint32_t (�   uint64_t *0�   plist_t Y2  �   |�  
PLIST_ERR_SUCCESS  PLIST_ERR_INVALID_ARG PLIST_ERR_FORMAT ~PLIST_ERR_PARSE }PLIST_ERR_NO_MEM |PLIST_ERR_UNKNOWN �~ plist_err_t �1  �   '�  
IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 0�  �  2 �  �  idevice_t 3�  	�  6idevice_options �   9D  
IDEVICE_LOOKUP_USBMUX 
IDEVICE_LOOKUP_NETWORK 
IDEVICE_LOOKUP_PREFER_NETWORK  	�   D  �   $3	  
LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ lockdownd_error_t PN    R)Y	    lockdownd_client_t S#y	  	M	  $lockdownd_service_descriptor `�	  port a�   ssl_enabled b
�  identifier c�   lockdownd_service_descriptor_t e.
  	~	  �   �
  
MCINSTALL_E_SUCCESS  MCINSTALL_E_INVALID_ARG MCINSTALL_E_PLIST_ERROR ~MCINSTALL_E_CONN_FAILED }MCINSTALL_E_REQUEST_FAILED |MCINSTALL_E_MDM_ERROR ZMCINSTALL_E_UNKNOWN_ERROR �~ mcinstall_error_t 
  ]  )�
  ]  mcinstall_client_t #  	�
  �   i�  
OP_INSTALL  
OP_LIST 
OP_ClOUDCONFIG 
OP_CONFIGREMOVE 
OP_PREPARE 
OP_TESTPREPARE 
OP_WIPE 
OP_ALL 
OP_MDM 
NUM_OPS 	 fgets x�  �  �  �   �   	9  �  lockdownd_set_value �3	    ^	  D  D  !   fread ��   -  4  �   �   �   fclose j�   G  �   ftell ��   `  �   fseek ��   �  �  �   �    strerror 	R�  �  �    %_errno 
�y  fopen �  �  I  I   &plist_array_append_item $
�  !  !   plist_new_string �
!  
  D   %plist_new_array �
!  strrchr 	]�  Q
  D  �    mcinstall_client_free H�
  y
  �
   mcinstall_copy h�
  �
  �
  �
   	!  mcinstall_EraseDevice ��
  �
  �
   mcinstall_DeleteConfigs u�
     �
  D  �
   mcinstall_is_mdm }�
  (  �
  �
   'plist_free �
A  !   plist_to_xml ��  k  !  �  k   	�  mcinstall_GetCloudConfig |�
  �  �
  �
   mcinstall_Prepare {�
  �  �
  !  �    mcinstall_get_status_code ~�   �  �
   mcinstall_Wifi z�
     �
  !   &free 
4  2   plist_new_data �
!  Z  D     lockdownd_service_descriptor_free >3	  �  �	   mcinstall_client_new .�
  �  �  �	  �   	�
  lockdownd_client_free �3	  �  ^	   lockdownd_start_service �3	  !  ^	  D  !   	�	  idevice_free ��  E  �   fprintf ��   f  �  I  ( __acrt_iob_func ]�  �  �    lockdownd_client_new_with_handshake �3	  �  �  �  D   	^	  printf ��   �  D  ( idevice_new_with_options ��      D  �   	�  'idevice_set_debug_level lH  �    strcmp 	?�   f  D  D   strdup 	l�    D   7_assert .�  D  D  �    strtok 	a�  �  �  I   malloc 
2  �  �    strlen 	@�   �  D   8main �   �/ @   �      �)#  )argc �     �  )argv �  �  �  client ^	  ��zldret 	3	  �  �  service 
$�	  ��zdevice �  ��zret �  @  .  res 
	�   �  �  i 	�   	  �  op 	�   �  �  output_xml 	�      �  supervise 	�       use_network 	�        udid D  0  .  profilename D  @  >  ConfigFile D  P  N  SkipListFile D  d  ^  Language �  �  �  Locale �  �  �  mis ��
  ��z
�   �  P  ��  ��z�  ��   ��zpsize �  �  �  pdata �!      �  ��   2  &  
�   v  sc ��   u4 @   �  �4 @   �  Rv Qt  4 @   I)  �  R��zQ��zX��z >4 @   4  �  Rv  P4 @      `4 @   �  �  Qu  *; @   .  R	�U @     
�   r  �  �   i  _  
�   �  ?  �#3	  �  �  �4 @   �#  K  Q��z �4 @   .  j  R	�U @    �9 @   .  R	�U @     
�   �  �  
#3	  �  �  �4 @   )#  �  Q��z �4 @   .  �  R	�U @    : @   .  R	�U @     
�   d  misresp $�
  �  �  5 @   x$  1  R|  (5 @   �  I  Xv  @5 @   �  Ru Qt  �4 @   �   
L  �  �  S�   �  �  
d    ?  7#3	      �7 @   �#  �  Q��z �7 @   .  �  R	�U @    �9 @   .  R	�U @     
v  s  �  C#3	  0  ,  �7 @   )#  8  Q��z �7 @   .  W  R	�U @    �9 @   .  R	�U @     
�  �  misresp W$�
  J  F  9�%  (8 @    �  W/e  :�  ;�%  ��z�%  b  ^  �%  z  v  <&  m8 @   $       $  =&  r8 @   .  ~8 @   �  �8 @   �  Rt   P8 @   
  `8 @   u'  O  Rv Q, �8 @      Ru    �8 @   �  �  Qt X��z� �8 @   �  R	
V @   Q|  8 @   �   
  �  P  k�  ��z�  l�   ��zpsize r  �  �  pdata s!  �  �  
: @   6       �  sc {�   �  �  : @   �  ': @   f  W  R2 >: @   E  Q	(V @   X��zYt   
(  �  ?  �#3	  �  �  �5 @   �#  �  Q��z 	6 @   .  �  R	�U @    L; @   .  R	�U @     
:  e  �  �#3	  	  	   6 @   )#  *  Q��z 46 @   .  I  R	�U @    ;; @   .  R	�U @     �5 @   I)  �  R��zQ��zX��z �5 @   4  �  Ru  �5 @      �  Ru  �5 @   �  �  Qt  �5 @   .  �  R	�U @    >6 @   �  R6 @   .     R	[V @    \6 @   x$  :  R��z q6 @   �  U  X��z� �6 @   .  t  R	hV @    79 @   .  R	�U @     �6 @   *       �  �  �!  ��z�  �  ��  8  ��   �  sc ��    �6 @   p  Q��z  !3 @   Y       �  �  �!  ��z=  �  ��  8  ��   K3 @   /       �  sc ��   $	  	  X3 @   �  e3 @   f  �  R2 z3 @   E  Q	xV @     A3 @      Q��z  P7 @   D       ?  �  �!  ��z�  �  ��  8  ��   x7 @   �
    Qu X��z �7 @   �  R	�V @   Qu   
�   T  �  !  ��z�6 @   >       �  �  �  ��z8  	�  ��z�6 @   A  �  Q��zX��z �6 @   �  R	uV @     7 @   4       1  sc �   C	  =	  )7 @   �  67 @   f    R2 K7 @   E  Q	�V @     �3 @   (  7 @   y
  Q��z  �/ @   +.  Q0 @   "  x  R1 �0 @   �  �  R��zQ| X	�0��z�0)( 	�# �0 @   �  �  Q��zX	�T @    �0 @   �  �  Q	U @   X��z �0 @   �    X��z 
1 @   Z  ;1 @   H  ?  Rv Q}  b1 @   H  d  Rv Q	�S @    �1 @   H  �  Rv Q	�S @    �1 @   ?,  �  *P'  u *\'  t  �1 @   H  �  Rv Q	�S @    �1 @   H  �  Rv Q	T @    c2 @   H     Rv Q	T @    z2 @   H  ?   Rv Q	T @    �2 @   H  d   Rv Q	T @    �2 @   H  �   Rv Q	T @    �2 @   H  �   Rv Q	#T @    �2 @   H  �   Rv Q	/T @    �2 @   H  �   Rv Q	;T @    �3 @   Q
  �3 @   &  W5 @   �
  K9 @   H  D!  Rv Q	IT @    b9 @   H  i!  Rv Q	LT @    y9 @   H  �!  Rv Q	RT @    �9 @   H  �!  Rv Q	UT @    �9 @   H  �!  Rv Q	\T @    W: @   �  �!  R	`T @   Q|  j: @   f  "  R2 �: @   :.  >"  R	(U @   Q1X5 �: @   �  �: @   &  �: @   f  o"  R2 �: @   E  �"  Q	�T @   Xs  �: @   &  �: @   .  �"  R	�T @    �: @   f  �"  R2 �: @   :.  #  R	`U @   Q1X< ; @   Z  ; @   �  ; @   &    SetLocale �3	  � @   .       ��#  +�  �8^	  d	  \	  locale �H�  �	  �	  lerr  3	  � @   �  �#  R�Q !� @   �  R�RQ		P @   X	!P @      SetLanguage �3	  P @   .       �x$  +�  �:^	  �	  �	  langauge �I�  �	  �	  lerr �3	  ` @   �  H$  R�Q !~ @   �  R�RQ		P @   X	 P @      Get_File_plist �!  � @   �       ��%  path �+D  �	  �	  >v  �
!  
  
  ,line ��%  ��}file ��  1
  -
  � @   
  � @   �  #%  R�RQ	(P @    � @   �&  ;%  Rs  � @   f  � @   �  � @   �  m%  Ru  � @   �  �%  Rs Q
 Xt   @   -  Rt   "{   �%  -�   � ?Get_TestPrepare_plist �!  &  skiplist �&  @v  �
!  tokens ��  .i �
�     "{   !&  A�   6 Bprofile_read_from_file ��   �&  #path �/D  /P  �E�&  /�  �a�  f ��  size ��   buf ��  cur ��   .r ��     	�  0strip_copy v�  0 @   Q       �;'  s vD  O
  E
  p w�  x
  v
  K @   0       '  p2 y�  �
  �
   = @   �  -'  Rs  F @   �   Cprint_usage U
u'  #argc U�   #argv U*�  name W�   0str_split #�   @         �9)  a_str #�  �
  �
  a_delim #*�   �
  �
  result %�  3  '  count &�   t  d  tmp '�  �  �  last_comma (�  �  �  ,delim )
9)  �N
x   
)  idx C�        token D�  )  !  � @   �  (  Rs Qv  � @   f  � @   �  �(  R0Qv   @     �(  R	�S @   Q	�S @   XH - @     R	�S @   Q	�S @   XL  _ @   �  ")  Rs  | @   �  R| 3$  "{   I)  -�    1!&  � @   �      �?,  E&  P  H  Q&  ~  v  \&  �  �  g&  �  �  p&  
  
  |&  B
  8
  �&  o
  k
  D�&  h   	*  �&  �
  �
  Q @     R~  $ &u "Q1X
 Ys   � @   �  .*  Rv Q	(P @    � @   `  P*  Rs Q0X2 � @   G  h*  Rs   @   `  �*  Rs Q0X0  @   �  �*  Rt  $ & ^ @   -  �*  Rs  � @   f  �*  R2 � @   E  �*  Q	`P @   Xv  � @   -  +  Rs  � @      ,+  Ru  � @   f  C+  R2 � @   E  u+  Q	�P @   Xv Yt w ~  � @   �  � @   �  � @   �  �+  R	+P @    � @   f  �+  R2  @   E  �+  Q	DP @   Xv   @   f  ,  R2 5 @   :.  *,  R	�P @   Q1XM = @   -  Rs   1;'  P @   �       �.  h'  �
  �
  \'  �
  �
  P'  �
  �
  b @   2
  �,  Rs Q/ | @   �  �,  R	�P @   Qs  � @   .  �,  R	 Q @    � @   .  -  R	0Q @    � @   .  !-  R	`Q @    � @   .  @-  R	�Q @    � @   .  _-  R	�Q @    � @   .  ~-  R	 R @    � @   .  �-  R	hR @    � @   .  �-  R	�R @    � @   .  �-  R	 S @    � @   .  �-  R	wS @    ! @   .  R	�S @     2puts __builtin_puts E__main __main 2fwrite __builtin_fwrite  ]   �	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 j  R  � @   �       �  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	0� @   atexit ��   �  Y   __main 5� @          ��  � @   �   	__do_global_ctors    @   j       �  
nptrs "�   �
  �
  
i #�   	    m @   j  R	� @     	__do_global_dtors � @   :       �[  p [  	 @ @    	   �   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 R  :    char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
�  �   �,  __uninitialized  __initializing __initialized  �  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	@ @   �  	@ @   =  
"	H� @   [  	@� @    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 `  H  Z  _dowildcard  �   	 @ @   int  }   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  � @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   � @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _newmode �   	P� @   int  �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 :  "  � @   �       $  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	l� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�Y @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�Y @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	h� @   __mingw_initltsdyn_force ��   	d� @   __mingw_initltssuo_force ��   	`� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@  � @   /       �}  
�  �  $     
�  *M  6  2  
�  ;d  H  D  � @   �   __tlregdtor m�   � @          ��  func m  R __dyn_tls_init L@  	  �  �  �  *M  �  ;d  pfunc N
$  ps O
�    �  � @   �       ��  ^  V  �  �  ~  �  �  �  �  �  �    @      @   +       L�  �  �  �  �  �  �  �  �  �  �    �  �       e @   �    �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 (    :  _commode �   	p� @   int  w   5  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  t  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 f	  N	  � @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   � @   �       �0  pexcept 0  =  7  type 
�  _  S  � @   b  �  R2  @   7  Q	�Z @   Xs Yt w �ww(�ww0�w  5   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 #
  
  � @          l  _fpreset 	� @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  �  __mingw_app_type �   	�� @   int  G   �  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 X  �  � @   =      �  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �� @   ]      ��  4was_init �%  	�� @   5mSecs �%  �  �  !�   @   �  �4  �  �  �  6�  
�  �  �  
�  E    
�  \  D  
�  �  �  

    �  
  >  .  "E  �  �  
F  �  �  
[  �  �    @   `  R	(\ @   Xu w t     � @   � @          �;  �  �  �  �  �  �  �        � @   � @          �      �      �  +  )  � @   �  Ru    !  r  @     ��  �  5  3  �  @  >  �  O  M  7  r  @     �  Y  W  �  d  b  �  s  q  ~  @   �  Ru      '! @   '! @   
       �w  �  }  {  �  �  �  �  �  �    '! @   '! @   
       �  �  �  �  �  �  �  �  �  /! @   �  Ru      @! @   @! @          �   �  �  �  �  �  �  �  �  �    @! @   @! @          �  �  �  �  �  �  �      H! @   �  Ru    "$    �  
)      83    
4  +  )    �! @   �! @   
       s�  5  3  �  @  >  �  O  M    �! @   �! @   
       �  Y  W  �  d  b  �  s  q  �! @   �  Rt      
�! @   `    R	�[ @    �! @   `  R	�[ @      9�  �  @   X       �|  
�    {  :�  ���  @   
  Yu   � @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable � @   b      �`  &addr ��  �  �  b �:  ��h �g  �  �  i �%      >� @   P       �  new_protect �
u  !    
$ @   
  �  Ys  . @    
  < @   `  R	�[ @     
p @   �
  �  Rs  � @   n
  
� @   E
    Q��X0 
b @   `  >  R	`[ @    r @   `  R	@[ @   Qs   ?__report_error T� @   i       �/  &msg T  -  )  @argp ��   �X
� @     �  R2 
� @   /  �  R	 [ @   Q1XK 
� @       R2 
 @   �
  !  Qs Xt  	 @   �
   Afwrite __builtin_fwrite   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �! @   L       ~  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr � " @          �P  f ,�  H  D  ," @   �  R�R  __mingw_raise_matherr ��! @   >       �typ !�   \  V  name 2�  t  p  a1 ?w   �  �  a2 Jw   �  �  rslt 
w   � ex 0  �@" @   R�@   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  q
  ,  _fmode �   	�� @   int  �     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 (    0" @   �      f  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�    ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	    z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	�� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   0" @   �      ��  'exception_data �-�  �  �  old_handler �
	  	  �  action ��   Z  @  reset_fpu ��   �  �  
�" @   �
  �  R8Q0 (�" @   �  R�R 
�" @   �
  �  R4Q0 �" @   �  R4 
L# @   �
    R8Q0 
e# @   �
  7  R8Q1 
|# @   �
  S  R;Q0 �# @   f  R; �# @   y  R8 
�# @   �
  �  R;Q1 
�# @   �
  �  R4Q1 
�# @   �
  �  R8Q1 )�# @   �
   �	   �
   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 9  !  �# @   b      �  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	�� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  `% @   �       �n  	hDllHandle z�  -    	reason {<  �  �  	reserved |S  +     �% @   K       �  
keyp �&�  �  �  
t �-�  �  �  �% @   �  
& @   C  R	 � @     !n  �% @   �% @          �  �  �% @   )
   "n  �% @   .  �E  #.  �  5& @   )
    %& @   6  
M& @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   �$ @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�    �   % @   �  B	  Rt  3% @   �  
<% @   �  Rt   ___w64_mingwthr_add_key_dtor *�   `$ @   o       �$
  	key *%<  ,  "  	dtor *1.  `  R  
new_key ,$
  �  �  �$ @   �  �	  R1QH �$ @   �  
  Rt  
�$ @   �  Rt   �  &n  �# @   p       ��  �  �  '�  ($ @          �
  �  �  �  ,$ @     1$ @     (C$ @   Rt   
$ @   �  �
  R|  )`$ @   �  R	 � @      �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ^  F  k   _CRT_MT �   	0@ @   int  �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �   __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	A� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	@� @    �   K  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  `& @   �      �   long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  %  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  =  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  X  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  X  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "=  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #=  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   %  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �) @   �       �7
  i �(�   �  �  M  �	`  3  �	  �  �  importDesc �@        �^
  importsStartRVA �	I  %    �  �) @   	�  ��  �  �  �  �  �  	�  �) @    �  �  �  �  �  d  `  �  u  s      M  �) @   �) @   J       �q  �    f  }  �  �  �  �  �  �  �  �    
_IsNonwritableInCurrentImage �   ) @   �       ��  pTarget �%`  �  �  M  �	`  rvaTarget �
�  �  �    �^
  	    �   ) @   �  �/  �  �  �  �  �  	�  ) @    �  �  �  �  �      �  &  $      M  4) @   4) @   I       �q  2  0  f  }  <  :  �  F  D  �  P  N    
_GetPEImageBase �`  �( @   6       �0  M  �	`  	�  �( @   �  �	�  �  �  �  �  	�  �( @    �  �  �  �  �  ]  Y  �  n  l       
_FindPESectionExec y^
  @( @   s       �%  eNo y�   |  x  M  {	`  3  |	  �  �    }^
  �  �  g  ~�   �  �  	�  @( @   �  �	�  �  �  �  �  	�  Q( @    �  �  �  �  �  �  �  �  �  �       
__mingw_GetSectionCount g�    ( @   7       ��  M  i	`  3  j	  �  �  	�   ( @   }  m	�  }  �  �  �  	�  ( @    �  �  �  �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
  �' @   �       �  p Y&s  �  �  M  [	`  rva \
�      �  �' @   W  _�  �  W  �  �  �  	�  �' @    g  �  g  �  �  +  '  �  <  :      	M  �' @   r  c
q  H  F  f  r  }  T  P  �  r  p  �  |  z     
_FindPESectionByName :^
  �& @   �       �M  pName :#�  �  �  M  <	`  3  =	  �  �    >^
  �  �  g  ?�   �  �  �  �& @   L  F  �  L  �  �  �  �  ' @    ' @          �  �  �  �  �  �  �  �     &�& @   �  -  Rt  'Z' @   z  Rs Qt X8  _FindPESection $^
  �  M  $`  (rva $-�  3  &	    '^
  g  (�    _ValidateImageBase   �  M  `  pDOSHeader �  3  	  pOptHeader v   )�  `& @   ,       �~  �  �  �  �       �  �  	�  i& @    E  �      E  �  �  2  .  �  ?  =     *M  �& @   P       �f  K  G  +q  Q}  ^  Z  �  }  {  �  �  �    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  *&  _MINGW_INSTALL_DEBUG_MATHERR �   	@@ @   int  �     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 I  1  �* @          d&  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	P@ @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  �* @          �_File *�  �  �  _Format J�  �  �  _ArgList Z�     
  �* @   �  R0Q�RX�QY0w �X   �   ;  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 1    �* @   H       �&  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	`@ @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �* @   H       �_Format .�  6  0  
ap 
�   �Xret 	  K  I  �* @   �  �  R1 + @   �  R0Xs Y0w t    �   �  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  + @   2       k'  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	p@ @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  + @   2       �_File )�  c  ]  _Format I�  |  v  
ap 
�   �hret 	  �  �  =+ @   �  R0Q�RX�QY0w �   �   	!  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �'  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	x� @   local__winitenv 
`  	p� @   '  
	�@ @     
	�@ @    �   �!  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  P+ @         P(  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	 A @   �      �   __imp_at_quick_exit g)  	�@ @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	�@ @   
initial_tzname1 }
V  	�@ @   
initial_tznames ~�  	�@ @   
initial_timezone 
%  	�@ @   
initial_daylight �  	�@ @   __imp_tzname ��  	�@ @   __imp_timezone ��  	�@ @   __imp_daylight ��  	�@ @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�@ @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�@ @   �	  __imp__amsg_exit �V  	�@ @   F  __imp__get_output_format �\
  	�@ @   -
  __imp_tzset ��  	�@ @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�@ @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �, @   5       ��
  file �!�
  �  �  fmt �6�  �  �  
ap ��   �h+ret �  �  �  �, @   �
  R4Q�RX�QY0w �  ,tzset "�, @   6       �  -  �, @   �, @   -       �- @   +  - @     - @       ._tzset �/_get_output_format nF  P+ @          �0_amsg_exit i�, @   .       ��  ret i  �  �  �, @   z  �  R2 �, @   Q  �  Q	�\ @   Xs  �, @   <  R�  at_quick_exit �  `, @          �   func ]*�    �  1u, @   �   _onexit ��  @, @          �p  func V%�      M, @   �  Rs   __wgetmainargs J  �+ @   j       �[  _Argc J"�  9  3  _Argv J5I  X  R  _Env JGI  w  q   p  JQ  �  �  !|  Jl�	  � �+ @   0   , @   	  &  R	v  $0.# , @   �  , @   �  , @   �  -, @   U   __getmainargs >  `+ @   j       �E  _Argc >!�  �  �  _Argv >1S  �  �  _Env >@S  �  �   p  >J      �  !|  >e�	  � �+ @   �  �+ @   �    R	v  $0.# �+ @   �  �+ @   �  �+ @   u  �+ @   U   2  0- @   6       �B- @   +  N- @     Z- @                                                                                                                                                                                                                                                                                                                                                                                                                        
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  H}   I  ( 
  H }  H}  .?:;9'I<  4 :!;9I�B  	 !I  
(   $ >   :;9I  
U  .?:;9'I<  4 :!;9I  4 :!;9I  4 :!;9I�B  4 :!;9I�B  4 1�B    4 :!;9I   :!;9I�B  >!I:;9   1�B  
 :;9I8  7 I  4 :!;9I     :;9I   <  4 :!;9I   .:!;9'I@z  !H}�  "I  # :!;9I  $:;9  %. ?:;9'I<  &.?:;9'<  '.?:;9'<  (   ) :!;!�9I�B  *I �~  + :!;9I�B  ,4 :!;9I  -! I/  .  / :!;9I  0.?:!;9'I@z  1.1@z  2. ?<n:!;!   3%U  4& I  5   6>I:;9  7.?:;9'�<  8.?:;9'I@z  91R�BUXYW  :U  ;4 1  <1  =4 1  >4 :;9I�B  ?.:;9I   @4 :;9I  A! I/  B.:;9'I   C.:;9'   D1U  E. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                                                                                                                                                                                  5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �    m   �
        ]  v  �  
�   �   �  �  �  �         ,  5  >  G    	P @   �Zh X> V X 	� @   Yg X> V X 	� @   ��>rhX=����/Z���=	�
<
	Y<	YX�^K,LX<..	`< �� <Y�w	X� �� <�	].6	 f t t <Y �� <Z	. �  <Y�
�.Z
T@Y  t ������������uW���@Ky
.9[y�=,	
�
L
K	>yQyJJ$<2 X X <N
L�	Z�	>
ZS.
]Y!X<K -
Y�	^Y�� WJ,t.
 3	 � �! 	0 @   *Y!= X�Z;=
u*=IXY<H<	R
LX� ��?	\�=	X=�A: � � X < <)S
) �	X��   	�/ @   �.��$�� y�y�<( Ywt���	����<J� �
��  <�J]  X � X��(X .� X�	�	=��	 t	�	X	YZ( �~&$ ��- �) �
KX Xs<` �
K� X	� ��<	� .Y� �|X� �Q �
K� X
][�;
YX
YhXz.B�� �� �� �� �� �	� �� �
�� ���<
�X�=�
� t��W/ f�; <Y
?	�ʠ
�|X� �
�]�9
YX
YX
Y� XU=>�
�
� t
��u
�<qt
=[%  ���2 vX
<X2 v%  �
�Z6���
�3���
YA
.t
mY
\% 
v�/ �� <��2 sX
<X2 s%  �
���~XU/0�
�
v X
��u
�;
=[ ��
�6���
�3���
�
>r
ZY �� < X�
�]�=�
� t �� �-0WK��Z
w�/�
� �J�W/ f�; <YX
KW=�� ��<
�~�Z6���
�3���
	Y2
�Y�|J��
[% ��|�z��2Z�=X>�
Y �< <% U ��/ �vR2 s�
JX2 s%  .�~���r�WJ��} �- �) �� �- �) �� ��d����X�X��X�X� �W/ f� < �	�~
Y�	� �  <Y��	v. �� <Y�
w.�	. �  <Y
X
Y	Y��X�X��Xt� #    K   �
      �  �  �  �  �  	      '  1   	� @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �  �  �  �  	      )  2  =  6     .   �
      �  �  �  �  R     .   �
      0  H  o  y   	� @    6     .   �
      �  �          K   �
      j  �  �  �  �  �  �  �  �  �   	� @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      Z  r  �  �  6     .   �
      �  	  8	  C	  �     <   �
      �	  �	  �	  �	  �	  �	  
   	� @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      U
  m
  �
  �
   	� @   		3 6     .   �
      �
    8  H  |    s   �
      �  �  �         '  1  =  G  O  \  e  m  y  �  �  �    	� @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	� @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      �  
  3
  N
  \
  j
   	�! @   
L�Z
�KTYZ
g=yuX 6     .   �
      �
  �
  �
    �    U   �
      ]  u  �  	�  �  �  �  �  �         	0" @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      j  �  �  �  �  �  �  �  �     
    !  5   	�# @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      �  �  �  �  6     .   �
      5  M  t  �  G    K   �
      �  �  #  >  G  P  Z  f  p  x   	`& @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< 6     .   �
      �  �    !  n     A   �
      �  �  �  �  �  �       	�* @   K
�<.Y �     A   �
      h  �  �  �  �  �  �  �   	�* @   g?	YT�Y	 X� <uX �     A   �
      K  c  �  �  �  �  �  �   	+ @   KU	\fp	\;Y	Y W     O   �
      ,  D  l  �  �  �  �  �  �  �  �  |    h   �
      N  f  �  �  �  �  �  �        "  /  8  C  U   	P+ @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	�, @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                                                    ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �      $   P  P @   .       A�D0cA� $   P  � @   .       A�D0cA� d   P  � @   �      B�B�B �A(�A0�A8�A@�Dp�
@A�8A�0A�(A� B�B�B�A       $   P  P @   �       A�D0�A�L   P   @         B�A�A �A(�A0�D`�
0A�(A� A�A�B�A $   P  0 @   Q       A�D0JA�<   P  � @   �       A�A�A �G�x A�A�A�     l   P  �/ @   �      B�B�B �B(�A0�A8�A@�AH�	G�0
HA�@A�8A�0A�(B� B�B�B�A       ���� x �         p  � @   :       D0u  4   p    @   j       A�A�D@@
A�A�H       p  � @             ���� x �         �  � @             ���� x �      $   (  � @   /       D0R
JN    L   (  � @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       (  � @             ���� x �      <   �  � @   �       A�A�D�P�
���
���A�A�B    ���� x �         (  � @             ���� x �      $   X  � @   i       A�A�DP   <   X   @   b      A�A�A �Dp�
 A�A�A�D   \   X  � @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         8  �! @   >       D`y     8   " @             ���� x �      4   �  0" @   �      A�D0}
A�Mf
A�I     ���� x �      L   �  �# @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �  `$ @   o       A�A�A �D@U
 A�A�A�A    D   �  �$ @   �       A�A�D@R
A�A�FR
A�A�D      4   �  `% @   �       A�D0p
A�J�
A�A      ���� x �            `& @   ,             �& @   P       L      �& @   �       A�A�A �D@~
 A�A�A�HI A�A�A�          �' @   �              ( @   7             @( @   s             �( @   6              ) @   �             �) @   �          ���� x �         (	  �* @          D@Y     ���� x �      ,   `	  �* @   H       A�A�D`A�A�   ���� x �         �	  + @   2       DPm     ���� x �         �	  P+ @          L   �	  `+ @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �	  �+ @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �	  @, @          A�D0WA�    �	  `, @             �	  �, @   .       A�D0   �	  �, @   5       DPp     �	  �, @   6       D0q     �	  0- @   6       D0q                                                                                                                                                  Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion lockdownd_client_private length Languageresponce profile_data mcinstall_client_private ArrContent profiles localeresonce profile_size lockdown attemp buffer idevice_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection _DoWildCard _StartInfo                                                                                                                          C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> ideviceconfiguration.c C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/plist ../include/libimobiledevice ideviceconfiguration.c ideviceconfiguration.c corecrt.h stdio.h stdint.h plist.h libimobiledevice.h lockdown.h mcinstall.h string.h stdlib.h assert.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                                                                                                                                                                                                                                                                                                                    �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p�                                                   �/ @    R�U���R���U���R���	U�	�
�R��
�
U�
��R���U���R���U���R���U���R���U���R���U���R���U���R���U���R�                                   �/ @    Q�T���Q���T���Q���	T�	�
�Q��
�
T�
��Q���T���Q���T���Q���T���Q���T���Q���T���Q�                         �/ @    � ����P��S�� ���� ���� ���� ���� ���� ����S��P��S�� ��                 �/ @    �	����P��	����	����	����	����	����P��P       �/ @    �0���0���S��0���	����0�                              �/ @    ]1�]�S��S��s���S��s���S��s���s���s���S��s���S��s���S��S��s���s���s~���S��S��S��S��S     �/ @    ]	����4���3���7�  �/ @   ]0�  �/ @   ]0�  �/ @   ]0�  �/ @   ]0� 	 �/ @   ]0� 
 �/ @   ]0�      �/ @    ]0���P����z          �/ @    ]0���P����z��P����z 
     �/ @    ]0���P��P    '4 @    ��z������Q       K4 @    PAU�
�
U        P4 @     0� -T-1Q12t�2<T�
�
T        5 @    	0�	7T7;Q;<t�<FT     �4 @    P�
�
P     �4 @    P�
�
P   +5 @   P           8 @    0��\��Q��|���\��\     �7 @    P��P     �7 @    P��P     �8 @    PvzP    [8 @    P>T    c8 @    
P
6U    �5 @    ��z������Q       �5 @    P)T��T     !: @    P"T     �5 @    P�
�
P      6 @    P�
�
P       _3 @    PSX       07 @    PSX         � @    R)S)-R-.�R�     � @    Q.�Q�         P @    R)S)-R-.�R�     P @    Q.�Q�         � @    R!S!(R(��R�       � @    PaUabP     � @    PXT         0 @    R/S/9s�9@s�@PS   F @   ;P      K @    
P
X%x�%0X            @    R�S���R���S���R�              @    QJRJN�NN��Q���R���Q�             @    e0�eyPy�U��P��0���U                 @     0� =TQUTU�\��t���0���\��t�          @    RS:P��S      @    0�+.P��0�      � @    !0�!LSpt0�        � @    P4FP]zP{�P         � @    "R"�V���R���V         � @    Q�]���Q���]         � @    "X"�\���X���\           � @    P�S��S��P��S          @    PlT{�T��T��T           @    PTU��U��P��U     " @    	0�	4^     + @    
P&+P    U @    
0�
&P   U @   ��\'  �   U @   ��P'  � 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
@Z @   ���
 Z @   ���
�Z @   ���
hZ @   ���
�Z @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ����     ��U  ��2�  ����     ��U  ��1�  ����     ��U  ��1�  ����     ��U  �	�	4�  �	�	��     �	�	U  �	�	4�  �	�	��     �	�	U  �	�	8�  �	�	��     �	�	U  �	�	8�  �	�	��     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
�T�     �
�
T  �
�
4�  �
�
�T�     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � T                RQ�R�        QX�Q�        X�`�X� )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              X         Z���� ���� ���� ���� ���� ���� ���� k        0 @    & � @    Yp� z3 @    
�� �3 @    ��� A4 @    '9<A �4 @    ��
� �4 @    !�
�
 �4 @    !�
�
 �4 @    EHM \5 @    ����	�	�� �5 @    !�
�
 6 @    !�
�
 �7 @    ����� �7 @    !�� �7 @    !�� 8 @    !����� 8 @    � P @   �	�/ @   � S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����                                                                                                                                                                                                                                                             .file   a   ��  gcrtexe.c              �                                �              �   p
                        �   �
                        �   �
                        %   
                        @  `
                        `             k  @                        �                           �  �                        �  `                        �  0          �  p                    envp           argv            argc    (                          0                        '  �          9  �
                        ^  �
                        �             �  0
                        �  P                        �  �
                          @
                    mainret            "  �
                        8  �
                        N                          d                           z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  �     +                 .file     ��  g    	                m  P                           y  �          �  �          �  P          �            �  0          �  �      main    �      .text   P     �  Q             .data                            .bss    0                        .rdata         t	  	             .xdata  t      T                 .pdata  l      T                    �  �     �  �                 �  �                           �  �                          �  �&  
   T.  �                �  �                         �  �       A                 �  0      @                    �  \      o                     9     �                          �                       �     �                    )  �     +                     4  P                     .text    	      .idata$78
      .idata$5�      .idata$4x      .idata$6�      .text   (	      .idata$74
      .idata$5x      .idata$4p      .idata$6�      .text   0	      .idata$70
      .idata$5p      .idata$4h      .idata$6�      .text   8	      .idata$7,
      .idata$5h      .idata$4`      .idata$6p      .text   @	      .idata$7(
      .idata$5`      .idata$4X      .idata$6X      .text   H	      .idata$7$
      .idata$5X      .idata$4P      .idata$6D      .text   P	      .idata$7 
      .idata$5P      .idata$4H      .idata$60      .text   X	      .idata$7
      .idata$5H      .idata$4@      .idata$6      .text   `	      .idata$7
      .idata$5@      .idata$48      .idata$6�      .text   h	      .idata$7
      .idata$58      .idata$40      .idata$6�      .text   p	      .idata$7
      .idata$50      .idata$4(      .idata$6�      .text   x	      .idata$7
      .idata$5(      .idata$4       .idata$6�      .text   �	      .idata$7
      .idata$5       .idata$4      .idata$6�      .text   �	      .idata$7
      .idata$5      .idata$4      .idata$6`      .text   �	      .idata$7 
      .idata$5      .idata$4      .idata$6H      .text   �	      .idata$7�      .idata$5      .idata$4       .idata$6,      .text   �	      .idata$7�      .idata$5       .idata$4�       .idata$6      .text   �	      .idata$7�      .idata$5�      .idata$4�       .idata$6       .text   �	      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �	      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �	      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �	      .idata$7|      .idata$5�      .idata$4�      .idata$6�      .text   �	      .idata$7x      .idata$5�      .idata$4�      .idata$6�      .text   �	      .idata$7t      .idata$5�      .idata$4�      .idata$6|      .file   ?  ��  ggccmain.c                �	                       p.0                 2   
          D  �                    __main  �
          a  0       .text   �	     �                .data                          .bss    0                       .xdata  �                       .pdata  �      $   	                 �  �T  
   a                   �  �	     ?                    �  �
     5                     �  p      0                      �     '                     R     �                     )       +                     4  p     �                .file   U  ��  gnatstart.c        .text   �
                       .data                          .bss    @                           �  L[  
     
                 �  �
     �                     �  �                                  V   
                   �                            :                         )  @     +                 .file   i  ��  gwildcard.c        .text   �
                       .data                           .bss    P                            �  Na  
   �                    �  �     .                     �  �                             Z     :                      H     �                     )  p     +                 .file   �  ��  gdllargv.c         _setargv�
                       .text   �
                      .data   0                        .bss    P                        .xdata  �                       .pdata  �                          �  �a  
   �                   �  �     :                     �  �      0                      �     V                      �     �                     )  �     +                     4  �     0                .file   �  ��  g_newmode.c        .text   �
                       .data   0                        .bss    P                           �  \c  
   �                    �  �     .                     �                              �     :                      �     �                     )  �     +                 .file   �  ��  gtlssup.c              m  �
                           |  �
          �  �                    __xd_a  P       __xd_z  X           �  �      .text   �
     �                .data   0                        .bss    `                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �	     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  �c  
   �  6                 �        �                    �                          �  0     0                      $                          �                            "     �                     )        +                     4  (     �                .file   �  ��  gxncommod.c        .text   �                       .data   0                        .bss    p                           �  �k  
   �                    �       .                     �  `                            :     :                           �                     )  0     +                 .file     ��  gcinitexe.c        .text   �                       .data   0                        .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  7l  
   {                   �  5     a                     �  �                            t     :                      �     �                     )  `     +                 .file   !  ��  gmerr.c            _matherr�                       .text   �     �                .data   0                        .bss    �                        .rdata  �	     @               .xdata                        .pdata                            �  �m  
   6  
                 �  �                         �  +     �                    �  �     0                      �     �                      N	     �                     )  �     +                     4  �     X                .file   >  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   0                        .bss    �                        .xdata  ,                      .pdata  ,                         �  �p  
   �                    �  �     -                     �  �     0                      l     X                      
     �                     )  �     +                     4  (     0                .file   R  ��  gmingw_helpers.    .text   �                       .data   0                        .bss    �                           �  q  
   �                    �  �     .                     �                               �     :                      �
     �                     )  �     +                 .file     ��  gpseudo-reloc.c        �  �                           �  
          �  �       the_secs�           �  �          �  �             
                        5   
                    .text   �     =  &             .data   0                        .bss    �                       .rdata        [                .xdata  0     0                 .pdata  8     $   	                 �  r  
   K  �                 �  �     �                    �  �     �  
                 �        0                    �  �     W                       �     �                          	                       X     O                    )        +                     4  X     �                .file   �  ��  gusermatherr.c         b  �                           x  �           �         .text   �     L                .data   0                        .bss    �                       .xdata  `                      .pdata  \                         �  \�  
   �                   �  �                         �  8     r                     �  P     0                      ~     �                      �     �                     )  P     +                     4  8     P                .file   �  ��  gxtxtmode.c        .text   0                       .data   0                        .bss    �                           �  Q�  
   �                    �  �     .                     �  �                            ,     :                      q
     �                     )  �     +                 .file   �  ��  gcrt_handler.c         �  0                       .text   0     �               .data   0                        .bss    �                       .xdata  l                      .rdata  �     (   
             .pdata  t                         �  ٌ  
   �                   �       ~                    �  �     _                    �  �     0                      f     �  
                                                                        )  �     +                     4  �     P                .file   �  ��  gtlsthrd.c             �  �                           �             �  �           �  `          
  �              �          @  `      .text   �     b  "             .data   0                        .bss    �      H                 .xdata  t     0                 .pdata  �     0                    �  ��  
   �
  A                 �  �     a                    �  	     �                    �  �     0                    �  "                            �     x                     !     %                    )  �     +                     4  �     (               .file     ��  gtlsmcrt.c         .text   `                       .data   0                       .bss    @                           �  ��  
   �                    �  �     .                     �                               k      :                      F     �                     )       +                 .file   #  ��  g    T            .text   `                       .data   @                        .bss    @                          �  �  
   �                    �       0                     �                               �      :                      �     �                     )  @     +                 .file   [  ��  gpesect.c              h  `                           {  �          �  �          �  �          �             �  @          �  �          �               �      .text   `     �  	             .data   @                        .bss    P                       .xdata  �     ,                 .pdata  �     l                    �  �  
   �  �                 �  K     �                    �  �     �                    �  @     0                    �  9     �                       �      K                          T                       �     �                     )  p     +                     4        (               .text   `     2                 .data   @                        .bss    P                       .text   �                       .data   @                        .bss    P                           )  �     +                 .file   o  ��  gmingw_matherr.    .text   �                       .data   @                       .bss    p                           �  ��  
   �                    �  �     .                     �  p                            *&     :                      �     �                     )  �     +                 .file   �  ��  gucrt_vfprintf.    vfprintf�                       .text   �                     .data   P                      .bss    p                       .xdata  �                      .pdata                           �  V�  
   �                   �       8                    �  �     X                     �  �     0                      d&     r   	                   1     �                     )        +                     4  (	     8                .file   �  ��  gucrt_printf.c     printf  �                       .text   �     H                .data   `                      .bss    p                       .xdata  �                      .pdata  (                         �  ��  
   �  
                 �  ;     l                    �  $     -                     �  �     0                      �&     �   	                        �                     )  0     +                     4  `	     H                .file   �  ��  gucrt_fprintf.c    fprintf                        .text        2                .data   p                      .bss    p                       .xdata  �                      .pdata  4                         �  ��  
   �                   �  �     b                    �  Q     F                     �  �     0                      k'     �   	                   �     �                     )  `     +                     4  �	     8                .file   �  ��  g__initenv.c           6  p          F  x      .text   P                       .data   �                      .bss    p                          �  H�  
   �                   �  	!     �                     �                               �'     [                      �                         )  �     +                 .file   .  ��  gucrtbase_compa        U  P                           h  `          v  �      _onexit @          �  `          �  �
                        �  �          �  �      tzset   �          �  P
                    _tzset  0          �  �           �  �           	  �           	  �           /	  �       .text   P       "             .data   �      x   
             .bss    �                       .xdata  �     P                 .pdata  @     l                .rdata  �                          �  ��  
   �  Y                 �  �!                          �  �     |                    �  @     0                      P(     �                     p                            �     `                    )  �     +                     4  �	     �               .text   p      .data         .bss    �      .idata$7D      .idata$5�      .idata$4�      .idata$6J      .text   x      .data         .bss    �      .idata$7H      .idata$5�      .idata$4�      .idata$6X      .text   �      .data         .bss    �      .idata$7L      .idata$5�      .idata$4�      .idata$6f      .text   �      .data         .bss    �      .idata$7P      .idata$5�      .idata$4�      .idata$6r      .file   <  ��  gfake              hname   �      fthunk  �      .text   �                       .data                          .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   m  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7T                      .text   �      .data         .bss    �      .idata$7      .idata$5p      .idata$4h      .idata$6      .text   �      .data         .bss    �      .idata$7      .idata$5x      .idata$4p      .idata$6"      .text   �      .data         .bss    �      .idata$7      .idata$5�      .idata$4x      .idata$6,      .text   �      .data         .bss    �      .idata$7      .idata$5�      .idata$4�      .idata$66      .text   �      .data         .bss    �      .idata$7      .idata$5�      .idata$4�      .idata$6@      .file   {  ��  gfake              hname   h      fthunk  p      .text   �                       .data                          .bss    �                       .idata$2�                      .idata$4h      .idata$5p      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7      !                 .text   �      .data         .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6n      .text   �      .data         .bss    �      .idata$7�      .idata$5      .idata$4       .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5      .idata$4      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5      .idata$4      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5       .idata$4      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5(      .idata$4       .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$50      .idata$4(      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$58      .idata$40      .idata$6�      .text          .data         .bss    �      .idata$7�      .idata$5@      .idata$48      .idata$6�      .text         .data         .bss    �      .idata$7�      .idata$5H      .idata$4@      .idata$6�      .text         .data         .bss    �      .idata$7�      .idata$5P      .idata$4H      .idata$6�      .text         .data         .bss    �      .idata$7�      .idata$5X      .idata$4P      .idata$6      .text          .data         .bss    �      .idata$7�      .idata$5`      .idata$4X      .idata$6      .file   �  ��  gfake              hname   �      fthunk         .text   0                       .data                          .bss    �                       .idata$2�                      .idata$4�      .idata$5       .file   �  ��  gfake              .text   0                       .data                          .bss    �                       .idata$4`                      .idata$5h                      .idata$7�                       .text   0      .data         .bss    �      .idata$7D      .idata$5X      .idata$4P      .idata$6"
      .text   8      .data         .bss    �      .idata$7H      .idata$5`      .idata$4X      .idata$60
      .text   @      .data         .bss    �      .idata$7L      .idata$5h      .idata$4`      .idata$6>
      .text   H      .data         .bss    �      .idata$7P      .idata$5p      .idata$4h      .idata$6L
      .text   P      .data         .bss    �      .idata$7T      .idata$5x      .idata$4p      .idata$6V
      .text   X      .data         .bss    �      .idata$7X      .idata$5�      .idata$4x      .idata$6`
      .text   `      .data         .bss    �      .idata$7\      .idata$5�      .idata$4�      .idata$6z
      .text   h      .data         .bss    �      .idata$7`      .idata$5�      .idata$4�      .idata$6�
      .text   p      .data         .bss    �      .idata$7d      .idata$5�      .idata$4�      .idata$6�
      .text   x      .data         .bss    �      .idata$7h      .idata$5�      .idata$4�      .idata$6�
      .text   �      .data         .bss    �      .idata$7l      .idata$5�      .idata$4�      .idata$6�
      .text   �      .data         .bss    �      .idata$7p      .idata$5�      .idata$4�      .idata$6�
      .text   �      .data         .bss    �      .idata$7t      .idata$5�      .idata$4�      .idata$6�
      .text   �      .data         .bss    �      .idata$7x      .idata$5�      .idata$4�      .idata$6
      .text   �      .data         .bss    �      .idata$7|      .idata$5�      .idata$4�      .idata$6      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6&      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6H      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6P      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6X      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6b      .file   �  ��  gfake              hname   P      fthunk  X      .text   �                       .data                          .bss    �                       .idata$2x                      .idata$4P      .idata$5X      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7�     "                 .text   �      .data         .bss    �      .idata$7      .idata$58      .idata$40      .idata$6�	      .text   �      .data         .bss    �      .idata$7      .idata$5@      .idata$48      .idata$6
      .text   �      .data         .bss    �      .idata$7      .idata$5H      .idata$4@      .idata$6
      .file   �  ��  gfake              hname   0      fthunk  8      .text   �                       .data                          .bss    �                       .idata$2d                      .idata$40      .idata$58      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4H                      .idata$5P                      .idata$7      "                 .text   �      .data         .bss    �      .idata$7�
      .idata$5(      .idata$4       .idata$6�	      .file   �  ��  gfake              hname          fthunk  (      .text                           .data                          .bss    �                       .idata$2P                      .idata$4       .idata$5(      .file     ��  gfake              .text                           .data                          .bss    �                       .idata$4(                      .idata$50                      .idata$7�
                      .text          .data         .bss    �      .idata$7�
      .idata$5       .idata$4�      .idata$6�	      .text         .data         .bss    �      .idata$7�
      .idata$5      .idata$4       .idata$6�	      .text         .data         .bss    �      .idata$7�
      .idata$5      .idata$4      .idata$6�	      .text         .data         .bss    �      .idata$7�
      .idata$5      .idata$4      .idata$6�	      .file   &  ��  gfake              hname   �      fthunk         .text                           .data                          .bss    �                       .idata$2<                      .idata$4�      .idata$5       .file   B  ��  gfake              .text                           .data                          .bss    �                       .idata$4                      .idata$5                       .idata$7�
                      .text          .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6�	      .text   (      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6�	      .file   P  ��  gfake              hname   �      fthunk  �      .text   0                       .data                          .bss    �                       .idata$2(                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   0                       .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7�
     &                 .text   0      .data         .bss    �      .idata$7|
      .idata$5�      .idata$4�      .idata$6�	      .text   8      .data         .bss    �      .idata$7x
      .idata$5�      .idata$4�      .idata$6t	      .text   @      .data         .bss    �      .idata$7t
      .idata$5�      .idata$4�      .idata$6f	      .text   H      .data         .bss    �      .idata$7p
      .idata$5�      .idata$4�      .idata$6^	      .text   P      .data         .bss    �      .idata$7l
      .idata$5�      .idata$4�      .idata$6@	      .text   X      .data         .bss    �      .idata$7h
      .idata$5�      .idata$4�      .idata$6(	      .text   `      .data         .bss    �      .idata$7d
      .idata$5�      .idata$4�      .idata$6	      .text   h      .data         .bss    �      .idata$7`
      .idata$5�      .idata$4�      .idata$6�      .text   p      .data         .bss    �      .idata$7\
      .idata$5�      .idata$4�      .idata$6�      .text   x      .data         .bss    �      .idata$7X
      .idata$5�      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   �                       .data                          .bss    �                       .idata$2                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7�
     
                 .file   �  ��  gcygming-crtend        ?	  `+                       .text   �                       .data                          .bss    �                           �  `+                         �  <                          �  �                         S	  x+                         )  �     +                 .idata$2        .idata$5�      .idata$4�       .idata$2�       .idata$5�      .idata$4�      .idata$4�      .idata$5�      .idata$7<
      .idata$4�      .idata$5�      .idata$7�      .rsrc       
    __xc_z             `	  `	          v	             �	  p          �	  �          �	  �          �	  �
          �	  �          �	              
  �+          
  �           !
  8	          6
  �          M
             l
  d           �
  P          �
  p
      strerror�          �
  �	          �
            �
              X             �           -  @          I      	        X  �
          w  �          �  �       __xl_a  0           �  h          �  �          �             �         _cexit  P            `  ��       &     ��       ?  T          m              �  �          �      ��       �     ��       �  0           �  �      __xl_d  @           �  �          
  �	      _tls_end   	        /
  �          E
  �      __tzname�          c
  0          p
  `          �
  (	          �
             �
  �           �
  0       fgets   �          �
  �
          �
  �          �
  @
            P                	    memcpy  �          (            D  0	          S  �          i  `      puts               z   
          �  �	          �  �           �  �	          �   	      malloc        _CRT_MT 0           �  @      _assert H          	  x                        $  P	          6  H	          E  8          `             �  p          �  �          �     ��       �            �  H          �  �          �  0            �          0  h           J            U  �          p  �           �  �
          �             �  H          �  `
          �  `             8            (           R  �          _  P           q  �          �  �          �  �
      abort   �          �  
          �  �              P            �	          #  P           3  0      __dll__     ��       Q      ��       f  8          q  <
          �             �  p          �  @           �  p          �  `          
                          '  �          7  P           c  �          o     ��       �         strrchr �          �         calloc            �  �	          �             �  �             @                       ;  p	          S  �      Sleep   H          b  p      _commodep           s  �	          �  p          �        fseek             �  X          �  p+          �             �  <           �  �                        -  �      __xi_z  (           9  �	          Q             i             y  �
          �  X	          �  �          �  �
          �  �          �  l       signal  �            �            H           )  �          <              N  �	      strncmp �          d  8          �  p+          �  0
          �  �          �  �           �  �            h	            �
          :      ��   strtok  �          M   
          a            �  �          �  h          �  �          �            �  P            �             �	          @  �      fread              Q     ��       d  �      strdup  �          �  �          �  (          �  �          �  h          �  `          �  �      fopen   �          �  �           #              B  P           Q     ��       f          ftell             u  �           �  �          �  �       fclose  �          �  �          �  x      __xl_z  H       __end__              �             �  �                   strcmp  �            �+          +  �      __xi_a             B  �          Q  �          ]  X      __xc_a              r  (          �     ��       �  P           �     ��   _fmode  �           �  �          �  �          �  �	            �            �          #             4  p           B  `          ^  `          s             �            �  �          �  �
          �  0          �  �          �  �	      __xl_c  8                	          P
          -  �          @  �          P  (          ]  d           v              �  �          �  �	          �  x          �  �
      _newmodeP             �      fwrite              �            @          )             ?      ��       W      ��       h  (          �  `          �  x	          �  @           �  �          �         exit    �          �     ��       �      ��         �          !  @	      _errno  x          7  0          C  �      _exit   �          \  `           i  @          u  �
          �  X      strlen  �          �            �  p          �  �          �  x          �  x            �          .            L  �          i  x           �             �  �           �  8          �  �          �  �
          �  �              P               �          8   H          D   �           S   @      free              _   X          t   �	          �   h          �   �       �   .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame SetLanguage SetLocale profile_read_from_file.constprop.0 print_usage.isra.0 str_split strip_copy Get_File_plist .text.startup .xdata.startup .pdata.startup ideviceconfiguration.c __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 mcinstall_EraseDevice ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone mcinstall_client_new libplist_2_0_dll_iname __imp_idevice_new_with_options _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force plist_array_append_item __imp_calloc __imp___p__fmode __imp___p___argc __imp_tzname __imp_mcinstall_EraseDevice ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __rt_psrelocs_start __imp_lockdownd_service_descriptor_free __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler plist_new_array .refptr.__CTOR_LIST__ __imp_plist_array_append_item VirtualQuery __imp___p___argv mcinstall_get_status_code ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll __imp__errno .refptr.__imp___initenv __imp_ftell _tls_start __imp_lockdownd_client_free mcinstall_copy __imp_plist_new_array .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ plist_new_string __mingw_oldexcpt_handler lockdownd_service_descriptor_free mcinstall_is_mdm TlsGetValue __imp_strcmp __bss_start__ mcinstall_Prepare mcinstall_Wifi __imp___C_specific_handler ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp__assert __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ __imp_mcinstall_GetCloudConfig .refptr.__mingw_app_type __mingw_initltssuo_force VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection __C_specific_handler .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf __imp_mcinstall_Prepare plist_free ___crt_xp_end__ __imp_lockdownd_start_service __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_strdup __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname _tls_used __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ lockdownd_start_service __imp_strerror .refptr._newmode plist_new_data __imp_mcinstall_copy __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ idevice_set_debug_level __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force mcinstall_GetCloudConfig __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func __native_startup_state __imp_plist_to_xml ___crt_xc_start__ lockdownd_client_free __imp_mcinstall_DeleteConfigs ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_plist_new_data mcinstall_DeleteConfigs .refptr.__mingw_initltsdyn_force __rt_psrelocs_size .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp___p___wargv __imp_strlen __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __imp_strtok __p__wenviron _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_plist_new_string _head_libplist_2_0_dll __IAT_start__ __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit __DTOR_LIST__ __imp_mcinstall_is_mdm __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf __imp_mcinstall_client_free _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_VirtualProtect idevice_free ___tls_end__ .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __imp_fclose __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_lockdownd_set_value ___chkstk_ms lockdownd_set_value __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ __minor_image_version__ __imp___set_app_type mcinstall_client_free __imp_fgets __imp__crt_at_quick_exit __imp_printf __imp_fread .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR __imp_mcinstall_get_status_code DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv __imp_fopen __imp_plist_free .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_fseek __imp_daylight __p___wargv __imp_mcinstall_Wifi plist_to_xml __imp_mcinstall_client_new __mingw_app_type 