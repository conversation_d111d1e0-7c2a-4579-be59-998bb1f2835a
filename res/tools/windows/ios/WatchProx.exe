MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� ���g � �  � & ( "   V     �        @                       �    ��  `                                             �  �
   �  �   `  �           �  �                           �S  (                   ��  x                          .text   H!      "                 `  `.data      @      (              @  �.rdata  P   P      *              @  @.pdata  �   `      :              @  @.xdata     p      >              @  @.bss    �   �                      �  �.idata  �
   �      B              @  �.CRT    `    �      P              @  �.tls        �      R              @  �.rsrc   �   �      T              @  �.reloc  �    �      Z              @  B/4      �   �      \              @  B/19     ��   �   �   b              @  B/31     3$   �  &   .             @  B/45     �"   �  $   T             @  B/57     �
         x             @  B/70     	   0     �             @  B/81        @     �             @  B/97     C   `     �             @  B/113    �   �     �             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H�UG  1��    H�VG  �    H�YG  �    H��F  f�8MZuHcP<HЁ8PE  tfH��F  �
�o  � ��tC�   �  �  H��G  ���l  H��G  ���4  H�=F  �8tP1�H��(Ð�   ��  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
aG  �<  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H�5G  L��n  H��n  H�
�n  � ��n  H��F  D�H��n  H�D$ �-  �H��8��    ATUWVSH�� H�/F  H�-`�  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5�E  1�����V  �����  �n     ����L  ���e  H�!E  H� H��tE1��   1����x	  H�
!F  ���  H�tE  H�
����H��M  �`  ��m  �{Hc�H��H���  L�%�m  H�Ņ��F  H��1�I��O  H�pH���c  I��H�D I�H��H���  H9�u�H�H�    H�-]m  ��  H�qD  L�Bm  �
Lm  H� L� H�7m  �  �
m  �m  ����   � m  ��ttH�� [^_]A\�f�     H�5�D  �   ���������   �  ��������H��D  H�
�D  �)  �   �������1�H�����f�     ��  ��l  H�� [^_]A\�f.�     H�iD  H�
RD  �   ��  �7���f�H�����������  �H��(H��C  �    ������H��(� H��(H�eC  �     �z�����H��(� H��(�g  H���H��(Ð�����������H�
	   �����@ Ð��������������H��(��H�
�;  �  ��k     H��(�ff.�     @ VSH��HH�D$(    H��L��H�T$(E1��  ����   H�L$(H��L�L$0I��H�D$0    ��  H�L$(����  ��t(���ucH��H�
�;  �  H�L$0��  �H��H[^�f�H�L$0H��t6H����  H��H���  ����   ��tu��tAH��H�
.;  ��  �H��H�
f;  �  ��    ��H�
�:  �  �H��H[^�f�H���p  L��:  H��H�
�:  ��H��:  LE��e  �L���H�T$8H��H�D$8    �  L�D$8H��H�
�:  �6  H�L$8�  �����    H�T$8H��H�D$8    ��   L�D$8H��H�
P:  ��  ������H��(H��H�ы��t/��uH��uH��(�H�P�  ��u���i      H��(�@ H��t�H�P�r  ��u���i     H��(Ð���������������%2}  ���%"}  ���%}  ���%}  ���%�|  ���%�|  ���%�|  ���%�|  ���%"  ���%  ���%  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ��H��(H��(  H� H��t"D  ��H��(  H�PH�@H��(  H��u�H��(�fD  VSH��(H��?  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^���� 1�fD  D�@��J�<� L��u��fD  ��h  ��t�D  �vh     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H��>  �8t�    ��t��tN�   H��([^�f�H��  H�5
�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�,<  Hc�H����    H�;  �DA �y�qH�q�   ��  �DD$0I��H��;  �|$(H��I���t$ �  �t$@|$P1�DD$`H��x[^ÐH��:  ��    H��:  ��    H��:  �s���@ H�	;  �c���@ H��:  �S���H�#;  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�  A�   �   H�
";  I���  H�t$(�   ��  H��H��I���
  �x  ��    WVSH��PHc5ff  H�˅��  H�Xf  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H�f  H��H��H�H�x �     �#  �WA�0   H�H��e  H�T$ H�L�/y  H���}   �D$D�P����t�P���u��e  H��P[^_� ��H�L$ H�T$8A�@   �   DD�Hue  H�KI��H�S��x  ��u���x  H�
C:  ���d���@ 1��!���H�:e  �WH�
�9  L�D�>���H��H�
�9  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%�d  E��tH�e[^_A\A]A^A_]�fD  ��d     �9	  H�H��H��   H����  L�-;  H�;  ��d      H)�H�D$0H��d  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5�:  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
�8  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  ��b  ������H�5Cv  1�H�}�D  H��b  H�D� E��t
H�PH�HI����A��H��(D;%�b  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5@8  �s�;H��L�>H���Z����>L9�r��������H�

7  �����H�
�6  ���������H��XH��a  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
ia  �  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H��6  Hc�H��� 1ҹ   �  H���>  H���  H�
a  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �D  H���@����   �   �+  �f�     1ҹ   �  H��t*H�������   ���i���f�     �   ���T����   �   ��
  �@����   �   ��
  �,����   �   �
  �����������ATUWVSH�� L�%�_  L����r  H��_  H��t6H�-�r  H�=�r  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%hr  WVSH�� �[_  ��H�օ�u
1�H�� [^_ú   �   �9
  H��H��t3H�pH�5>_  �8H���r  H�_  H��H�_  H�C� r  묃��멐VSH��(��^  �˅�u1�H��([^�D  H�5�^  H����q  H�
�^  H��t'1��H��H��tH���9�H�Au�H��tH�B�	  H����q  1�H��([^� H�q^  ��ff.�     @ SH�� ����   w0��tL�N^  ����   �<^     �   H�� [�f�     ��u�^  ��t��<�����f.�     �^  ��uf��]  ��u�H��]  H��t�    H��H�[��  H��u�H�
�]  H��]      ��]      ��p  �l����k����   H�� [������f�     H�
�]  �sp  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H����  H��w{H��2  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���f  ��u�H��H�� [^_��    1�H��H�� [^_� H�Y2  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H��1  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L��1  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H�1  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H��0  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L�I0  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������H��(i��  �(l  1�H��(Ð��������H��8E1�L�D$ I��H��1��  H��8Ð�VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H���  ���   ����  �[  � ��Z  H� H��  H� H�M��t	A�$��  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���P  ���   ����  ��  � ���  H� H��  H� H�M��t	A�$�s  1�H�� [^_]A\�fD  SH�� H����  ���    HD�H�� [�f�H��-  �8 t1�Ð�  ff.�     SH�� �˹   �  A��H��,  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H�-  ��~   H��  �j   H�s  �V   H�_  H��(�f.�     H��(H��,  ��>   H�G  �*   H�3  �   H�  H��(Ð����������%�j  ���%�j  ���%�j  ���     �%�j  ���%�j  ���%�j  ���     �%2j  ���%2j  ���%2j  ���%2j  ���%2j  ���%2j  ���%2j  ���     �%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���%bi  ���     �%�h  ���%�h  ���%�h  ���     �%bh  ���%bh  ���%bh  ���%bh  ���%*h  ���%*h  ���%
h  ���     �%�g  ���%�g  ���%�g  ���%�g  ���%�g  ���%�g  ���%�g  ���%zg  ���%jg  ���%Zg  ��ATUWVSH��`��H���,������=  H�5�����   H������H��   ����H�SH�
W��������H�
H$  �V���D�cS  E��t �[�     �   �����D�CS  E��u=�<S  ��t�H�[�   �h  H�:&  H��I���/����   ��H��`[^_]A\�H�SH�L$0A�   �9�������  H�L$0H�T$8E1��O����ƅ���  ��uH�KH��#  �P�������  H�
�$  ����H�L$8E1�H�T$@L�D$@�����H�L$8��������t'H�L$0������\���H�#  H�
'#  �#����?���H�L$@1�H�\$PL�d$XH�T$HH�T$HH�-3$  �����1�H�=9$  H�L$P�  1�L��H�D$X����H�T$XH�������H�T$XH�L$0I���6���H�T$XH�L$0L��#  � ���H�T$XH�L$0L��#  �
���H�T$XH�L$0L��#  �����H�T$XH�L$0L��#  �����H�T$XH�L$0L��#  �����H�T$XH�L$0L��#  ����H�T$XH�L$0L��#  ����H�T$XH�L$0L��#  ����H�T$XH�L$0L��#  �p���H�T$XH�L$0L��#  �Z���H�T$XH�L$0L��#  �D���H�T$XH�L$0L��#  �.���H�T$XH�L$0L��#  ����H�T$XH�L$0L��#  ����H�T$XH�L$0L��#  �����H�T$XH�L$0L��#  �����H�L$X�<���H�|$P t H�L$@H�T$HI���"���H�L$PH���@���H�L$H�
���H�L$@����������H�[�   �$e  H�!  H��I���:��������   �e  A�   �   H�
!  I�������H�L$0�>��������H�K����H�\$8f��O  f��u	f��O  ~���O  H�
�   f�D$X  �t���E1���O  H��L�L$ L��   L�L$X�������u8�T$XH�
�   �<���H�
�   �P����
�   ������VO  ��t������H�
�   �������   ����������������������������������������1 @           ��������                                                                                                                                                                                                @1 @           ��������        ����                           ����            �' @            ( @           P( @           p� @   x� @   �) @   0* @   �( @   �) @   ) @   �( @   �@ @   �@ @   �@ @      �p  �@ @   �@ @   PDT PST �) @   �) @                                                                                                                                                                                                                                                           
Caught signal %d. Exiting...
 true false       Failed to start watch service (%d)!
 %s: %llu
 %s: %s
 %s: Unsupported type
    Timeout trying to read value for key '%s'
      Error occurred while reading value for key '%s'
 COPYRIGHT PHONECHECK LLC 2025 %s - Ver 2.0
 Waiting for device... Could not find device %s
    Failed to start watch service.
 -f Remote Port: %d
 com.apple.os_trace_relay    Port forwarding successful. Forwarded port: %d
 Port forwarding active. Press Ctrl+C to exit... Port forwarding failed with error: %d
  Retrieving data from watch ... Checking watch: %s
 ProductType SerialNumber BatteryCurrentCapacity BatteryIsCharging AmountDataAvailable HardwareModel DeviceName BluetoothAddress RegionInfo UniqueDeviceID ModelNumber ProductName WiFiAddress ProductVersion DeviceColor DeviceEnclosureColor TotalDataCapacity      Device %s not connected. Exiting...
             @                            � @   � @   |� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ����L�������l���|�������\���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
       ��� ��� ��� ��� ������� �����������{���        runtime error %d
               0@ @           @@ @            1 @              @           P^ @           P^ @           �S @           �@ @           �� @           �� @           x� @           t� @           p� @            � @           Ѐ @           P� @           X� @            � @           � @           � @           (� @           �� @            @ @           �� @           P @           � @           `� @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                                                                                                                                                                                            p    .  p  0  y  p  �  �  p  �  �  $p  �  
  Dp    $  dp  0  <  lp  @  A  pp  P  q  tp  �    |p    q  �p     :  �p  @  �  �p  �  �  �p  �  �  �p  �    �p    �  �p  �  �  �p  �  �  �p  �  �  �p  �  )  �p  0  �  �p  �  �  q     >   q  @  L  (q  P  
!  ,q  !  �!  4q  �!  �!  Dq  �!  q"  Pq  �"  r#  \q  �#  �#  dq  �#   $  hq   $  �$  lq  �$   %  xq   %  W%  |q  `%  �%  �q  �%  &  �q   &  �&  �q  �&  v'  �q  �'  �'  �q  �'  �'  �q   (  H(  �q  P(  �(  �q  �(  �(  �q  �(  
)  �q  )  z)  �q  �)  �)  �q  �)  �)  �q  �)  �)  �q  �)  %*  �q  0*  f*  �q  p*  �*  �q  p,  1  �p  1  1  r                                                                                                                                                                                                                                                                                                                                                                                  B   b  
 
20`pP�	 B  �+     �  �  P  �  	 B  �+     �    P     B         B   �0`   B  
 
�0`pP� B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   B   b   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             �          0�  ��  P�          t�  ȓ  ��          ��   �  ��          ��  0�  Б          �  H�  ��          �  p�  �          8�  ��   �          ��  ��  ��          ��  (�  �          �  h�  �          @�  ��  8�          ��  ��                          ��      �      @�      h�      ��      Ė      ��      �              �      $�      <�      L�      h�      ��      ��      ��      ��      Ɨ              ֗              ޗ      �              ��      �      �       �              *�              >�      V�              `�      n�      |�      ��      ��      ��      Ƙ      ܘ      �      �      �      4�      @�      P�      r�      z�      ��              ��      ��      ��      ��      ֙      �      ��              �      �      �              "�      0�      >�      J�              T�      l�      ��      ��      ��      Ț      ��      ��              ��      �      @�      h�      ��      Ė      ��      �              �      $�      <�      L�      h�      ��      ��      ��      ��      Ɨ              ֗              ޗ      �              ��      �      �       �              *�              >�      V�              `�      n�      |�      ��      ��      ��      Ƙ      ܘ      �      �      �      4�      @�      P�      r�      z�      ��              ��      ��      ��      ��      ֙      �      ��              �      �      �              "�      0�      >�      J�              T�      l�      ��      ��      ��      Ț      ��      ��                companion_proxy_client_free   " companion_proxy_client_start_service  # companion_proxy_get_device_registry   $ companion_proxy_get_value_from_registry   ' companion_proxy_start_forwarding_service_port b idevice_event_subscribe   f idevice_free  l idevice_new_with_options  DeleteCriticalSection =EnterCriticalSection  tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery  W atoi   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf  � fwrite  � puts  � strcmp  � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset  	 plist_array_new_iter  
 plist_array_next_item 
 plist_bool_val_is_true     plist_dict_get_item    plist_free    ( plist_get_node_type   , plist_get_string_val  . plist_get_uint_val     �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  �  �  �  �  �  �  �  �  KERNEL32.dll    (�  api-ms-win-crt-convert-l1-1-0.dll   <�  <�  api-ms-win-crt-environment-l1-1-0.dll   P�  P�  P�  P�  api-ms-win-crt-heap-l1-1-0.dll  d�  api-ms-win-crt-math-l1-1-0.dll  x�  x�  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  ��  api-ms-win-crt-string-l1-1-0.dll    Ȑ  Ȑ  Ȑ  Ȑ  api-ms-win-crt-time-l1-1-0.dll  ܐ  ܐ  ܐ  ܐ  ܐ  ܐ  ܐ  ܐ  libplist-2.0.dll                                                                                                                        0 @                    @                    @   � @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          0     (�   @  0    �P�`�p�������������������ȠР���� �   P  H   ����������Ц�� �� �0�@�P�`�p�����������Ч�� �� �0�@�P�`�p� �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                            ,               @   $                      <    �&       P @   !      p, @   �                      ,    F>         @   �                           �D                           �J                       ,    6K       � @                              �L                       ,    AM       � @   �                           U                           �U                       ,    
W       � @   �                       ,    CZ       � @                              �Z                       ,    l[       � @   =                      ,    �r         @   L                           �u                       ,    4v       P @   �                      ,    �       ! @   b                          �                           q�                       ,    A�       �# @   �                          �                       ,    ��       �' @                          ,    ��       �' @                          ,     �        ( @   H                       ,    Ӱ       P( @   2                           u�                       ,    �       �( @                                                                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   �   �  #GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden   �  �           9  char {   long long unsigned int long long int short unsigned int int long int unsigned int $_iobuf !
  _Placeholder #    %FILE /�   unsigned char double float long double sig_atomic_t �   &U  __p_sig_fn_t 0�  �  �  �    long unsigned int {   signed char short int uint16_t &�   uint64_t *0�   plist_t Y  plist_array_iter c  �   i�  PLIST_BOOLEAN  PLIST_UINT PLIST_REAL PLIST_STRING PLIST_ARRAY PLIST_DICT PLIST_DATE PLIST_DATA PLIST_KEY PLIST_UID 	PLIST_NULL 
PLIST_NONE  plist_type v  �   '�  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 0�    2 �    idevice_t 3�  �  idevice_options �   9J  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  idevice_connection_type �   @�  CONNECTION_USBMUXD CONNECTION_NETWORK  idevice_event_type �   P�  IDEVICE_DEVICE_ADD IDEVICE_DEVICE_REMOVE IDEVICE_DEVICE_PAIRED  'X	4  event Y�   udid Z4  conn_type [J   �   4  idevice_event_t \�  >  idevice_event_cb_t `v  {  �  �     V  �   %$  COMPANION_PROXY_E_SUCCESS  COMPANION_PROXY_E_INVALID_ARG COMPANION_PROXY_E_PLIST_ERROR ~COMPANION_PROXY_E_MUX_ERROR }COMPANION_PROXY_E_SSL_ERROR |COMPANION_PROXY_E_NOT_ENOUGH_DATA {COMPANION_PROXY_E_TIMEOUT zCOMPANION_PROXY_E_OP_IN_PROGRESS yCOMPANION_PROXY_E_NO_DEVICES �COMPANION_PROXY_E_UNSUPPORTED_KEY �COMPANION_PROXY_E_TIMEOUT_REPLY �COMPANION_PROXY_E_UNKNOWN_ERROR �~ companion_proxy_error_t 2�  /  4/P  /  companion_proxy_client_t 5)v  D  	quit_flag 
j  	8� @   	connected �   	4� @   (remote_port 
�  	0� @   
plist_bool_val_is_true 3	�   �  �   plist_get_uint_val 
   �      �  
plist_get_node_type ��  L  �   
plist_dict_get_item �
�  x  �  4   
companion_proxy_get_value_from_registry �$  �  U  4  4  �   �  )exit � �  �    
companion_proxy_start_forwarding_service_port �$  .	  U  �  4  .	  �   �  *plist_free �
L	  �   free `	     plist_get_string_val �
�	  �  �	   �  plist_array_next_item R
�	  �  �  �   plist_array_new_iter G
�	  �  �	   �  
companion_proxy_client_free `$  
  U   
companion_proxy_get_device_registry �$  T
  U  �   
atoi ��   l
  4   
strcmp 	?�   �
  4  4   
idevice_free ��  �
  �   
companion_proxy_client_start_service U$  �
  �  �
  4   U  
idevice_new_with_options ��  $  $  4  �   �  
fprintf ��   J  O  9     J  
__acrt_iob_func ]J  v  �    
sleep 
.�   �  �    
idevice_event_subscribe ��  �  [     
signal <o  �  �   o   
printf ��   �  4   +main f�   p, @   �      ��  argc f�   �  �  argv f�	  B  ,  	device ��  ��	cpx �U  ��y   �  	devices ��  ��cerr �$  �  �  �- @         �  	iter ��  ��	value ��  �@�   >  val �
�  �  �  	watch_udid ��  �H+. @   `	  
  Q|  8. @   �  (
  Rv  J. @   <  @
  Xu  `. @   <  _
  X	WR @    v. @   <  ~
  X	dR @    �. @   <  �
  X	{R @    �. @   <  �
  X	�R @    �. @   <  �
  X	�R @    �. @   <  �
  X	�R @    �. @   <    X	�R @    �. @   <  8  X	�R @    / @   <  W  X	�R @    &/ @   <  v  X	�R @    </ @   <  �  X	�R @    R/ @   <  �  X	�R @    h/ @   <  �  X		S @    ~/ @   <  �  X	S @    �/ @   <    X	$S @    �/ @   <  0  X	9S @    �/ @   L	   	. @   �	  W  Q�� �/ @   �	  o  Xs  �/ @   L	  �/ @   3	   �- @   q  �  R	R @    �- @   
  �  Q�� �- @   �	   �  d0 @   d0 @   �       �	�    �  �  ,  �H -  �  �  �0 @   �  5  R	cQ @    �0 @   �  f  Rs X	tQ @   Y�Hw 0 �0 @   �  �  R	�Q @    �0 @   q  �  R	�Q @    �0 @   v  �  R1 �0 @   �  �  R	�Q @    1 @   �  R1  �, @   �  �, @   �    R2Qt  �, @   �  5  R?Qt  �, @   �  T  R	 @    �, @   q  s  R	
Q @    �, @   v  �  R1 - @   T  �  R2 !- @   )  �  Q	PS @   Xs  G- @   �
  �  R��X6 a- @   �
    Q��X0 �- @   l
  !  Q	`Q @    �- @   �
  �- @   �  Z  R	�P @   Q	�P @    0 @   T  q  R2 0 @   )  �  Q	#Q @   Xs  &0 @   T  �  R2 @0 @   �  �  R	@Q @   Q1XO J0 @   �
  X0 @   T
   !handle_port_forwarding P<  cpx P=U  forward_port U�  result X$   -get_value_from_watch "
� @   �      �)  device ",�    �  udid "@4  #    key "R4  ]  Q  	cpx #U  �Hcerr $$  �  �  	val *
�  �Ph   a  node /�  �  �  � @   /       [  	u64val 2�  �X� @   �  9  Rt Q�X 
 @   �  R	UP @   Qs   � @   @       �  	strval 8�  �X� @   `	  �  Rt Q�X � @   �  �  R	_P @   Qs  � @   L	    @   L  �  Qs   @   %    Rt  > @   �  '  R	gP @   Qs  x @   �  ?  Rt  � @   �  R	_P @   Qs   � @   �
  �  R�RQ�HX0 � @   x  �  Qt Xs Y�P � @   �	  � @   �  �  R	�P @   Qs  � @   3	  O @   �  
  R	�P @   Qs  g @   �  R	0P @     !device_event_cb j  event 4�  user_data A  udid �   .handle_sigint P @   !       ��  sig �   �  �  b @   �  R	 P @   Q�R  /)   @   a       �q  ?    
  L  \  N   ]  �  �  )  0 @    0 @          
[  0?  L  �  �  1]  9 @   l
  R�Q  ^ @   l
  R�Q  "puts __builtin_puts 2__main __main "fwrite __builtin_fwrite  ]   l  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 @  (    @   �       "  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	@� @   atexit ��   �  Y   __main 5� @          ��  � @   �   	__do_global_ctors  @ @   j       �  
nptrs "�     �  
i #�       � @   j  R	  @     	__do_global_dtors   @   :       �[  p [  	 @ @    	   �   �	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 (    I	  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
N  �   �,  __uninitialized  __initializing __initialized  N  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	@ @   �  	@ @   =  
"	X� @   [  	P� @    �    a
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 6    �	  _dowildcard  �   	 @ @   int  }   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  � @          �	  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   � @          � �    �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 q  Y  /
  _newmode �   	`� @   int  �   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  � @   �       i
  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	|� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�S @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�S @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	x� @   __mingw_initltsdyn_force ��   	t� @   __mingw_initltssuo_force ��   	p� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@  � @   /       �}  
f  �  6  2  
|  *M  H  D  
q  ;d  Z  V   @   �   __tlregdtor m�   � @          ��  func m  R __dyn_tls_init L@  	  f  �  |  *M  q  ;d  pfunc N
$  ps O
�    �   @   �       ��  p  h  �  �  �  �  �  �  �  �  �  @ @    @ @   +       L�  �  �  �  �  �  �  �    �  �      �  (  $   � @   �    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �    _commode �   	�� @   int  w   
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   m
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 <	  $	  � @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   � @   �       �0  pexcept 0  O  I  type 
�  q  e  
 @   b  �  R2 6 @   7  Q	�T @   Xs Yt w �ww(�ww0�w  5   �    r  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  � @          �  _fpreset 	� @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  	
  __mingw_app_type �   	�� @   int  G   �  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 .  d  � @   =      C
  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �� @   ]      ��  4was_init �%  	�� @   5mSecs �%  �  �  !�  % @   �   �4  �  �  �  6�   
�  �  �  
�  W  '  
�  n  V  
�  �  �  

  	  	  
  P	  @	  "E  �   �  
F  �	  �	  
[  �	  �	  9 @   `  R	(V @   Xu w t     � @   � @          �;  �  �	  �	  �  

  
  �  
  
    � @   � @          �  #
  !
  �  .
  ,
  �  =
  ;
  � @   �  Ru    !  � @   �   ��  �  G
  E
  �  R
  P
  �  a
  _
  7  � @   �   �  k
  i
  �  v
  t
  �  �
  �
  � @   �  Ru      G @   G @   
       �w  �  �
  �
  �  �
  �
  �  �
  �
    G @   G @   
       �  �
  �
  �  �
  �
  �  �
  �
  O @   �  Ru      ` @   ` @          �   �  �
  �
  �  �
  �
  �  �
  �
    ` @   ` @          �  �
  �
  �      �      h @   �  Ru    "$  �   �  
)  #    83    
4  =  ;    � @   � @   
       s�  G  E  �  R  P  �  a  _    � @   � @   
       �  k  i  �  v  t  �  �  �  � @   �  Rt      
� @   `    R	�U @    � @   `  R	�U @      9�  � @   X       �|  
�  �  �  :�  ��� @   
  Yu   � @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �0 @   b      �`  &addr ��  �  �  b �:  ��h �g  �  �  i �%      > @   P       �  new_protect �
u  3  1  
D @   
  �  Ys  N @    
  \ @   `  R	�U @     
� @   �
  �  Rs  � @   n
  
� @   E
    Q��X0 
� @   `  >  R	`U @    � @   `  R	@U @   Qs   ?__report_error T� @   i       �/  &msg T  ?  ;  @argp ��   �X
� @     �  R2 
 @   /  �  R	 U @   Q1XK 
 @       R2 
# @   �
  !  Qs Xt  ) @   �
   Afwrite __builtin_fwrite   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  }    @   L       �  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr �@ @          �P  f ,�  Z  V  L @   �  R�R  __mingw_raise_matherr �  @   >       �typ !�   n  h  name 2�  �  �  a1 ?w   �  �  a2 Jw   �  �  rslt 
w   � ex 0  �@9 @   R�@   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 _
  G
  q  _fmode �   	�� @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  P @   �      �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	Ѐ @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   P @   �      ��  'exception_data �-�  �  �  old_handler �
	  
  
  action ��   l
  R
  reset_fpu ��   �
  �
  
� @   �
  �  R8Q0 (� @   �  R�R 
  @   �
  �  R4Q0   @   �  R4 
l  @   �
    R8Q0 
�  @   �
  7  R8Q1 
�  @   �
  S  R;Q0 �  @   f  R; �  @   y  R8 
�  @   �
  �  R;Q1 
�  @   �
  �  R4Q1 
! @   �
  �  R8Q1 )! @   �
   �	   �
   c  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  ! @   b      8  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	�� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �" @   �       �n  	hDllHandle z�  ?  '  	reason {<  �  �  	reserved |S  =  %   �" @   K       �  
keyp �&�  �  �  
t �-�  �  �  # @   �  
;# @   C  R	 � @     !n  �" @   �" @          �  �  �" @   )
   "n  �" @     �E  #  �  U# @   )
    E# @   6  
m# @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   �! @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�       " @   �  B	  Rt  S" @   �  
\" @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �! @   o       �$
  	key *%<  >  4  	dtor *1.  r  d  
new_key ,$
  �  �  �! @   �  �	  R1QH �! @   �  
  Rt  
�! @   �  Rt   �  &n  ! @   p       ��  �  �  '�  H! @          �
  �  �  �  L! @     Q! @     (c! @   Rt   *! @   �  �
  R|  )�! @   �  R	 � @      �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 4    �  _CRT_MT �   	0@ @   int  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	A� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	@� @    �   "  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  r  �# @   �      $  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  �  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  �  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  �  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   �  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �& @   �       �7
  i �(�   �  �  �  �	`  �  �	  	    importDesc �@  '  %  �  �^
  importsStartRVA �	I  7  /  �  �& @   	�  ��  �  �  �  �  �  	�  �& @    �  �  �  �  �  v  r  �  �  �      M  �& @   �& @   J       �q  �  �  f  }  �  �  �  �  �  �  �  �    
_IsNonwritableInCurrentImage �   & @   �       ��  pTarget �%`  �  �  �  �	`  rvaTarget �
�      �  �^
      �   & @   �  �/  �  �  �  �  �  	�  0& @    �  �  �  �  �  '  #  �  8  6      M  T& @   T& @   I       �q  D  B  f  }  N  L  �  X  V  �  b  `    
_GetPEImageBase �`  �% @   6       �0  �  �	`  	�  �% @   �  �	�  �  �  �  �  	�  �% @    �  �  �  �  �  o  k  �  �  ~       
_FindPESectionExec y^
  `% @   s       �%  eNo y�   �  �  �  {	`  �  |	  �  �  �  }^
  �  �  �  ~�   �  �  	�  `% @   �  �	�  �  �  �  �  	�  q% @    �  �  �  �  �  �  �  �  �  �       
__mingw_GetSectionCount g�    % @   7       ��  �  i	`  �  j	  �  �  	�   % @   j  m	�  j  �  �  �  	�  0% @    z  �  z  �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
  �$ @   �       �  p Y&s      �  [	`  rva \
�  1  /  �  �$ @   D  _�  �  D  �  �  �  	�  �$ @    T  �  T  �  �  =  9  �  N  L      	M  �$ @   _  c
q  Z  X  f  _  }  f  b  �  �  �  �  �  �     
_FindPESectionByName :^
   $ @   �       �M  pName :#�  �  �  �  <	`  �  =	  �  �  �  >^
  �  �  �  ?�   �  �  �  $ @   9  F  �  9  �  �  �  �  %$ @    %$ @          �  �  �  �  �  �  �  �     &$ @   �  -  Rt  'z$ @   z  Rs Qt X8  _FindPESection $^
  �  �  $`  (rva $-�  �  &	  �  '^
  �  (�    _ValidateImageBase   �  �  `  pDOSHeader �  �  	  pOptHeader v   )�  �# @   ,       �~  �       �      �  �  	�  �# @    2  �  *  $  2  �  �  D  @  �  Q  O     *M  �# @   P       �f  ]  Y  +q  Q}  p  l  �  �  �  �  �  �    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 o  W  o  _MINGW_INSTALL_DEBUG_MATHERR �   	@@ @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99     �' @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char short int DWORD ��   float signed char double long double _Float16 __bf16 Sleep      sleep .�   �' @          �seconds "�   �  �  �' @   k  	R�R
�   �   e  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �' @            __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	P@ @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  �' @          �_File *�      _Format J�  '  !  _ArgList Z�   @  :  �' @   �  R0Q�RX�QY0w �X   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   ( @   H       �  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	`@ @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	   ( @   H       �_Format .�  f  `  
ap 
�   �Xret 	  {  y  ,( @   �  �  R1 A( @   �  R0Xs Y0w t    �   	  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  P( @   2       %  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	p@ @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  P( @   2       �_File )�  �  �  _Format I�  �  �  
ap 
�   �hret 	  �  �  }( @   �  R0Q�RX�QY0w �   �   k   	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	x� @   local__winitenv 
`  	p� @   '  
	�@ @     
	�@ @    �   !  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �( @         
   __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	 A @   �      �   __imp_at_quick_exit g)  	�@ @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	�@ @   
initial_tzname1 }
V  	�@ @   
initial_tznames ~�  	�@ @   
initial_timezone 
%  	�@ @   
initial_daylight �  	�@ @   __imp_tzname ��  	�@ @   __imp_timezone ��  	�@ @   __imp_daylight ��  	�@ @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�@ @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�@ @   �	  __imp__amsg_exit �V  	�@ @   F  __imp__get_output_format �\
  	�@ @   -
  __imp_tzset ��  	�@ @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�@ @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �) @   5       ��
  file �!�
  �  �  fmt �6�  �  �  
ap ��   �h+ret �       * @   �
  R4Q�RX�QY0w �  ,tzset "0* @   6       �  -  4* @   4* @   -       �B* @   +  N* @     Z* @       ._tzset �/_get_output_format nF  �( @          �0_amsg_exit i�) @   .       ��  ret i      �) @   z  �  R2 �) @   Q  �  Q	�V @   Xs  �) @   <  R�  at_quick_exit �  �) @          �   func ]*�  2  .  1�) @   �   _onexit ��  �) @          �p  func V%�  J  D  �) @   �  Rs   __wgetmainargs J  ) @   j       �[  _Argc J"�  i  c  _Argv J5I  �  �  _Env JGI  �  �   �  JQ  �  �  !�  Jl�	  � 0) @   0  @) @   	  &  R	v  $0.# E) @   �  N) @   �  Y) @   �  m) @   U   __getmainargs >  �( @   j       �E  _Argc >!�  �  �  _Argv >1S  �  �  _Env >@S       �  >J  0  *  !�  >e�	  � �( @   �  �( @   �    R	v  $0.# �( @   �  �( @   �  �( @   u  �( @   U   2  p* @   6       ��* @   +  �* @     �* @                                                                                                           
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  H}   I  (   ( 
   !I  $ >   :;9I  	4 :!;9I  
.?:;9'I<  H }  H}  
.?:;9'I<   :!;9I�B  .?:;9'<  
 :;9I8  4 :!;9I�B   1�B  >!I:;9  >!!I:!;9!  U     :!;9I  4 :!;9I  & I  '   :;9I   <  7 I     1R�BX!YW   4 1�B  !.:!;9!
' !  ". ?<n:!;!   #%U  $:;9  %   &5 I  ':;9  (4 :;9I?  ).?:;9'�<  *.?:;9'<  +.?:;9'I@z  ,4 1  -.:;9'@z  ..?:;9'@z  /.1@z  0 1  14 1  2. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   %  4 :;9I?  $ >   $ >  %   :;9I  .?:;9'<   I  .?:;9'I@z   :;9I�B  H}  	I ~    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �    h   �
        Q  j  �  �   �   �  �  �  �  �  �            	P @   K/tY�W
<g�W$hX<Y�
��YW/ZJ	Y��X+ aJ	��;	=	 X �.	]t�&X r<) �  .�xX��Y=��v��Y=LcfKLh.Z6 �
yXB > �	K�, �8 X4 �	K� ! 	p, @   � ���=��	Y� � fN	J �t <�n� X��	 <�	 <� X �	��%W%=WY%Y%W/Y	J)��X	00XW|xXY	XwtY
.tv�/;YZ�!YYYYYYYYYYYYYYYZ�c�!X���	EJ �t <�	] �  <Y�	\!	� W	u
@	Y��uZ&#	K�	/�	�� #    K   �
      q  �  �  �  �  �  �  �  �     	  @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      Z  r  �  �  �  �  �  �      6     .   �
      h  �  �  �  R     .   �
          E  O   	� @    6     .   �
      �  �  �  �      K   �
      @  X    �  �  �  �  �  �  �   	� @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      0  H  o  z  6     .   �
      �  �  	  	  �     <   �
      j	  �	  �	  �	  �	  �	  �	   	� @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      +
  C
  j
  u
   	� @   		3 6     .   �
      �
  �
      |    s   �
      |  �  �  �  �  �  �        %  2  ;  C  O  `  i  r    	� @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	� @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      �  �  	
  $
  2
  @
   	  @   
L�Z
�KTYZ
g=yuX 6     .   �
      �
  �
  �
  �
  �    U   �
      3  K  r  	�  �  �  �  �  �  �  �  �   	P @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      @  X    �  �  �  �  �  �  �  �  �  �     	! @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      e  }  �  �  6     .   �
        #  J  ^  G    K   �
      �  �  �      &  0  <  F  N   	�# @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< 6     .   �
      �  �  �  �  q     A   �
      O  g  �  �  �  �  �  �   	�' @   Kfg n     A   �
      +  C  l  �  �  �  �  �   	�' @   K
�<.Y �     A   �
        )  R  m  {  �  �  �   	 ( @   g?	YT�Y	 X� <uX �     A   �
      �    5  P  _  n  w  �   	P( @   KU	\fp	\;Y	Y W     O   �
      �  �    0  [  g  o  |  �  �  �  |    h   �
      �    6  Q  |  �  �  �  �  �  �  �  �  �  �  �   	�( @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	�) @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                                                                                                                          ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �         P  P @   !       D0\  D   P  � @   �      A�A�D`u
A�A�Ck
A�A�C     ,   P   @   a       D0[
A[
E`      L   P  p, @   �      B�A�A �A(�A0�D��
0A�(A� A�A�B�A   ���� x �         P    @   :       D0u  4   P  @ @   j       A�A�D@@
A�A�H       P  � @             ���� x �         �  � @             ���� x �      $     � @   /       D0R
JN    L      @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�         � @             ���� x �      <   �  � @   �       A�A�D�P�
���
���A�A�B    ���� x �           � @             ���� x �      $   8  � @   i       A�A�DP   <   8  0 @   b      A�A�A �Dp�
 A�A�A�D   \   8  � @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �             @   >       D`y       @ @             ���� x �      4   h  P @   �      A�D0}
A�Mf
A�I     ���� x �      L   �  ! @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �  �! @   o       A�A�A �D@U
 A�A�A�A    D   �  �! @   �       A�A�D@R
A�A�FR
A�A�D      4   �  �" @   �       A�D0p
A�J�
A�A      ���� x �         �  �# @   ,          �  �# @   P       L   �   $ @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       �  �$ @   �          �   % @   7          �  `% @   s          �  �% @   6          �   & @   �          �  �& @   �          ���� x �           �' @          D0R     ���� x �         @  �' @          D@Y     ���� x �      ,   x   ( @   H       A�A�D`A�A�   ���� x �         �  P( @   2       DPm     ���� x �         �  �( @          L   �  �( @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �  ) @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �  �) @          A�D0WA�    �  �) @             �  �) @   .       A�D0   �  �) @   5       DPp     �  0* @   6       D0q     �  p* @   6       D0q                                                                                                                                                                                                                                                                                                                                                                                          Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion idevice_private companion_proxy_client_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection _DoWildCard _StartInfo                                                                                                                                                                                                                                                        C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools comptest.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/plist ../include/libimobiledevice comptest.c comptest.c stdio.h signal.h stdint.h plist.h libimobiledevice.h companion_proxy.h stdlib.h string.h unistd.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/sleep.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include sleep.c sleep.c minwindef.h synchapi.h unistd.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                                                                                                                                           �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� %                        p, @    R�U���R���U���R���U���R���	U                       p, @    Q�S���Q���S���Q���S���Q���S���Q���S��	�Q�       �- @    P	S0<S  . @   �0�  d0 @   �S     �0 @    P<IP     � @    !R!��R�           � @    QOTO��Q���T���Q�             � @    X|S|~�X�~�S���X���S             � @    %P-1P1JT\vT��T��P        @    P(TV�T       P @    
R
Q!�R�                @    
R
P �R� (P(<�R�<MPMa�R�                @    QR �Q� (R(<�Q�<MRMa�Q�                @    QR�Q�R2�Q�2CRCW�Q�     0 @    R�Q� 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
@T @   ���
 T @   ���
�T @   ���
hT @   ���
�T @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ���mj     ��U  ��2�  ���mj     ��U  ��1�  ���mj     ��U  ��1�  ���mj     ��U  �	�	4�  �	�	�mj     �	�	U  �	�	4�  �	�	�mj     �	�	U  �	�	8�  �	�	�mj     �	�	U  �	�	8�  �	�	�mj     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
��m     �
�
T  �
�
4�  �
�
��m     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� �               
R
�R� T                RQ�R�        QX�Q�        X�`�X� )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                              X         Z���� ���� ���� ���� ���� ���� ���� X         
 @    6f� �- @    3Z� �- @    $.� P @   �p, @   �	 S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����                .file   a   ��  gcrtexe.c              �                                �              �   p                        �   �                        �   �                        %                           @  `                        `             k  @                        �                           �  �                        �  `                        �  0          �  p                    envp           argv            argc    (                          0                        '  �          9  �                        ^  �                        �             �  0                        �  P                        �  �                          @                    mainret            "  �                        8  �                        N                          d                           z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  �     +                 .file   �   ��  gcomptest.c            m  P                           {  8           �  �          �            �  4       main    p      .text   P     !  !             .data                            .bss    0                       .rdata         u                .xdata  t                       .pdata  l      $   	                 �  p     �  ^                 �  �                           �  �                          �  �&  
   �  �                 �  �     �                    �  �     )                   �  0      @                    �  \      \                      9     �                          /                       �     b                    )  �     +                     4  P                     .text   �      .idata$7,      .idata$5�      .idata$4@      .idata$6�      .text   �      .idata$7(      .idata$5�      .idata$48      .idata$6�      .text   �      .idata$7$      .idata$5�      .idata$40      .idata$6�      .text   �      .idata$7       .idata$5�      .idata$4(      .idata$6�      .text   �      .idata$7      .idata$5�      .idata$4       .idata$6h      .text   �      .idata$7      .idata$5�      .idata$4      .idata$6@      .text   �      .idata$7      .idata$5�      .idata$4      .idata$6      .text   �      .idata$7      .idata$5�      .idata$4      .idata$6�      .text   �      .idata$7|
      .idata$5�      .idata$4p      .idata$6�
      .text   �      .idata$7x
      .idata$5�      .idata$4h      .idata$6�
      .text   �      .idata$7t
      .idata$5�      .idata$4`      .idata$6�
      .text   �      .idata$7p
      .idata$5�      .idata$4X      .idata$6�
      .text   �      .idata$7l
      .idata$5�      .idata$4P      .idata$6�
      .text   �      .idata$7h
      .idata$5�      .idata$4H      .idata$6�
      .text   �      .idata$7d
      .idata$5�      .idata$4@      .idata$6l
      .text   �      .idata$7`
      .idata$5�      .idata$48      .idata$6T
      .file     ��  ggccmain.c             �                          p.0                 �  @            �                    __main  �          !  @       .text         �                .data                          .bss    @                       .xdata  �                       .pdata  �      $   	                 �  F>  
   a                   �  l     ?                    �  �     5                     �  p      0                      "     '                     (     �                     )  	     +                     4  P     �                .file   +  ��  gnatstart.c        .text   �                       .data                          .bss    P                           �  �D  
     
                 �  �	     �                     �  �                             I	     V   
                   N                                                     )  @	     +                 .file   ?  ��  gwildcard.c        .text   �                       .data                           .bss    `                            �  �J  
   �                    �  a
     .                     �  �                             �	     :                           �                     )  p	     +                 .file   [  ��  gdllargv.c         _setargv�                       .text   �                      .data   0                        .bss    `                        .xdata  �                       .pdata  �                          �  6K  
   �                   �  �
     :                     �  �      0                      �	     V                      �     �                     )  �	     +                     4  �     0                .file   o  ��  g_newmode.c        .text   �                       .data   0                        .bss    `                           �  �L  
   �                    �  �
     .                     �                              /
     :                      Y     �                     )  �	     +                 .file   �  ��  gtlssup.c              -  �                           <            K  �                    __xd_a  P       __xd_z  X           b  �      .text   �     �                .data   0                        .bss    p                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  AM  
   �  6                 �  �
     �                    �  &                        �  0     0                      i
                          f                            �     �                     )   
     +                     4       �                .file   �  ��  gxncommod.c        .text   �                       .data   0                        .bss    �                           �  U  
   �                    �  �     .                     �  `                                 :                      �     �                     )  0
     +                 .file   �  ��  gcinitexe.c        .text   �                       .data   0                        .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �U  
   {                   �  
     a                     �  �                            �     :                      �     �                     )  `
     +                 .file   �  ��  gmerr.c            _matherr�                       .text   �     �                .data   0                        .bss    �                        .rdata  �     @               .xdata  �                       .pdata  �                          �  
W  
   6  
                 �  m
                         �  =     �                    �  �     0                      �     �                      $	     �                     )  �
     +                     4  �     X                .file     ��  gCRT_fp10.c        _fpreset�	                       fpreset �	      .text   �	                      .data   0                        .bss    �                        .xdata  �                       .pdata  �                          �  CZ  
   �                    �  r     -                     �  �     0                      �     X                      �	     �                     )  �
     +                     4       0                .file   (  ��  gmingw_helpers.    .text   �	                       .data   0                        .bss    �                           �  �Z  
   �                    �  �     .                     �                               	
     :                      �
     �                     )  �
     +                 .file   U  ��  gpseudo-reloc.c        n  �	                           }  0
          �  �       the_secs�           �  �          �  �           �                          �                       .text   �	     =  &             .data   0                        .bss    �                       .rdata        [                .xdata  �      0                 .pdata       $   	                 �  l[  
   K  �                 �  �     �                    �  �     �  
                 �        0                    �  �      W                       C
     �                     �     	                       .     O                    )        +                     4  8     �                .file   u  ��  gusermatherr.c         "                              8  �           F  @      .text         L                .data   0                        .bss    �                       .xdata                         .pdata  ,                         �  �r  
   �                   �  �                         �  J     r                     �  P     0                      �     �                      }     �                     )  P     +                     4       P                .file   �  ��  gxtxtmode.c        .text   P                       .data   0                        .bss    �                           �  �u  
   �                    �  �     .                     �  �                            q     :                      G
     �                     )  �     +                 .file   �  ��  gcrt_handler.c         ]  P                       .text   P     �               .data   0                        .bss    �                       .xdata  ,                      .rdata  �     (   
             .pdata  D                         �  4v  
   �                   �  �     ~                    �  �     _                    �  �     0                      �     �  
                   �                            �
                         )  �     +                     4  h     P                .file   �  ��  gtlsthrd.c             t                             �             �  �           �  �          �  �           �  �             �      .text        b  "             .data   0                        .bss    �      H                 .xdata  4     0                 .pdata  P     0                    �  �  
   �
  A                 �  c     a                    �       �                    �  �     0                    �                              8     x                     �     %                    )  �     +                     4  �     (               .file   �  ��  gtlsmcrt.c         .text   �                       .data   0                       .bss    @                           �  �  
   �                    �  �     .                     �                               �     :                           �                     )       +                 .file   �  ��  g                .text   �                       .data   @                        .bss    @                          �  q�  
   �                    �  �     0                     �                               �     :                      �     �                     )  @     +                 .file   1  ��  gpesect.c              (  �                           ;  �          J             _  �          |             �  `          �  �          �             �  �      .text   �     �  	             .data   @                        .bss    P                       .xdata  d     ,                 .pdata  �     l                    �  A�  
   �  �                 �  "     �                    �  �     �                    �  @     0                    �  &     �                       $     K                     �     T                       r     �                     )  p     +                     4  �     (               .text   �     2                 .data   @                        .bss    P                       .text   �                       .data   @                        .bss    P                           )  �     +                 .file   E  ��  gmingw_matherr.    .text   �                       .data   @                       .bss    p                           �  �  
   �                    �  �     .                     �  p                            o     :                      W     �                     )  �     +                 .file   c  ��  gsleep.c           sleep   �                       .text   �                     .data   P                        .bss    p                       .xdata  �                      .pdata  �                         �  ��  
   �  	                 �  �     �                     �  �                          �  �     0                      �     u   	                        �                     )   
     +                     4       8                .file   �  ��  gucrt_vfprintf.    vfprintf�                       .text   �                     .data   P                      .bss    p                       .xdata  �                      .pdata  �                         �  ��  
   �                   �  e     8                    �  �     X                     �  �     0                           r   	                   �     �                     )  0
     +                     4  @     8                .file   �  ��  gucrt_printf.c     printf                          .text         H                .data   `                      .bss    p                       .xdata  �                      .pdata                           �   �  
   �  
                 �  �     l                    �  T     -                     �  �     0                      �     �   	                   �     �                     )  `
     +                     4  x     H                .file   �  ��  gucrt_fprintf.c    fprintf P                       .text   P     2                .data   p                      .bss    p                       .xdata  �                      .pdata                           �  Ӱ  
   �                   �  	     b                    �  �     F                     �        0                      %     �   	                   �     �                     )  �
     +                     4  �     8                .file   �  ��  g__initenv.c           �  p            x      .text   �                       .data   �                      .bss    p                          �  u�  
   �                   �  k      �                     �  P                            �     [                      �                         )  �
     +                 .file   "  ��  gucrtbase_compa          �                           (  �          6        _onexit �          E  �          S  �                        x  �          �  �      tzset   0          �  P                    _tzset  p          �  �           �  �           �  �           �  �           �  �       .text   �       "             .data   �      x   
             .bss    �                       .xdata  �     P                 .pdata       l                .rdata  �                          �  �  
   �  Y                 �  !                          �  �     |                    �  p     0                      
      �                     �                            �     `                    )  �
     +                     4  �     �               .text   �      .data         .bss    �      .idata$70
      .idata$5�      .idata$4      .idata$6"
      .text   �      .data         .bss    �      .idata$74
      .idata$5�      .idata$4      .idata$60
      .text   �      .data         .bss    �      .idata$78
      .idata$5�      .idata$4       .idata$6>
      .text   �      .data         .bss    �      .idata$7<
      .idata$5�      .idata$4(      .idata$6J
      .file   0  ��  gfake              hname         fthunk  �      .text   �                       .data                          .bss    �                       .idata$2�                      .idata$4      .idata$5�      .file   S  ��  gfake              .text   �                       .data                          .bss    �                       .idata$40                      .idata$5�                      .idata$7@
                      .text   �      .data         .bss    �      .idata$7 
      .idata$5h      .idata$4�      .idata$6
      .text   �      .data         .bss    �      .idata$7
      .idata$5p      .idata$4�      .idata$6
      .text   �      .data         .bss    �      .idata$7
      .idata$5x      .idata$4       .idata$6
      .file   a  ��  gfake              hname   �      fthunk  h      .text   �                       .data                          .bss    �                       .idata$2�                      .idata$4�      .idata$5h      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4                      .idata$5�                      .idata$7
     !                 .text   �      .data         .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6�	      .text   �      .data         .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6�	      .text          .data         .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6�	      .text         .data         .bss    �      .idata$7�      .idata$5@      .idata$4�      .idata$6�	      .text         .data         .bss    �      .idata$7�      .idata$5H      .idata$4�      .idata$6�	      .text         .data         .bss    �      .idata$7�      .idata$5P      .idata$4�      .idata$6�	      .text          .data         .bss    �      .idata$7�      .idata$5X      .idata$4�      .idata$6�	      .file   �  ��  gfake              hname   �      fthunk  (      .text   0                       .data                          .bss    �                       .idata$2�                      .idata$4�      .idata$5(      .file   3  ��  gfake              .text   0                       .data                          .bss    �                       .idata$4�                      .idata$5`                      .idata$7�                       .text   0      .data         .bss    �      .idata$7\      .idata$5�      .idata$4       .idata$6`      .text   8      .data         .bss    �      .idata$7`      .idata$5�      .idata$4(      .idata$6n      .text   @      .data         .bss    �      .idata$7d      .idata$5�      .idata$40      .idata$6|      .text   H      .data         .bss    �      .idata$7h      .idata$5�      .idata$48      .idata$6�      .text   P      .data         .bss    �      .idata$7l      .idata$5�      .idata$4@      .idata$6�      .text   X      .data         .bss    �      .idata$7p      .idata$5�      .idata$4H      .idata$6�      .text   `      .data         .bss    �      .idata$7t      .idata$5�      .idata$4P      .idata$6�      .text   h      .data         .bss    �      .idata$7x      .idata$5�      .idata$4X      .idata$6�      .text   p      .data         .bss    �      .idata$7|      .idata$5�      .idata$4`      .idata$6�      .text   x      .data         .bss    �      .idata$7�      .idata$5�      .idata$4h      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4p      .idata$6	      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4x      .idata$64	      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6@	      .text   �      .data         .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6P	      .text   �      .data         .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6r	      .text   �      .data         .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6z	      .text   �      .data         .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�	      .file   A  ��  gfake              hname          fthunk  �      .text   �                       .data                          .bss    �                       .idata$2�                      .idata$4       .idata$5�      .file   ]  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4�                      .idata$5                       .idata$7�     "                 .text   �      .data         .bss    �      .idata$70      .idata$5�      .idata$4      .idata$6>      .text   �      .data         .bss    �      .idata$74      .idata$5�      .idata$4      .idata$6V      .file   k  ��  gfake              hname         fthunk  �      .text   �                       .data                          .bss    �                       .idata$2x                      .idata$4      .idata$5�      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4                      .idata$5�                      .idata$78     "                 .text   �      .data         .bss    �      .idata$7      .idata$5p      .idata$4�      .idata$6*      .file   �  ��  gfake              hname   �      fthunk  p      .text   �                       .data                          .bss    �                       .idata$2d                      .idata$4�      .idata$5p      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4                       .idata$5x                      .idata$7                      .text   �      .data         .bss    �      .idata$7�      .idata$5H      .idata$4�      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5P      .idata$4�      .idata$6      .text   �      .data         .bss    �      .idata$7�      .idata$5X      .idata$4�      .idata$6      .text   �      .data         .bss    �      .idata$7�      .idata$5`      .idata$4�      .idata$6       .file   �  ��  gfake              hname   �      fthunk  H      .text                           .data                          .bss    �                       .idata$2P                      .idata$4�      .idata$5H      .file   �  ��  gfake              .text                           .data                          .bss    �                       .idata$4�                      .idata$5h                      .idata$7�                      .text          .data         .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6�      .text         .data         .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  0      .text                          .data                          .bss    �                       .idata$2<                      .idata$4�      .idata$50      .file     ��  gfake              .text                          .data                          .bss    �                       .idata$4�                      .idata$5@                      .idata$7�     &                 .text         .data         .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�      .file     ��  gfake              hname   �      fthunk         .text                           .data                          .bss    �                       .idata$2(                      .idata$4�      .idata$5       .file   g  ��  gfake              .text                           .data                          .bss    �                       .idata$4�                      .idata$5(                      .idata$7�     "                 .text          .data         .bss    �      .idata$7p      .idata$5      .idata$4�      .idata$6�      .text   (      .data         .bss    �      .idata$7l      .idata$5      .idata$4�      .idata$6�      .text   0      .data         .bss    �      .idata$7h      .idata$5       .idata$4�      .idata$6�      .text   8      .data         .bss    �      .idata$7d      .idata$5�      .idata$4�      .idata$6�      .text   @      .data         .bss    �      .idata$7`      .idata$5�      .idata$4x      .idata$6�      .text   H      .data         .bss    �      .idata$7\      .idata$5�      .idata$4p      .idata$6h      .text   P      .data         .bss    �      .idata$7X      .idata$5�      .idata$4h      .idata$6L      .text   X      .data         .bss    �      .idata$7T      .idata$5�      .idata$4`      .idata$6<      .text   `      .data         .bss    �      .idata$7P      .idata$5�      .idata$4X      .idata$6$      .text   h      .data         .bss    �      .idata$7L      .idata$5�      .idata$4P      .idata$6      .file   u  ��  gfake              hname   P      fthunk  �      .text   p                       .data                          .bss    �                       .idata$2                      .idata$4P      .idata$5�      .file   �  ��  gfake              .text   p                       .data                          .bss    �                       .idata$4�                      .idata$5                      .idata$7t     
                 .file   �  ��  gcygming-crtend        �  !                       .text   p                       .data                          .bss    �                           �  !                         �                            �  �                         	  (!                         )        +                 .idata$2        .idata$5�      .idata$4      .idata$2�       .idata$5�      .idata$48      .idata$4H      .idata$5�      .idata$70      .idata$4x      .idata$5�      .idata$7�
      .rsrc       
    __xc_z              	  �          ;	  P          Z	  �          e	            ~	            �	  t          �	  �          �	  0          �	              �	  8!          �	  �           �	  �
          
  �          4
  �          P
  x           
  @          �
  p          �
  P          �
  8          �
  �          �
  �             �           
      	          �          ;  �          Z  �       __xl_a  0           f  X          s  �          �  P          �  �      _cexit  H          �  `  ��       �     ��       �  �            @
          G  �          u              �  �          �      ��       �     ��       �  0           �  �      __xl_d  @           
         _tls_end   	        '
  �      __tzname�          =
             J
  �          [
             m
  �           ~
  0           �
  �          �
  @          �
      	    memcpy  �          �
  `      puts               �
               �             �          A  �      malloc  �      _CRT_MT 0           X  0          d  h          q                �          �  P          �  �          �     ��       �  @            �            0          )  �          N  x           h  �          {  X          �  �          �  �           �            �             �  `          	  p           "  (          1  <           d  �          q  P           �  �          �  �          �  �          �  �      abort   �          �            #  �           7  �          B  P       __dll__     ��       R      ��       g  8          r  �          �  0          �  
          �  `          
  @           '  X          2  �          @             O  �          _             j  d           �  �          �     ��       �             �  8      calloc  �            �            �          )  �          5  �           a  �          n  P          �  �          �  �      Sleep   8          �  p      _commode�           �            �  P          �   !          �  H            P           4  �           H             b  �      __xi_z  (           n             �             �  �          �             �  �          �  �          �  |       signal  �            �            X           -          strncmp �          ?  �          \   !          k  0          �            �  �           �  �          �  (           
  �            �          @      ��       S             g  �          �  �          �  p          �  �          �  `          �  P          
  �           "  �          3     ��       F  �          f            t  x          �  `          �  P          �            �  �           �                P           &     ��       ;              J  �           Z  �          t  �           �  �          �  �          �  �      __xl_z  H       __end__              �  H          �  �                   strcmp  �            8!      __xi_a             +  �          F  �          U  �          a  H      __xc_a              v  p          �     ��       �  P           �     ��   _fmode  �           �             �  8          �  �            �            �          '             8  p           F  X          [             l  0            �          �  �          �  0          �            �  �      __xl_c  8           �     	           �          1  P          F            Y  �          i  t           �              �  �          �  �          �  �          �  �      _newmode`             �      fwrite              x          &  @          5  (          K      ��       c      ��       t  �          �  P           �  �          �  P      exit    �          �     ��       �  0           �      ��       �  �      atoi                �      _exit   p          $  `           1  �          @  �          \  P      strlen  �          s            �  h          �  �          �  h          �  �          �  �            �           2  P          T  �           e  �          y  �          �  �          �  �          �  �           �  P           �              �          3  �           B  @      free    �          N  �       _  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame handle_sigint quit_flag get_value_from_watch device_event_cb connected .text.startup .xdata.startup .pdata.startup __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 __imp_plist_get_string_val ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname idevice_event_subscribe __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname __imp_idevice_new_with_options companion_proxy_client_free _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode __imp___p___argc plist_get_node_type __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __rt_psrelocs_start __imp_plist_get_node_type __dll_characteristics__ __size_of_stack_commit__ companion_proxy_get_device_registry __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname companion_proxy_start_forwarding_service_port __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll .refptr.__imp___initenv _tls_start .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler __imp_companion_proxy_client_free plist_bool_val_is_true TlsGetValue __imp_strcmp __bss_start__ __imp___C_specific_handler ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force plist_get_uint_val __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection companion_proxy_client_start_service __C_specific_handler .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf plist_free ___crt_xp_end__ __minor_os_version__ __p___argv __imp_companion_proxy_start_forwarding_service_port libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT __imp_atoi _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname __imp_idevice_event_subscribe _tls_used __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ __imp_plist_get_uint_val plist_array_next_item .refptr._newmode __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func __native_startup_state ___crt_xc_start__ __imp_plist_bool_val_is_true ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a plist_get_string_val _head_lib64_libapi_ms_win_crt_convert_l1_1_0_a plist_array_new_iter .refptr.__mingw_initltsdyn_force __rt_psrelocs_size .refptr.__ImageBase __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp___p___wargv __imp_strlen __imp_companion_proxy_get_device_registry __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __p__wenviron _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_plist_dict_get_item _head_libplist_2_0_dll __IAT_start__ companion_proxy_get_value_from_registry __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit __DTOR_LIST__ __imp_plist_array_new_iter __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_VirtualProtect idevice_free ___tls_end__ __lib64_libapi_ms_win_crt_convert_l1_1_0_a_iname .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ remote_port __minor_image_version__ __imp___set_app_type __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a __imp_plist_array_next_item _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR DeleteCriticalSection _initialize_wide_environment __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv plist_dict_get_item __imp_plist_free __imp_companion_proxy_get_value_from_registry .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_companion_proxy_client_start_service __imp_daylight __p___wargv __mingw_app_type 