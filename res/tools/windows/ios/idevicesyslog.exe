MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� =�g J 6  � & ( >   �     �        @                            �e  `                                             �  �   �  �   �              �  �                           `o  (                   ��  �                          .text   �<      >                 `  `.data   `   P      D              @  �.rdata  �   `      H              @  @.pdata      �      d              @  @.xdata  �   �      h              @  @.bss       �                      �  �.idata  �   �      l              @  �.CRT    `    �      |              @  �.tls        �      ~              @  �.rsrc   �   �      �              @  �.reloc  �    �      �              @  B/4      �         �              @  B/19     *�     �   �              @  B/31     �(     *   �             @  B/45     v8   @  :   �             @  B/57     �
   �     �             @  B/70     d   �     �             @  B/81     �   �                  @  B/97     �)   �  *                @  B/113    �   �     F             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H��d  1��    H��d  �    H��d  �    H��c  f�8MZuHcP<HЁ8PE  tfH�/d  �
��  � ��tC�   ��2  �42  H��d  ���2  H��d  ���t  H�mc  �8tP1�H��(Ð�   �2  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�d  �|  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H�ed  L�֎  H�׎  H�
؎  � ���  H�d  D�H���  H�D$ ��.  �H��8��    ATUWVSH�� H�_c  H�-�  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5,c  1�����V  �����  ��     ����L  ���e  H�Qb  H� H��tE1��   1����  H�
Qc  �K�  H��b  H�
����H��
1  �  �ҍ  �{Hc�H��H���p1  L�%��  H�Ņ��F  H��1�I��0  H�pH���C1  I��H�D I�H��H����0  H9�u�H�H�    H�-]�  �(  H��a  L�B�  �
L�  H� L� H�7�  �1  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�5�a  �   ���������   �/.  ��������H��a  H�
�a  ��/  �   �������1�H�����f�     �/  ���  H�� [^_]A\�f.�     H��a  H�
�a  �   �/  �7���f�H����������/  �H��(H��`  �    ������H��(� H��(H��`  �     �z�����H��(� H��(�-  H���H��(Ð�����������H�
	   �����@ Ð��������������H��(�   �y�  A�   �   H�
�K  I���_.  �4�  H��(�ff.�     H��(�   �9�  H���!.  H�
��  H��t�  H���      H�
��  H��t�<  H�q�      H��(�ff.�     �AWAVAUATUWVSH��X�.�  �=$�  H�%�  �B���9���   Lc��GIމ�  A�6@��tH��X[^_]A\A]A^A_��     �=ڊ  ��~�{ u
�{ �5  H�
�J  ��
  ���  �  ��   �
��  ����  �5ˊ  ����  ���  ����  H�=�  �   ��Lch�  �   H��I��� -  H�
�J  �T
  �   ��H����,  ��t
�W�      H�0�  �}fD  ��   H�ى�  Hc��-  H��H����  H���  ������H�5��  �   ��Lcۉ  �   H��I���s,  H�
J  ��  �   ��H���H,  H���  � ���      �����    �{ ������{ H�{tL9�r�b  f�I9��W  H���? u�Hc��  D�=��  H�o����  D�%e�  E����  H�5]�  L�$���    H��I9���  H�H���x,  H��t�   A�   E��~7L�%<�  Ic�M�,�� I��M9��*���I�$H���7,  H��t�A�   �W��[��  L9�srH���
D  I9�tcH��H���?[u�H�H��(��  H9���  I���L9�tI��A�} (u��?]tL9�r� �     I9�tH���?]u� �K  ���  ��  D	���  E�����������D  �v�  L�  �U����P�  ���G����=!�   ������=�   �����H�5w�  �   ��Lc��  �   H��I���Y*  H�
H  �
  �   ��H���.*  �b����Hc��  ��~mD�
��  E��uaH�5��  L�$��	H��I9�tUH�H����*  H��t��q�     1��D���Hcs�  ����  ���  D=��  ������o���1�E1�����D=s�  �����S���H�OH��I�������=>�   L�w�?  D�=A�  D�
6�  E��~eM��L�/�  E1�I)�K��H��t*H��D�L$8L�T$0L�D$(�)  L�D$(L�T$0��D�L$8tI��E9��E��u�D���E���;���A�   A�	   H�sF  L���(  ���r  L�L�F  L�2F  H�
qF  L�T$8L)�L�\$0�$	  H���  �   H�D$(��H�
�  A�   �   I���(  H�
3F  ��  �   H�D$(��M��   H��I)�I���i(  H�

F  �  �   H�D$(��L�G�   L��I���=(  H�L$0�  M9�L�T$8rxL��L��H)�L��)o�  �r  D�=��  �&����   ���  A�   �   H�
FE  I����'  �   �g(  1��0����=Y�   ������=@�   ����������L�T$0�   H�D$(��M��L��   M)�I��M���'  L�T$0�T���A�   H��D  L���('  ��uqL�
L��D  L��D  �p���I���9���E1�H�T$HL�D$HA�
   �U(  ��H�D$HH��t�8]tYD�=ل  D�
΄  E��������=��   �!��������A�
   H�mD  L���&  ��uEL�L�D  L�D  �����D�
|�  D�e�  1�L�d�  D���H��A9L��t7A9���8A�   H�D  L���@&  ��u8L�
L��C  L��C  ����1�E����D�=�  E��~���7��������L��C  M��M���U���������� ���ff.�     f�AVAUATUWVSH��0I��L�l$(�%  I�^Lc�M�s�7�    H��I9�r'�C�H��<|t��u�H�s�L)�H��I��H��I9�s�H��0[^_]A\A]A^ÐH�N�&  H��H����   I��L��H���5&  �D5  L��H��H�D$(    A�
   �&  ��H�D$(H��t�8 uE���  H�
 �  �hLc�I��L���>&  H����   H�ނ  B�t0��-˂  �R���fD  �΂  H�
˂  �pLc�I��L����%  H��tFH���  J�l0��5��  �����   �ʗ  A�   �   H�
oB  I���$  �   �6%  �   ���  A�   �   H�
XB  I���$  �   �%  �ff.�      ATUWVSH��@���  �A��u��tH��@[^_]A\�f�     ��u���td��u�H�=��   t�H�QH�
�  �#  ��u�����H�́  �   ��  H�7C  H��I���!  ���  ��t����  �D  H�=0�   �n���H�5��  H�QH���.  H���N#  ���J�����H��H�
�  E�A���A���  ���|  H�
�  H�T$0L�gA  H�D$0    �`  �Å��@  H�L$0H�l$8H�=xA  H�D$8    I��H���!  H�5�  �Ã����   ���  H�L$0�  H�
n�  H�T$8L�Z�  ��  H�L$8����  ����  H�
:�  E1�H������  ���$  H�|�  �   ��H��A  I��H���  �   ��H���t"  �3����    H��H�T$(�"  H�T$(H�/�  H�������   ��A�O   �   H�
�@  I���6"  D��  L�%ؓ  E��t�9f�     ��  A��D��  E��uH�L$0I��H����  ���t׉�������   ��A��H��?  H����  H�
@  ��  E1�L�
1  H��  �   ��H��@  H��I��H��@[^_]A\�  �   ��A�*   �   H�
^@  I���n!  H�
�~  �Z  H�
�~  1�H��~  �  1�H�
�~  뉹   ��A�7   �   H�
�?  I���!!  H�
�~  �M  E1�L��~  �M���H�5��  �   ����H��~  H�5�  �   ��H��>  H��I����  �����VSH��(�ֺ/   H���s!  H�PH��HEڅ�tSH�5��  �   ��I��H��?  H���  �   ��I��A��  �   H�
�?  �c   H�
zD  H��([^�Y   H�5J�  �   ��I��H��?  H���Y  �   ��I��뫐�������������%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%Ґ  ���%  ���%��  ���%��  ���%��  ���%
�  ���%��  ��H��(H��.  H� H��t"D  ��H��.  H�PH�@H��.  H��u�H��(�fD  VSH��(H��P  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^�C��� 1�fD  D�@��J�<� L��u��fD  ��|  ��t�D  ��|     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H��O  �8t�    ��t��tN�   H��([^�f�H�ћ  H�5ʛ  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H��K  Hc�H����    H��J  �DA �y�qH�q�   �S  �DD$0I��H�:K  �|$(H��I���t$ �  �t$@|$P1�DD$`H��x[^ÐH�	J  ��    H�YJ  ��    H�)J  �s���@ H��J  �c���@ H�QJ  �S���H��J  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�t  A�   �   H�
�J  I���  H�t$(�   �K  H��H��I���-  ��  ��    WVSH��PHc5�z  H�˅��  H��z  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H�Ez  H��H��H�H�x �     �#  �WA�0   H�H�z  H�T$ H�L��  H���}   �D$D�P����t�P���u��y  H��P[^_� ��H�L$ H�T$8A�@   �   DD�H�y  H�KI��H�S��  ��u��ڌ  H�
�I  ���d���@ 1��!���H�zy  �WH�
hI  L�D�>���H��H�
4I  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%$y  E��tH�e[^_A\A]A^A_]�fD  ��x     �9	  H�H��H��   H����  L�-�K  H�L  ��x      H)�H�D$0H��x  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5~K  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
tH  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  �.w  ������H�5��  1�H�}�D  H�w  H�D� E��t
H�PH�HI����A��H��(D;%�v  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�50I  �s�;H��L�>H���Z����>L9�r��������H�
�F  �����H�
IF  ���������H��XH��u  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
�u  �$  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H�lF  Hc�H��� 1ҹ   �  H���>  H���  H�Ju  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �)  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   ��  H���@����   �   �  �f�     1ҹ   �  H��t*H�������   ���i���f�     �   ���T����   �   �U  �@����   �   �A  �,����   �   �-  �����������ATUWVSH�� L�%t  L����  H��s  H��t6H�-�  H�=�  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%��  WVSH�� ��s  ��H�օ�u
1�H�� [^_ú   �   ��  H��H��t3H�pH�5~s  �8H���K�  H�Ls  H��H�Bs  H�C�P�  묃��멐VSH��(�,s  �˅�u1�H��([^�D  H�5)s  H�����  H�
�r  H��t'1��H��H��tH���9�H�Au�H��tH�B�=  H���܅  1�H��([^� H��r  ��ff.�     @ SH�� ����   w0��tL��r  ����   �|r     �   H�� [�f�     ��u�]r  ��t��<�����f.�     �Br  ��uf�8r  ��u�H�$r  H��t�    H��H�[�|  H��u�H�
 r  H��q      ��q      �Մ  �l����k����   H�� [������f�     H�
�q  �Ä  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���A  H��w{H��C  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H����  ��u�H��H�� [^_��    1�H��H�� [^_� H�IC  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H��B  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L��B  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H�	B  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H��A  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L�9A  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������AUATUWVS��D�Ɖ�L��)�)։�������   �� A�ՙA��D���u�D��)șA��E��~lHc�A��A�L�׉څ�~Yf�     M�1��f�     M��A��D�2A)�9�D��AL҃�Lc�N��M�M�M�9�u�A��I��E9�tD��뱐[^_]A\A]�A���u���ff.�     @ WVSH��0H�t$XL�L$hH��H�T$XL�D$`H�t$(�   �   H� H�8�
  H�q=  I��H����
  �   �|
  I��H��H���^
  �   �d
  �
   H���
  �H��0[^_�ff.�      AWAVAUATUWVSH��H�  H�=�  H��$�   H��M��H��$�   �=   �D$<��L��$�   �D$8��  ��
  I��H���  H��I��H)�M�4$M����   1�L�D$0I�\$ E1�D��$�   �L$(�����A���*Hc�H��L��P9S�tV�D$(   L�3H�� A��M��tfI��L��H���`  ��u�L���L  H9���   H��uE��u��u�D���fD  H�PH9S�u��@9C��   DD$(�D$(��     �L$(L�D$0��uKA����ux��$�   ���  ��  ���.  ��      �?   H��H[^_]A\A]A^A_�fD  D�
�  E��t�H��$�   �8:t�I����H�
#<  �����@ L�D$0Ic�H��L�k��uRM��teD�F  E��tH��$�   �8:��   H�{ �l  H��$�   �-  �8:�Z����:   �U���@ �U���wM����   L�Xj  H��$�    tH��$�   D�8H�S�CH�������1��
���f�     H��H�D$(�
  L�D$(H�������fD  H��$�   �8:�����H��H�
�:  ���������I����H�
�:  �����%������d���HcT$8H��$�   D�t$<H��A��D�5'  H��i  H���3����
  ��t)H��$�   �8:tH��H�
�:  �E�����  ���D$81�H�{ tB��  �D$8��  H��$�   �8:�
��������k�����D$<��  �����������C��     AWAVAUATUWVSH��HD��$�   A��H��M��M��M���y  D�
X  �6  E����  �����  ��h  ����  A�E <-�  D�  E����  <+��  H�vh      ����   ��  ������  ����f.�     �Nh      ��  D9���  Hc�H�t� �>-��  H��8  H��  A����  A����  �5v  ����5  �=c  ���t%��I��A�؉������)����A  ����)��=  ���H  H�=5  ����Z���M��tkHc*  H9|� t]<-�r  A���w  <:�k  ��L���	  H�������D$ M��L��H��L��$�   �����Ã����  H�=�  �L���L�=�  <:�,  ��-��   ��L���  H���  �PM��t��Wu	��;��  ��:��   � ��  �c  �}  f���f     �H     1�E1�H�
H7  �y  ��f  �������  A�E <-�����A��I�������@ �F����  �-   L����  H���0����=�  �t
�=�  ���  L�~H���-   L�=�  � �8  �-   L���  H����   �x:�%���H��e      � �z  ��  L�=�e  H��6  ��H�P  �V  �s�     �2  �����D  A���<+�#��������    �5  H�C6  �=�  H��  ���uP���t�=�  ��  ������  �����������H��H[^_]A\A]A^A_�f���H�56e  ��  �   �ԉ�)�A��I���)��&�����  �L���L�=�  <:������ u�w  �
u  ��tA�} :��   �X  �?   �q���fD  A����2����
)  ���t
�=  ��  H�~H�=  <-������~ ������5�  ��H�*5  ��  H��  ��������A�؉�I��L$<�[����L$<)�)��  ������     ��H�
5  �����G���H��H�=�  1������ ��   ��  ���~  D9�|e�w  H��4  H�Y  ��tA�} :t�W   H�
r5  �����;  W   A�} :������:   �I����  �S����  �����H�H�D� H��  �D$     M��L��H��L��$�   �������H�4  H��  ������x:�\�������  A9�~Hc�H�D� H� c  �:���H��3  H��  ��  ��tA�} :t��H�
�4  ������p  A�} :�
����0����     H��8E1��D$(    H�D$     ����H��8�ff.�      H��8H�D$`�D$(   H�D$ ����H��8�H��8H�D$`�D$(   H�D$ �e���H��8�H��8E1�L�D$ I��H��1��  H��8Ð�VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H����  ���   ����  �k  � ��j  H� H��W  H� H�M��t	A�$�  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���`  ���   ����(  ��  � ��  H� H���  H� H�M��t	A�$�  1�H�� [^_]A\�fD  SH�� H����  ���    HD�H�� [�f�H�y3  �8 t1�Ð�  ff.�     SH�� �˹   �  A��H�U2  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H��2  ��~   H�'  �j   H�  �V   H��  H��(�f.�     H��(H�U2  ��>   H��  �*   H��  �   H��  H��(Ð����������%�s  ���%�s  ���%�s  ���     �%�s  ���%�s  ���%�s  ���%�s  ���%2s  ���%2s  ���%2s  ���%2s  ���%2s  ���%2s  ���%2s  ���%2s  ���%2s  ���     �%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���%Rr  ���     �%�q  ���%�q  ���%�q  ���%�q  ���%�q  ���     �%Rq  ���     �%q  ���%q  ���%q  ���%q  ���%q  ���     �%�p  ���%�p  ���%�p  ���     �%�p  ���%zp  ���%jp  ���%Zp  ���%Jp  ���%:p  ���%*p  ���%p  ���%
p  ���%�o  ���%�o  ���     AWAVAUATUWVSH��  H�5v  E1�E1�E1�H�l$pA��H���M���H��D   �H�H�5�����   H�=T#  H������H��   H�5�$  �����D$8    I��I��H��D��H�D$     �Q���A���tA��xwYD��Hc�H���E����E����  �|$8 ��E����  ����  �   ��p  A�-   �   H�
#  I���r���H��   ����A�   �WA��p��  A��H� 0  H��9 �}  �����?���� [     �0���L�?"  H�  H�
D"  �����E1�D��H�Ę  [^_]A\A]A^A_�H��/  H��: �T  H�
�Z  �����H��/  H��i���H��Z  �����L��/  I� �8 �Q  �,Z  H�
-Z  ���D$<H�H��H��H�D$@����L�L/  H����  I�H��Y  H�D$H�����H�L$@H�T$HH�D
��D$<��Y  �F���H�
%  A�������1�����Y     �"���L��.  I� �8 ��  ��Y  H�
�Y  ���D$<H�H��H��H�D$@�����L��.  H���  I�H�iY  H�D$H�_���H�L$@H�T$HH�D
��D$<�>Y  ����A������A������1����������H�
\$  �w����a����   �����h���H�1������C���L�.  I� �8 �V  ��X  H�
�X  ���D$<H�H��H��H�D$@����L��-  H���V  I�H��X  H�D$H����H�L$@H�T$HH�D
��D$<�YX  ������D$8�w�����t;��t7�   ��m  A�*   �   H�
Y   I������H��   ������"���E����   �4X     E���c  1�H�-E   �H��9X  ��   H�X  H��D�T$8H�4�H�>H�������D�T$8��u�H������E1�D�T$8L�봄��\����   �m  A�*   �   H�
G  I�������H��   �����p���E����  E����  �=@W   ~�=CW   u
�'W     1�1�D�T$8�T$\H�T$\H�L$`H�L$`����H�L$`������|$\ u-H�>W  �   H����  �Zl  I��H��  H���p�����V     �   �4���H�L$hE1�H�����H�~V  1�H�D$h�Z���H��j  ���  �Ӄ=�V   t�H�L$h�/��������=�V  ��~"1�H�5�V  H��H�������9��H�������=gV   ~H�
fV  �����=CV  ��~"1�H�5>V  H��H������9��H���u����=V  ��~"1�H�5V  H��H���U���9��H���I����=�U  ��~"1�H�5�U  H��H���)���9��H������H�
~U  ����H�
�U  ���������E���<���H�
�  D�T$8�����D�T$8�!���H�
�  D�T$8��U     ����D�T$8������   ��j  A�(   �   H�
�  I������H��   ������#����   �}j  A�   �   H�
�  I���c���H��   ����������   �Fj  A�0   �   H�
�  I���,���H��   �W��������   �j  A�0   �   H�
}  I�������H��   � ����~����   ��i  A�2   �   H�
�  I������H��   ������G����   ��i  A�   �   H�
^  I�������   �
����wi  A�[   �   H�
U  I���]���D�T$8�M��������������������������������������L @           ��������                                                                                                                                                                                                                                                                                                                                `i @                   d       fi @                   h       ki @                  u       pi @                   n       xi @                   x       }i @                  t       �i @                  T       �i @                  m       �i @                  p       �i @                  e       �i @                   q       �h @                   k       �i @                   K       �i @                          �i @                          �i @                   v                                       �L @           ��������        ����                           ������������    �r @   ?                     ����            �? @           �? @            @ @            � @   � @   �A @   �A @   @@ @   pA @   �@ @   P@ @   (S @   ,S @   0S @      �p  DS @   @S @   PDT PST PA @   0A @                                                                                                                                                                           
Exiting...
 [0;32m [0;31m [0;33m [0;35m [1;37m ERROR: realloc failed
 <Notice>: <Error>: <Warning>: <Debug>: [0;37m [1;36m [0;36m [m ERROR: malloc() failed
 ERROR: realloc() failed
 Device with udid %s not found!?
 idevicesyslog  ERROR: Could not connect to lockdownd: %d
 com.apple.syslog_relay       *** Device is passcode protected, enter passcode on the device to continue ***
 ERROR: Could not start service com.apple.syslog_relay.
 ERROR: Unable tot start capturing syslog.
 [connected:%s]
      Could not start logger for udid %s
 [disconnected:%s]
 Usage: %s [OPTIONS]
     
Relay syslog of a connected device.

OPTIONS:
  -u, --udid UDID       target specific device by UDID
  -n, --network         connect to network device
  -x, --exit            exit when device disconnects
  -h, --help            prints usage information
  -d, --debug           enable communication debugging
  -v, --version         prints version information
 --no-colors            disable colored output

FILTER OPTIONS:
  -m, --match STRING      only print messages that contain STRING
  -t, --trigger STRING    start logging when matching STRING
  -T, --untrigger STRING  stop logging when matching STRING
  -p, --process PROCESS   only print messages from matching process(es)
  -e, --exclude PROCESS   print all messages except matching process(es)
                          PROCESS is a process name or multiple process names
                          separated by "|".
  -q, --quiet             set a filter to exclude common noisy processes
  --quiet-list            prints the list of processes for --quiet and exits
  -k, --kernel            only print kernel messages
  -K, --no-kernel         suppress kernel messages

For filter examples consult idevicesyslog(1) man page.

 PhoneCheck 2.0    ERROR: UDID must not be empty!
 ERROR: filter string must not be empty!
        ERROR: message filter string must not be empty!
        ERROR: trigger filter string must not be empty!
        ERROR: untrigger filter string must not be empty!
 1.3.0-225-gfefa2f3 %s %s
 dhu:nxt:T:m:e:p:qkKv       ERROR: -k and -K cannot be used together.
      ERROR: -p and -e/-q cannot be used together.
   ERROR: -p and -K cannot be used together.
 kernel       No device found. Plug in a device or pass UDID with -u to wait for device to be available.
     Waiting for device with UDID %s to become available...
 debug help udid network exit trigger untrigger match process exclude quiet no-kernel quiet-list no-colors version   ��������}���������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������t�������������������������������������������������������������������������������������������������������������������������������k�����������������������������������O������������������        CircleJoinRequested|CommCenter|HeuristicInterpreter|MobileMail|PowerUIAgent|ProtectedCloudKeySyncing|SpringBoard|UserEventAgent|WirelessRadioManagerd|accessoryd|accountsd|aggregated|analyticsd|appstored|apsd|assetsd|assistant_service|backboardd|biometrickitd|bluetoothd|calaccessd|callservicesd|cloudd|com.apple.Safari.SafeBrowsing.Service|contextstored|corecaptured|coreduetd|corespeechd|cdpd|dasd|dataaccessd|distnoted|dprivacyd|duetexpertd|findmydeviced|fmfd|fmflocatord|gpsd|healthd|homed|identityservicesd|imagent|itunescloudd|itunesstored|kernel|locationd|maild|mDNSResponder|mediaremoted|mediaserverd|mobileassetd|nanoregistryd|nanotimekitcompaniond|navd|nsurlsessiond|passd|pasted|photoanalysisd|powerd|powerlogHelperd|ptpd|rapportd|remindd|routined|runningboardd|searchd|sharingd|suggestd|symptomsd|timed|thermalmonitord|useractivityd|vmd|wifid|wirelessproxd                             P$ @                            � @   � @   �� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ���̴��d�������������ܴ��Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      �������������������� �������`��� �������                        %s:     P O S I X L Y _ C O R R E C T           unknown option -- %s            unknown option -- %c                            option doesn't take an argument -- %.*s         ambiguous option -- %.*s                        option requires an argument -- %s                               option requires an argument -- %c                               runtime error %d
               PR @           �R @           �L @              @           �{ @           �{ @           @o @           �R @           h� @           � @           �� @           �� @           � @            � @           P� @           Р @           ؠ @            � @           � @           � @           (� @            � @           @R @           @� @           �+ @           �$ @           � @           � @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P  �  t�  �  �  |�  �  �  ��  �  b  ��  p  "  ��  "  �"  ��  @#  z#  �  �#  �#  �  �#  $  ��  $  $  ��   $  O$   �  P$  �$  �  �$  �$  �  �$  �%  �  �%  �%  0�   &  i&  4�  p&  �'  @�  �'  =+  L�  @+  ~+  d�  �+  �+  l�  �+  M-  p�  P-  �-  x�  �-  /.  ��  0.  �.  ��  �.  �/  ��  �/  �/  ��  �/  @0  ��  @0  �0  ��  �0  `1  ��  `1  �1  ��  �1  2  đ   2  V2  ȑ  `2  �2  ̑  �2  �3  Б   4  �4  ԑ  �4  R5  �  `5  �8  �  �8  ?  �   ?  B?   �  P?  p?  (�  p?  �?  0�  �?  �?  8�  �?  �?  @�   @  2@  L�  @@  C@  T�  P@  �@  X�  �@  *A  h�  0A  NA  x�  PA  eA  ��  pA  �A  ��  �A  �A  ��  �A  B  ��   B  VB  ��  pD  �L  ̐  �L  �L  ��                                                                                                                                                                                                                                                                      B   b  
 
20`pP�	 B  �C     �  �  �+  �  	 B  �C     �    �+     B         B   B  	 �0`
p	P����   R
0	`pP���
 
r0`pP� B0`  
 S 0`
p	P���� B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   0`pP�� R0`p	 �0`
p	P����  	 �0`
p	P����   b   b   b   b   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                             �          (�  ��  ��          L�  8�  ��          ��  P�  ��          ��  ��  �          ؼ  ��   �          �  ش  P�          8�  �  `�          l�  �  ��          Խ  H�   �          �  ص  p�          L�  (�  ��          ��  P�                          x�      ��      ��      ض      �      �      (�      D�      \�      ��      ��      ķ      �      ��               �      ,�              H�      `�      x�      ��      ��      ��      ָ      ��      ��      
�      �              ,�              6�      F�              V�      f�      p�      x�      ��              ��              ��      ��      ¹      ̹      ֹ              �      �      ��      
�      �      .�      F�      \�      j�      r�      ��      ��      ��      к      �      ��      �              �      �      .�      <�      V�      r�      |�      ��      ��              ��      ��      ��      ��              ��      ̻      ڻ      �              x�      ��      ��      ض      �      �      (�      D�      \�      ��      ��      ķ      �      ��               �      ,�              H�      `�      x�      ��      ��      ��      ָ      ��      ��      
�      �              ,�              6�      F�              V�      f�      p�      x�      ��              ��              ��      ��      ¹      ̹      ֹ              �      �      ��      
�      �      .�      F�      \�      j�      r�      ��      ��      ��      к      �      ��      �              �      �      .�      <�      V�      r�      |�      ��      ��              ��      ��      ��      ��              ��      ̻      ڻ      �              _ idevice_device_list_extended_free d idevice_events_subscribe  e idevice_events_unsubscribe    f idevice_free  h idevice_get_device_list_extended  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_service_descriptor_free � lockdownd_start_service   8syslog_relay_client_free  9syslog_relay_client_new   >syslog_relay_start_capture_raw     cprintf   4 term_colors_set_enabled   DeleteCriticalSection =EnterCriticalSection  KGetEnvironmentVariableW tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery  h strtol   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc   realloc 
 __setusermatherr   __C_specific_handler  xmemcpy  |strchr  }strrchr ~strstr   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf  � fflush  � fputc � fwrite  � puts  6 _strdup � strcmp  � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset   �   �   �   �   �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  libimobiledevice-glue-1.0.dll   (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  KERNEL32.dll    <�  api-ms-win-crt-convert-l1-1-0.dll   P�  P�  api-ms-win-crt-environment-l1-1-0.dll   d�  d�  d�  d�  d�  api-ms-win-crt-heap-l1-1-0.dll  x�  api-ms-win-crt-math-l1-1-0.dll  ��  ��  ��  ��  ��  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll Ȱ  Ȱ  Ȱ  Ȱ  api-ms-win-crt-string-l1-1-0.dll    ܰ  ܰ  ܰ  ܰ  api-ms-win-crt-time-l1-1-0.dll                                                                                                                                                                                                                                                                                                                                                                          0 @                    @                   P$ @    $ @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          @     ��   P  P    � �@�`�������� � �@�`�������� �p�������Тآ����� ���� �0�8�H�P� `     @�`�h�p�x�   p  @    �� �0�@�P�`�p�����������Ф�� �� �0�@�P�`�p��������� �     � �8�@�                                                                                                                                                                                                                                                                                                                                ,               @   $                      <    �&       P @   c      pD @                         ,    j]       @# @   �                           �c                           �i                       ,    Zj       $ @                              �k                       ,    el        $ @   �                           ,t                           �t                       ,    1v       �$ @   �                       ,    gy       �% @                              �y                       ,    �z        & @   =                      ,    ۑ       @+ @   L                           Д                       ,    X�       �+ @   �                      ,    9�       P- @   b                          �                           ��                       ,    e�       �/ @   �                      ,    7�        4 @   �                          s�                       ,    �       �? @                          ,    ��       �? @   H                       ,    a�        @ @   2                           �                       ,    ��       @@ @                                                                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   �6   �  5GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden   �  �          9  char {   size_t #,�   long long unsigned int long long int short unsigned int int long int unsigned int _iobuf !
"  _Placeholder #"    6FILE /�   __p_sig_fn_t 0F  K  !V  �    unsigned char double float long double �   long unsigned int +optind �   +optarg �  {   �  option  >  name @   has_arg A�   flag B�  val C�    �  �     �   Gg  no_argument  required_argument optional_argument  DWORD ��    u  7signed char short int _Float16 __bf16 ,JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  �  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   ,VARENUM �   		N  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � uint8_t 
$V  uint16_t 
&�   uint32_t 
(�   �   'H  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 0�  "n  2 l  #n  idevice_t 3�  `  idevice_options �   9�  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  idevice_connection_type �   @E  CONNECTION_USBMUXD CONNECTION_NETWORK  idevice_info F�  udid G�   product_id Ho  -  I�  conn_data J"   idevice_info_t L�  E  idevice_event_type �   P	  IDEVICE_DEVICE_ADD IDEVICE_DEVICE_REMOVE IDEVICE_DEVICE_PAIRED  8X	O	  event Y�   udid Z  -  [�   idevice_event_t \	  O	  idevice_event_cb_t `�	  �	  !�	  �	  "   g	  idevice_subscription_context_t c.�	  �	  9idevice_subscription_context �   $�  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ lockdownd_error_t P�	  ")  R)�  #)  lockdownd_client_t S#  �  lockdownd_service_descriptor `y  port a^   ssl_enabled b
N  identifier c�   lockdownd_service_descriptor_t e.�    �   
&y  SYSLOG_RELAY_E_SUCCESS  SYSLOG_RELAY_E_INVALID_ARG SYSLOG_RELAY_E_MUX_ERROR ~SYSLOG_RELAY_E_SSL_ERROR }SYSLOG_RELAY_E_NOT_ENOUGH_DATA |SYSLOG_RELAY_E_TIMEOUT {SYSLOG_RELAY_E_UNKNOWN_ERROR �~ syslog_relay_error_t 
.�  "L  
0,�  #L  syslog_relay_client_t 
1&�  �  syslog_relay_receive_cb_t 
4�  �  !  {   "   quit_flag �   	�� @   exit_on_disconnect �   	�� @   show_device_name �   udid !�  	�� @   proc_filters "�  	�� @   num_proc_filters #�   	�� @   proc_filter_excluding $�   	�� @   pid_filters &
�  	�� @   num_pid_filters '�   	�� @   msg_filters )�  	�� @   num_msg_filters *�   	x� @   trigger_filters ,�  	p� @   num_trigger_filters -�   	h� @   untrigger_filters .�  	`� @   num_untrigger_filters /�   	\� @   triggered 0�   	X� @   device 2q  	P� @   syslog 3�  	H� @   .�   =  :�   c ,  QUIET_FILTER 5=  	�k @   use_network 7�   	@� @   line 9�  	8� @   line_buffer_size :�   	4� @   lp ;�   	0� @   
memcpy 2"  �  "  u  �    
strlen @�        
strrchr ]�  $    �    fwrite ��   M  z  �   �   R   $  M  
strncmp V�   {      �    strtol �   �    �  �    
strstr `�  �       
cprintf S�   �    $ fflush s�   �  M   
syslog_relay_client_free 
^y    �   
syslog_relay_start_capture_raw 
�y  X  �  �  "   lockdownd_service_descriptor_free >�  �  y   
syslog_relay_client_new 
Dy  �  q  y  �   �  
lockdownd_client_free ��  �  �   
lockdownd_start_service ��  "  �    "   y  
idevice_free �H  F  q   
lockdownd_client_new_with_handshake ��  �  q  �     �  
idevice_new_with_options �H  �  �    �   q  
idevice_events_unsubscribe �H  �  �	   %Sleep   g   
idevice_events_subscribe {H  ;  ;  l	  "   �	  malloc "  Z  �    fprintf ��   {  R    $ 
idevice_device_list_extended_free �H  �  �   �  
idevice_get_device_list_extended �H  �  �  �   �  
strcmp ?�          
getopt_long M�   A  �   A    F  �   �    printf ��   f    $ %term_colors_set_enabled P�  �    ;exit � �  �    realloc "  �  "  �    
strdup l�  �     <free �  "   
__acrt_iob_func ]M    �    %idevice_set_debug_level l5  �    
signal <1  S  �   1   =main ��   pD @         ��#  &argc ��   �  �  &argv ��  
  �  include_filter ��   [  K  exclude_filter ��   �  �  include_kernel ��   �  �  exclude_kernel ��   �  �  c ��   L    longopts ��#  ��{num ��   ��zdevices ��  ��{context �!�	  ��{�F @   d       �  new_msg_filters 4�  !    �F @   �  �  Q��z !G @   �   6F @   d       :  new_trigger_filters D�  ;  7  ]F @   �  ,  Q��z �F @   �   ~  �  new_untrigger_filters T�  U  Q  �G @   �  �  Q��z �G @   �  7L @   �  �  R2 QL @   |6  �  R	�` @   Q1XH [L @   �  R1  oH @   U       >  i ��   q  k  �H @   �  )  Ru Qv  �H @   �  Ru   �I @           �  i ��   �  �  J @   �  J @   �  Rt   ;J @           �  i ��   �  �  OJ @   �  [J @   �  Rt   gJ @             i ��   �  �  {J @   �  �J @   �  Rt   �J @           b  i ��   �  �  �J @   �  �J @   �  Rt   �D @   �6  �D @   5  �  R2Qt  �D @   5  �  R?Q	P @    �D @     �  R| Qs Xu Yv w 0 LE @   �  �  R2 fE @   |6  !  R	`h @   Q1X- sE @   "5  H  Q1�#  | �#  s  �E @   \.  �E @   K  �  R	h @   Q	�` @   X	�g @    F @   �  F @   �  �F @   \.  �  R	�k @    XG @   f  �  R0 iG @   �6  �  R	�k @    xG @       R1 �G @   "5  ;  Q0�#  | �#  s  %H @   �  R  R2 ?H @   |6  |  R	�h @   Q1X* LH @   "5  �  Q1�#  | �#  s  �H @   �  �  R2 �H @   |6  �  R	0h @   Q1X* �H @   "5     Q1�#  | �#  s  RI @   �  +   R��{Q��z \I @   {  ~I @   �  O   R2 �I @   Z  t   Q	(i @   Xs  �I @   @  �   R
  �I @     �   R��{Q	p @   X0 �I @   �  �   R
� �I @   �  �I @   �$  /J @   �  �J @   �  �J @   �  �J @   \.  1!  R	�h @    K @   \.  P!  R	�h @    $K @   �  g!  R2 >K @   |6  �!  R	(g @   Q1X( KK @   "5  �!  Q1�#  | �#  s  [K @   �  �!  R2 uK @   |6  �!  R	g @   Q1XO �K @   "5  "  Q1�#  | �#  s  �K @   �  6"  R2 �K @   |6  `"  R	�g @   Q1X0 �K @   "5  �"  Q1�#  | �#  s  �K @   �  �"  R2 �K @   |6  �"  R	Xg @   Q1X0 �K @   "5  �"  Q1�#  | �#  s   L @   �  #  R2 L @   |6  0#  R	�g @   Q1X2 'L @   "5  W#  Q1�#  | �#  s  aL @   �  {L @   |6  R	�h @   Q1X[  .  �#  >�    /print_usage ��#  argc ��   argv �*�  is_error �4�   name ��   0clean_exit �P @   5       �\$  &sig ��   �  �  _ @   �  6$  R2 y @   |6  R	 ` @   Q1X<  /device_event_cb ��$  event �4�	  userdata �A"   0stop_logging �� @   T       ��$  � @   �  �$  R1 � @   �  � @   �  � @   '   ?start_logging K�   X%  ret MH  lockdown S�  lerr T�  svc ]!y  serr ry   1syslog_callback s� @   �      �*.  'c s"{       'user_data s+"  T  P  h   4&  	_line w	�  r  l  ' @   �  �%  Rs  � @   �  �%  R2 � @   |6   &  R	5` @   Q1XF  @   �  R1  2   	shall_print ��   �  �  	trigger_off ��   &    	linep �	�  h  V  �   �,  	end ��  �  �  	p ��  5    	device_name_start ��  �  �  	device_name_end ��  	  �  	proc_name_start ��  ]  W  	proc_name_end ��  �  {  	process_name_start ��  �  �  	process_name_end ��  	  	  	pid_start ��  X	  R	  	pp ��  }	  u	  	proc_matched �	�   �	  �	  level_start �   
  �	  level_end �  N
  :
  level_color 	  �
  �
   @   "       N(  h  �
�   9  7  i �
�   ( @   �  Rv   �   �(  h  �
�   K  G  i �
�   � @   �  Rv   P @   $       �(  @h  �
�   i �
�   i @   �  Rv     <)  endp ��  ��B  �
�   k  a  !  !)  h  ��   �  �  	i ��   �  �   � @   {  Q��X:  g @   _       �)  h  �
�   �  �  	i �
�       � @   W  Qv X��  (*.  � @    �   �
�)  
Q.  :  8  
I.  I  G  
A.  ]  [   (*.  t @    �   �
*  
Q.  p  l  
I.  �  �  
A.  �  �   (*.  � @    �   �	U*  
Q.  �  �  
I.  
  �  
A.  5
  /
   A*.  � @    � @   "       �
�*  
Q.  Z
  X
  
I.  i
  g
  
A.  }
  {
   � @   W  �*  R~ Q	L` @   X9  @   �  �*  R	s` @    ' @   �  +  R1 A @   $  +  Q1X@ M @   �  >+  R	{` @    Y @   �  U+  R1 o @   $  {+  Rv Q1X} v  { @   �  �+  R	�` @    � @   �  �+  R1 � @   $  �+  R} Q1Xu � @   �  �+  R�� � @   �  8 @   �  ,  R1 Q @   $  ),  Q1 p @   W  S,  R~ Q	V` @   X8 � @   W  },  R~ Q	_` @   X: X @   W  R~ Q	j` @   X8  w @   �  �,  R	-` @    � @   �  �,  R1 � @   $  �,  Rs Q1 � @   �  -  R	�` @    � @   �  --  R1 � @   �  N @   �  Q-  R1 e @   $  n-  Rs Q1 q @   �  �-  R	�` @    x @   �  �-  R1 � @   �  h @   �  �-  R1  @   $  �-  Rs Q1 � @   �  .  R	�` @    � @   �  .  R1 � @   �    Bfind_char i�   \.  )c {   )p %�  )end 4   1add_filter =� @   �      ��0  'filterstr =$  �
  �
  	filter_len ?�   �
  �
  	start @  �
  �
  	end A  �
  �
  	p B  ,    1  �0  	procn F�  o  e  endp M�  ��B  N	�   �  �  � @   :       u/  	new_pid_filters P�  �  �  � @   �  Q~   B  
0  	new_proc_filters Y
�  �  �  � @   �  �/  Q~  = @   �  �/  R2 W @   |6  �/  R	�` @   Q1XH b @   �  R1  9 @   @  "0  Rt S @   �6  F0  Rv Q~ Xt  r @   {  i0  Rv Q} X:  @   �  �0  R2 ( @   |6  �0  R	�` @   Q1XG 2 @   �  R1  � @   �  R~   3\$  p @   �      �"5  
s$  �  �  
�$  1  %  C\$  � @     S  �

�$  o  g  
s$  �  �  D�$  B @    f  �	f4  2f   %  �  �  4 %  �@ 1%  
  �  4>%  �H J%  i  ]  _ @   �  �1  R	P� @   Qt X	�0s  $0)( 	�# � @   F  �1  Q�@X	�` @    � @   �  2  Qu Xv  � @   �  � @   �  ;2  X	H� @    � @   X    @     l2  Q	� @   X0 3  @   �  �2  R1 E  @   Z  �2  Q	�a @   Xs  L  @   �  �2  R1 T  @   �  �  @   �  �2  R2 �  @   |6  
3  R	8a @   Q1XO �  @   �  &3  R
� �  @   �  D3  Qu Xv  �  @   �  	! @   Z  v3  Q	�` @   Xs  ! @   '  P! @   �  �3  R2 j! @   |6  �3  R	�a @   Q1X* v! @   �  �! @   '  �! @   �  �3  R2 �! @   |6  4  R	�a @   Q1X7 �! @   '  �! @   �  C4  R2 
" @   Z  Q	�` @   Xs    � @   �  � @   �$  � @   �  �4  R1 � @   Z  �4  Q	$b @   Xs  : @   �  �4  Rt  m  @   �  �4  R�� -! @   �  5  R2 EI! @   Z  Q	 b @      3�#  " @   �       �|6  
�#  �  �   �#  �  �  
�#  �  �  
�#  �  �  %" @     �5  Rs Q/ B" @   �  �5  R2 T" @   Z  �5  Q	7b @   Xs  [" @   �  �5  R2 u" @   |6  
6  R	Pb @   Q1X
� F�" @   �6  ,6  R	�f @    �" @   �  C6  R1 �" @   Z  h6  Q	7b @   Xs  �" @   �  R1  *fwrite __builtin_fwrite G__main __main *puts __builtin_puts *memcpy __builtin_memcpy  ]   �	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @# @   �       "  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	�� @   atexit ��   �  Y   __main 5�# @          ��  $ @   �   	__do_global_ctors  �# @   j       �  
nptrs "�        
i #�   8  4  �# @   j  R	@# @     	__do_global_dtors @# @   :       �[  p [  	 R @    	   �   
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  {  I  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
~  �   �,  __uninitialized  __initializing __initialized  ~  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	4R @   �  	0R @   =  
"	ؠ @   [  	Р @    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _dowildcard  �   	@R @   int  }   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 @  (  $ @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   $ @          � �    (  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  /  _newmode �   	� @   int  �   V  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 {  c   $ @   �       i  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	�� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	`o @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	@o @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	�� @   __mingw_initltsdyn_force ��   	�� @   __mingw_initltssuo_force ��   	� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@   $ @   /       �}  
�  �  S  O  
�  *M  e  a  
�  ;d  w  s  E$ @   �   __tlregdtor m�   �$ @          ��  func m  R __dyn_tls_init L@  	  �  �  �  *M  �  ;d  pfunc N
$  ps O
�    �  P$ @   �       ��  �  �  �  �  �  �  �  �  �  �  �  �$ @    �$ @   +       L�  �    �  �      �      �  1  -  �  E  A   �$ @   �    �    =  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 i  Q    _commode �   	 � @   int  w   k  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  "	  �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  �$ @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   �$ @   �       �0  pexcept 0  l  f  type 
�  �  �  M% @   b  �  R2 v% @   7  Q	�p @   Xs Yt w �ww(�ww0�w  5   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 d
  L
  �% @          �  _fpreset 	�% @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �
  	  __mingw_app_type �   	� @   int  G   ,  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   & @   =      C  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	(� @   	�  maxSections �%  	$� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator ��' @   ]      ��  4was_init �%  	 � @   5mSecs �%  �  �  !�  e( @   �  �4  �  �  �  6�  
�    �  
�  t  D  
�  �  s  
�      

  0  $  
  m  ]  "E  �  �  
F  �  �  
[  �  �  y) @   `  R	�q @   Xu w t     ) @   ) @          �;  �      �  '  %  �  6  4    ) @   ) @          �  @  >  �  K  I  �  Z  X  ) @   �  Ru    !  �) @   �  ��  �  d  b  �  o  m  �  ~  |  7  �) @   �  �  �  �  �  �  �  �  �  �  �) @   �  Ru      �* @   �* @   
       �w  �  �  �  �  �  �  �  �  �    �* @   �* @   
       �  �  �  �  �  �  �  �  �  �* @   �  Ru      �* @   �* @          �   �  �  �  �  �  �  �        �* @   �* @          �      �  #  !  �  2  0  �* @   �  Ru    "$  �  �  
)  @  :  83  �  
4  Z  X    + @   + @   
       s�  d  b  �  o  m  �  ~  |    + @   + @   
       �  �  �  �  �  �  �  �  �  + @   �  Rt      
0+ @   `    R	�q @    =+ @   `  R	�q @      9�  �) @   X       �|  
�  �  �  :�  ��/* @   
  Yu   '( @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �p& @   b      �`  &addr ��  �  �  b �:  ��h �g    �  i �%  7  1  >P' @   P       �  new_protect �
u  P  N  
�' @   
  �  Ys  �' @    
  �' @   `  R	Xq @     
�& @   �
  �  Rs  �& @   n
  
!' @   E
    Q��X0 
�' @   `  >  R	 q @    �' @   `  R	 q @   Qs   ?__report_error T & @   i       �/  &msg T  \  X  @argp ��   �X
,& @     �  R2 
F& @   /  �  R	�p @   Q1XK 
U& @       R2 
c& @   �
  !  Qs Xt  i& @   �
   Afwrite __builtin_fwrite   �     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99  
  �  @+ @   L       �  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	0� @   
__setusermatherr ��  �   __mingw_setusermatherr ��+ @          �P  f ,�  w  s  �+ @   �  R�R  __mingw_raise_matherr �@+ @   >       �typ !�   �  �  name 2�  �  �  a1 ?w   �  �  a2 Jw   �  �  rslt 
w   � ex 0  �@y+ @   R�@   �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  q   _fmode �   	@� @   int  �   D  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 i  Q  �+ @   �      �   char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	P� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   �+ @   �      ��  'exception_data �-�  �  �  old_handler �
	  8  (  action ��   �  o  reset_fpu ��     �  
�+ @   �
  �  R8Q0 (, @   �  R�R 
G, @   �
  �  R4Q0 ], @   �  R4 
�, @   �
    R8Q0 
�, @   �
  7  R8Q1 
�, @   �
  S  R;Q0 �, @   f  R; - @   y  R8 
- @   �
  �  R;Q1 
/- @   �
  �  R4Q1 
C- @   �
  �  R8Q1 )H- @   �
   �	   �
   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 z  b  P- @   b      8"  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	�� @   __mingwthr_cs_init �   	h� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	`� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �. @   �       �n  	hDllHandle z�  \  D  	reason {<  �  �  	reserved |S  Z  B   5/ @   K       �  
keyp �&�  �  �  
t �-�  �  �  T/ @   �  
{/ @   C  R	�� @     !n  / @   / @          �  �  / @   )
   "n   / @   
  �E  #
  �  �/ @   )
    �/ @   6  
�/ @   e  R	�� @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   0. @   �       �d	  	key A(<  �  �  
prev_key C�      
cur_key D�  4  ,  `. @   �  B	  Rt  �. @   �  
�. @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �- @   o       �$
  	key *%<  [  Q  	dtor *1.  �  �  
new_key ,$
  �  �  �- @   �  �	  R1QH 
. @   �  
  Rt  
(. @   �  Rt   �  &n  P- @   p       ��  �  �  '�  �- @          �
  �  �  �  �- @     �- @     (�- @   Rt   j- @   �  �
  R|  )�- @   �  R	�� @      �    #  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �$  _CRT_MT �   	PR @   int  �    Q  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ;  #  �$  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	�� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	�� @    �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �/ @   �      $%  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  �  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  
  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  
  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   �  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �2 @   �       �7
  i �(�       �  �	`  �  �	  &  "  importDesc �@  D  B  �  �^
  importsStartRVA �	I  T  L  �  �2 @   	�  ��  �  �  �  �  �  	�  3 @    �  �  �  �  �  �  �  �  �  �      M  43 @   43 @   J       �q  �  �  f  }  �  �  �  �  �  �  �  �    
_IsNonwritableInCurrentImage �  `2 @   �       ��  pTarget �%`  	    �  �	`  rvaTarget �
�  .  ,  �  �^
  8  6  �  `2 @   �  �/  �  �  �  �  �  	�  p2 @    �  �  �  �  �  D  @  �  U  S      M  �2 @   �2 @   I       �q  a  _  f  }  k  i  �  u  s  �    }    
_GetPEImageBase �`   2 @   6       �0  �  �	`  	�   2 @   �  �	�  �  �  �  �  	�  02 @    �  �  �  �  �  �  �  �  �  �       
_FindPESectionExec y^
  �1 @   s       �%  eNo y�   �  �  �  {	`  �  |	  �  �  �  }^
  �  �    ~�   �  �  	�  �1 @   t  �	�  t  �  �  �  	�  �1 @    �  �  �  �  �  �  �  �  �  �       
__mingw_GetSectionCount g�   `1 @   7       ��  �  i	`  �  j	  �  �  	�  `1 @   Y  m	�  Y  �  �  �  	�  p1 @    i  �  i  �  �      �           
__mingw_GetSectionForAddress Y^
  �0 @   �       �  p Y&s  )  !  �  [	`  rva \
�  N  L  �  �0 @   3  _�  �  3  �  �  �  	�  �0 @    C  �  C  �  �  Z  V  �  k  i      	M  1 @   N  c
q  w  u  f  N  }  �    �  �  �  �  �  �     
_FindPESectionByName :^
  @0 @   �       �M  pName :#�  �  �  �  <	`  �  =	  �  �  �  >^
  �  �    ?�   �  �  �  U0 @   (  F  �  (  �  �  �  �  e0 @    e0 @          �  �  �  	      �           &O0 @   �  -  Rt  '�0 @   z  Rs Qt X8  _FindPESection $^
  �  �  $`  (rva $-�  �  &	  �  '^
    (�    _ValidateImageBase   �  �  `  pDOSHeader �  �  	  pOptHeader v   )�  �/ @   ,       �~  �  !      �  3   /   �  �  	�  �/ @    !  �  G   A   !  �  �  a   ]   �  n   l      *M  �/ @   P       �f  z   v   +q  Q}  �   �   �  �   �   �  �   �     8     "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   4 @   �      o*  
__gnuc_va_list �   #__builtin_va_list �   char �   
va_list w   
size_t #,�   long long unsigned int long long int 
wchar_t b
  short unsigned int int long int 	�   6  	#  unsigned int long unsigned int unsigned char double float long double 	�  	6  optind #  optopt #  opterr #  optarg 6  option  >*  name @/   has_arg A#  flag B@  val C#   �  	�   /  $E  G~  no_argument  required_argument optional_argument  _iobuf 0!
  _ptr %6   _cnt &	#  _base '6  _flag (	#  _file )	#  _charbuf *	#   _bufsiz +	#  $_tmpfname ,6  ( 
FILE /~  
DWORD �U  signed char short int WCHAR 1�   E  	E  	S  LPWSTR 5X  LPCWSTR 9]  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS E  �i  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  %tagCOINITBASE E  ��  COINITBASE_MULTITHREADED   VARENUM E  		+  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � �  	�R @   �  	|R @   �  	xR @   &__mingw_optreset D#  	�� @   �  	� @   
place f6  	pR @   
nonopt_start i#  	hR @   
nonopt_end j#  	dR @   �   �  �   ! �  
recargchar m�  	�s @   
recargstring n�  	`s @   �   :  �    *  
ambig o:  	0s @   �   f  �   ' V  
noarg pf  	 s @   �   �  �    �  
illoptchar q�  	�r @   
illoptstring r�  	�r @   vfprintf )#  �  �  4  �    	  �  fprintf "#  	  �  4   __acrt_iob_func ]�  @	  E   '__p___argv ��  strncmp 
V#  w	  /  /  �    strlen 
@�   �	  /   strchr 
D6  �	  /  #   GetEnvironmentVariableW ?  �	  q  b     getopt_long_only O#  p? @           ��
  :  ,#  !  !  4  ,+�
  !!  !  "  ,>/  7!  3!  @  -�
  M!  I!  idx --@  � �? @     R�RQ�QX�XY�Yw � w(5  	;  	*  getopt_long M#  P? @           �q  :  #  c!  _!  4  &�
  y!  u!  "  9/  �!  �!  @   �
  �!  �!  idx  -@  � k? @     R�RQ�QX�XY�Yw � w(1  getopt #   ? @   "       �  :  #  �!  �!  4  !�
  �!  �!  "  4/  �!  �!  =? @     R�RQ�QX�XY0w 0w(0  (getopt_internal C#  �8 @   (      ��  :  C#  "  �!  4  C*�
  ?"  3"  "  C=/  w"  m"  @  D�
  �"  �"  idx D*@  � )flags D3#  �"  �"   oli F6  #  #   optchar G#  E#  9#  **  G#  x#  r#  +posixly_correct H
#  	`R @   ,start g: @      C
  Rt Qu Xs Yv  ~: @   �	  [
  R}  �: @   �  �
  Rv Q} X~ Y�  �: @   �	  �
  R} Qs  F; @   �	  �
  R	�r @   Q0X0 �; @   �	  �
  R} Q- �; @   �	  	  R} Q- �< @      '  Ru Yv  �= @      T  R���Qt Xs Yv  �= @   �  y  R	�r @   Qs  3> @   �  �  R	�s @   QW �> @   �  �  Rv Q} X~ Y� w 0 ? @   �  R	�s @   Qs   -parse_long_options �#  `5 @   �      �   4  �"�
  �#  �#  "  �5/  �#  �#  @  ��
  �#  �#  idx �*@  �#  �#  .*  �3#  � current_argv �6  �#  �#  has_equal �6  $  $  current_argv_len �	�   b$  Z$  i �#  �$  $  ambiguous �	#  �$  �$  match �#  %  �$  �5 @   �	  /  Ru Q= 86 @   S	  S  Ru Q~ Xt  D6 @   w	  k  R~  7 @   �  �  R	0s @   Qt Xu  �7 @   w	  �  Ru  8 @   �  �  R	�r @   Qu  &8 @   �  �  R	 s @   Qt Xu  �8 @   �  R	`s @   Qu   !permute_args � 4 @   �       ��  panonopt_start �#  :%  4%  panonopt_end �&#  X%  P%  opt_end �8#  {%  u%  4  ��
  �%  �%  cstart �#  �%  �%  cyclelen �#  �%  �%  i �#  �%  �%  j �#  �%  �%  ncycle �#  $&  "&  nnonopts �&#  0&  *&  nopts �0#  R&  L&  pos �7#  v&  n&  swap �6  �&  �&  /�  4 @    �  ��  �&  �&  �  �&  �&  0�  1�  �&  �&     2gcd �#  �  a �	#  b �#  3c �#   !warnx ~�4 @   �       ��  fmt ~/  '  �&  
ap ��   �X4�  �4 @    �4 @   W       �  '  '    +'  ''  5�4 @   @	  5 @   	  u  R2 5 @   �  �  Q	�r @   Xu  $5 @   	  �  R2 25 @   �  �  Qs Xt  <5 @   	  �  R2 J5 @   "  R:   6_vwarnx u"  fmt u/  ap u!�    7fputc __builtin_fputc 
  �    �   GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  �3  _MINGW_INSTALL_DEBUG_MATHERR �   	�R @   int  �   �   GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �? @          
4  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	�R @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  �? @          �_File *�  L'  F'  _Format J�  e'  _'  _ArgList Z�   ~'  x'  �? @   �  R0Q�RX�QY0w �X   �   �!  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �? @   H       |4  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	�R @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �? @   H       �_Format .�  �'  �'  
ap 
�   �Xret 	  �'  �'  �? @   �  �  R1 �? @   �  R0Xs Y0w t    �   _#  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  j   @ @   2       5  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�R @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	   @ @   2       �_File )�  �'  �'  _Format I�  �'  �'  
ap 
�   �hret 	  �'  �'  -@ @   �  R0Q�RX�QY0w �   �   �$  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 g  O  �5  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	� @   local__winitenv 
`  	 � @   '  
	�R @     
	�R @    �   i%  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  l  @@ @         �5  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	PS @   �      �   __imp_at_quick_exit g)  	HS @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	DS @   
initial_tzname1 }
V  	@S @   
initial_tznames ~�  	0S @   
initial_timezone 
%  	,S @   
initial_daylight �  	(S @   __imp_tzname ��  	 S @   __imp_timezone ��  	S @   __imp_daylight ��  	S @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	S @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	 S @   �	  __imp__amsg_exit �V  	�R @   F  __imp__get_output_format �\
  	�R @   -
  __imp_tzset ��  	�R @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�R @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �A @   5       ��
  file �!�
  (  (  fmt �6�  6(  0(  
ap ��   �h+ret �  Q(  O(  �A @   �
  R4Q�RX�QY0w �  ,tzset "�A @   6       �  -  �A @   �A @   -       ��A @   +  �A @     
B @       ._tzset �/_get_output_format nF  @@ @          �0_amsg_exit ipA @   .       ��  ret i  ](  Y(  �A @   z  �  R2 �A @   Q  �  Q	�s @   Xs  �A @   <  R�  at_quick_exit �  PA @          �   func ]*�  p(  l(  1eA @   �   _onexit ��  0A @          �p  func V%�  �(  �(  =A @   �  Rs   __wgetmainargs J  �@ @   j       �[  _Argc J"�  �(  �(  _Argv J5I  �(  �(  _Env JGI  �(  �(   M  JQ  )  �(  !Y  Jl�	  � �@ @   0  �@ @   	  &  R	v  $0.# �@ @   �  �@ @   �  	A @   �  A @   U   __getmainargs >  P@ @   j       �E  _Argc >!�  #)  )  _Argv >1S  <)  6)  _Env >@S  U)  O)   M  >J  n)  h)  !Y  >e�	  � p@ @   �  �@ @   �    R	v  $0.# �@ @   �  �@ @   �  �@ @   u  �@ @   U   2   B @   6       �2B @   +  >B @     JB @                                                                                                                                                                                                                             
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  H}   I  (   ( 
  H }   !I  4 :!;9I  	4 :!;9I�B  
.?:;9'I<  H}   :;9I  
 1�B  I �~  $ >  4 :!;9I�B  
 :;9I8    (   .?:;9'I<  U  4 :!;9I  4 :!;9I�B  & I   :!;9I  :;9  7 I  >!I:;9  >!!I:;9  4 :!;9I  4 :!;9I   4 1�B  !'  " :;9I  # <  $   %.?:;9'<  & :!;9I�B  ' :!;9I�B  (1R�BUX!YW  ) :!;!� 9I  *. ?<n:!;!   +4 :!;9I?<  ,>!!I:;9  -
 :!;9!I8  .I  /.:!;9!
' !  0.:!;9!
'@z  1.:!;9!
'@z  2U  3.1@z  44 1  5%U  6   7&   8:;9  9 <  :! I/  ;.?:;9'�<  <.?:;9'<  =.?:;9'I@z  >! I/  ?.:;9'I   @4 :;9I  A1R�BXYW  B.:;9'I   C1R�BUXYW  D1R�BUXYW  EH}�  FH}�  G. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   I ~  (   H}  $ >   I   :!;9I�B  4 :!;9I�B  
 :;9I8  	 !I  
4 :!;9I  & I  (   
 :;9I  H}  .?:;9'I<   :!;9I�B  4 :!;9I?<  4 G  I  ! I/   :!;9I�B   1�B   :!;9I   :!;9I  .?:!;9!'I@z   :!;9I  :;9  7 I  >!!I:;9  .?:!;9!'I<      4 :!;9I�B  !.:!;9!'@z  "%  # I  $>I:;9  %>I:;9  &4 :;9I?  '. ?:;9'I<  (.:;9'I@z  ) :;9I�B  *4 :;9I�B  +4 :;9I  ,
 :;9  -.:;9'I@z  . :;9I  /1R�BUXYW  0U  14 1�B  2.:;9'I   34 :;9I  41R�BXYW  5H }  6.:;9'   7. ?<n:;   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                                                                                                                                                                                                                                                          5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �    �   �
        V  o  �  �   �   �  �  �  �  �  
      (  1  D  O  ^  g  p  }  �    	P @   �KX f  <YuM�K �ZtYY
�tYY
��}�	fhr;/	�	f	<
f=�X�~�g^ f��� 6  e �N fj �� �� <Y� �YK���~g;g����t t� <Y� �Yt=�) �~��^"JMW . ��IX(�t�~tM# �. $  J�
 �Y\f�( �  J�
 �	Xf�
J �)X< WtY<IX� K� �� � �XYIt� � X �YIX� ���6 � 	J�~.� 6  N �j �� �� <Y� �Y�~f$t( J, �  JY
 �]�M%t$tJguL� �~ -/�U� �~ � K�)Z<� 	�qLf�Y H;H�;
JY�
 �) ,  J_	�	�	f
 J�K�. � <rXY .  <Z� �� <Y� �� <[��
?
9=;gX=�~tXn �  <Y�N �tj �  qX ��= ;Y^� JKKD.>
X=WY�/
	X X X��	  JKKP. vt$/q=/J s t/ JKK	Q..	gt X(�qt<f��~��<qXYJ=<"�^JYtJK	tX
>^J$X�J_.!J���Ye��	X XYPfPt<<�	�u#Yxf
�W fWt< <�	\u%Y�h �  <Y� �  <Y��� f<M X f<
V.	XY � J t XKY  t <��tqt�uI� ���  < � t��-�X/�	!��	��
���	u	W/Y�	Y	� ֞ <Y �Z�<
 ���� t  <Y
y
o�
}��W| t� <Y�
�> �t <
< s�TX t  <Y�
s�Y
�r. t  <Y�
�]Xy Jt <�
� fh
0
V>Y  � J t t � <Y  �fue `X t t � <Y   	pD @   �4r;;@zX���yt��yt^`O �O �Y�� �+f�� �  <Y����L	��Y8X�X�v� < ..�~ 	���	 �u'X	��_"f_t"��,;�,* X��PXsKYS�X	��SfSt��$;�$" X��"XKZKXuS��X�� X�fX	��e$fet$��0;�0. X��FXX� X� �  <Y�
[��
�' �  J�
t�
J ����h� �  <Y�
X
�
�  ��
�/pKWY��uuW� f� <[�	� �v!uZs/	s��Zfit IK& W M���fit IK% W M�fit IK) W M�fit IK+ W M���	
�X
��X���X�� �  <Y�hX �  <Y�,X �  <Y�nX �  <Y�X �  <Y�\ �  <Y��  f  <Y
 #    K   �
      �  �    6  @  J  R  _  h  r   	@# @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �  �      J  U  ]  j  s  ~  6     .   �
      �  �      R     .   �
      q  �  �  �   	$ @    6     .   �
        &  M  X      K   �
      �  �  �        !  -  7  ?   	 $ @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �  �  �  �  6     .   �
      :	  R	  y	  �	  �     <   �
      �	  �	  
  /
  6
  =
  D
   	�$ @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �
  �
  �
  �
   	�% @   		3 6     .   �
      :  R  y  �  |    s   �
      �  �  &  A  P  _  h  r  ~  �  �  �  �  �  �  �  �  �    	 & @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	�' @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      5
  M
  t
  �
  �
  �
   	@+ @   
L�Z
�KTYZ
g=yuX 6     .   �
      �
    ;  F  �    U   �
      �  �  �  	�        (  2  C  P  Y   	�+ @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      �  �  �        #  /  9  A  N  Y  b  v   	P- @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      �  �      6     .   �
      v  �  �  �  G    K   �
      %  =  d    �  �  �  �  �  �   	�/ @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< ]	    n   �
        #  K  f  o  x  �  �  �  �  �  �  �  �  �  �  �    	 4 @   ��/x<_//a</�< 9zXL  X�  J"�
y<	�8	�M 
A	t>> w<  G  J�
= <    TXpfuV"Ys	 X � � <Y �f <Y	 �X < f	X� �bur�p<�Vrt��X <� = I=   Ju�.	p�<d�Xk
��% j�  <% J JZ �N �
��
Y
 ��<�J�</X�J�
�
� Xf <�� ��f^�<KY ��� ��8<Z�	t��2K;�/
���X� � �P<	X�X�XJ�w� ��	��+̟^X!&��w�!!��
A�
tvf
�( � f�X�
 ��	������f���
���f	�fY�-0��
�N  �� �, X t5 t�� � ��
�0�t<J<t, �
�. ��3 J- �����~�
�	�Xt	fxfK  �gX�	M
J
�' I� �� ��'tXu
�. �!��
�nJfgt	u	;u��P�	K
<
ew
cu	^]h��
Q�X�...�X<ugit0,�,Z� �J<t
�gu ��
g �~�	K;Xf X � J+ ��
zf=sg
y�f*Je.	
�!t�f<�
e� Ju� ����k� X
.�
�	�
	X�<��z��	 �u�g
t
J
�
.L
h	XL
h 6     .   �
      @  X  �  �  n     A   �
      �  	  2  M  ]  m  v  �   	�? @   K
�<.Y �     A   �
      �  �    3  A  O  X  b   	�? @   g?	YT�Y	 X� <uX �     A   �
      �  �  �    %  4  =  G   	 @ @   KU	\fp	\;Y	Y W     O   �
      �  �  �  �  !  -  5  B  K  V  b  |    h   �
      �  �  �    B  T  f  m  v  �  �  �  �  �  �  �   	@@ @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	pA @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                                                                                                                                              ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �         P  P @   5       D0p     P  � @   T       D0O l   P  � @   �      B�B�B �B(�A0�A8�A@�AH�	D�{
HA�@A�8A�0A�(B� B�B�B�I      d   P  � @   �      B�B�B �A(�A0�A8�A@�DpV
@A�8A�0A�(A� B�B�B�B       l   P  p @   �      B�A�A �A(�A0�DpV
0A�(A� A�A�B�J�
0A�(A� A�A�B�E      4   P  " @   �       A�A�D@j
A�A�E    l   P  pD @         B�B�B �B(�A0�A8�A@�AH�	G�Y
HA�@A�8A�0A�(B� B�B�B�A       ���� x �         �  @# @   :       D0u  4   �  �# @   j       A�A�D@@
A�A�H       �  �# @             ���� x �            $ @             ���� x �      $   P   $ @   /       D0R
JN    L   P  P$ @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       P  �$ @             ���� x �      <   �  �$ @   �       A�A�D�P�
���
���A�A�B    ���� x �         P  �% @             ���� x �      $   �   & @   i       A�A�DP   <   �  p& @   b      A�A�A �Dp�
 A�A�A�D   \   �  �' @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         `  @+ @   >       D`y     `  �+ @             ���� x �      4   �  �+ @   �      A�D0}
A�Mf
A�I     ���� x �      L      P- @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <      �- @   o       A�A�A �D@U
 A�A�A�A    D      0. @   �       A�A�D@R
A�A�FR
A�A�D      4      �. @   �       A�D0p
A�J�
A�A      ���� x �         (  �/ @   ,          (  �/ @   P       L   (  @0 @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       (  �0 @   �          (  `1 @   7          (  �1 @   s          (   2 @   6          (  `2 @   �          (  �2 @   �          ���� x �      T   P	   4 @   �       B�B�A �A(�A0�A8��
�0A�(A� A�B�B�A      <   P	  �4 @   �       A�A�A �DPw A�A�A�      l   P	  `5 @   �      B�B�B �B(�A0�A8�A@�AH�	D�e
HA�@A�8A�0A�(B� B�B�B�G    l   P	  �8 @   (      B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�C       P	   ? @   "       D@]     P	  P? @           D@[     P	  p? @           D@[     ���� x �         @  �? @          D@Y     ���� x �      ,   x  �? @   H       A�A�D`A�A�   ���� x �         �   @ @   2       DPm     ���� x �         �  @@ @          L   �  P@ @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �  �@ @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �  0A @          A�D0WA�    �  PA @             �  pA @   .       A�D0   �  �A @   5       DPp     �  �A @   6       D0q     �   B @   6       D0q                                                                                                                          Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion conn_type lockdownd_client_private pid_value syslog_relay_client_private found idevice_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection options short_too nargv nargc long_options _DoWildCard _StartInfo                                                                                                                                                             C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools idevicesyslog.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include ../include/libimobiledevice C:/msys64/ucrt64/include/libimobiledevice-glue idevicesyslog.c idevicesyslog.c corecrt.h stdio.h signal.h getopt.h minwindef.h winnt.h combaseapi.h wtypes.h stdint.h libimobiledevice.h lockdown.h syslog_relay.h string.h stdlib.h termcolors.h synchapi.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:/M/B/src/mingw-w64/mingw-w64-crt/misc/getopt.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include getopt.c getopt.c vadefs.h corecrt.h getopt.h stdio.h minwindef.h winnt.h combaseapi.h wtypes.h string.h processenv.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                     �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� B              pD @    2R2�\��	\��\             pD @    2Q2�S��S���Q���	S�	�	�Q���S               �D @    B0�B���z����z����z����z����z����z����z      �D @    B0�B�]��]      �D @    B0�B�^��^      �D @    B0�B�_��_                                                          �D @    B0�_mPmvZv|P|�Z��Z��Z��Z��Z��Z��Z��Z��Z��Z��Z��Z��Z��Z��Z��Z��Z����z��Z����z��
Z�
�
Z�
�
Z��Z��Z     �F @    #P#B��z     ]F @    #P#B��z     �G @    #P#B��z     xH @     S s�LS    �I @    Ss�    ;J @    Ss�    gJ @    Ss�    �J @    Ss�     P @    	R	5�R�             � @    0R0GTG��R���R��T���R�     � @    Q��Q�       * @    P�	�	P�	�	S                       P @    0�c1���1���0���\��\��0���0���1���0���\��\��0���\          P @    c0���0���0���0���0�                 V @     	8� @   ��	8� @   ��S��	8� @   ��	8� @   ��	8� @   �
�
^��	8� @   ��S          � @    �^��^��^��^�	�	^                            � @    2U2�V��U��u���V��V��V��U��u���^��V��	^�	�	R�	�	^�	�	U�	�^            � @    U�s���s���s���s���s�                � @    U s� �U��v���s���U��v���U��v�      t @    �V��V��
V                t @    �V��V��u���~���~���r���u���V��
u�     � @    WV��V��
V             � @    +U+W]��V��U��]��]��U��
]       � @    SR��R��R       � @    V']��V��V          N @    0���0���0���0���q  $0.����q  $0.�              � @    �^��^��R��^��^��^��^                    � @    !^!�_��_��^��_��^��_��^��_��^                � @    !0�!/

` @   �CG[G���������0���
` @   ���0���
` @   ���0���
%` @   ���0�  2 @    1�     � @    
1�EW0�           � @    P8Rh�R��R��R           / @    QDFQFLYLLq ��LeQw�Q      / @    0�P
P    � @    
Y
y  $0)��      g @    0�C\CG|�G_\  � @    ^  � @    �7M     � @     �   t @   -^4 @    ^   t @   -�7M   4 @    �7M      t @   -[�4 @    [�      � @    'U��V��U��U    � @    '�N   ���N   ���N       � @    '(���(���(�  � @   "^  � @   "�7M     � @   "]�       � @    R)^)��R�   � @   P           � @    9^9@S@S^T�^��^��^       � @    ~ | "�H\M�\            � @    ^s�SU5s�59S9>UM�s�           < @    PWV��V��P��V     t @    PdT   � @    P   � @   P                 p @    R'�R�'VRV��R���R���R���R���R�             p @    Q'�Q�'OQO��Q���Q���Q�        � @    Qd�Q�f�Q���Q�             � @    !R!d�R�f�R���R���R���R�     _ @    (P��P                     � @    ,P,6S6PPPpS��P��S��	���P��	���P��P           � @     ��#P#BSBWP��P��S           " @    
Q
+T+w�Q�w~T~��Q�     %" @    PboP   " @   ���#  �   " @   ���#  � 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
 p @   ���
�o @   ���
Pp @   ���
(p @   ���
�p @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  �����     ��U  ��2�  �����     ��U  ��1�  �����     ��U  ��1�  �����     ��U  �	�	4�  �	�	���     �	�	U  �	�	4�  �	�	���     �	�	U  �	�	8�  �	�	���     �	�	U  �	�	8�  �	�	���     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
�ӌ     �
�
T  �
�
4�  �
�
�ӌ     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � ;            ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�             �	�
R�
�\��R��\���R���\           �	�
Q�
�V��Q��V���Q���V           �	�
X�
�]��X��]��]         �	�
Y�
�^���Y���^               �	�
�(���(��_���(��_���(��_       �
�
P�
�p���P��p���P             �
�
P�
�S��-���S��S��S     ��
0��
�
P��0�     ��R��	�      ��Q��	�         ��X��\���X���	\     ��Y��	�     ��U��	U               ��P��X����������P������	���	�	��         ��T��T��P��	T      ��0���_��_                  ��0�����������R��1�������0���	0��	�	0�            ��	����_��_��	����	_�	�	_        WRW��R���R          Q�S���Q���S        WXW��X���X          QYQ�U���Y���U          MW\WgQg�\��|���\   ?�P  ?W0�      Mg0�g�R��r���R��0�  6M]       �V���Q�R���V       �T���X�Q���T       MW\W�Q��Q��\     gsZ��Z       %T%%P%,Q,6]��T     %V%/P��V        ,Q,/]/6Q��Q       ��R��S���R�  ��T    ��R��S T                RQ�R�        QX�Q�        X�`�X� )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                X         Z���� ���� ���� ���� ���� ���� ���� G         @    ���� P @    ����� � @    /6����������	� � @    ( 2 @    ���� t @    -�� � @    '���� � @   ) @    .^f � @    _� � @    ;j� � @    ��� B @    ����� �G @    d�	�	 P @   �pD @   � S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����          6��                                                                                                                                                                                                                                                                            .file   a   ��  gcrtexe.c              �                                �              �   �                        �   �                        �   �                        %  0                        @  �                        `             k  p                        �  P                        �                          �  �                        �  0          �  �                    envp           argv            argc    (                          `                        '  �          9  �                        ^                           �             �  `                        �  �                        �  �                          p                    mainret            "                           8                          N  @                        d  0                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  �     +                 .file   
  ��  gidevicesyslog.        m  P                           x  �           �  �      syslog  H       device  P           �  �          �  4       lp      0       line    8           �  x           �  �           �  �           �  h           �  \             X             `           '  �           3  p           C  �           Y  �           f  �           r  �          }  p          �  @       udid    �           �  �           �        main    p4          �  �                        �  �      .text   P     c  �             .data                          .bss    0      �                 .rdata         $  y             .xdata  t      X                 .pdata  l      H                    �  p4       �                 �  �                           �  �                          �  �&  
   �6  �                �  �     I                    �  �     F  _                 �  0      @                    �  \      K                     9     �                          _                       �     �                    )        +                     4  P     H               .text   �      .idata$7$      .idata$5(      .idata$4p      .idata$6�      .text   �      .idata$7       .idata$5       .idata$4h      .idata$6�      .text   �      .idata$7      .idata$5      .idata$4`      .idata$6�      .text   �      .idata$7      .idata$5      .idata$4X      .idata$6�      .text   �      .idata$7      .idata$5      .idata$4P      .idata$6�      .text   �      .idata$7      .idata$5       .idata$4H      .idata$6\      .text   �      .idata$7      .idata$5�      .idata$4@      .idata$6D      .text   �      .idata$7      .idata$5�      .idata$48      .idata$6(      .text          .idata$7      .idata$5�      .idata$40      .idata$6      .text         .idata$7       .idata$5�      .idata$4(      .idata$6�      .text         .idata$7�      .idata$5�      .idata$4       .idata$6�      .text         .idata$7�      .idata$5�      .idata$4      .idata$6�      .text          .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   (      .idata$7�      .idata$5�      .idata$4      .idata$6x      .text   0      .idata$7H      .idata$5@      .idata$4�      .idata$6,      .text   8      .idata$7D      .idata$58      .idata$4�      .idata$6       .file   .  ��  ggccmain.c               @                       p.0                   �          2                       __main  �          O  �       .text   @     �                .data                         .bss    �                       .xdata  �                       .pdata  �      $   	                 �  j]  
   a                   �  �	     ?                    �       5                     �  p      0                      "     '                     �     �                     )  P     +                     4  �     �                .file   D  ��  gnatstart.c        .text                          .data   0                      .bss    �                           �  �c  
     
                 �  
     �                     �  �                             I     V   
                   ~                            {                         )  �     +                 .file   X  ��  gwildcard.c        .text                          .data   @                      .bss    �                            �  �i  
   �                    �  �     .                     �  �                             �     :                      �     �                     )  �     +                 .file   t  ��  gdllargv.c         _setargv                       .text                         .data   P                       .bss    �                        .xdata  �                       .pdata  �                          �  Zj  
   �                   �  �     :                     �  �      0                      �     V                      (     �                     )  �     +                     4        0                .file   �  ��  g_newmode.c        .text                           .data   P                       .bss    �                           �  �k  
   �                    �  (     .                     �                              /     :                      �     �                     )       +                 .file   �  ��  gtlssup.c              [                              j  P          y                       __xd_a  P       __xd_z  X           �  �      .text         �                .data   P                       .bss    �                       .xdata                         .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  @     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  el  
   �  6                 �  V     �                    �  C                        �  0     0                      i                          �                            c     �                     )  @     +                     4  P     �                .file   �  ��  gxncommod.c        .text   �                       .data   P                       .bss                               �  ,t  
   �                    �  =     .                     �  `                                 :                      Q     �                     )  p     +                 .file   �  ��  gcinitexe.c        .text   �                       .data   P                       .bss                           .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �t  
   {                   �  k     a                     �  �                            �     :                      �     �                     )  �     +                 .file     ��  gmerr.c            _matherr�                       .text   �     �                .data   P                       .bss                           .rdata  �     @               .xdata                        .pdata                           �  1v  
   6  
                 �  �                         �  Z     �                    �  �     0                      �     �                      �	     �                     )  �     +                     4  �     X                .file   -  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   P                       .bss                           .xdata  0                      .pdata                            �  gy  
   �                    �  �     -                     �  �     0                      �     X                      L
     �                     )        +                     4  P     0                .file   A  ��  gmingw_helpers.    .text                           .data   P                       .bss                              �  �y  
   �                    �  �     .                     �                               	     :                      �
     �                     )  0     +                 .file   n  ��  gpseudo-reloc.c        �                              �  p          �  $      the_secs(          �  �          �             �  @                        #  P                    .text         =  &             .data   P                       .bss                           .rdata  �     [                .xdata  4     0                 .pdata  ,     $   	                 �  �z  
   K  �                 �  ,     �                    �  �     �  
                 �        0                    �  �     W                       C     �                     �     	                       �     O                    )  `     +                     4  �     �                .file   �  ��  gusermatherr.c         P  @                           f  0          t  �      .text   @     L                .data   P                       .bss    0                      .xdata  d                      .pdata  P                         �  ۑ  
   �                   �                           �  g     r                     �  P     0                      �     �                      �     �                     )  �     +                     4  `     P                .file   �  ��  gxtxtmode.c        .text   �                       .data   P                       .bss    @                          �  Д  
   �                    �       .                     �  �                            q      :                      �
     �                     )  �     +                 .file   �  ��  gcrt_handler.c         �  �                       .text   �     �               .data   P                       .bss    P                      .xdata  p                      .rdata  @     (   
             .pdata  h                         �  X�  
   �                   �  D     ~                    �  �     _                    �  �     0                      �      �  
                   �                            Q                         )  �     +                     4  �     P                .file   �  ��  gtlsthrd.c             �  P                           �  �          �  `          �  �          �  h            0          .  �      .text   P     b  "             .data   P                       .bss    `     H                 .xdata  x     0                 .pdata  t     0                    �  9�  
   �
  A                 �  �     a                    �  8     �                    �  �     0                    �  �                            8"     x                     b     %                    )        +                     4        (               .file   �  ��  gtlsmcrt.c         .text   �                       .data   P                      .bss    �                           �  �  
   �                    �  #     .                     �                               �$     :                      �     �                     )  P     +                 .file     ��  g    B            .text   �                       .data   `                       .bss    �                          �  ��  
   �                    �  Q     0                     �                               �$     :                      #     �                     )  �     +                 .file   J  ��  gpesect.c              V  �                           i  �          x  @           �  �           �  `!          �  �!          �   "          �  `"          	  �"      .text   �     �  	             .data   `                       .bss    �                       .xdata  �     ,                 .pdata  �     l                    �  e�  
   �  �                 �  �     �                    �       �                    �  @     0                    �       �                       $%     K                     �     T                       �     �                     )  �     +                     4  (     (               .text   �#     2                 .data   `                       .bss    �                       .text    $                       .data   `                       .bss    �                           )  �     +                 .file   ~  ��  ggetopt.c              $	   $                       warnx   �$          1	  `%      place   p      ambig   0          D	  �      noarg              Q	  `          ^	  �(          n	  `          �	  d          �	  h          �	  �          �	  �      getopt   /          �	  P/          �	  p/      .text    $     �  t             .data   `     $                .bss    �                      .xdata  �     d                 .pdata       T                .rdata  �     B                    �  7�  
   <  �                 �       �                    �  �      ?                    �  p     0                    �  �                            o*     a	                     "     +                       �     .                    )       +                     4  P	     �               .file   �  ��  gmingw_matherr.    .text   �/                       .data   �                      .bss                                �  s�  
   �                    �  �      .                     �  �                            �3     :                      �     �                     )  @     +                 .file   �  ��  gucrt_vfprintf.    vfprintf�/                       .text   �/                     .data   �                     .bss                            .xdata  8                      .pdata  d                         �  �  
   �                   �  �      8                    �  :'     X                     �  �     0                      
4     r   	                   �     �                     )  p     +                     4  @     8                .file   �  ��  gucrt_printf.c     printf  �/                       .text   �/     H                .data   �                     .bss                            .xdata  @                      .pdata  p                         �  ��  
   �  
                 �  �!     l                    �  �'     -                     �  �     0                      |4     �   	                   �     �                     )  �     +                     4  x     H                .file   �  ��  gucrt_fprintf.c    fprintf  0                       .text    0     2                .data   �                     .bss                            .xdata  L                      .pdata  |                         �  a�  
   �                   �  _#     b                    �  �'     F                     �        0                      5     �   	                   j     �                     )  �     +                     4  �     8                .file     ��  g__initenv.c           �	             �	        .text   @0                       .data   �                     .bss                               �  �  
   �                   �  �$     �                     �  P                            �5     [                      O                         )        +                 .file   Q  ��  gucrtbase_compa        �	  @0                           �	  P0          
  �0      _onexit 01          
  P1          (
  �                        M
  p1          X
  �1      tzset   �1          f
  �                    _tzset   2          �
  (          �
  ,          �
  0          �
  D          �
  @      .text   @0       "             .data   �     x   
             .bss                           .xdata  T     P                 .pdata  �     l                .rdata  �                          �  ��  
   �  Y                 �  i%                          �  (     |                    �  p     0                      �5     �                     M                            l     `                    )  0     +                     4  �     �               .text   `2      .data   `      .bss          .idata$7p      .idata$5P      .idata$4�      .idata$6�      .text   h2      .data   `      .bss          .idata$7t      .idata$5X      .idata$4�      .idata$6�      .text   p2      .data   `      .bss          .idata$7x      .idata$5`      .idata$4�      .idata$6�      .text   x2      .data   `      .bss          .idata$7|      .idata$5h      .idata$4�      .idata$6�      .file   _  ��  gfake              hname   �      fthunk  P      .text   �2                       .data   `                       .bss                           .idata$2�                      .idata$4�      .idata$5P      .file   �  ��  gfake              .text   �2                       .data   `                       .bss                           .idata$4�                      .idata$5p                      .idata$7�                      .text   �2      .data   `      .bss          .idata$7<      .idata$5(      .idata$4p      .idata$6�      .text   �2      .data   `      .bss          .idata$7@      .idata$50      .idata$4x      .idata$6�      .text   �2      .data   `      .bss          .idata$7D      .idata$58      .idata$4�      .idata$6�      .text   �2      .data   `      .bss          .idata$7H      .idata$5@      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   p      fthunk  (      .text   �2                       .data   `                       .bss                           .idata$2�                      .idata$4p      .idata$5(      .file   �  ��  gfake              .text   �2                       .data   `                       .bss                           .idata$4�                      .idata$5H                      .idata$7L     !                 .text   �2      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4       .idata$6      .text   �2      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4(      .idata$6      .text   �2      .data   `      .bss          .idata$7       .idata$5�      .idata$40      .idata$6.      .text   �2      .data   `      .bss          .idata$7      .idata$5�      .idata$48      .idata$6<      .text   �2      .data   `      .bss          .idata$7      .idata$5�      .idata$4@      .idata$6V      .text   �2      .data   `      .bss          .idata$7      .idata$5       .idata$4H      .idata$6r      .text   �2      .data   `      .bss          .idata$7      .idata$5      .idata$4P      .idata$6|      .text   �2      .data   `      .bss          .idata$7      .idata$5      .idata$4X      .idata$6�      .text   �2      .data   `      .bss          .idata$7      .idata$5      .idata$4`      .idata$6�      .file   �  ��  gfake              hname          fthunk  �      .text   �2                       .data   `                       .bss                           .idata$2�                      .idata$4       .idata$5�      .file   w  ��  gfake              .text   �2                       .data   `                       .bss                           .idata$4h                      .idata$5                       .idata$7                       .text   �2      .data   `      .bss          .idata$7�
      .idata$5H      .idata$4�      .idata$6�	      .text   �2      .data   `      .bss          .idata$7�
      .idata$5P      .idata$4�      .idata$6�	      .text    3      .data   `      .bss          .idata$7�
      .idata$5X      .idata$4�      .idata$6�	      .text   3      .data   `      .bss          .idata$7�
      .idata$5`      .idata$4�      .idata$6

      .text   3      .data   `      .bss          .idata$7�
      .idata$5h      .idata$4�      .idata$6
      .text   3      .data   `      .bss          .idata$7�
      .idata$5p      .idata$4�      .idata$6.
      .text    3      .data   `      .bss          .idata$7�
      .idata$5x      .idata$4�      .idata$6F
      .text   (3      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4�      .idata$6\
      .text   03      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4�      .idata$6j
      .text   83      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4�      .idata$6r
      .text   @3      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4�      .idata$6�
      .text   H3      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4�      .idata$6�
      .text   P3      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4�      .idata$6�
      .text   X3      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4�      .idata$6�
      .text   `3      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4       .idata$6�
      .text   h3      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4      .idata$6�
      .text   p3      .data   `      .bss          .idata$7�
      .idata$5�      .idata$4      .idata$6      .file   �  ��  gfake              hname   �      fthunk  H      .text   �3                       .data   `                       .bss                           .idata$2�                      .idata$4�      .idata$5H      .file   �  ��  gfake              .text   �3                       .data   `                       .bss                           .idata$4                      .idata$5�                      .idata$7�
     "                 .text   �3      .data   `      .bss          .idata$7X
      .idata$5      .idata$4`      .idata$6�	      .text   �3      .data   `      .bss          .idata$7\
      .idata$5       .idata$4h      .idata$6�	      .text   �3      .data   `      .bss          .idata$7`
      .idata$5(      .idata$4p      .idata$6�	      .text   �3      .data   `      .bss          .idata$7d
      .idata$50      .idata$4x      .idata$6�	      .text   �3      .data   `      .bss          .idata$7h
      .idata$58      .idata$4�      .idata$6�	      .file   �  ��  gfake              hname   `      fthunk        .text   �3                       .data   `                       .bss                           .idata$2�                      .idata$4`      .idata$5      .file   �  ��  gfake              .text   �3                       .data   `                       .bss                           .idata$4�                      .idata$5@                      .idata$7l
     "                 .text   �3      .data   `      .bss          .idata$74
      .idata$5      .idata$4P      .idata$6�	      .file   �  ��  gfake              hname   P      fthunk        .text   �3                       .data   `                       .bss                           .idata$2x                      .idata$4P      .idata$5      .file     ��  gfake              .text   �3                       .data   `                       .bss                           .idata$4X                      .idata$5                      .idata$78
                      .text   �3      .data   `      .bss          .idata$7 
      .idata$5�      .idata$4       .idata$6V	      .text   �3      .data   `      .bss          .idata$7
      .idata$5�      .idata$4(      .idata$6f	      .text   �3      .data   `      .bss          .idata$7
      .idata$5�      .idata$40      .idata$6p	      .text   �3      .data   `      .bss          .idata$7
      .idata$5�      .idata$48      .idata$6x	      .text   �3      .data   `      .bss          .idata$7
      .idata$5�      .idata$4@      .idata$6�	      .file   &  ��  gfake              hname          fthunk  �      .text   �3                       .data   `                       .bss                           .idata$2d                      .idata$4       .idata$5�      .file   B  ��  gfake              .text   �3                       .data   `                       .bss                           .idata$4H                      .idata$5                       .idata$7
                      .text   �3      .data   `      .bss          .idata$7�      .idata$5�      .idata$4      .idata$66	      .text   �3      .data   `      .bss          .idata$7�      .idata$5�      .idata$4      .idata$6F	      .file   P  ��  gfake              hname         fthunk  �      .text    4                       .data   `                       .bss                           .idata$2P                      .idata$4      .idata$5�      .file   e  ��  gfake              .text    4                       .data   `                       .bss                           .idata$4                      .idata$5�                      .idata$7�     &                 .text    4      .data   `      .bss          .idata$7�      .idata$5�      .idata$4�      .idata$6,	      .file   s  ��  gfake              hname   �      fthunk  �      .text   4                       .data   `                       .bss                           .idata$2<                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   4                       .data   `                       .bss                           .idata$4                       .idata$5�                      .idata$7�     "                 .text   4      .data   `      .bss          .idata$7�      .idata$5�      .idata$4�      .idata$6	      .text   4      .data   `      .bss          .idata$7�      .idata$5�      .idata$4�      .idata$6
	      .text    4      .data   `      .bss          .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   (4      .data   `      .bss          .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   04      .data   `      .bss          .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   84      .data   `      .bss          .idata$7�      .idata$5x      .idata$4�      .idata$6�      .text   @4      .data   `      .bss          .idata$7|      .idata$5p      .idata$4�      .idata$6�      .text   H4      .data   `      .bss          .idata$7x      .idata$5h      .idata$4�      .idata$6�      .text   P4      .data   `      .bss          .idata$7t      .idata$5`      .idata$4�      .idata$6x      .text   X4      .data   `      .bss          .idata$7p      .idata$5X      .idata$4�      .idata$6`      .text   `4      .data   `      .bss          .idata$7l      .idata$5P      .idata$4�      .idata$6H      .file   �  ��  gfake              hname   �      fthunk  P      .text   p4                       .data   `                       .bss                           .idata$2(                      .idata$4�      .idata$5P      .file   �  ��  gfake              .text   p4                       .data   `                       .bss                           .idata$4�                      .idata$5�                      .idata$7�     
                 .file     ��  gcygming-crtend        �
  �<                       .text   p4                       .data   `                       .bss                               �  �<                         �  �                          �  �                         �
  �<                         )  `     +                 .idata$2        .idata$5�      .idata$4      .idata$2       .idata$58      .idata$4�      .idata$4x      .idata$50      .idata$7(      .idata$4�      .idata$5H      .idata$7L      .rsrc       
    __xc_z             �
  �            `2            �2          8  �          D  �          `  `          ~  �          �              �  �<          �            �  �          �  �             04          (  �          J  �          W  �          h  �          �  H          �             �      	        �             �  X3          �  �      __xl_a  0           �  H4          
  �          .
  �          B
        _cexit  3          j
             �
  `  ��       �
     ��       �
  �          �
              �
  �                ��       *     ��       F  0       cprintf 8          X  P      __xl_d  @           t  �      _tls_end   	        �         __tznamep2          �            �  4          �  P          �             �  �          �  0             �          *            E  �          d  p          |      	    memcpy  �3          �  �          �  �      puts    �2          �  P          �  P          �  �                      4  �      malloc  �3      _CRT_MT P      optarg  �          C   4          O  �          \  0          i              w            �  �          �  `          �     ��       �  �          �  0            h            `          /  �          T  �           n  �      opterr  �          y  p          �  H          �  8
          �  �3          �  �          �  �       fflush  �2            4          $  P           W  h          d  P           v  x          �  @          �  �3          �  �          �  �      abort   `3          �  @             �          4  P           D        __dll__     ��       b      ��       w  �2      strtol   4          �  (          �  L          �  X4          �  �            (                        �3          )  0          8             H  x           t  �          �     ��       �  4      strrchr �3          �  l
      calloc  �3          �  `          �  x          �  �           !             .  �          L  �      Sleep   (4          d  �      _commode           u  `          �            �  �<          �  �          �  d           �            �  (       optind  |                  __xi_z  (             �          /  8          <  0          T             d  �      strstr  �3          �  �2          �  �          �  X          �  �       signal  p3          �  �2          �               �                         1  �      strncmp �2          G  �<          V  `          v  �          �  �           �  8      realloc �3          �  �          �  <             �          0      ��       C  0          W             �  �
          �  X          �  8          �  L          �  �            �          !             6  �          Z  P          k     ��       ~  p      strdup  �2          �  0          �  �3          �  P4          �  83          �  �          	   3            @4          6  �          A  �           n              �  �          �     ��       �              �  �          �  �          �  (            `      __xl_z  H       __end__                �          /  �          Q  P      strcmp  �2          _  �<          m         __xi_a             �  P3          �  �          �  84      __xc_a              �            �     ��       �  P           �     ��   _fmode  @            �            �          ,             E  �3          V  X          g  P          x  �          �  3          �             �  �          �  �          �  �            �2            �          &        fputc   �2      __xl_c  8           3     	    optopt  x          @  �          q  �          �  �          �  �          �  �           �  @          �            �  @            h2             
      _newmode�           N  H3      fwrite  �2          X            y  @          �  p          �  �          �      ��       �      ��       �  (          �  �#          �  �             �2            �      exit    h3          $  �          L     ��       h             u      ��       �  �          �  x      _exit   03          �  �          �            �  3      strlen  �2          �  @          �  (3          	             .   �      strchr  �3          U   `4          k   @3          �   �          �   h          �   �           �   �          !  �          %!  (          G!             V!  �          o!  P           !  �          �!  �2          �!            �!   3      free    �3          �!  �          �!        �!  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame clean_exit quit_flag stop_logging syslog_callback line_buffer_size num_msg_filters num_proc_filters num_pid_filters num_trigger_filters num_untrigger_filters triggered untrigger_filters msg_filters trigger_filters proc_filter_excluding proc_filters pid_filters add_filter device_event_cb use_network exit_on_disconnect print_usage.isra.0 .rdata$.refptr.optarg QUIET_FILTER .text.startup .xdata.startup .pdata.startup __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names permute_args parse_long_options illoptstring recargstring getopt_internal posixly_correct.0 nonopt_end nonopt_start illoptchar recargchar getopt_long getopt_long_only local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp_GetEnvironmentVariableW __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone __imp_idevice_new_with_options _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode syslog_relay_start_capture_raw __imp___p___argc __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __rt_psrelocs_start __imp_lockdownd_service_descriptor_free __imp_syslog_relay_client_new __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ __imp_fputc VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll idevice_events_unsubscribe __imp_idevice_events_subscribe .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler lockdownd_service_descriptor_free __imp_syslog_relay_client_free .refptr.optarg TlsGetValue __imp_strtol __imp_strcmp __bss_start__ __imp___C_specific_handler ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection __imp_term_colors_set_enabled __C_specific_handler __mingw_optreset .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf ___crt_xp_end__ __imp_lockdownd_start_service __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_strdup __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname _tls_used __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ lockdownd_start_service .refptr._newmode __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ idevice_set_debug_level __imp_strstr __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func _head_libimobiledevice_glue_1_0_dll __native_startup_state ___crt_xc_start__ lockdownd_client_free ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_cprintf __imp_idevice_events_unsubscribe _head_lib64_libapi_ms_win_crt_convert_l1_1_0_a .refptr.__mingw_initltsdyn_force __rt_psrelocs_size .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp___p___wargv __imp_strlen libimobiledevice_glue_1_0_dll_iname __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection term_colors_set_enabled __p__wenviron GetEnvironmentVariableW _initialize_narrow_environment __imp_realloc _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __IAT_start__ __imp_syslog_relay_start_capture_raw __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit __DTOR_LIST__ idevice_events_subscribe __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_VirtualProtect idevice_free ___tls_end__ __lib64_libapi_ms_win_crt_convert_l1_1_0_a_iname .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm idevice_get_device_list_extended __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_strchr ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __imp_idevice_device_list_extended_free __minor_subsystem_version__ __imp_fflush __minor_image_version__ __imp___set_app_type __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR __imp_idevice_get_device_list_extended DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv idevice_device_list_extended_free .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ syslog_relay_client_free __stdio_common_vfprintf __imp_daylight __p___wargv syslog_relay_client_new __mingw_app_type 