MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� E<�g � �  � & ( B   �     �        @                       �    ��  `                                             �  $     �   �  �            �                           �x  (                   8�  0                          .text   �@      B                 `  `.data       `      H              @  �.rdata     p      L              @  @.pdata  �   �      b              @  @.xdata     �      f              @  @.bss    �   �                      �  �.idata  $   �      j              @  �.CRT    `    �      |              @  �.tls        �      ~              @  �.rsrc   �         �              @  �.reloc  �         �              @  B/4      `         �              @  B/19     W�   0  �   �              @  B/31     @$      &   V             @  B/45     �'   0  (   |             @  B/57     @   `     �             @  B/70        p     �             @  B/81     �   �     �             @  B/97     ;   �     �             @  B/113    �   �     �             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H�n  1��    H�n  �    H�	n  �    H�lm  f�8MZuHcP<HЁ8PE  tfH��m  �
��  � ��tC�   ��>  �$>  H�mn  ���>  H�=n  ���T  H��l  �8tP1�H��(Ð�   �>  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
n  �\&  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H��m  L�֞  H�מ  H�
؞  � ���  H��m  D�H���  H�D$ �:  �H��8��    ATUWVSH�� H��l  H�-��  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5�l  1�����V  �����  ��     ����L  ���e  H��k  H� H��tE1��   1����!  H�
�l  �۲  H�$l  H�
����H��-=  �  �ҝ  �{Hc�H��H���=  L�%��  H�Ņ��F  H��1�I���;  H�pH���S=  I��H�D I�H��H����<  H9�u�H�H�    H�-]�  �  H�!k  L�B�  �
L�  H� L� H�7�  �m  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�5Yk  �   ���������   �:  ��������H�Mk  H�
6k  �	<  �   �������1�H�����f�     �;  ���  H�� [^_]A\�f.�     H�k  H�
k  �   �;  �7���f�H����������;  �H��(H�5j  �    ������H��(� H��(H�j  �     �z�����H��(� H��(��8  H���H��(Ð�����������H�
	   �����@ Ð��������������UH��H�� H��[  H���:  H��[  H���:  H��[  H���{:  H��[  H���l:  H� \  H���]:  H�.\  H���N:  �H�� ]�UH��M�}�  8�}�  }�}"  t�}'  t�H�\  �H�\  �H�\  �H�\  ]�UH��H��0H�MH�UL�E H�E H�     H�EH��[  H���9  H�E�H�}� ��   H�E�A�   �    H���~9  H�E�H���z9  H�H�E�H�E�H���9  H�}� uH�E�H���%9  �VH�E�H��H���C:  H��H�EH�H�EH� H�M�H�U�I��I�к   H���9  H�E�H����8  H�E H�U�H���H��0]�UH��H��0H�MH�UH�E�    �'H�UH�E�H�� ����H��Z  H���c5  H�E�H�E�H;Erϐ�H��0]�UH��H��PH�MH�E�    H�EH��H���  ��H�E�H�}� �>  H�E�� ��H��Z  H���5  H�E��@��H��Z  H����4  H�E��@��H��Z  H����4  H�E�H�@H��H�tZ  H���4  H�E��@��H�mZ  H���4  H�E��@��H�cZ  H���4  H�E��@ ��H�YZ  H���p4  H�E�H�@8H��t
H�E�H�@8�H�AZ  H��H�;Z  H���@4  H�E�H�@(H��t
H�E�H�@(�H�Z  H��H�Z  H���4  H�E�H�@0H��t
H�E�H�@0�H��Y  H��H��Y  H����3  H��Y  H����3  H�E�H�@HH��tH�E��@P��H�E�H�@HH�������H��Y  H���3  �
   ��6  H��Y  H���3  H�E�H�@XH��tH�E��@`��H�E�H�@XH�������H�AY  H���J3  �
   �6  H�E�H�@@H�PY  H���Q7  H�E�H�}� tRH�E�H�E�]   H��� 7  H�E�H�}� t1H�E�H+E���H�E�I��H�Y  H����2  �H�Y  H���<6  H�U�H�EH��H���  �ЉE�}� u�E��������H��H��X  H���2  H�U�H�EH��H�9�  ��H�E�H��tMH�E�H� H��H��X  H���]2  H�E�H�@H��H��X  H���C2  H�E�H�@H��H��X  H���)2  �H��P]�UH��H��PH�ê  ��H�E�H�E�    �E�    �E�    �\�E�H�H��H��H�E�H�H�E�H�E�H�HH�E�PH�E�D�HH�E�L�@H�E�H� H�L$(�T$ H��H�X  H���1  �E��E�H�H��H��H�E�H�H� H��u���H��P]�UH��H�� H�MH�EH��W  H���/4  ��tEH�EH��W  H���4  ��t.H�EH��W  H���4  ��tH�EH��W  H����3  ��u�   ��    H�� ]�UH��H��`H�MH�UD�E H�EH���3  H�E�H�E�H�wW  H���3  H�E�H�E�H�bW  H���3  ��u�@�     �f  H�E�H�BW  H���`3  ��u
�?����E  H�E�H�'W  H���?3  ��usH�W  H�¹    �?3  H�E؋�  ��t'�   H��  ��H��H�E�I��H��V  H���j0  H�}� ��  H�U�H�EA�    H��H��  ���  H�E�H��V  H���2  ��uH�EH�������  H�E�H��V  H���2  ��uH�TV  H�¹    �2  H�E��7�  ��t'�   H�S�  ��H��H�E�I��H�YV  H���/  H�}� tH�U�H�EA�    H��H�i�  ��H�EH��H�y�  ����   H�E�H�-V  H����1  ����   H��U  H�¹    ��1  H�E���  ��t'�   H���  ��H��H�E�I��H��U  H���/  H�}� ��   H�E�    H�E�    H�M�H�U�H�E�I��H������H�E�H��t0H�U�H�E�H��  H�U�H�EH��H�;�  ��H�E�H���2  �.H�E�H��H�yU  H���M.  �H�E�H��H�}U  H���5.  H�E�H���y2  �H��`]�UH��H�� H��U  H����  �H�� ]�UH��H�� H�MH�EH���  H�mU  H���  �H�� ]�USH��8H�l$0H�M �E�    ����H�E A�    L��  �   H��H�[�  ��H�E A�    L�f  �   H��H�9�  ��H�E A�    L��  �   H��H��  ��H�E A�    L��  �   H��H���  ����   H�E H��H��  �ЉE��}� tG���  ����   �E���H�3�  ��H�ù   H���  ��H��I��H�nT  H���#-  �   H�^T  H���w  H�E�H�}� tyH�E�� ��tnH�E�H���'�����tH�U�H�E A�   H��H���  �ЉE��H�U�H�E H��H�x�  �ЉE��}� t
��     H�E�H���C���H�E�H���0  �Ր  ����������H��8[]�UH��H��0H�MH�UH�E�@��uK�E�    H�E� �E�H�EH�@H�E��E�    ��E�Hc�H�E�H�� �����//  �E��E�;E�|ڸ    H��0]�UH��H�� H�MH�UH�E�@��u5H�EH�@� </u&H�E� ��H�EH�PH�EA��H���j����������    H�� ]�USH��XH�l$PH�M H�U(H�E�    H�E�    H�E�    H�E�    �E�    H�E(�@���
  H�E(H�@H����-  H�E�H�E�H��Q  H����-  H�E�H�E�H�nR  H���-  ����   H�gQ  H�¹    �-  H�E�H�M�H�U�H�E I��H��H�s�  �ЉE�}� tO�&�  ��t4�E��H���  ��H�ù   H�1�  ��H��I��H��Q  H���*  H�E�H���.  �E��JH�E�H���-  H�E�H���q.  H�E�H��P  H����,  ��u
���     H�E�H���D.  �    H��X[]�UH��H�� H�MH�UH�E�@��uH�EH�@fHn��   �    H�� ]�UH��H��0�E�E�    f��f/E��   �Ef/�V  v
��V  �EH�
Q  H���a)  �E�    �:f���*M��E�bV  �^�f/�v�=   �,  �
�    �,  �E��}�1~��EH�Ef(�H��H��P  H����(  �   H���  ��H���,  �Ef.�U  z�Ef.�U  u
�
   �#,  ��H��0]�UH��H��0�MH�UH�E�    H�EH� �/   H���,  H�E�H�}� t
H�E�H���H�EH� H��H�P  H���Y(  �
   �+  H�P  H���+  �
   �+  H�,P  H���+  H�(P  H���+  H�YP  H���y+  H�rP  H���j+  H��P  H���[+  H��P  H���L+  H��P  H���=+  H��P  H���.+  H�Q  H���+  H�HQ  H���+  H�iQ  H���+  H��Q  H����*  H��Q  H����*  H��Q  H����*  H�R  H����*  �
   �*  H�R  H���<'  �H��0]�UATWVSH�ĀH��$�   �M0H�U8�W  �E�    �E�    �E�    H�E�    �E�����H�E�    �E�    H�E�    H�E�    �}0�"  H�U8�E0���
����    �Q	  �E���V�� ��  ��H��    H��R  �H�H��R  H���H�Z  H� H����  H�E�    H��Y  H� H�U�A�    H���*  H�E�H�E�H��tH�E�� ��tH�E�    H�}� �v  H��Y  H��   H�٠  ��H��I��H��P  H���B&  ������  ���  ���}�  �,  H�U8�E0�������    �[  �E�   �  �E�   ��   �E�   ��   �E�   ��   �E�   H�Y  H� H�E���   �E�   H�Y  H� H�E��   �E�   H��X  H� H�E��   �E�   H��X  H� H�E���E�	   �v�E�
   ������    �  L�P  H�*P  H��H�*P  H����$  �    �}  �   H���  ��I��A�   �   H��O  H���(  ������H  �H�U8�E0H�D$     L�
9  L��O  ���#$  �E��}� ������}� uC�   H�4�  ��I��A�   �   H��O  H���'  H�U8�E0�������������  �ˈ  ��t���  ��H�<�  ��H�E�    �E�    ��   ���  ��t+�   H���  ��I��A�   �   H�VO  H���)'  H�U�H�E�H��H���  �ЉE܁}����u>�E܉�H�՜  ��H�ù   H�\�  ��H��I��H�O  H����#  ������
  �}� tb��  H��  �Ѓ}�u>�E܉�H�}�  ��H�ù   H��  ��H��I��H��N  H���m#  ������  �E��}�������H�E�    H�E�H�U�H��H���  ��H�E�H��tU�{�  ��tKH�E��pH�E��XH�E�L�`H�E�H�8�   H�z�  ��H���t$(�\$ M��I��H�@N  H����"  �}�	��  �E�H��    H� O  �H�H��N  H���H�E�H��H�M�  ����  H�E�A�    L�M����   H��H��  ��H�E�H�U�A�   H��H�,�  �ЉE���  ���p  �E��H�%�  ��H�ù   H���  ��H��I��H�`I  H���"  �7  H�E�H���>�����tH�E�H�U�A�   H��H���  �ЉE��H�E�H�U�H��H���  �ЉE��  ����  �E��H���  ��H�ù   H��  ��H��I��H��H  H���!  �  H�}� ��   H�E�A�    L�/����   H��H�ș  ��H�E�H�U�A�    H��H��  �ЉE�}� tG���  ���R  �E��H��  ��H�ù   H���  ��H��I��H�<H  H����   �  H�E�H��H�Ι  �ЉE�'�  ����  �E��H���  ��H�ù   H�.�  ��H��I��H��G  H���   ��  H�E�H���`����  H�M�H�U�H�E�I��H������H�E�H��tzH�U�H�E�H��  H�U�H�E�H��H���  �ЉE�}� t>���  ��t4�E��H��  ��H�ù   H���  ��H��I��H�CG  H����  H�E�H����#  �!  �   H�[�  ��H��H�E�I��H��F  H����  ��  H�E�H�U�H��H�1�  �ЋE؉��v���H��H�(K  H���;  ��  H�E�L�K  H�K  H��H�J�  �ЉE�}� tG���  ����  �E��H�-�  ��H�ù   H���  ��H��I��H�hF  H���  �K  H�E�H��H�  �ЉE�}� tG�M�  ���'  �E��H�͗  ��H�ù   H�T�  ��H��I��H�F  H���  ��   H�E�H��H�J�  �ЉE�}� tG��  ����   �E��H�m�  ��H�ù   H���  ��H��I��H��E  H���]  �   ���  ����   �E��H�&�  ��H�ù   H���  ��H��I��H�aE  H���  �MH�E�H���l����@�   H�z�  ��I��A�   �   H��I  H����   �����
��
�������H�E�H��H��  �и    H��[^_A\]Ð������%�  ���%�  ���%�  ���%�  ���%j�  ���%Z�  ���%J�  ���%:�  ���%*�  ���%�  ���%
�  ���%��  ���%�  ���%ڕ  ���%ʕ  ���%��  ���%��  ���%��  ���%��  ���%z�  ���%j�  ���%Z�  ���%J�  ���%:�  ���%2�  ���%
�  ���%��  ���     H��(H��2  H� H��t"D  ��H��2  H�PH�@H��2  H��u�H��(�fD  VSH��(H�3N  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^�c��� 1�fD  D�@��J�<� L��u��fD  �j�  ��t�D  �V�     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H�CM  �8t�    ��t��tN�   H��([^�f�H��  H�5�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�LI  Hc�H����    H�0H  �DA �y�qH�q�   �c  �DD$0I��H��H  �|$(H��I���t$ �  �t$@|$P1�DD$`H��x[^ÐH��G  ��    H��G  ��    H��G  �s���@ H�)H  �c���@ H��G  �S���H�CH  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�  A�   �   H�
BH  I����  H�t$(�   �[  H��H��I���-  �8  ��    WVSH��PHc5F~  H�˅��  H�8~  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H��}  H��H��H�H�x �     �#  �WA�0   H�H��}  H�T$ H�L�/�  H���}   �D$D�P����t�P���u�}  H��P[^_� ��H�L$ H�T$8A�@   �   DD�HU}  H�KI��H�S�đ  ��u����  H�
cG  ���d���@ 1��!���H�}  �WH�
G  L�D�>���H��H�
�F  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%�|  E��tH�e[^_A\A]A^A_]�fD  ��|     �9	  H�H��H��   H����  L�-�I  H��I  �n|      H)�H�D$0H�c|  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5I  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
F  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  ��z  ������H�5C�  1�H�}�D  H��z  H�D� E��t
H�PH�HI����A��H��(D;%�z  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5�F  �s�;H��L�>H���Z����>L9�r��������H�
-D  �����H�
�C  ���������H��XH��y  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
Iy  �T  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H�D  Hc�H��� 1ҹ   ��  H���>  H���  H��x  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �i  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �  H���@����   �   ��  �f�     1ҹ   ��  H��t*H�������   ���i���f�     �   ���T����   �   �  �@����   �   �  �,����   �   �m  �����������ATUWVSH�� L�%�w  L�����  H��w  H��t6H�-Ë  H�=��  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%h�  WVSH�� �;w  ��H�օ�u
1�H�� [^_ú   �   �	  H��H��t3H�pH�5w  �8H�����  H��v  H��H��v  H�C� �  묃��멐VSH��(��v  �˅�u1�H��([^�D  H�5�v  H�����  H�
�v  H��t'1��H��H��tH���9�H�Au�H��tH�B�m  H�����  1�H��([^� H�Qv  ��ff.�     @ SH�� ����   w0��tL�.v  ����   �v     �   H�� [�f�     ��u��u  ��t��<�����f.�     ��u  ��uf��u  ��u�H��u  H��t�    H��H�[�  H��u�H�
�u  H��u      ��u      ���  �l����k����   H�� [������f�     H�
yu  �s�  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���A  H��w{H�tA  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H����  ��u�H��H�� [^_��    1�H��H�� [^_� H��@  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H�i@  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L�)@  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H��?  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H�i?  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L��>  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������AUATUWVS��D�Ɖ�L��)�)։�������   �� A�ՙA��D���u�D��)șA��E��~lHc�A��A�L�׉څ�~Yf�     M�1��f�     M��A��D�2A)�9�D��AL҃�Lc�N��M�M�M�9�u�A��I��E9�tD��뱐[^_]A\A]�A���u���ff.�     @ WVSH��0H�t$XL�L$hH��H�T$XL�D$`H�t$(�@  �   H� H�8�
  H�;  I��H����
  �   �
  I��H��H���^
  �   �t
  �
   H���
  �H��0[^_�ff.�      AWAVAUATUWVSH��H��   H�=�   H��$�   H��M��H��$�   �=   �D$<��L��$�   �D$8��   �  I��H���  H��I��H)�M�4$M����   1�L�D$0I�\$ E1�D��$�   �L$(�����A���*Hc�H��L��P9S�tV�D$(   L�3H�� A��M��tfI��L��H���`  ��u�L���L  H9���   H��uE��u��u�D���fD  H�PH9S�u��@9C��   DD$(�D$(��     �L$(L�D$0��uKA����ux��$�   ���  ��  ���.  ��      �?   H��H[^_]A\A]A^A_�fD  D�
q  E��t�H��$�   �8:t�I����H�
�9  �����@ L�D$0Ic�H��L�k��uRM��teD�&  E��tH��$�   �8:��   H�{ �l  H��$�   �-�  �8:�Z����:   �U���@ �U���wM����   L��m  H��$�    tH��$�   D�8H�S�CH�������1��
���f�     H��H�D$(�
  L�D$(H�������fD  H��$�   �8:�����H��H�
E8  ���������I����H�
8  �����%������d���HcT$8H��$�   D�t$<H��A��D�5  H�4m  H���3����
�  ��t)H��$�   �8:tH��H�
z8  �E�����  ���D$81�H�{ tB��  �D$8��  H��$�   �8:�
��������k�����D$<��  �����������C��     AWAVAUATUWVSH��HD��$�   A��H��M��M��M���y  D�
8  �  E����  �����  �Vl  ����  A�E <-�  D��  E����  <+��  H�l      ����   ��  ������  ����f.�     ��k      ��  D9���  Hc�H�t� �>-��  H�w6  H�x  A����  A����  �5V  ����5  �=C  ���t%��I��A�؉������)����!  ����)��  ���(  H�=  ����Z���M��tkHc
  H9|� t]<-�r  A���w  <:�k  ��L���J	  H�������D$ M��L��H��L��$�   �����Ã����  H�=�  �L���L�=�  <:�,  ��-��   ��L����  H���  �PM��t��Wu	��;��  ��:��   � ��  �C  �}  f��nj     �(     1�E1�H�
�4  ��}  �Lj  ��������  A�E <-�����A��I�������@ �F����  �-   L���7  H���0����=�  �t
�=�  ���  L�~H���-   L�=�  � �8  �-   L����  H����   �x:�%���H��i      � �Z  ��  L�=�i  H�24  ��H�0  �6  �s�     �  �����D  A���<+�#��������    �5�  H��3  �=�  H��  ���uP���t�=�  ��  ������  �����������H��H[^_]A\A]A^A_�f���H�5�h  ��  �   �ԉ�)�A��I���)��&����|  �L���L�=`  <:������ u�W  �
U  ��tA�} :��   �8  �?   �q���fD  A����2����
	  ���t
�=�  ��  H�~H�=�  <-������~ ������5�  ��H��2  ��  H��  ��������A�؉�I��L$<�[����L$<)�)��  ������     ��H�
�2  �����G���H��H�=r  1������ ��   �g  ���^  D9�|e�W  H�82  H�9  ��tA�} :t�W   H�
3  �����  W   A�} :������:   �I�����  �S�����  �����H�H�D� H��  �D$     M��L��H��L��$�   �������H��1  H��  ������x:�\�������  A9�~Hc�H�D� H��f  �:���H�l1  H�m  �w  ��tA�} :t��H�
C2  ������P  A�} :�
����0����     H��8E1��D$(    H�D$     ����H��8�ff.�      H��8H�D$`�D$(   H�D$ ����H��8�H��8H�D$`�D$(   H�D$ �e���H��8�H��8E1�L�D$ I��H��1��  H��8Ð�VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H���  ���   �����  �  � ��  H� H��w  H� H�M��t	A�$�C  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���  ���   ����h  �;  � ��B  H� H��  H� H�M��t	A�$��  1�H�� [^_]A\�fD  SH�� H���+  ���    HD�H�� [�f�H�1  �8 t1�Ð��  ff.�     SH�� �˹   �/  A��H��/  H���m�����   ��  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8�   H��H�ff.�     H��(H�50  ��~   H�  �j   H��  �V   H��  H��(�f.�     H��(H��/  ��>   H��  �*   H��  �   H��  H��(Ð����������%�x  ���%�x  ���%�x  ���     �%�x  ���%�x  ���%�x  ���%�x  ���%�x  ���     �%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���     �%v  ���%v  ���%v  ���%v  ���%�u  ���     �%�u  ���%�u  ���%�u  ���%�u  ���%bu  ���%bu  ���%Bu  ���     �%u  ���%
u  ���%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���     ������������������������`P @           ��������                                                                                                                                                                                                                                                                                                                                                                                Lx @                  i       Qx @                  c       Yx @                   m       ^x @                  f       cx @                  k       kx @                   r       qx @                   n       xx @                  e       x @                   s       �x @                   q       �x @                   a       �x @                   v       �x @                   h       �x @                   V                                       �P @           ��������        ����                           ������������    (| @   ?                     ����            pK @           �K @           �K @           �� @   �� @   �M @   �M @    L @   PM @   �L @   0L @   �b @   �b @   �b @      �p  c @    c @   PDT PST 0M @   M @                                                                                                                                                                                                                                           Usage:    /upload FILE		send FILE to device       /limera1n [FILE]	run limera1n exploit and send optional payload from FILE       /deviceinfo		print device information (ECID, IMEI, etc.)   /help			show this help       /exit			exit interactive shell Recovery DFU WTF Unknown rb %02x CPID: 0x%04x
 CPRV: 0x%02x
 BDID: 0x%02x
 ECID: 0x%016llx
 CPFM: 0x%02x
 SCEP: 0x%02x
 IBFL: 0x%02x
 N/A SRTG: %s
 SRNM: %s
 IMEI: %s
 NONC:  SNON:  PWND:[ PWND: %.*s
 Could not get device info?! MODE: %s
 PRODUCT: %s
 MODEL: %s
 NAME: %s
 %s %s 0x%02x 0x%04x %s
 go bootx reboot memboot   /exit /help /upload Uploading file %s
 /deviceinfo /limera1n Sending limera1n payload %s
 /execute Executing script %s
 Could not read file '%s'
   Unsupported command %s. Use /help to get a list of available commands.
 .irecovery %s
 >  getenv 
[ ] %3.1f%% Usage: %s [OPTIONS]
      Interact with an iOS device in DFU or recovery mode. OPTIONS:     -i, --ecid ECID	connect to specific device by its ECID          -c, --command CMD	run CMD on device     -m, --mode		print current device mode   -f, --file FILE	send file to device     -k, --payload FILE	send limera1n usb exploit payload from FILE   -r, --reset		reset client      -n, --normal		reboot device into normal mode (exit recovery loop)       -e, --script FILE	executes recovery script from FILE    -s, --shell		start an interactive shell         -q, --query		query device info          -a, --devices		list information for all known devices   -v, --verbose		enable verbose output, repeat for higher verbosity       -h, --help		prints this usage information       -V, --version		prints version information PhoneCheck 2.0      ERROR: Could not parse ECID from argument '%s'
 1.0.0-70-g4d726d1-dirty irecovery %s %s
 Unknown argument
 i:vVhrsmnc:f:e:k:qa ERROR: Missing action option
 Attempting to connect... 
 ERROR: %s
      Connected to %s, model %s, cpid 0x%04x, bdid 0x%02x
 %s Mode
 true auto-boot Unknown action
    ���������������������������������Ү�����~����������d���������o�������������4���@���������ɮ��L���X�������������������*�������&���9���;������6�������ecid command mode file payload reset normal script shell query devices verbose help version       Y@       @        00 @                            � @   � @   |� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  x���,���Ķ��L���\���l���<���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
       ��� ��� ��� ��� ������� �����������[���                        %s:     P O S I X L Y _ C O R R E C T           unknown option -- %s            unknown option -- %c                            option doesn't take an argument -- %.*s         ambiguous option -- %.*s                        option requires an argument -- %s                               option requires an argument -- %c                               runtime error %d
               b @           Pb @           pP @              @           � @           � @           �x @           �b @           0� @           �� @           x� @           t� @           p� @            � @           а @           P� @           X� @            � @           � @           � @           (� @           �� @            b @           �� @           p7 @           �0 @           `� @           p� @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P  �  t�  �  
  ��  
  �  ��  �  D  ��  D  n  ��  n    ��    �  ��  �  n  Ġ  n  �  Р  �  �  ܠ  �  n  �  n  �  ��  �  <    �  <   �!  �  �!  �!  �  �!  �"  $�  �"  [$  0�  [$  :.  <�   /  Z/  P�  `/  �/  X�  �/  �/  d�  �/  �/  h�   0  /0  l�  00  �0  t�  �0  �0  ��  �0  �1  ��  �1  �1  ��  �1  I2  ��  P2  �3  ��  �3  7  ��   7  ^7  С  `7  l7  ء  p7  -9  ܡ  09  �9  �  �9  :  ��  :  �:   �  �:  �;  �  �;  �;  �  �;   <  �   <  �<  �  �<  @=  (�  @=  w=  ,�  �=  �=  0�   >  6>  4�  @>  �>  8�  �>  �?  <�  �?  �@  @�  �@  2A  P�  @A  �D  \�  �D  �J  t�   K  "K  ��  0K  PK  ��  PK  pK  ��  pK  �K  ��  �K  �K  ��  �K  L  ��   L  #L  ��  0L  �L  Ģ  �L  
M  Ԣ  M  .M  �  0M  EM  �  PM  ~M  �  �M  �M  ��  �M  �M   �   N  6N  �  `P  eP  �                                                                                                                                  B   b  
 
20`pP�	 B   /     �  �  p7  �  	 B   /     �    p7     B        2P  PRP  RP  �P  �P  2P  �P  2P  2P  5b0PRP  2P  U�0P2P  RP  RP  �
�0`p�P   B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   0`pP�� R0`p	 �0`
p	P����  	 �0`
p	P����   b   b   b   b   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                 �          ��  8�  ��          ��  ��  �          ��  H�  (�           �  X�  @�          X�  p�  h�          |�  ��  x�          ��  ��  ��          �  ��  0�          x�  `�  ��          ��  ��  ��          ��  �  �          �  @�                          h�      x�      ��      ��      ��      ��       �      �      $�      <�      L�      \�      l�      |�      ��      ��      ��      ��      ��       �              $�      <�      T�      n�      ~�      ��      ��      ��      ��      ��      ��      �               �              ,�      <�              L�      \�      f�      n�              x�              ��      ��      ��      ��              ��      ��      ��      ��      ��      �      �      0�      >�      F�      h�      ��      ��      ��      ��      ��      ��              ��      ��      �      �      *�      F�      P�      Z�      b�      j�      r�      z�      ��      ��      ��      ��              ��      ��      ��      ��      ��              ��      ��      ��      �              �      �      ,�      8�              h�      x�      ��      ��      ��      ��       �      �      $�      <�      L�      \�      l�      |�      ��      ��      ��      ��      ��       �              $�      <�      T�      n�      ~�      ��      ��      ��      ��      ��      ��      �               �              ,�      <�              L�      \�      f�      n�              x�              ��      ��      ��      ��              ��      ��      ��      ��      ��      �      �      0�      >�      F�      h�      ��      ��      ��      ��      ��      ��              ��      ��      �      �      *�      F�      P�      Z�      b�      j�      r�      z�      ��      ��      ��      ��              ��      ��      ��      ��      ��              ��      ��      ��      �              �      �      ,�      8�               irecv_close    irecv_devices_get_all  irecv_devices_get_device_by_client     irecv_event_subscribe 
 irecv_execute_script  
 irecv_get_device_info  irecv_get_mode     irecv_getenv   irecv_open_with_ecid   irecv_reboot   irecv_receive  irecv_reset    irecv_saveenv  irecv_send_command     irecv_send_command_breq    irecv_send_file    irecv_set_debug_level   irecv_setenv  " irecv_strerror    # irecv_trigger_limera1n_exploit    DeleteCriticalSection =EnterCriticalSection  KGetEnvironmentVariableW tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery  -__C_specific_handler  k strtoull   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr  xmemcpy  |strchr  }strrchr ~strstr   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf  � fclose  � fflush  � fopen � fputc � fread � fseek � ftell � fwrite  � putchar � puts  � rewind  6 _strdup � strcmp  � strlen  � strncmp � strtok  	 __daylight   __timezone   __tzname  < _tzset  add_history   Cread_history  Ereadline  �write_history  �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �  libirecovery-1.0.dll    �  �  �  �  �  �  �  �  �  �  �  �  KERNEL32.dll    (�  api-ms-win-crt-convert-l1-1-0.dll   <�  <�  api-ms-win-crt-environment-l1-1-0.dll   P�  P�  P�  P�  api-ms-win-crt-heap-l1-1-0.dll  d�  api-ms-win-crt-math-l1-1-0.dll  x�  x�  x�  x�  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  ��  ��  ��  api-ms-win-crt-string-l1-1-0.dll    ��  ��  ��  ��  api-ms-win-crt-time-l1-1-0.dll  ��  ��  ��  ��  libreadline8.dll                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        0 @                    @                   00 @    0 @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          P     x�   `  L    � �@�`�������� � �@�`������0�`�p�����������������ȢТآ������ p  L   �����������������Э�� �� �0�@�P�`�p�����������Ю�� �� �0�   �     � �8�@�                                                                                                                                                                                                                                                                                                                                            ,               @   $                      ,    �&        / @   �                           �,                           �2                       ,    �3       �/ @                              5                       ,    �5        0 @   �                           Y=                           �=                       ,    ^?       �0 @   �                       ,    �B       �1 @                              +C                       ,    �C       �1 @   =                      ,    [        7 @   L                           �]                       ,    �^       p7 @   �                      ,    fn       09 @   b                          9y                           �y                       ,    �z       �; @   �                      ,    d�       �? @   �                          ��                       ,    >�       pK @                          ,    ۧ       �K @   H                       ,    ��       �K @   2                           0�                       ,    Ҵ        L @                                                                                                                                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   ]   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   / @   �       9  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	@� @   atexit ��   �  Y   __main 5�/ @          ��  �/ @   �   	__do_global_ctors  `/ @   j       �  
nptrs "�   �  �  
i #�   �  �  �/ @   j  R	 / @     	__do_global_dtors  / @   :       �[  p [  	�a @    	   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  `  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
  �   �,  __uninitialized  __initializing __initialized    ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	�a @   �  	�a @   =  
"	X� @   [  	P� @    �    w  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _dowildcard  �   	 b @   int  }   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 s  [  �/ @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   �/ @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  F  _newmode �   	`� @   int  �   
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   0 @   �       �  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	|� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�x @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�x @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	x� @   __mingw_initltsdyn_force ��   	t� @   __mingw_initltssuo_force ��   	p� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@   0 @   /       �}  
7  �  
  	  
M  *M      
B  ;d  1  -  %0 @   �   __tlregdtor m�   �0 @          ��  func m  R __dyn_tls_init L@  	  7  �  M  *M  B  ;d  pfunc N
$  ps O
�    �  00 @   �       ��  G  ?  �  o  g  �  �  �  �  �  �  `0 @    `0 @   +       L�  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �0 @   �    �    �	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _commode �   	�� @   int  w   "
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 #  U  �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �0 @   �       
  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   �0 @   �       �0  pexcept 0  &     type 
�  H  <  -1 @   b  �  R2 V1 @   7  Q	z @   Xs Yt w �ww(�ww0�w  5   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �    �1 @          �  _fpreset 	�1 @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 6	  	   	  __mingw_app_type �   	�� @   int  G   �  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  
  �1 @   =      Z	  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /V  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0V  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator ��3 @   ]      ��  4was_init �%  	�� @   5mSecs �%  �  �  !�  E4 @   h   �4  �  �  �  6h   
�  �  �  
�  .  �  
�  E  -  
�  �  �  

  �  �  
  '    "E  x   �  
F  t  l  
[  �  �  Y5 @   `  R	h{ @   Xu w t     �4 @   �4 @          �;  �  �  �  �  �  �  �  �  �    �4 @   �4 @          �  �  �  �      �      �4 @   �  Ru    !  �5 @   �   ��  �      �  )  '  �  8  6  7  �5 @   �   �  B  @  �  M  K  �  \  Z  �5 @   �  Ru      g6 @   g6 @   
       �w  �  f  d  �  q  o  �  �  ~    g6 @   g6 @   
       �  �  �  �  �  �  �  �  �  o6 @   �  Ru      �6 @   �6 @          �   �  �  �  �  �  �  �  �  �    �6 @   �6 @          �  �  �  �  �  �  �  �  �  �6 @   �  Ru    "$  �   �  
)  �  �  83  �   
4        �6 @   �6 @   
       s�      �  )  '  �  8  6    �6 @   �6 @   
       �  B  @  �  M  K  �  \  Z  �6 @   �  Rt      
7 @   `    R	8{ @    7 @   `  R	 { @      9�  �5 @   X       �|  
�  h  d  :�  ��6 @   
  Yu   4 @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �P2 @   b      �`  &addr ��  �  x  b �:  ��h �g  �  �  i �%  �  �  >03 @   P       �  new_protect �
u  
	  	  
d3 @   
  �  Ys  n3 @    
  |3 @   `  R	�z @     
�2 @   �
  �  Rs  �2 @   n
  
3 @   E
    Q��X0 
�3 @   `  >  R	�z @    �3 @   `  R	�z @   Qs   ?__report_error T�1 @   i       �/  &msg T  	  	  @argp ��   �X
2 @     �  R2 
&2 @   /  �  R	`z @   Q1XK 
52 @       R2 
C2 @   �
  !  Qs Xt  I2 @   �
   Afwrite __builtin_fwrite   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 3     7 @   L       �  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr �`7 @          �P  f ,�  1	  -	  l7 @   �  R�R  __mingw_raise_matherr � 7 @   >       �typ !�   E	  ?	  name 2�  ]	  Y	  a1 ?w   o	  k	  a2 Jw   �	  	  rslt 
w   � ex 0  �@Y7 @   R�@   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _fmode �   	�� @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  p7 @   �      �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  _  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  _  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	а @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   p7 @   �      ��  'exception_data �-�  �	  �	  old_handler �
	  �	  �	  action ��   C
  )
  reset_fpu ��   �
  �
  
�7 @   �
  �  R8Q0 (�7 @   �  R�R 
'8 @   �
  �  R4Q0 =8 @   �  R4 
�8 @   �
    R8Q0 
�8 @   �
  7  R8Q1 
�8 @   �
  S  R;Q0 �8 @   f  R; �8 @   y  R8 
�8 @   �
  �  R;Q1 
9 @   �
  �  R4Q1 
#9 @   �
  �  R8Q1 )(9 @   �
   �	   �
   y  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  09 @   b      O  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �: @   �       �n  	hDllHandle z�    �
  	reason {<  �  }  	reserved |S    �   ; @   K       �  
keyp �&�  �  {  
t �-�  �  �  4; @   �  
[; @   C  R	 � @     !n  �: @   �: @          �  �  �: @   )
   "n   ; @   �   �E  #�   �  u; @   )
    e; @   6  
�; @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   : @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�  �  �  @: @   �  B	  Rt  s: @   �  
|: @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �9 @   o       �$
  	key *%<  
  
  	dtor *1.  I
  ;
  
new_key ,$
  �
  �
  �9 @   �  �	  R1QH �9 @   �  
  Rt  
: @   �  Rt   �  &n  09 @   p       ��  �
  �
  '�  h9 @          �
  �  �
  �
  l9 @     q9 @     (�9 @   Rt   J9 @   �  �
  R|  )�9 @   �  R	 � @      �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _CRT_MT �   	b @   int  �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 n  V    __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	A� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	@� @    �   8  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 (    �; @   �      ;  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  x  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  �  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  �  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   x  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �> @   �       �7
  i �(�   �
  �
  �  �	`  �  �	  �
  �
  importDesc �@  �
  �
  o  �^
  importsStartRVA �	I      �  �> @   	z  ��  �  z  �  �  �  	�  �> @    �  �  �  �  �  M  I  �  ^  \      M  ? @   ? @   J       �q  j  h  f  }  v  r  �  �  �  �  �  �    
_IsNonwritableInCurrentImage �  @> @   �       ��  pTarget �%`  �  �  �  �	`  rvaTarget �
�  �  �  o  �^
  �  �  �  @> @   _  �/  �  _  �  �  �  	�  P> @    o  �  o  �  �  �  �  �    
      M  t> @   t> @   I       �q      f  }  %  #  �  /  -  �  9  7    
_GetPEImageBase �`   > @   6       �0  �  �	`  	�   > @   D  �	�  D  �  �  �  	�  > @    T  �  T  �  �  F  B  �  W  U       
_FindPESectionExec y^
  �= @   s       �%  eNo y�   e  a  �  {	`  �  |	  v  t  o  }^
  �  ~  �  ~�   �  �  	�  �= @   )  �	�  )  �  �  �  	�  �= @    9  �  9  �  �  �  �  �  �  �       
__mingw_GetSectionCount g�   @= @   7       ��  �  i	`  �  j	  �  �  	�  @= @     m	�    �  �  �  	�  P= @      �    �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
  �< @   �       �  p Y&s  �  �  �  [	`  rva \
�      �  �< @   �   _�  �  �   �  �  �  	�  �< @    �   �  �   �  �      �  %  #      	M  �< @     c
q  1  /  f    }  =  9  �  [  Y  �  e  c     
_FindPESectionByName :^
   < @   �       �M  pName :#�  x  n  �  <	`  �  =	  �  �  o  >^
  �  �  �  ?�   �  �  �  5< @   �   F  �  �   �  �  �  �  E< @    E< @          �  �  �  �  �  �  �  �     &/< @   �  -  Rt  '�< @   z  Rs Qt X8  _FindPESection $^
  �  �  $`  (rva $-�  �  &	  o  '^
  �  (�    _ValidateImageBase   �  �  `  pDOSHeader �  �  	  pOptHeader v   )�  �; @   ,       �~  �  �  �  �  �  �  �  �  	�  �; @    �   �    �  �   �  �      �  (  &     *M  �; @   P       �f  4  0  +q  Q}  G  C  �  f  d  �  p  l    8   �  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  &  �? @   �      �  
__gnuc_va_list �   #__builtin_va_list �   char �   
va_list w   
size_t #,�   long long unsigned int long long int 
wchar_t b
  short unsigned int int long int 	�   6  	#  unsigned int long unsigned int unsigned char double float long double 	�  	6  optind #  optopt #  opterr #  optarg 6  option  >*  name @/   has_arg A#  flag B@  val C#   �  	�   /  $E  G~  no_argument  required_argument optional_argument  _iobuf 0!
  _ptr %6   _cnt &	#  _base '6  _flag (	#  _file )	#  _charbuf *	#   _bufsiz +	#  $_tmpfname ,6  ( 
FILE /~  
DWORD �U  signed char short int WCHAR 1�   E  	E  	S  LPWSTR 5X  LPCWSTR 9]  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS E  �i  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  %tagCOINITBASE E  ��  COINITBASE_MULTITHREADED   VARENUM E  		+  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � �  	@b @   �  	<b @   �  	8b @   &__mingw_optreset D#  	x� @   �  	p� @   
place f6  	0b @   
nonopt_start i#  	(b @   
nonopt_end j#  	$b @   �   �  �   ! �  
recargchar m�  	 } @   
recargstring n�  	�| @   �   :  �    *  
ambig o:  	�| @   �   f  �   ' V  
noarg pf  	�| @   �   �  �    �  
illoptchar q�  	P| @   
illoptstring r�  	0| @   vfprintf )#  �  �  4  �    	  �  fprintf "#  	  �  4   __acrt_iob_func ]�  @	  E   '__p___argv ��  strncmp 
V#  w	  /  /  �    strlen 
@�   �	  /   strchr 
D6  �	  /  #   GetEnvironmentVariableW ?  �	  q  b     getopt_long_only O#  PK @           ��
  �  ,#  �  �  �  ,+�
  �  �  �  ,>/  �  �  �  -�
      idx --@  � kK @     R�RQ�QX�XY�Yw � w(5  	;  	*  getopt_long M#  0K @           �q  �  #      �  &�
  3  /  �  9/  I  E  �   �
  _  [  idx  -@  � KK @     R�RQ�QX�XY�Yw � w(1  getopt #   K @   "       �  �  #  u  q  �  !�
  �  �  �  4/  �  �  K @     R�RQ�QX�XY0w 0w(0  (getopt_internal C#  �D @   (      ��  �  C#  �  �  �  C*�
  �  �  �  C=/  1  '  �  D�
  ]  U  idx D*@  � )flags D3#  �  }   oli F6  �  �   optchar G#  �  �  *�  G#  2  ,  +posixly_correct H
#  	 b @   ,start g�E @      C
  Rt Qu Xs Yv  ^F @   �	  [
  R}  �F @   �  �
  Rv Q} X~ Y�  �F @   �	  �
  R} Qs  &G @   �	  �
  R	| @   Q0X0 qG @   �	  �
  R} Q- �G @   �	  	  R} Q- �H @      '  Ru Yv  �I @      T  R���Qt Xs Yv  �I @   �  y  R	P| @   Qs  J @   �  �  R	 } @   QW tJ @   �  �  Rv Q} X~ Y� w 0 �J @   �  R	 } @   Qs   -parse_long_options �#  @A @   �      �   �  �"�
  N  J  �  �5/  b  ^  �  ��
  z  r  idx �*@  �  �  .�  �3#  � current_argv �6  �  �  has_equal �6  �  �  current_argv_len �	�       i �#  ?  9  ambiguous �	#  h  V  match �#  �  �  �A @   �	  /  Ru Q= B @   S	  S  Ru Q~ Xt  $B @   w	  k  R~  �B @   �  �  R	�| @   Qt Xu  �C @   w	  �  Ru  �C @   �  �  R	0| @   Qu  D @   �  �  R	�| @   Qt Xu  kD @   �  R	�| @   Qu   !permute_args ��? @   �       ��  panonopt_start �#  �  �  panonopt_end �&#    
  opt_end �8#  5  /  �  ��
  S  K  cstart �#  z  p  cyclelen �#  �  �  i �#  �  �  j �#  �  �  ncycle �#  �  �  nnonopts �&#  �  �  nopts �0#      pos �7#  0  (  swap �6  N  J  /�  �? @    �  ��  e  [  �  �  �  0�  1�  �  �     2gcd �#  �  a �	#  b �#  3c �#   !warnx ~�@ @   �       ��  fmt ~/  �  �  
ap ��   �X4�  �@ @    �@ @   W       �  �  �    �  �  5�@ @   @	  �@ @   	  u  R2 �@ @   �  �  Q	 | @   Xu  A @   	  �  R2 A @   �  �  Qs Xt  A @   	  �  R2 *A @   "  R:   6_vwarnx u"  fmt u/  ap u!�    7fputc __builtin_fputc 
  �    D  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ;  #  �"  _MINGW_INSTALL_DEBUG_MATHERR �   	Pb @   int  �   r  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  pK @          !#  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	`b @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  pK @          �_File *�       _Format J�      _ArgList Z�   8  2  �K @   �  R0Q�RX�QY0w �X   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �K @   H       �#  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	pb @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �K @   H       �_Format .�  ^  X  
ap 
�   �Xret 	  s  q  �K @   �  �  R1 �K @   �  R0Xs Y0w t    �     
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �K @   2       ($  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�b @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  �K @   2       �_File )�  �  �  _Format I�  �  �  
ap 
�   �hret 	  �  �  
L @   �  R0Q�RX�QY0w �   �   x   	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �$  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	�� @   local__winitenv 
`  	�� @   '  
	�b @     
	�b @    �    !  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   L @         
%  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	c @   �      �   __imp_at_quick_exit g)  	c @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	c @   
initial_tzname1 }
V  	 c @   
initial_tznames ~�  	�b @   
initial_timezone 
%  	�b @   
initial_daylight �  	�b @   __imp_tzname ��  	�b @   __imp_timezone ��  	�b @   __imp_daylight ��  	�b @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�b @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�b @   �	  __imp__amsg_exit �V  	�b @   F  __imp__get_output_format �\
  	�b @   -
  __imp_tzset ��  	�b @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�b @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �M @   5       ��
  file �!�
  �  �  fmt �6�  �  �  
ap ��   �h+ret �    	  �M @   �
  R4Q�RX�QY0w �  ,tzset "�M @   6       �  -  �M @   �M @   -       ��M @   +  �M @     �M @       ._tzset �/_get_output_format nF   L @          �0_amsg_exit iPM @   .       ��  ret i      aM @   z  �  R2 sM @   Q  �  Q	`} @   Xs  ~M @   <  R�  at_quick_exit �  0M @          �   func ]*�  *  &  1EM @   �   _onexit ��  M @          �p  func V%�  B  <  M @   �  Rs   __wgetmainargs J  �L @   j       �[  _Argc J"�  a  [  _Argv J5I  �  z  _Env JGI  �  �   �  JQ  �  �  !�  Jl�	  � �L @   0  �L @   	  &  R	v  $0.# �L @   �  �L @   �  �L @   �  �L @   U   __getmainargs >  0L @   j       �E  _Argc >!�  �  �  _Argv >1S  �  �  _Env >@S    	   �  >J  (  "  !�  >e�	  � PL @   �  `L @   �    R	v  $0.# eL @   �  nL @   �  yL @   u  �L @   U   2   N @   6       �N @   +  N @     *N @                                                                                                                                                                                                                                                                                                                                                                                                                                                
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   I ~  (   H}  $ >   I   :!;9I�B  4 :!;9I�B  
 :;9I8  	 !I  
4 :!;9I  & I  (   
 :;9I  H}  .?:;9'I<   :!;9I�B  4 :!;9I?<  4 G  I  ! I/   :!;9I�B   1�B   :!;9I   :!;9I  .?:!;9!'I@z   :!;9I  :;9  7 I  >!!I:;9  .?:!;9!'I<      4 :!;9I�B  !.:!;9!'@z  "%  # I  $>I:;9  %>I:;9  &4 :;9I?  '. ?:;9'I<  (.:;9'I@z  ) :;9I�B  *4 :;9I�B  +4 :;9I  ,
 :;9  -.:;9'I@z  . :;9I  /1R�BUXYW  0U  14 1�B  2.:;9'I   34 :;9I  41R�BXYW  5H }  6.:;9'   7. ?<n:;   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                                                                                                                                                                                                                                                                                                                                   5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg #    K   �
        '  N  i  s  }  �  �  �  �   	 / @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �    7  R  }  �  �  �  �  �  6     .   �
          E  P  R     .   �
      �  �  �  �   	�/ @    6     .   �
      A  Y  �  �      K   �
      �  �    8  A  J  T  `  j  r   	 0 @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �  �  
    6     .   �
      m  �  �  �  �     <   �
           G  b  i  p  w   	�0 @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �  �  	  	   	�1 @   		3 6     .   �
      m	  �	  �	  �	  |    s   �
      
  2
  Y
  t
  �
  �
  �
  �
  �
  �
  �
  �
  �
  �
  �
  �
        	�1 @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	�3 @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      h  �  �  �  �  �   	 7 @   
L�Z
�KTYZ
g=yuX 6     .   �
      /  G  n  y  �    U   �
      �  �  
  	+
  9
  G
  O
  [
  e
  v
  �
  �
   	p7 @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      �
  �
    8  B  L  V  b  l  t  �  �  �  �   	09 @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
          B  L  6     .   �
      �  �  �  �  G    K   �
      X  p  �  �  �  �  �  �  �  �   	�; @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< ]	    n   �
      >  V  ~  �  �  �  �  �  �  �  �  �  �  �          	�? @   ��/x<_//a</�< 9zXL  X�  J"�
y<	�8	�M 
A	t>> w<  G  J�
= <    TXpfuV"Ys	 X � � <Y �f <Y	 �X < f	X� �bur�p<�Vrt��X <� = I=   Ju�.	p�<d�Xk
��% j�  <% J JZ �N �
��
Y
 ��<�J�</X�J�
�
� Xf <�� ��f^�<KY ��� ��8<Z�	t��2K;�/
���X� � �P<	X�X�XJ�w� ��	��+̟^X!&��w�!!��
A�
tvf
�( � f�X�
 ��	������f���
���f	�fY�-0��
�N  �� �, X t5 t�� � ��
�0�t<J<t, �
�. ��3 J- �����~�
�	�Xt	fxfK  �gX�	M
J
�' I� �� ��'tXu
�. �!��
�nJfgt	u	;u��P�	K
<
ew
cu	^]h��
Q�X�...�X<ugit0,�,Z� �J<t
�gu ��
g �~�	K;Xf X � J+ ��
zf=sg
y�f*Je.	
�!t�f<�
e� Ju� ����k� X
.�
�	�
	X�<��z��	 �u�g
t
J
�
.L
h	XL
h 6     .   �
      s  �  �  �  n     A   �
      $  <  e  �  �  �  �  �   	pK @   K
�<.Y �     A   �
      
  "  K  f  t  �  �  �   	�K @   g?	YT�Y	 X� <uX �     A   �
      �    .  I  X  g  p  z   	�K @   KU	\fp	\;Y	Y W     O   �
      �  �    )  T  `  h  u  ~  �  �  |    h   �
      �    /  J  u  �  �  �  �  �  �  �  �  �  �  �   	 L @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	PM @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                       ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �         P   / @   :       D0u  4   P  `/ @   j       A�A�D@@
A�A�H       P  �/ @             ���� x �         �  �/ @             ���� x �      $      0 @   /       D0R
JN    L     00 @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�         �0 @             ���� x �      <   �  �0 @   �       A�A�D�P�
���
���A�A�B    ���� x �           �1 @             ���� x �      $   8  �1 @   i       A�A�DP   <   8  P2 @   b      A�A�A �Dp�
 A�A�A�D   \   8  �3 @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �            7 @   >       D`y       `7 @             ���� x �      4   h  p7 @   �      A�D0}
A�Mf
A�I     ���� x �      L   �  09 @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �  �9 @   o       A�A�A �D@U
 A�A�A�A    D   �  : @   �       A�A�D@R
A�A�FR
A�A�D      4   �  �: @   �       A�D0p
A�J�
A�A      ���� x �         �  �; @   ,          �  �; @   P       L   �   < @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       �  �< @   �          �  @= @   7          �  �= @   s          �   > @   6          �  @> @   �          �  �> @   �          ���� x �      T     �? @   �       B�B�A �A(�A0�A8��
�0A�(A� A�B�B�A      <     �@ @   �       A�A�A �DPw A�A�A�      l     @A @   �      B�B�B �B(�A0�A8�A@�AH�	D�e
HA�@A�8A�0A�(B� B�B�B�G    l     �D @   (      B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�C          K @   "       D@]       0K @           D@[       PK @           D@[     ���� x �         �  pK @          D@Y     ���� x �      ,   0	  �K @   H       A�A�D`A�A�   ���� x �         x	  �K @   2       DPm     ���� x �         �	   L @          L   �	  0L @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �	  �L @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �	  M @          A�D0WA�    �	  0M @             �	  PM @   .       A�D0   �	  �M @   5       DPp     �	  �M @   6       D0q     �	   N @   6       D0q                                                                                                                                                                                                  Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection options short_too nargv nargc long_options _DoWildCard _StartInfo                                                                                                                                                                                                                                                            C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:/M/B/src/mingw-w64/mingw-w64-crt/misc/getopt.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include getopt.c getopt.c vadefs.h corecrt.h getopt.h stdio.h minwindef.h winnt.h combaseapi.h wtypes.h string.h processenv.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h  �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
�y @   ���
`y @   ���
�y @   ���
�y @   ���
z @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ����R     ��U  ��2�  ����R     ��U  ��1�  ����R     ��U  ��1�  ����R     ��U  �	�	4�  �	�	��R     �	�	U  �	�	4�  �	�	��R     �	�	U  �	�	8�  �	�	��R     �	�	U  �	�	8�  �	�	��R     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
� V     �
�
T  �
�
4�  �
�
� V     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � ;            ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�             �	�
R�
�\��R��\���R���\           �	�
Q�
�V��Q��V���Q���V           �	�
X�
�]��X��]��]         �	�
Y�
�^���Y���^               �	�
�(���(��_���(��_���(��_       �
�
P�
�p���P��p���P             �
�
P�
�S��-���S��S��S     ��
0��
�
P��0�     ��R��	�      ��Q��	�         ��X��\���X���	\     ��Y��	�     ��U��	U               ��P��X����������P������	���	�	��         ��T��T��P��	T      ��0���_��_                  ��0�����������R��1�������0���	0��	�	0�            ��	����_��_��	����	_�	�	_        WRW��R���R          Q�S���Q���S        WXW��X���X          QYQ�U���Y���U          MW\WgQg�\��|���\   ?�P  ?W0�      Mg0�g�R��r���R��0�  6M]       �V���Q�R���V       �T���X�Q���T       MW\W�Q��Q��\     gsZ��Z       %T%%P%,Q,6]��T     %V%/P��V        ,Q,/]/6Q��Q       ��R��S���R�  ��T    ��R��S T                RQ�R�        QX�Q�        X�`�X� )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                                                                                                                                                                                                                      X         Z���� ���� ���� ���� ���� ���� ���� S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����          6��                                                                                       .file   a   ��  gcrtexe.c              �                                �              �                            �   0                        �   @                        %  �
                        @                          `             k  �                        �  �                        �  �
                        �                          �  0          �                       envp           argv            argc    (                          �                        '  �          9  p                        ^  �                        �             �  �
                        �                           �  `                          �
                    mainret            "  �                        8  �                        N  �                        d  �                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  @     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  p     +                 .file   '  ��  girecovery.c       quit    0       verbose 4           m  P                           y  �          �  
          �  �          �  D          �  n	          �  
          �  �
          �  n
          �  �
            �
          "  n          .  �          <  <          K  �          W  �          j  �      main    [          v  0                        �          .text   P     �  a            .data          �               .bss    0                       .rdata         �  +             .xdata  t      �                 .pdata  l      �   6                 )  �     +                 .text   @      .idata$7      .idata$5X      .idata$4(      .idata$68
      .text   H      .idata$7      .idata$5P      .idata$4       .idata$6,
      .text   P      .idata$7      .idata$5H      .idata$4      .idata$6
      .text   X      .idata$7       .idata$5@      .idata$4      .idata$6
      .text   `      .idata$7�
      .idata$5�      .idata$4�      .idata$6 	      .text   h      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text   p      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text   x      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7�
      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7�
      .idata$5�      .idata$4x      .idata$6�      .text   �      .idata$7|
      .idata$5�      .idata$4p      .idata$6|      .text   �      .idata$7x
      .idata$5�      .idata$4h      .idata$6l      .text   �      .idata$7t
      .idata$5�      .idata$4`      .idata$6\      .text   �      .idata$7p
      .idata$5�      .idata$4X      .idata$6L      .text   �      .idata$7l
      .idata$5�      .idata$4P      .idata$6<      .text   �      .idata$7h
      .idata$5x      .idata$4H      .idata$6$      .text   �      .idata$7d
      .idata$5p      .idata$4@      .idata$6      .text   �      .idata$7`
      .idata$5h      .idata$48      .idata$6       .text   �      .idata$7\
      .idata$5`      .idata$40      .idata$6�      .text   �      .idata$7X
      .idata$5X      .idata$4(      .idata$6�      .text   �      .idata$7T
      .idata$5P      .idata$4       .idata$6�      .text   �      .idata$7P
      .idata$5H      .idata$4      .idata$6�      .text   �      .idata$7L
      .idata$5@      .idata$4      .idata$6x      .text   �      .idata$7H
      .idata$58      .idata$4      .idata$6h      .text          .data   �      .bss    @       .idata$7�
      .idata$58      .idata$4      .idata$6
      .text         .data   �      .bss    @       .idata$7�
      .idata$5      .idata$4�      .idata$6�	      .text         .data   �      .bss    @       .idata$7�
      .idata$5      .idata$4�      .idata$6�	      .file   5  ��  gfake              hname   �      fthunk  �      .text                           .data   �                       .bss    @                        .idata$2                      .idata$4�      .idata$5�      .file   C  ��  gfake              .text                           .data   �                       .bss    @                        .idata$4                      .idata$5@                      .idata$7�
     
                 .file   g  ��  ggccmain.c             �                          p.0     �          �  `          �  �
                    __main  �          �  @       .text         �                .data   �                     .bss    @                       .xdata  P                      .pdata  D     $   	                 �  �&  
   a                   �  �     ?                    �  �     5                     �  0      0                      9     '                     �     �                     )  �     +                     4  P     �                .file   }  ��  gnatstart.c        .text   �                       .data   �                      .bss    P                           �  �,  
     
                 �  �     �                     �  `                             `     V   
                                               �                         )        +                 .file   �  ��  gwildcard.c        .text   �                       .data                          .bss    `                            �  �2  
   �                    �  w     .                     �  �                             �     :                      �     �                     )  0     +                 .file   �  ��  gdllargv.c         _setargv�                       .text   �                      .data                          .bss    `                        .xdata  h                      .pdata  h                         �  �3  
   �                   �  �     :                     �  �      0                      �     V                      [     �                     )  `     +                     4  �     0                .file   �  ��  g_newmode.c        .text                            .data                          .bss    `                           �  5  
   �                    �  �     .                     �  �                             F     :                      �     �                     )  �     +                 .file   �  ��  gtlssup.c              �                               �  0             �
                    __xd_a  P       __xd_z  X             �       .text          �                .data                          .bss    p                       .xdata  l                      .pdata  t     $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  �5  
   �  6                 �  
     �                    �  �                        �  �      0                      �                          7                            �     �                     )  �     +                     4       �                .file   
  ��  gxncommod.c        .text   �                        .data                          .bss    �                           �  Y=  
   �                    �  �	     .                     �                               �     :                      �     �                     )  �     +                 .file   )  ��  gcinitexe.c        .text   �                        .data                          .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �=  
   {                   �  "
     a                     �  @                            �     :                      #     �                     )        +                 .file   I  ��  gmerr.c            _matherr�                        .text   �      �                .data                          .bss    �                        .rdata   	     @               .xdata  �                      .pdata  �                         �  ^?  
   6  
                 �  �
                         �       �                    �  `     0                      
     �                      �     �                     )  P     +                     4  �     X                .file   f  ��  gCRT_fp10.c        _fpreset�!                       fpreset �!      .text   �!                      .data                          .bss    �                        .xdata  �                      .pdata  �                         �  �B  
   �                    �  �     -                     �  �     0                      �     X                           �                     )  �     +                     4       0                .file   z  ��  gmingw_helpers.    .text   �!                       .data                          .bss    �                           �  +C  
   �                    �  �     .                     �  �                             	     :                      	     �                     )  �     +                 .file   �  ��  gpseudo-reloc.c        %  �!                           4  P"          J  �       the_secs�           V  �#          p  �           {  �
                        �  �
                    .text   �!     =  &             .data                          .bss    �                       .rdata  `
     [                .xdata  �     0                 .pdata  �     $   	                 �  �C  
   K  �                 �  �     �                    �  �     �  
                 �  �     0                    �  \      W                       Z	     �                     V     	                       �	     O                    )  �     +                     4  8     �                .file   �  ��  gusermatherr.c         �   '                           �  �           �  `'      .text    '     L                .data                          .bss    �                       .xdata  �                      .pdata  �                         �  [  
   �                   �  �                         �  !	     r                     �       0                      �     �                           �                     )       +                     4       P                .file   �  ��  gxtxtmode.c        .text   p'                       .data                          .bss    �                           �  �]  
   �                    �  �     .                     �  @                            �     :                      �     �                     )  @     +                 .file   �  ��  gcrt_handler.c           p'                       .text   p'     �               .data                          .bss    �                       .xdata  �                      .rdata  �     (   
             .pdata  �                         �  �^  
   �                   �  �     ~                    �  �	     _                    �  `     0                      �     �  
                   _                            �                         )  p     +                     4  h     P                .file   #  ��  gtlsthrd.c             +  0)                           K             Y  �           g  �)          �  �           �  *          �  �*      .text   0)     b  "             .data                          .bss    �      H                 .xdata  �     0                 .pdata  �     0                    �  fn  
   �
  A                 �  y     a                    �  �
     �                    �  �     0                    �  �                             O     x                     �
     %                    )  �     +                     4  �     (               .file   7  ��  gtlsmcrt.c         .text   �+                       .data                         .bss    @                           �  9y  
   �                    �  �     .                     �  �                            �     :                      �     �                     )  �     +                 .file   K  ��  g    �            .text   �+                       .data                           .bss    @                          �  �y  
   �                    �       0                     �  �                                 :                      V     �                     )        +                 .file   �  ��  gpesect.c              �  �+                           �  �+             ,            �,          3  @-          K  �-          ^   .          n  @.          �  �.      .text   �+     �  	             .data                           .bss    P                       .xdata       ,                 .pdata  (     l                    �  �z  
   �  �                 �  8     �                    �  �
     �                    �        0                    �  �      �                       ;     K                     o     T                            �                     )  0     +                     4  �     (               .text   �/     2                 .data                           .bss    P                       .text   �/                       .data                           .bss    P                           )  `     +                 .file   �  ��  ggetopt.c              �  �/                       warnx   �0          �  @1      place   0      ambig   �          �  0      noarg   �          �  �          �  �4          �             		  $          	  (          !	  P          ,	   
      getopt   ;          7	  0;          C	  P;      .text   �/     �  t             .data         $                .bss    p                      .xdata  @     d                 .pdata  �     T                .rdata        B                    �  d�  
   <  �                 �  �     �                    �  �     ?                    �  0     0                    �  �                            �     a	                     �     +                       �     .                    )  �     +                     4       �               .file   �  ��  gmingw_matherr.    .text   p;                       .data   P                      .bss    �                           �  ��  
   �                    �  D     .                     �  `                            �"     :                      #     �                     )  �     +                 .file   �  ��  gucrt_vfprintf.    vfprintfp;                       .text   p;                     .data   `                     .bss    �                       .xdata  �                      .pdata  �                         �  >�  
   �                   �  r     8                    �  �     X                     �  �     0                      !#     r   	                   �     �                     )  �     +                     4  �     8                .file     ��  gucrt_printf.c     printf  �;                       .text   �;     H                .data   p                     .bss    �                       .xdata  �                      .pdata  �                         �  ۧ  
   �  
                 �  �     l                    �  L     -                     �  �     0                      �#     �   	                   �     �                     )        +                     4  0	     H                .file   %  ��  gucrt_fprintf.c    fprintf �;                       .text   �;     2                .data   �                     .bss    �                       .xdata  �                      .pdata                            �  ��  
   �                   �       b                    �  y     F                     �  �     0                      ($     �   	                   �     �                     )  P     +                     4  x	     8                .file   ;  ��  g__initenv.c           T	  �          d	  �      .text    <                       .data   �                     .bss    �                          �  0�  
   �                   �  x      �                     �                              �$     [                      �                         )  �     +                 .file   �  ��  gucrtbase_compa        s	   <                           �	  0<          �	  �<      _onexit =          �	  0=          �	  P                        �	  P=          �	  �=      tzset   �=          �	                       _tzset   >          
  �          
  �          -
  �          =
            M
         .text    <       "             .data   �     x   
             .bss    �                       .xdata  �     P                 .pdata       l                .rdata  `
                          �  Ҵ  
   �  Y                 �   !                          �  �     |                    �  0     0                      
%     �                     �                            �     `                    )  �     +                     4  �	     �               .text   @>      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�      .text   H>      .data          .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�      .text   P>      .data          .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6�      .text   X>      .data          .bss    �      .idata$7�      .idata$50      .idata$4       .idata$6
      .file   �  ��  gfake              hname   �      fthunk        .text   `>                       .data                           .bss    �                       .idata$2�                      .idata$4�      .idata$5      .file   �  ��  gfake              .text   `>                       .data                           .bss    �                       .idata$4                      .idata$58                      .idata$7�                      .text   `>      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   h>      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   p>      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   x>      .data          .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�      .text   �>      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   �>                       .data                           .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   U  ��  gfake              .text   �>                       .data                           .bss    �                       .idata$4�                      .idata$5                      .idata$7�     !                 .text   �>      .data          .bss    �      .idata$78      .idata$5`      .idata$40      .idata$6�      .text   �>      .data          .bss    �      .idata$7<      .idata$5h      .idata$48      .idata$6�      .text   �>      .data          .bss    �      .idata$7@      .idata$5p      .idata$4@      .idata$6      .text   �>      .data          .bss    �      .idata$7D      .idata$5x      .idata$4H      .idata$6      .text   �>      .data          .bss    �      .idata$7H      .idata$5�      .idata$4P      .idata$6*      .text   �>      .data          .bss    �      .idata$7L      .idata$5�      .idata$4X      .idata$6F      .text   �>      .data          .bss    �      .idata$7P      .idata$5�      .idata$4`      .idata$6P      .text   �>      .data          .bss    �      .idata$7T      .idata$5�      .idata$4h      .idata$6Z      .text   �>      .data          .bss    �      .idata$7X      .idata$5�      .idata$4p      .idata$6b      .text   �>      .data          .bss    �      .idata$7\      .idata$5�      .idata$4x      .idata$6j      .text   �>      .data          .bss    �      .idata$7`      .idata$5�      .idata$4�      .idata$6r      .text   �>      .data          .bss    �      .idata$7d      .idata$5�      .idata$4�      .idata$6z      .text   �>      .data          .bss    �      .idata$7h      .idata$5�      .idata$4�      .idata$6�      .text   �>      .data          .bss    �      .idata$7l      .idata$5�      .idata$4�      .idata$6�      .text    ?      .data          .bss    �      .idata$7p      .idata$5�      .idata$4�      .idata$6�      .text   ?      .data          .bss    �      .idata$7t      .idata$5�      .idata$4�      .idata$6�      .file   c  ��  gfake              hname   0      fthunk  `      .text   ?                       .data                           .bss    �                       .idata$2�                      .idata$40      .idata$5`      .file   �  ��  gfake              .text   ?                       .data                           .bss    �                       .idata$4�                      .idata$5�                      .idata$7x                       .text   ?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   ?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text    ?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   (?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   0?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   8?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6      .text   @?      .data          .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6      .text   H?      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$60      .text   P?      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6>      .text   X?      .data          .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6F      .text   `?      .data          .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6h      .text   h?      .data          .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6�      .text   p?      .data          .bss    �      .idata$7       .idata$50      .idata$4       .idata$6�      .text   x?      .data          .bss    �      .idata$7      .idata$58      .idata$4      .idata$6�      .text   �?      .data          .bss    �      .idata$7      .idata$5@      .idata$4      .idata$6�      .text   �?      .data          .bss    �      .idata$7      .idata$5H      .idata$4      .idata$6�      .text   �?      .data          .bss    �      .idata$7      .idata$5P      .idata$4       .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   �?                       .data                           .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file      ��  gfake              .text   �?                       .data                           .bss    �                       .idata$4(                      .idata$5X                      .idata$7     "                 .text   �?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4x      .idata$6�
      .text   �?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text   �?      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .file   .  ��  gfake              hname   x      fthunk  �      .text   �?                       .data                           .bss    �                       .idata$2x                      .idata$4x      .idata$5�      .file   C  ��  gfake              .text   �?                       .data                           .bss    �                       .idata$4�                      .idata$5�                      .idata$7�     "                 .text   �?      .data          .bss    �      .idata$7x      .idata$5�      .idata$4h      .idata$6x
      .file   Q  ��  gfake              hname   h      fthunk  �      .text   �?                       .data                           .bss    �                       .idata$2d                      .idata$4h      .idata$5�      .file   {  ��  gfake              .text   �?                       .data                           .bss    �                       .idata$4p                      .idata$5�                      .idata$7|                      .text   �?      .data          .bss    �      .idata$7H      .idata$5p      .idata$4@      .idata$6L
      .text   �?      .data          .bss    �      .idata$7L      .idata$5x      .idata$4H      .idata$6\
      .text   �?      .data          .bss    �      .idata$7P      .idata$5�      .idata$4P      .idata$6f
      .text   �?      .data          .bss    �      .idata$7T      .idata$5�      .idata$4X      .idata$6n
      .file   �  ��  gfake              hname   @      fthunk  p      .text   �?                       .data                           .bss    �                       .idata$2P                      .idata$4@      .idata$5p      .file   �  ��  gfake              .text   �?                       .data                           .bss    �                       .idata$4`                      .idata$5�                      .idata$7X                      .text   �?      .data          .bss    �      .idata$7      .idata$5X      .idata$4(      .idata$6,
      .text   �?      .data          .bss    �      .idata$7      .idata$5`      .idata$40      .idata$6<
      .file   �  ��  gfake              hname   (      fthunk  X      .text    @                       .data                           .bss    �                       .idata$2<                      .idata$4(      .idata$5X      .file   �  ��  gfake              .text    @                       .data                           .bss    �                       .idata$48                      .idata$5h                      .idata$7      &                 .text    @      .data          .bss    �      .idata$7�
      .idata$5H      .idata$4      .idata$6 
      .file   �  ��  gfake              hname         fthunk  H      .text   @                       .data                           .bss    �                       .idata$2(                      .idata$4      .idata$5H      .file   #  ��  gfake              .text   @                       .data                           .bss    �                       .idata$4                       .idata$5P                      .idata$7�
     "                 .text   @      .data          .bss    �      .idata$7�
      .idata$50      .idata$4       .idata$6�	      .text   @      .data          .bss    �      .idata$7�
      .idata$5(      .idata$4�      .idata$6�	      .text    @      .data          .bss    �      .idata$7�
      .idata$5       .idata$4�      .idata$6�	      .text   (@      .data          .bss    �      .idata$7�
      .idata$5      .idata$4�      .idata$6�	      .text   0@      .data          .bss    �      .idata$7�
      .idata$5       .idata$4�      .idata$6~	      .text   8@      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6n	      .text   @@      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6T	      .text   H@      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6<	      .text   P@      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6$	      .file   D  ��  gcygming-crtend        ]
  `@                       .text   `@                       .data                           .bss    �                           q
  `@                         
                            �
  x                         �
  x@                         )  �     +                 .idata$2        .idata$58      .idata$4      .idata$2�       .idata$5@      .idata$4      .idata$4�      .idata$5�      .idata$7�
      .idata$40      .idata$5`      .idata$7      .rsrc       
    __xc_z         putchar �>          �
              �
            �
  @>          �
  �>            @            �
          0  �          N  X          a              p  �@            �          �  �          �  �          �  h          �  x           �              @          -             O  8          a  x          n  p            �          �  H          �  �          �      	        �  �          �  x?          
  �      __xl_a  0           
  �          1
  @          ?
  8@          L
             o
            �
  X      _cexit  (?          �
  `  ��       �
     ��       �
  �          �
                    ��       -     ��       I  0           [  �      __xl_d  @           w  �          �  8      _tls_end   	        �  �
      __tznameP>          �  P          �  �          �  @          �  �                          �          #  0           3  P          Q  X          e  �
          }  �          �      	        �  �      memcpy  �?          �  `          �  �      readlineH          �        puts     ?          �  �
             �             �          ,  x          B  0      malloc  �?      _CRT_MT       optarg  p          Q   @          ]  �          v  �          �              �  8          �  �          �            �  (          �     ��         x          "  �          0  �          C  �          W            |  x           �  �      opterr  @          �  �          �            �  |          �  �?            �                      3  p           L  �       fflush  �>          c  @          r  <           �  P          �  0          �  P           �  �          �            �  �
                       ,  x          =  `      abort   �?          ^  �
          �  �          �  P           �  x          �  �      __dll__     ��       �      ��       �  ?            �          3  H@          H  P          e  �          r  �          }  �?          �  �          �  �
          �  d           �            �  �          �     ��       
  �      strrchr �?          %  �          V  �      calloc  �?          b  �          l  h          x  �           �  �          �            �  �      Sleep             �         _commode�           �         fseek   �>          �  �            p@            p          )  P           U  �      optind  <          i             �  �      __xi_z  (           �  �          �  �          �             �  H          �  @          �  h      strstr  �?            �>            p          0  �          K  |       signal  �?          V  �>          f  X           }              �  P      strncmp x>          �  p@          �  �
          �  @          �  P          �  �           '  �          5  (           d  0          �      ��       �  p      strtok  �>          �  �
          �            �  �            �            �          %  p          2  �          ?             ^  �          s  X                  fread   �>          �     ��       �         strdup  `>          �            �  �?          �  @@          �  X?      strtoull @            @?          (  0@          B  H      fopen   �>          M  �           z  `          �     ��       �          ftell   �>          �  �      fclose  �>          �  8          �  �      __xl_z  H       __end__              �  �          �                        '  �      strcmp  h>          J  �@      __xi_a             X  p?          g            s  (@      __xc_a              �  �          �     ��       �  P           �     ��   _fmode  �           �             �  `          �  �?                          �          1  �          ?  8?          T             e  h          x            �             �  �          �  ?          �  (      fputc   �>      __xl_c  8             �          "     	    optopt  8          /  �
          `             u  0          �  (          �  �          �  �          �  t           �             �  x            �          $  H>          /  X      _newmode`           ]  h?      fwrite  �>          g  �          }             �  �          �  `          �      ��       �            �      ��       �  �          �  �      rewind  ?            �/            P           4  �>          A  �          N        exit    �?          `  �          v     ��       �  �          �      ��       �  0          �  `          �  �          �         _exit   P?             p          $   �          0   �          ?   0?      strlen  p>          V   �          e   H?          q   �
          �   �          �   H      strchr  �?          �   P@          �   `?          �   �          !  �          !  �          7!  �           f!            �!  �          �!  �          �!  �          �!  �          �!  P           �!  �>          �!  �          "  �          "   ?      free    �?          "  �       -"  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame shell_usage mode_to_str buffer_read_from_filename print_hex print_device_info print_devices _is_breq_command parse_command load_command_history append_command_to_history init_shell received_cb precommand_cb postcommand_cb progress_cb print_progress_bar print_usage .rdata$.refptr.optarg longopts.0 __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names permute_args parse_long_options illoptstring recargstring getopt_internal posixly_correct.0 nonopt_end nonopt_start illoptchar recargchar getopt_long getopt_long_only local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .text.startup .xdata.startup .pdata.startup .ctors.65535 _head_libirecovery_1_0_dll ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp_GetEnvironmentVariableW __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone irecv_saveenv __imp_irecv_strerror __imp_irecv_get_mode _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter __imp_irecv_devices_get_all .refptr.__mingw_initltsdrot_force __imp_irecv_close __imp_calloc __imp___p__fmode __imp___p___argc __imp_irecv_devices_get_device_by_client __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset __imp_irecv_reset write_history GetLastError __imp__initialize_wide_environment __rt_psrelocs_start __imp_irecv_execute_script __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection irecv_execute_script __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ __imp_readline __imp_fputc VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll __imp_write_history .refptr.__imp___initenv __imp_ftell _tls_start irecv_reboot __imp_irecv_get_device_info irecv_reset .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler __imp_irecv_setenv irecv_set_debug_level .refptr.optarg TlsGetValue __imp_irecv_send_command __imp_strcmp __bss_start__ __imp___C_specific_handler __imp_putchar ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ irecv_get_mode .refptr.__mingw_app_type __mingw_initltssuo_force _head_libreadline8_dll VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a read_history __imp__tzset ___crt_xp_start__ irecv_send_file __imp_LeaveCriticalSection libirecovery_1_0_dll_iname __C_specific_handler __mingw_optreset .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf ___crt_xp_end__ __imp_irecv_open_with_ecid __imp_irecv_set_debug_level __minor_os_version__ __p___argv __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_strdup __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit irecv_open_with_ecid __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname irecv_close _tls_used __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ __imp_rewind .refptr._newmode __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ __imp_strstr __native_vcclrit_reason ___crt_xc_end__ __imp_read_history .refptr.__mingw_initltssuo_force irecv_strerror __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func __native_startup_state ___crt_xc_start__ __imp_irecv_event_subscribe ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_add_history __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a irecv_receive _head_lib64_libapi_ms_win_crt_convert_l1_1_0_a .refptr.__mingw_initltsdyn_force __rt_psrelocs_size __imp_irecv_getenv .refptr.__ImageBase __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp_irecv_send_file __imp___p___wargv __imp_strlen irecv_setenv __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs add_history __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __imp_strtok __p__wenviron GetEnvironmentVariableW _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __IAT_start__ __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit irecv_devices_get_device_by_client __DTOR_LIST__ __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp_TlsGetValue __imp___p__wenviron __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __imp_irecv_trigger_limera1n_exploit __p___argc __imp_VirtualProtect __imp_irecv_send_command_breq ___tls_end__ __lib64_libapi_ms_win_crt_convert_l1_1_0_a_iname .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm irecv_send_command __imp_fclose __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm irecv_get_device_info __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ libreadline8_dll_iname __loader_flags__ __imp_strchr __imp_irecv_saveenv ___chkstk_ms __native_startup_lock __p__commode irecv_getenv __rt_psrelocs_end irecv_event_subscribe __minor_subsystem_version__ __imp_fflush __minor_image_version__ __imp___set_app_type irecv_trigger_limera1n_exploit __imp_irecv_reboot __imp__crt_at_quick_exit __imp_printf __imp_fread .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR __imp_irecv_receive __imp_strtoull DeleteCriticalSection _initialize_wide_environment irecv_devices_get_all __imp__configure_narrow_argv irecv_send_command_breq _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv __imp_fopen .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_fseek __imp_daylight __p___wargv __mingw_app_type 