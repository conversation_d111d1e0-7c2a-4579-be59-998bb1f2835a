MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� �<�g � �  � & ( ,   ^     �        @                       �    �  `                                             �  �   �  �   `  �           �  �                           �R  (                    �  @                          .text   �*      ,                 `  `.data   @   @      2              @  �.rdata  �   P      4              @  @.pdata  �   `      D              @  @.xdata  \   p      H              @  @.bss    �   �                      �  �.idata  �   �      L              @  �.CRT    `    �      X              @  �.tls        �      Z              @  �.rsrc   �   �      \              @  �.reloc  �    �      b              @  B/4      �   �      d              @  B/19     ��   �   �   j              @  B/31     �&   �  (   D             @  B/45     �*      ,   l             @  B/57         0     �             @  B/70     .   @     �             @  B/81     0   P     �             @  B/97     a   p     �             @  B/113    �   �     �             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H��G  1��    H��G  �    H��G  �    H�LG  f�8MZuHcP<HЁ8PE  tfH��G  �
�o  � ��tC�   �$  �$  H�MH  ����#  H�H  ���d  H��F  �8tP1�H��(Ð�   �v$  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�G  �l  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H��G  L��n  H��n  H�
�n  � ��n  H�aG  D�H��n  H�D$ ��   �H��8��    ATUWVSH�� H��F  H�-�  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5�F  1�����V  �����  �n     ����L  ���e  H��E  H� H��tE1��   1����  H�
�F  �c�  H�F  H�
����H���"  �  ��m  �{Hc�H��H���@#  L�%�m  H�Ņ��F  H��1�I���!  H�pH���#  I��H�D I�H��H���"  H9�u�H�H�    H�-]m  �  H�E  L�Bm  �
Lm  H� L� H�7m  �2#  �
m  �m  ����   � m  ��ttH�� [^_]A\�f�     H�59E  �   ���������   �   ��������H�-E  H�
E  ��!  �   �������1�H�����f�     �c!  ��l  H�� [^_]A\�f.�     H��D  H�
�D  �   �o!  �7���f�H����������y!  �H��(H�D  �    ������H��(� H��(H��C  �     �z�����H��(� H��(�  H���H��(Ð�����������H�
	   �����@ Ð��������������VSH��(�ֺ/   H���!  H�PH��HEڅ�tGH�5U�  �   ��I��H�t;  H���\  �   ��I��A�w  �   H�
p;  H��([^��  H�5�  �   ��I��H�-;  H���  �   ��I��뷐���������%b~  ���%R~  ���%B~  ���%2~  ���%"~  ���%~  ���%~  ���%�}  ��H��(H��*  H� H��t"D  ��H��*  H�PH�@H��*  H��u�H��(�fD  VSH��(H�B  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^�S��� 1�fD  D�@��J�<� L��u��fD  �Jj  ��t�D  �6j     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H�A  �8t�    ��t��tN�   H��([^�f�H��  H�5ډ  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�=  Hc�H����    H� <  �DA �y�qH�q�   �C  �DD$0I��H��<  �|$(H��I���t$ �  �t$@|$P1�DD$`H��x[^ÐH�y;  ��    H��;  ��    H��;  �s���@ H��;  �c���@ H��;  �S���H�<  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�d  A�   �   H�
<  I���z  H�t$(�   �;  H��H��I���-  ��  ��    WVSH��PHc5&h  H�˅��  H�h  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H��g  H��H��H�H�x �     �#  �WA�0   H�H��g  H�T$ H�L��z  H���}   �D$D�P����t�P���u�_g  H��P[^_� ��H�L$ H�T$8A�@   �   DD�H5g  H�KI��H�S�<z  ��u��z  H�
3;  ���d���@ 1��!���H��f  �WH�
�:  L�D�>���H��H�
�:  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%�f  E��tH�e[^_A\A]A^A_]�fD  �~f     �9	  H�H��H��   H����  L�-k=  H�t=  �Nf      H)�H�D$0H�Cf  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5�<  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
�9  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  ��d  ������H�5�w  1�H�}�D  H��d  H�D� E��t
H�PH�HI����A��H��(D;%fd  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5�:  �s�;H��L�>H���Z����>L9�r��������H�
�7  �����H�
�7  ���������H��XH�ec  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
)c  �  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H��7  Hc�H��� 1ҹ   �t  H���>  H���  H��b  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �  H���@����   �   �  �f�     1ҹ   �  H��t*H�������   ���i���f�     �   ���T����   �   �E  �@����   �   �1  �,����   �   �  �����������ATUWVSH�� L�%�a  L���t  H�a  H��t6H�-;t  H�=t  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%�s  WVSH�� �+a  ��H�օ�u
1�H�� [^_ú   �   �  H��H��t3H�pH�5a  �8H���ss  H��`  H��H��`  H�C�xs  묃��멐VSH��(��`  �˅�u1�H��([^�D  H�5�`  H��� s  H�
�`  H��t'1��H��H��tH���9�H�Au�H��tH�B�  H���s  1�H��([^� H�A`  ��ff.�     @ SH�� ����   w0��tL�`  ����   �`     �   H�� [�f�     ��u��_  ��t��<�����f.�     ��_  ��uf��_  ��u�H��_  H��t�    H��H�[�\  H��u�H�
�_  H��_      ��_      ��q  �l����k����   H�� [������f�     H�
i_  ��q  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���1  H��w{H�D5  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H����  ��u�H��H�� [^_��    1�H��H�� [^_� H��4  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H�94  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L��3  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H�y3  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H�93  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L��2  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������AUATUWVS��D�Ɖ�L��)�)։�������   �� A�ՙA��D���u�D��)șA��E��~lHc�A��A�L�׉څ�~Yf�     M�1��f�     M��A��D�2A)�9�D��AL҃�Lc�N��M�M�M�9�u�A��I��E9�tD��뱐[^_]A\A]�A���u���ff.�     @ WVSH��0H�t$XL�L$hH��H�T$XL�D$`H�t$(��
  �   H� H�8�
  H��.  I��H����
  �   �l
  I��H��H���^
  �   �T
  �
   H���o
  �H��0[^_�ff.�      AWAVAUATUWVSH��H��  H�=�  H��$�   H��M��H��$�   �=   �D$<��L��$�   �D$8��  ��
  I��H���  H��I��H)�M�4$M����   1�L�D$0I�\$ E1�D��$�   �L$(�����A���*Hc�H��L��P9S�tV�D$(   L�3H�� A��M��tfI��L��H���P  ��u�L���<  H9���   H��uE��u��u�D���fD  H�PH9S�u��@9C��   DD$(�D$(��     �L$(L�D$0��uKA����ux��$�   ���  ��  ���.  ��      �?   H��H[^_]A\A]A^A_�fD  D�
�  E��t�H��$�   �8:t�I����H�
�-  �����@ L�D$0Ic�H��L�k��uRM��teD�6  E��tH��$�   �8:��   H�{ �l  H��$�   �-�  �8:�Z����:   �U���@ �U���wM����   L��W  H��$�    tH��$�   D�8H�S�CH�������1��
���f�     H��H�D$(�
  L�D$(H�������fD  H��$�   �8:�����H��H�
,  ���������I����H�
O,  �����%������d���HcT$8H��$�   D�t$<H��A��D�5  H�$W  H���3����
  ��t)H��$�   �8:tH��H�
J,  �E�����  ���D$81�H�{ tB��  �D$8��  H��$�   �8:�
��������k�����D$<��  �����������C��     AWAVAUATUWVSH��HD��$�   A��H��M��M��M���y  D�
H  �&  E����  �����  �FV  ����  A�E <-�  D��  E����  <+��  H�V      ����   ��  ������  ����f.�     ��U      ��  D9���  Hc�H�t� �>-��  H�G*  H��  A����  A����  �5f  ����5  �=S  ���t%��I��A�؉������)����1  ����)��-  ���8  H�=%  ����Z���M��tkHc  H9|� t]<-�r  A���w  <:�k  ��L���	  H�������D$ M��L��H��L��$�   �����Ã����  H�=�  �L���L�=�  <:�,  ��-��   ��L���  H���  �PM��t��Wu	��;��  ��:��   � ��  �S  �}  f��^T     �8     1�E1�H�
�(  �Bf  �<T  ��������  A�E <-�����A��I�������@ �F����  �-   L����  H���0����=�  �t
�=�  ���  L�~H���-   L�=�  � �8  �-   L���  H����   �x:�%���H��S      � �j  ��  L�=qS  H�(  ��H�@  �F  �s�     �"  �����D  A���<+�#��������    �5�  H��'  �=�  H��  ���uP���t�=�  ��  ������  �����������H��H[^_]A\A]A^A_�f���H�5�R  ��  �   �ԉ�)�A��I���)��&�����  �L���L�=p  <:������ u�g  �
e  ��tA�} :��   �H  �?   �q���fD  A����2����
  ���t
�=	  ��  H�~H�=  <-������~ ������5�  ��H��&  ��  H��  ��������A�؉�I��L$<�[����L$<)�)��  ������     ��H�
w&  �����G���H��H�=�  1������ ��   �w  ���n  D9�|e�g  H�&  H�I  ��tA�} :t�W   H�
�&  �����+  W   A�} :������:   �I�����  �S�����  �����H�H�D� H��  �D$     M��L��H��L��$�   �������H�{%  H��  ������x:�\�������  A9�~Hc�H�D� H��P  �:���H�<%  H�}  ��  ��tA�} :t��H�
&  ������`  A�} :�
����0����     H��8E1��D$(    H�D$     ����H��8�ff.�      H��8H�D$`�D$(   H�D$ ����H��8�H��8H�D$`�D$(   H�D$ �e���H��8�H��8E1�L�D$ I��H��1���  H��8Ð�VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8�  H�t$ E1�I��H��1��  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�{  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H���  ���   ����  �[  � ��Z  H� H��'  H� H�M��t	A�$��  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���P  ���   ����  ��  � ���  H� H��  H� H�M��t	A�$�  1�H�� [^_]A\�fD  SH�� H����  ���    HD�H�� [�f�H��$  �8 t1�Ð�  ff.�     SH�� �˹   �  A��H��#  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H�$  ��~   H�
  �j   H�
  �V   H��  H��(�f.�     H��(H��#  ��>   H��  �*   H��  �   H��  H��(Ð����������%�`  ���%�`  ���%�`  ���     �%�`  ���%�`  ���%J`  ���%J`  ���%J`  ���%J`  ���%J`  ���%J`  ���%J`  ���%J`  ���%J`  ���     �%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���%j_  ���     �%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���     �%J^  ���%J^  ���%J^  ���%J^  ���%^  ���%^  ���%�]  ���%�]  ���%�]  ���%�]  ���%�]  ���%�]  ���%�]  ���%�]  ���%r]  ���%b]  ���%R]  ���     AWAVAUATUWVSH��  E1�E1�A�   H�-j  ��H��L�d$`����H�<  H�D$@    H�D$`H�-  H��$�   H�#  H��$�   H�  H�D$H    H�D$P    H�D$X    �D$h    H�D$p    �D$xd   Ǆ$�       HǄ$�       Ǆ$�   h   Ǆ$�       HǄ$�       Ǆ$�   l   H��$�   Ǆ$�       HǄ$�       Ǆ$�   n   HǄ$�       Ǆ$�       HǄ$�       Ǆ$�       M��I��H���H�D$     ������Ã��t]��lt<"��dtE��h�0  H�1�����1����� ��n�  A�   A�   렐A�   A�   �f��   �.����H�-!  HcH��H��)ǃ�t
A����  A��H�2��   H�T$<H�L$P���������  H�L$P1��D$<H�H����   D��H�5B  ���g��u��uH�x�  H�taH�
  �7���E��tHcT$<H�D$PH�ЃxtJH�������
   �$���H�L$P�D$<���D$<H�H��H��t+�P��u�E��u���H�
�  ������H�
�  �������I���1ۉ�H��  [^_]A\A]A^A_�H�L$@A�   H������H�L$@H���&  H�T$HL��  ���������   H�L$HH�T$X�����Å�u>H�|$X t6H�L$H����H�L$@����H�L$X�T���H�L$XH���g����!����]����   ������<[  A�"   �   H�
r  I������H�L$H�H���H�L$@�^����H��   �����   �m����   ��Z  A�'   �   H�
H  I�����������H�L$@�����   ��Z  A�$   �   H�
�  I���|�������������   ��Z  I��H�e  H�������א��������������������������`: @           ��������                                                                                                                                                                                                                                                                                                                                                                                �: @           ��������        ����                           ������������    V @   ?                     ����            �1 @           �1 @           �1 @           �� @   �� @   �3 @   �3 @   02 @   `3 @   �2 @   @2 @   A @   A @   A @      �p  $A @    A @   PDT PST @3 @    3 @                                                                                                                                                                                                           Usage: %s [OPTIONS] [UDID]
     Prints device name or a list of attached devices.

  If UDID is given, the name of the connected device with that UDID  will be retrieved.

  -l, --list      list UDIDs of all devices attached via USB
  -n, --network   list UDIDs of all devices available via network
  -d, --debug     enable communication debugging
  -h, --help      prints usage information

PhoneCheck 2.0
 debug help list network dhln    ERROR: No device with UDID %s attached.
 idevice_id     ERROR: Connecting to device failed!
    ERROR: Could not get device name!
      ERROR: Unable to retrieve device list!
 iWatch > %s iOS > %s  (WiFi)  (USB)                     @ @                            � @   � @   l� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ����\�������|�����������l���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      0���0���0���0���0�������0���������������                        %s:     P O S I X L Y _ C O R R E C T           unknown option -- %s            unknown option -- %c                            option doesn't take an argument -- %.*s         ambiguous option -- %.*s                        option requires an argument -- %s                               option requires an argument -- %c                               runtime error %d
               0@ @           p@ @           p: @              @           �^ @           �^ @           �R @           �@ @           P� @           �� @           h� @           d� @           `� @            � @           �� @           @� @           H� @            � @           � @           � @           (� @           p� @            @ @           �� @           � @           � @           P� @           \@ @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                            p    .  p  0  y  p  �  �  p  �  �  $p  �  
  Dp    $  dp  0  <  lp  @  A  pp  P  �  tp  0  j  �p  p  �  �p  �  �  �p       �p    ?  �p  @  �  �p  �  �  �p  �  �  �p  �  �  �p  �  Y  �p  `  �  �p  �  -   q  0  n  q  p  |   q  �  =  $q  @  �  ,q  �     <q      �   Hq  �   �!  Tq  �!  �!  \q  �!  0"  `q  0"  �"  dq  �"  P#  pq  P#  �#  tq  �#  $  xq  $  F$  |q  P$  �$  �q  �$  �%  �q  �%  �&  �q  �&  B'  �q  P'  �*  �q  �*  1  �q  1  21  �q  @1  `1  �q  `1  �1  �q  �1  �1  �q  �1  �1  �q  �1  "2   r  02  32  r  @2  �2  r  �2  3  r   3  >3  ,r  @3  U3  4r  `3  �3  8r  �3  �3  @r  �3  4  Hr  4  F4  Pr   6  ]:  �p  `:  e:  Xr                                                                                                                                                                                                                                                                                                                                  B   b  
 
20`pP�	 B  `5     �  �  �  �  	 B  `5     �    �     B         B0`  
 ! 0`
p	P���� B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   0`pP�� R0`p	 �0`
p	P����  	 �0`
p	P����   b   b   b   b   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                                                                         ��          ԙ   �  (�          �  h�  ��          4�  ȓ  ��          l�  ��  ȑ          ��  �  ؑ          ��  �   �          (�  @�  ��          p�  Д  ��          ��   �  ��          ̛  8�                          `�      ��      ��      ��      ԕ      �      �       �              <�      T�      l�      ��      ��      ��      ʖ      �      �      ��      �               �      0�              @�      P�      Z�      b�              l�              ��      ��      ��      ��              ��      ė      җ      ��      �      �      �      2�      @�      H�      j�      ��      ��      ��      Ș      И      ؘ              �      ��      �      �      ,�      H�      P�      Z�      d�              l�      v�              ��      ��      ��      ��              `�      ��      ��      ��      ԕ      �      �       �              <�      T�      l�      ��      ��      ��      ʖ      �      �      ��      �               �      0�              @�      P�      Z�      b�              l�              ��      ��      ��      ��              ��      ė      җ      ��      �      �      �      2�      @�      H�      j�      ��      ��      ��      Ș      И      ؘ              �      ��      �      �      ,�      H�      P�      Z�      d�              l�      v�              ��      ��      ��      ��              _ idevice_device_list_extended_free f idevice_free  h idevice_get_device_list_extended  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new  � lockdownd_get_device_name DeleteCriticalSection =EnterCriticalSection  KGetEnvironmentVariableW tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy  |strchr  }strrchr  __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf  � fputc � fwrite  � putchar � puts  � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset     �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  �  �  �  �  �  �  �  �  �  KERNEL32.dll    (�  (�  api-ms-win-crt-environment-l1-1-0.dll   <�  <�  <�  <�  api-ms-win-crt-heap-l1-1-0.dll  P�  api-ms-win-crt-math-l1-1-0.dll  d�  d�  d�  d�  api-ms-win-crt-private-l1-1-0.dll   x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  api-ms-win-crt-string-l1-1-0.dll    ��  ��  ��  ��  api-ms-win-crt-time-l1-1-0.dll                              0 @                    @                   @ @    @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          0     x�   @  0    �P�������������ȠРؠ����� ���(�0� P  L   ����ȢТآ`�p�����������Ч�� �� �0�@�P�`�p�����������Ш�� ��   �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                        ,               @   $                      <    �&       P @   �        6 @   =                      ,    :9       0 @   �                           �?                           �E                       ,    *F         @                              �G                       ,    5H        @   �                           �O                           �P                       ,    R       � @   �                       ,    7U       � @                              �U                       ,    `V       � @   =                      ,    �m       0 @   L                           �p                       ,    (q       � @   �                      ,    	�       @ @   b                          ܋                           e�                       ,    5�       �! @   �                      ,    �       �% @   �                          C�                       ,    �       �1 @                          ,    ~�       �1 @   H                       ,    1�       �1 @   2                           ��                       ,    u�       02 @                                                                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   �   �  GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden   �  h           9  char {   long long unsigned int long long int short unsigned int int long int unsigned int _iobuf !
  _Placeholder #    FILE /�   unsigned char double float long double �   optind �   {   i  option  >�  name @�   has_arg A�   flag BU  val C�    s  �   �  �   G  no_argument  required_argument optional_argument  signed char short int uint32_t (�   long unsigned int �   '  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 0S    2 ?    idevice_t 3V  3  idevice_options �   9�  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  idevice_connection_type �   @  CONNECTION_USBMUXD CONNECTION_NETWORK  idevice_info Fs  udid Gi   product_id H-  conn_type I�  conn_data J   idevice_info_t L�    �   $t	  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ lockdownd_error_t P�  /  R)�	  /  lockdownd_client_t S#�	  �	  	strrchr ]i  �	  �  �    	idevice_device_list_extended_free �  
  
   s  printf ��   2
  �   	idevice_get_device_list_extended �  j
  j
  U   
   free �
     	lockdownd_client_free �t	  �
  �	   lockdownd_get_device_name t	  �
  �	  �
   i  	idevice_free �    D   	lockdownd_client_new �t	  2  D  2  �   �	  fprintf ��   X  ]  �     X  	__acrt_iob_func ]X  �  �    	idevice_new_with_options �  �  �  �  [   D  	getopt_long M�   �  �   �  �  �  U   n  �  !exit �   �    "idevice_set_debug_level l4  �    #main '�    6 @   =      ��  argc �   �  �  argv �
  $    device )D  ��}client *�	  ��}dev_list +
  ��~device_name ,i  ��~
ret -�   o  e  i .�   ��}
mode /�   �  �  
include_usb 0�   �  �  
include_network 1�   �  �  
udid 2�  �  �  
c 4�        longopts 5�  ��~
U6 @   J  x7 @   �  �
  Ru Qt Xv Y| w 0 �7 @     �
  Q0�  u �  t  �7 @   �  �
  R0 �7 @       R1 8 @   2
  %  R��~Q��} i8 @   
  D  R	tR @    �8 @   
  \  Rt  �8 @   Y  s  R: �8 @   
  �  R	hR @    �8 @   
  �  R	}R @    
�8 @   �	  
9 @   �  �  R��}Qt X6 )9 @       Q��}X	�Q @    @9 @   �
  !  Q��~ 
X9 @   �
  
b9 @   �
  
l9 @   t  
9 @   o
  �9 @   b  l  R2 �9 @   �  �  R	R @   Q1X" 
�9 @   �
  
�9 @   �
  �9 @     �  Q1�  �R�  t  �9 @   �  �  R1 �9 @   b    R2  : @   �  0  R	@R @   Q1X' 
: @   �
  : @   b  T  R2 4: @   �  ~  R	�Q @   Q1X$ I: @   b  �  R2 [: @   7  Q	�Q @   Xt   $�  �  %�    &print_usage 
  argc �   argv *�
  is_error 4�   'name i   (�  P @   �       �J  �  z  p  )  �  �  �  �  �  �  �  �  e @   �	  |  Rs Q/ � @   b  �  R2 � @   7  �  Q	 P @   Xs  � @   b  �  R2 *� @   �  �  R	 P @   Q1X
w � @   b    R1 � @   7  6  Q	 P @   Xs  � @   b  R1  +__main __main putchar __builtin_putchar puts __builtin_puts fwrite __builtin_fwrite  ]   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  0 @   �       �  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	0� @   atexit ��   �  Y   __main 5� @          ��  � @   �   	__do_global_ctors  p @   j       �  
nptrs "�      �  
i #�       � @   j  R	0 @     	__do_global_dtors 0 @   :       �[  p [  	 @ @    	   �   <	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
H  �   �,  __uninitialized  __initializing __initialized  H  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	@ @   �  	@ @   =  
"	H� @   [  	@� @    �    �	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  	  _dowildcard  �   	 @ @   int  }    
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �    @          O	  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �     @          � �    Z
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 @  (  �	  _newmode �   	P� @   int  �   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   @   �       �	  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	l� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�R @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�R @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	h� @   __mingw_initltsdyn_force ��   	d� @   __mingw_initltssuo_force ��   	`� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@   @   /       �}  
`  �  3  /  
v  *M  E  A  
k  ;d  W  S  5 @   �   __tlregdtor m�   � @          ��  func m  R __dyn_tls_init L@  	  `  �  v  *M  k  ;d  pfunc N
$  ps O
�    �  @ @   �       ��  m  e  �  �  �  �  �  �  �  �  �  p @    p @   +       L�  �  �  �  �  �  �  �  �  �  �    
  �  %  !   � @   �    �    o  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �
  _commode �   	p� @   int  w   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 T  �  /  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 	  �  � @   �       i  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   � @   �       �0  pexcept 0  L  F  type 
�  n  b  = @   b  �  R2 f @   7  Q	�S @   Xs Yt w �ww(�ww0�w  5   �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  � @          '  _fpreset 	� @          � �    0  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 g
  O
    __mingw_app_type �   	�� @   int  G   ^  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  3  � @   =      �  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �� @   ]      ��  4was_init �%  	�� @   5mSecs �%  �  �  !�  U @   �   �4  �  �  �  6�   
�  �  �  
�  T  $  
�  k  S  
�  �  �  

      
  M  =  "E  �   �  
F  �  �  
[  �  �  i @   `  R	HU @   Xu w t     � @   � @          �;  �  �  �  �  	  	  �  	  	    � @   � @          �   	  	  �  +	  )	  �  :	  8	  � @   �  Ru    !  � @   �   ��  �  D	  B	  �  O	  M	  �  ^	  \	  7  � @   �   �  h	  f	  �  s	  q	  �  �	  �	  � @   �  Ru      w @   w @   
       �w  �  �	  �	  �  �	  �	  �  �	  �	    w @   w @   
       �  �	  �	  �  �	  �	  �  �	  �	   @   �  Ru      � @   � @          �   �  �	  �	  �  �	  �	  �  �	  �	    � @   � @          �  �	  �	  �  
  
  �  
  
  � @   �  Ru    "$  �   �  
)   
  
  83  �   
4  :
  8
    � @   � @   
       s�  D
  B
  �  O
  M
  �  ^
  \
    � @   � @   
       �  h
  f
  �  s
  q
  �  �
  �
   @   �  Rt      
  @   `    R	U @    - @   `  R	�T @      9�  � @   X       �|  
�  �
  �
  :�  �� @   
  Yu    @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �` @   b      �`  &addr ��  �
  �
  b �:  ��h �g  �
  �
  i �%      >@ @   P       �  new_protect �
u  0  .  
t @   
  �  Ys  ~ @    
  � @   `  R	�T @     
� @   �
  �  Rs  � @   n
  
 @   E
    Q��X0 
� @   `  >  R	�T @    � @   `  R	`T @   Qs   ?__report_error T� @   i       �/  &msg T  <  8  @argp ��   �X
 @     �  R2 
6 @   /  �  R	@T @   Q1XK 
E @       R2 
S @   �
  !  Qs Xt  Y @   �
   Afwrite __builtin_fwrite   �   6  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 d  L  0 @   L       9  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr �p @          �P  f ,�  W  S  | @   �  R�R  __mingw_raise_matherr �0 @   >       �typ !�   k  e  name 2�  �    a1 ?w   �  �  a2 Jw   �  �  rslt 
w   � ex 0  �@i @   R�@   �    H  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 .
  
  �  _fmode �   	�� @   int  �   v  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  � @   �      !  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	�� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   � @   �      ��  'exception_data �-�  �  �  old_handler �
	      action ��   i  O  reset_fpu ��   �  �  
� @   �
  �  R8Q0 ( @   �  R�R 
7 @   �
  �  R4Q0 M @   �  R4 
� @   �
    R8Q0 
� @   �
  7  R8Q1 
� @   �
  S  R;Q0 � @   f  R; � @   y  R8 
 @   �
  �  R;Q1 
 @   �
  �  R4Q1 
3 @   �
  �  R8Q1 )8 @   �
   �	   �
   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @ @   b      �  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	�� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �  @   �       �n  	hDllHandle z�  <
  $
  	reason {<  �
  �
  	reserved |S  :  "   %! @   K       �  
keyp �&�  �  �  
t �-�  �  �  D! @   �  
k! @   C  R	 � @     !n  �  @   �  @          �  �  ! @   )
   "n  ! @   �   �E  #�   �  �! @   )
    u! @   6  
�! @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�      @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�      P  @   �  B	  Rt  �  @   �  
�  @   �  Rt   ___w64_mingwthr_add_key_dtor *�   � @   o       �$
  	key *%<  ;  1  	dtor *1.  o  a  
new_key ,$
  �  �  � @   �  �	  R1QH � @   �  
  Rt  
  @   �  Rt   �  &n  @ @   p       ��  �  �  '�  x @          �
  �  �  �  | @     � @     (� @   Rt   Z @   �  �
  R|  )� @   �  R	 � @      �    U  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  &  _CRT_MT �   	0@ @   int  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  `  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	A� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	@� @    �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 Y  A  �! @   �      �  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  �  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  �  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  �  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   �  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �$ @   �       �7
  i �(�   �  �  �  �	`  �  �	      importDesc �@  $  "  �  �^
  importsStartRVA �	I  4  ,  �  �$ @   	�  ��  �  �  �  �  �  	�  �$ @    �  �  �  �  �  s  o  �  �  �      M  $% @   $% @   J       �q  �  �  f  }  �  �  �  �  �  �  �  �    
_IsNonwritableInCurrentImage �  P$ @   �       ��  pTarget �%`  �  �  �  �	`  rvaTarget �
�      �  �^
      �  P$ @   �  �/  �  �  �  �  �  	�  `$ @    �  �  �  �  �  $     �  5  3      M  �$ @   �$ @   I       �q  A  ?  f  }  K  I  �  U  S  �  _  ]    
_GetPEImageBase �`  $ @   6       �0  �  �	`  	�  $ @   g  �	�  g  �  �  �  	�   $ @    w  �  w  �  �  l  h  �  }  {       
_FindPESectionExec y^
  �# @   s       �%  eNo y�   �  �  �  {	`  �  |	  �  �  �  }^
  �  �  �  ~�   �  �  	�  �# @   L  �	�  L  �  �  �  	�  �# @    \  �  \  �  �  �  �  �  �  �       
__mingw_GetSectionCount g�   P# @   7       ��  �  i	`  �  j	  �  �  	�  P# @   1  m	�  1  �  �  �  	�  `# @    A  �  A  �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
  �" @   �       �  p Y&s  	    �  [	`  rva \
�  .  ,  �  �" @     _�  �    �  �  �  	�  �" @      �    �  �  :  6  �  K  I      	M  	# @   &  c
q  W  U  f  &  }  c  _  �  �    �  �  �     
_FindPESectionByName :^
  0" @   �       �M  pName :#�  �  �  �  <	`  �  =	  �  �  �  >^
  �  �  �  ?�   �  �  �  E" @      F  �     �  �  �  �  U" @    U" @          �  �  �  �  �  �  �  �     &?" @   �  -  Rt  '�" @   z  Rs Qt X8  _FindPESection $^
  �  �  $`  (rva $-�  �  &	  �  '^
  �  (�    _ValidateImageBase   �  �  `  pDOSHeader �  �  	  pOptHeader v   )�  �! @   ,       �~  �    �  �      �  �  	�  �! @    �   �  '  !  �   �  �  A  =  �  N  L     *M  �! @   P       �f  Z  V  +q  Q}  m  i  �  �  �  �  �  �    8   =  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 &  W  �% @   �      �  
__gnuc_va_list �   #__builtin_va_list �   char �   
va_list w   
size_t #,�   long long unsigned int long long int 
wchar_t b
  short unsigned int int long int 	�   6  	#  unsigned int long unsigned int unsigned char double float long double 	�  	6  optind #  optopt #  opterr #  optarg 6  option  >*  name @/   has_arg A#  flag B@  val C#   �  	�   /  $E  G~  no_argument  required_argument optional_argument  _iobuf 0!
  _ptr %6   _cnt &	#  _base '6  _flag (	#  _file )	#  _charbuf *	#   _bufsiz +	#  $_tmpfname ,6  ( 
FILE /~  
DWORD �U  signed char short int WCHAR 1�   E  	E  	S  LPWSTR 5X  LPCWSTR 9]  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS E  �i  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  %tagCOINITBASE E  ��  COINITBASE_MULTITHREADED   VARENUM E  		+  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � �  	`@ @   �  	\@ @   �  	X@ @   &__mingw_optreset D#  	x� @   �  	p� @   
place f6  	P@ @   
nonopt_start i#  	H@ @   
nonopt_end j#  	D@ @   �   �  �   ! �  
recargchar m�  	 W @   
recargstring n�  	�V @   �   :  �    *  
ambig o:  	�V @   �   f  �   ' V  
noarg pf  	`V @   �   �  �    �  
illoptchar q�  	0V @   
illoptstring r�  	V @   vfprintf )#  �  �  4  �    	  �  fprintf "#  	  �  4   __acrt_iob_func ]�  @	  E   '__p___argv ��  strncmp 
V#  w	  /  /  �    strlen 
@�   �	  /   strchr 
D6  �	  /  #   GetEnvironmentVariableW ?  �	  q  b     getopt_long_only O#  `1 @           ��
    ,#  �  �  �  ,+�
    �  �  ,>/      
  -�
  -  )  idx --@  � {1 @     R�RQ�QX�XY�Yw � w(5  	;  	*  getopt_long M#  @1 @           �q    #  C  ?  �  &�
  Y  U  �  9/  o  k  
   �
  �  �  idx  -@  � [1 @     R�RQ�QX�XY�Yw � w(1  getopt #  1 @   "       �    #  �  �  �  !�
  �  �  �  4/  �  �  -1 @     R�RQ�QX�XY0w 0w(0  (getopt_internal C#  �* @   (      ��    C#  �  �  �  C*�
      �  C=/  W  M  
  D�
  �  {  idx D*@  � )flags D3#  �  �   oli F6  �  �   optchar G#  %    *�  G#  X  R  +posixly_correct H
#  	@@ @   ,start g, @      C
  Rt Qu Xs Yv  n, @   �	  [
  R}  �, @   �  �
  Rv Q} X~ Y�  �, @   �	  �
  R} Qs  6- @   �	  �
  R	�U @   Q0X0 �- @   �	  �
  R} Q- �- @   �	  	  R} Q- �. @      '  Ru Yv  �/ @      T  R���Qt Xs Yv  �/ @   �  y  R	0V @   Qs  #0 @   �  �  R	 W @   QW �0 @   �  �  Rv Q} X~ Y� w 0 �0 @   �  R	 W @   Qs   -parse_long_options �#  P' @   �      �   �  �"�
  t  p  �  �5/  �  �  
  ��
  �  �  idx �*@  �  �  .�  �3#  � current_argv �6  �  �  has_equal �6  �  �  current_argv_len �	�   B  :  i �#  e  _  ambiguous �	#  �  |  match �#  �  �  �' @   �	  /  Ru Q= (( @   S	  S  Ru Q~ Xt  4( @   w	  k  R~  ) @   �  �  R	�V @   Qt Xu  �) @   w	  �  Ru   * @   �  �  R	V @   Qu  * @   �  �  R	`V @   Qt Xu  {* @   �  R	�V @   Qu   !permute_args ��% @   �       ��  panonopt_start �#      panonopt_end �&#  8  0  opt_end �8#  [  U  �  ��
  y  q  cstart �#  �  �  cyclelen �#  �  �  i �#  �  �  j �#  �  �  ncycle �#      nnonopts �&#    
  nopts �0#  2  ,  pos �7#  V  N  swap �6  t  p  /�  & @    �  ��  �  �  �  �  �  0�  1�  �  �     2gcd �#  �  a �	#  b �#  3c �#   !warnx ~�& @   �       ��  fmt ~/  �  �  
ap ��   �X4�  �& @    �& @   W       �  �  �        5�& @   @	  �& @   	  u  R2 
' @   �  �  Q	�U @   Xu  ' @   	  �  R2 "' @   �  �  Qs Xt  ,' @   	  �  R2 :' @   "  R:   6_vwarnx u"  fmt u/  ap u!�    7fputc __builtin_fputc 
  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 l  T  F&  _MINGW_INSTALL_DEBUG_MATHERR �   	p@ @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99     �1 @          �&  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	�@ @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  �1 @          �_File *�  ,  &  _Format J�  E  ?  _ArgList Z�   ^  X  �1 @   �  R0Q�RX�QY0w �X   �   %   GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  �1 @   H       �&  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	�@ @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �1 @   H       �_Format .�  �  ~  
ap 
�   �Xret 	  �  �  �1 @   �  �  R1 �1 @   �  R0Xs Y0w t    �   �!  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �1 @   2       �'  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�@ @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  �1 @   2       �_File )�  �  �  _Format I�  �  �  
ap 
�   �hret 	  �  �  2 @   �  R0Q�RX�QY0w �   �   �"  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  (  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	�� @   local__winitenv 
`  	�� @   '  
	�@ @     
	�@ @    �   �#  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  02 @         l(  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	0A @   �      �   __imp_at_quick_exit g)  	(A @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	$A @   
initial_tzname1 }
V  	 A @   
initial_tznames ~�  	A @   
initial_timezone 
%  	A @   
initial_daylight �  	A @   __imp_tzname ��  	 A @   __imp_timezone ��  	�@ @   __imp_daylight ��  	�@ @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�@ @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�@ @   �	  __imp__amsg_exit �V  	�@ @   F  __imp__get_output_format �\
  	�@ @   -
  __imp_tzset ��  	�@ @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�@ @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �3 @   5       ��
  file �!�
  �  �  fmt �6�      
ap ��   �h+ret �  1  /  �3 @   �
  R4Q�RX�QY0w �  ,tzset "�3 @   6       �  -  �3 @   �3 @   -       ��3 @   +  �3 @     �3 @       ._tzset �/_get_output_format nF  02 @          �0_amsg_exit i`3 @   .       ��  ret i  =  9  q3 @   z  �  R2 �3 @   Q  �  Q	@W @   Xs  �3 @   <  R�  at_quick_exit �  @3 @          �   func ]*�  P  L  1U3 @   �   _onexit ��   3 @          �p  func V%�  h  b  -3 @   �  Rs   __wgetmainargs J  �2 @   j       �[  _Argc J"�  �  �  _Argv J5I  �  �  _Env JGI  �  �     JQ  �  �  !#  Jl�	  � �2 @   0  �2 @   	  &  R	v  $0.# �2 @   �  �2 @   �  �2 @   �  
3 @   U   __getmainargs >  @2 @   j       �E  _Argc >!�    �  _Argv >1S      _Env >@S  5  /     >J  N  H  !#  >e�	  � `2 @   �  p2 @   �    R	v  $0.# u2 @   �  ~2 @   �  �2 @   u  �2 @   U   2  4 @   6       �"4 @   +  .4 @     :4 @                                                                                                                                                                                                                                                                             
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  ( 
  H}   I  $ >   !I  (   
 :;9I8  	.?:;9'I<  
H }   :;9I  4 :!;9I  
4 :!;9I�B  I �~  & I  :;9  >!I:;9  .?:;9'I<   :!;!9I   1�B  . ?<n:!	;!   7 I   :;9I   <  >!!I:!;9!      :!;!'9I�B  H}  %U     4 :;9I?<   .?:;9'<  !.?:;9'�<  ".?:;9'<  #.?:;9'I@z  $I  %! I/  &.:;9'   '4 :;9I  (.1@z  )4 1�B  *H}�  +. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   I ~  (   H}  $ >   I   :!;9I�B  4 :!;9I�B  
 :;9I8  	 !I  
4 :!;9I  & I  (   
 :;9I  H}  .?:;9'I<   :!;9I�B  4 :!;9I?<  4 G  I  ! I/   :!;9I�B   1�B   :!;9I   :!;9I  .?:!;9!'I@z   :!;9I  :;9  7 I  >!!I:;9  .?:!;9!'I<      4 :!;9I�B  !.:!;9!'@z  "%  # I  $>I:;9  %>I:;9  &4 :;9I?  '. ?:;9'I<  (.:;9'I@z  ) :;9I�B  *4 :;9I�B  +4 :;9I  ,
 :;9  -.:;9'I@z  . :;9I  /1R�BUXYW  0U  14 1�B  2.:;9'I   34 :;9I  41R�BXYW  5H }  6.:;9'   7. ?<n:;   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                                                                                                                                                                                                        5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg [    Z   �
        S  l  
�   �   �  �  �  �  �  �  �  �   
 	P @   h
1
U?Y  � J t t � <Y  �  uf! W t t! � <Y!   	 6 @   '	.;;fkt�tt�u*����?? �YO�z�	t	ez��.tgL1<�H>!fX �� 
X f  <5 �<5 ntX5 
�v:4
�Y�Ji�% i� �  fY<5 Xz��Y�� .@X�YX� � O J����X�t�YW f  <Y��e.�&� �  <YfX� �  <YzXW �� <Y #    K   �
      @  X    �  �  �  �  �  �  �   	0 @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      )  A  h  �  �  �  �  �  �  �  6     .   �
      7  O  v  �  R     .   �
      �  �       	  @    6     .   �
      r  �  �  �      K   �
        '  N  i  r  {  �  �  �  �   	 @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �    >  I  6     .   �
      �  �  �  �  �     <   �
      9	  Q	  x	  �	  �	  �	  �	   	� @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �	  
  9
  D
   	� @   		3 6     .   �
      �
  �
  �
  �
  |    s   �
      K  c  �  �  �  �  �  �  �  �  �    
      /  8  A    	� @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	� @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      �  �  �  �  
  
   	0 @   
L�Z
�KTYZ
g=yuX 6     .   �
      `
  x
  �
  �
  �    U   �
          A  	\  j  x  �  �  �  �  �  �   	� @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
        '  N  i  s  }  �  �  �  �  �  �  �  �   	@ @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      4  L  s  }  6     .   �
      �  �    -  G    K   �
      �  �  �  �  �  �  �         	�! @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< ]	    n   �
      o  �  �  �  �  �  �  �  �         !  *  3  @  I    	�% @   ��/x<_//a</�< 9zXL  X�  J"�
y<	�8	�M 
A	t>> w<  G  J�
= <    TXpfuV"Ys	 X � � <Y �f <Y	 �X < f	X� �bur�p<�Vrt��X <� = I=   Ju�.	p�<d�Xk
��% j�  <% J JZ �N �
��
Y
 ��<�J�</X�J�
�
� Xf <�� ��f^�<KY ��� ��8<Z�	t��2K;�/
���X� � �P<	X�X�XJ�w� ��	��+̟^X!&��w�!!��
A�
tvf
�( � f�X�
 ��	������f���
���f	�fY�-0��
�N  �� �, X t5 t�� � ��
�0�t<J<t, �
�. ��3 J- �����~�
�	�Xt	fxfK  �gX�	M
J
�' I� �� ��'tXu
�. �!��
�nJfgt	u	;u��P�	K
<
ew
cu	^]h��
Q�X�...�X<ugit0,�,Z� �J<t
�gu ��
g �~�	K;Xf X � J+ ��
zf=sg
y�f*Je.	
�!t�f<�
e� Ju� ����k� X
.�
�	�
	X�<��z��	 �u�g
t
J
�
.L
h	XL
h 6     .   �
      �  �  �  �  n     A   �
      U  m  �  �  �  �  �  �   	�1 @   K
�<.Y �     A   �
      ;  S  |  �  �  �  �  �   	�1 @   g?	YT�Y	 X� <uX �     A   �
        6  _  z  �  �  �  �   	�1 @   KU	\fp	\;Y	Y W     O   �
      �    ?  Z  �  �  �  �  �  �  �  |    h   �
      !  9  `  {  �  �  �  �  �  �  �  �        (   	02 @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	`3 @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                        ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �      4   P  P @   �       A�A�D@^
A�A�E    l   P   6 @   =      B�B�B �B(�A0�A8�A@�AH�	G��
HA�@A�8A�0A�(B� B�B�B�A       ���� x �           0 @   :       D0u  4     p @   j       A�A�D@@
A�A�H         � @             ���� x �         �    @             ���� x �      $   �   @   /       D0R
JN    L   �  @ @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       �  � @             ���� x �      <   p  � @   �       A�A�D�P�
���
���A�A�B    ���� x �         �  � @             ���� x �      $   �  � @   i       A�A�DP   <   �  ` @   b      A�A�A �Dp�
 A�A�A�D   \   �  � @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         �  0 @   >       D`y     �  p @             ���� x �      4   (  � @   �      A�D0}
A�Mf
A�I     ���� x �      L   x  @ @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   x  � @   o       A�A�A �D@U
 A�A�A�A    D   x     @   �       A�A�D@R
A�A�FR
A�A�D      4   x  �  @   �       A�D0p
A�J�
A�A      ���� x �         �  �! @   ,          �  �! @   P       L   �  0" @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       �  �" @   �          �  P# @   7          �  �# @   s          �  $ @   6          �  P$ @   �          �  �$ @   �          ���� x �      T   �  �% @   �       B�B�A �A(�A0�A8��
�0A�(A� A�B�B�A      <   �  �& @   �       A�A�A �DPw A�A�A�      l   �  P' @   �      B�B�B �B(�A0�A8�A@�AH�	D�e
HA�@A�8A�0A�(B� B�B�B�G    l   �  �* @   (      B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�C       �  1 @   "       D@]     �  @1 @           D@[     �  `1 @           D@[     ���� x �         �	  �1 @          D@Y     ���� x �      ,   �	  �1 @   H       A�A�D`A�A�   ���� x �         8
  �1 @   2       DPm     ���� x �         p
  02 @          L   p
  @2 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   p
  �2 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   p
   3 @          A�D0WA�    p
  @3 @             p
  `3 @   .       A�D0   p
  �3 @   5       DPp     p
  �3 @   6       D0q     p
  4 @   6       D0q  Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion idevice_private lockdownd_client_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection options short_too nargv nargc long_options _DoWildCard _StartInfo                                                                                                                                                                                                                   C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools idevice_id.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include ../include/libimobiledevice idevice_id.c idevice_id.c stdio.h getopt.h stdint.h libimobiledevice.h lockdown.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:/M/B/src/mingw-w64/mingw-w64-crt/misc/getopt.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include getopt.c getopt.c vadefs.h corecrt.h getopt.h stdio.h minwindef.h winnt.h combaseapi.h wtypes.h string.h processenv.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                                                                                                 �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� "                   6 @    4R4�U��u q ���u p ����Rp �              6 @    4Q4�T��Q��	p 3$�Q"���Q��	p 3$�Q"�        �6 @    �0���0���0���	����0� �6 @   �1� �6 @   �0� �6 @   �0�          �6 @    �0���T��T��0���T                   �6 @    �0���P��S��P��S��P��S��S��S��S           P @    
Q
+T+k�Q�krTr��Q�      V @    0�+PerP   V @   ���  �   V @   ���  � 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
`S @   ���
@S @   ���
�S @   ���
�S @   ���
�S @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ���ae     ��U  ��2�  ���ae     ��U  ��1�  ���ae     ��U  ��1�  ���ae     ��U  �	�	4�  �	�	�ae     �	�	U  �	�	4�  �	�	�ae     �	�	U  �	�	8�  �	�	�ae     �	�	U  �	�	8�  �	�	�ae     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
��h     �
�
T  �
�
4�  �
�
��h     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � ;            ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�             �	�
R�
�\��R��\���R���\           �	�
Q�
�V��Q��V���Q���V           �	�
X�
�]��X��]��]         �	�
Y�
�^���Y���^               �	�
�(���(��_���(��_���(��_       �
�
P�
�p���P��p���P             �
�
P�
�S��-���S��S��S     ��
0��
�
P��0�     ��R��	�      ��Q��	�         ��X��\���X���	\     ��Y��	�     ��U��	U               ��P��X����������P������	���	�	��         ��T��T��P��	T      ��0���_��_                  ��0�����������R��1�������0���	0��	�	0�            ��	����_��_��	����	_�	�	_        WRW��R���R          Q�S���Q���S        WXW��X���X          QYQ�U���Y���U          MW\WgQg�\��|���\   ?�P  ?W0�      Mg0�g�R��r���R��0�  6M]       �V���Q�R���V       �T���X�Q���T       MW\W�Q��Q��\     gsZ��Z       %T%%P%,Q,6]��T     %V%/P��V        ,Q,/]/6Q��Q       ��R��S���R�  ��T    ��R��S T                RQ�R�        QX�Q�        X�`�X� )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                                                                                                                                                                                X         Z���� ���� ���� ���� ���� ���� ����          P @   � 6 @   � S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����          6��                                                    .file   a   ��  gcrtexe.c              �                                �              �                            �                           �                            %  �                        @  �                        `             k  �                        �  �                        �  p                        �  �                        �  0          �   	                    envp           argv            argc    (                          �                        '  �          9  P                        ^  `                        �             �  �                        �  �                        �  @                          �                    mainret            "  �                        8  p                        N  �                        d  �                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )   	     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  P	     +                 .file   �   ��  gidevice_id.c          m  P                       main     &          �  	                    .text   P     �   	             .data                            .bss    0                        .rdata         �                .xdata  t                       .pdata  l                          �   &     =  /                 �  �                           �  x                          �  �&  
   �  V                 �  �     {                    �  �     &                   �  0      @                    �  \      #                      9     _                          )                       �     1                    )  �	     +                     4  P     �                .text   �      .idata$7�	      .idata$5X      .idata$4      .idata$6       .text   �      .idata$7�	      .idata$5P      .idata$4      .idata$6      .text          .idata$7�	      .idata$5H      .idata$4      .idata$6�      .text         .idata$7�	      .idata$5@      .idata$4       .idata$6�      .text         .idata$7�	      .idata$58      .idata$4�       .idata$6�      .text         .idata$7�	      .idata$50      .idata$4�       .idata$6�      .text          .idata$7�	      .idata$5(      .idata$4�       .idata$6�      .text   (      .idata$7�	      .idata$5       .idata$4�       .idata$6`      .file   �   ��  ggccmain.c             �  0                       p.0                 �  p          �  �                    __main  �            0       .text   0     �                .data                          .bss    0                       .xdata  �                       .pdata  �      $   	                 �  :9  
   a                   �  �     ?                    �  �     5                     �  p      0                      �     '                     �     �                     )  �	     +                     4       �                .file     ��  gnatstart.c        .text                           .data                          .bss    @                           �  �?  
     
                 �  <	     �                     �  �                             �     V   
                   H                            �                         )  �	     +                 .file     ��  gwildcard.c        .text                           .data                           .bss    P                            �  �E  
   �                    �  �	     .                     �  �                             	     :                      �     �                     )  
     +                 .file   1  ��  gdllargv.c         _setargv                        .text                          .data   0                        .bss    P                        .xdata  �                       .pdata  �                          �  *F  
   �                   �   
     :                     �  �      0                      O	     V                      �     �                     )  @
     +                     4  �     0                .file   E  ��  g_newmode.c        .text                          .data   0                        .bss    P                           �  �G  
   �                    �  Z
     .                     �                              �	     :                      (     �                     )  p
     +                 .file   }  ��  gtlssup.c                                             @          -  `                    __xd_a  P       __xd_z  X           D  �      .text        �                .data   0                        .bss    `                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  5H  
   �  6                 �  �
     �                    �  #                        �  0     0                      �	                          `                            �     �                     )  �
     +                     4  �     �                .file   �  ��  gxncommod.c        .text   �                       .data   0                        .bss    p                           �  �O  
   �                    �  o     .                     �  `                            �
     :                      �     �                     )  �
     +                 .file   �  ��  gcinitexe.c        .text   �                       .data   0                        .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �P  
   {                   �  �     a                     �  �                            /     :                      T     �                     )        +                 .file   �  ��  gmerr.c            _matherr�                       .text   �     �                .data   0                        .bss    �                        .rdata        @               .xdata  �                       .pdata  �                          �  R  
   6  
                 �  �                         �  :     �                    �  �     0                      i     �                      �     �                     )  0     +                     4  p     X                .file   �  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   0                        .bss    �                        .xdata  �                       .pdata  �                          �  7U  
   �                    �       -                     �  �     0                      '     X                      �	     �                     )  `     +                     4  �     0                .file   �  ��  gmingw_helpers.    .text   �                       .data   0                        .bss    �                           �  �U  
   �                    �  0     .                     �                                    :                      O
     �                     )  �     +                 .file   +  ��  gpseudo-reloc.c        P  �                           _  `          u  �       the_secs�           �  �	          �  �           �  �                        �  �                    .text   �     =  &             .data   0                        .bss    �                       .rdata  @     [                .xdata  �      0                 .pdata  �      $   	                 �  `V  
   K  �                 �  ^     �                    �  �     �  
                 �        0                    �        W                       �     �                          	                       �
     O                    )  �     +                     4  �     �                .file   K  ��  gusermatherr.c           0
                             �           (  p
      .text   0
     L                .data   0                        .bss    �                       .xdata                        .pdata                           �  �m  
   �                   �  6                         �  G     r                     �  P     0                      9     �                      L     �                     )  �     +                     4  �     P                .file   _  ��  gxtxtmode.c        .text   �
                       .data   0                        .bss    �                           �  �p  
   �                    �  H     .                     �  �                            �     :                      
     �                     )        +                 .file   �  ��  gcrt_handler.c         ?  �
                       .text   �
     �               .data   0                        .bss    �                       .xdata  $                      .rdata  �     (   
             .pdata  ,                         �  (q  
   �                   �  v     ~                    �  �     _                    �  �     0                      !     �  
                   �                            �
                         )  P     +                     4  (     P                .file   �  ��  gtlsthrd.c             V  @                           v             �  �           �  �          �  �           �             �  �      .text   @     b  "             .data   0                        .bss    �      H                 .xdata  ,     0                 .pdata  8     0                    �  	�  
   �
  A                 �  �     a                    �  
     �                    �  �     0                    �  �                             �     x                     �     %                    )  �     +                     4  x     (               .file   �  ��  gtlsmcrt.c         .text   �                       .data   0                       .bss    @                           �  ܋  
   �                    �  U     .                     �                               &     :                      �     �                     )  �     +                 .file   �  ��  g    �            .text   �                       .data   @                        .bss    @                          �  e�  
   �                    �  �     0                     �                               `     :                      �     �                     )  �     +                 .file     ��  gpesect.c              
  �                             �          ,  0          A  �          ^  P          v  �          �            �  P          �  �      .text   �     �  	             .data   @                        .bss    P                       .xdata  \     ,                 .pdata  h     l                    �  5�  
   �  �                 �  �     �                    �  �     �                    �  @     0                    �  �      �                       �     K                     �     T                       A     �                     )  
     +                     4  �     (               .text   �     2                 .data   @                        .bss    P                       .text   �                       .data   @                        .bss    P                           )  @
     +                 .file   ;  ��  ggetopt.c              �  �                       warnx   �          �  P      place   P       ambig   �          �        noarg   `            �            �          "  @           4  D           ?  H           L  0          W         getopt  !          b  @!          n  `!      .text   �     �  t             .data   @      $                .bss    p                      .xdata  �     d                 .pdata  �     T                .rdata  �     B                    �  �  
   <  �                 �  =     �                    �  �     ?                    �  p     0                    �  �                            �     a	                     �     +                       &     .                    )  p
     +                     4  �     �               .file   O  ��  gmingw_matherr.    .text   �!                       .data   p                       .bss    �                           �  C�  
   �                    �  �     .                     �  �                            F&     :                      T     �                     )  �
     +                 .file   m  ��  gucrt_vfprintf.    vfprintf�!                       .text   �!                     .data   �                      .bss    �                       .xdata  �                      .pdata  (                         �  �  
   �                   �  �     8                    �       X                     �  �     0                      �&     r   	                        �                     )  �
     +                     4  �	     8                .file   �  ��  gucrt_printf.c     printf  �!                       .text   �!     H                .data   �                      .bss    �                       .xdata  �                      .pdata  4                         �  ~�  
   �  
                 �  %      l                    �  r     -                     �  �     0                      �&     �   	                   �     �                     )        +                     4  �	     H                .file   �  ��  gucrt_fprintf.c    fprintf �!                       .text   �!     2                .data   �                      .bss    �                       .xdata                         .pdata  @                         �  1�  
   �                   �  �!     b                    �  �     F                     �        0                      �'     �   	                   �     �                     )  0     +                     4  8
     8                .file   �  ��  g__initenv.c             �          �  �      .text   0"                       .data   �                      .bss    �                          �  ��  
   �                   �  �"     �                     �  P                            (     [                      �                         )  `     +                 .file     ��  gucrtbase_compa        �  0"                           �  @"          �  �"      _onexit  #          �  @#          �  0                        	  `#          	  �#      tzset   �#          	  �                    _tzset  $          6	            G	            X	            h	  $          x	         .text   0"       "             .data   �      x   
             .bss    �                       .xdata       P                 .pdata  L     l                .rdata  @                          �  u�  
   �  Y                 �  �#                          �  �     |                    �  p     0                      l(     �                                                 �     `                    )  �     +                     4  p
     �               .text   P$      .data   @      .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6�	      .text   X$      .data   @      .bss    �      .idata$7�      .idata$5@      .idata$4       .idata$6�	      .text   `$      .data   @      .bss    �      .idata$7�      .idata$5H      .idata$4      .idata$6�	      .text   h$      .data   @      .bss    �      .idata$7�      .idata$5P      .idata$4      .idata$6�	      .file     ��  gfake              hname   �      fthunk  8      .text   p$                       .data   @                       .bss    �                       .idata$2�                      .idata$4�      .idata$58      .file   8  ��  gfake              .text   p$                       .data   @                       .bss    �                       .idata$4                      .idata$5X                      .idata$7�                      .text   p$      .data   @      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6l	      .text   x$      .data   @      .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6v	      .file   F  ��  gfake              hname   �      fthunk         .text   �$                       .data   @                       .bss    �                       .idata$2�                      .idata$4�      .idata$5       .file   �  ��  gfake              .text   �$                       .data   @                       .bss    �                       .idata$4�                      .idata$50                      .idata$7�     !                 .text   �$      .data   @      .bss    �      .idata$7L      .idata$5�      .idata$4�      .idata$6�      .text   �$      .data   @      .bss    �      .idata$7P      .idata$5�      .idata$4�      .idata$6�      .text   �$      .data   @      .bss    �      .idata$7T      .idata$5�      .idata$4�      .idata$6	      .text   �$      .data   @      .bss    �      .idata$7X      .idata$5�      .idata$4�      .idata$6	      .text   �$      .data   @      .bss    �      .idata$7\      .idata$5�      .idata$4�      .idata$6,	      .text   �$      .data   @      .bss    �      .idata$7`      .idata$5�      .idata$4�      .idata$6H	      .text   �$      .data   @      .bss    �      .idata$7d      .idata$5       .idata$4�      .idata$6P	      .text   �$      .data   @      .bss    �      .idata$7h      .idata$5      .idata$4�      .idata$6Z	      .text   �$      .data   @      .bss    �      .idata$7l      .idata$5      .idata$4�      .idata$6d	      .file   �  ��  gfake              hname   �      fthunk  �      .text   �$                       .data   @                       .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   &  ��  gfake              .text   �$                       .data   @                       .bss    �                       .idata$4�                      .idata$5                      .idata$7p                       .text   �$      .data   @      .bss    �      .idata$7�
      .idata$5@      .idata$4       .idata$6�      .text   �$      .data   @      .bss    �      .idata$7�
      .idata$5H      .idata$4      .idata$6�      .text   �$      .data   @      .bss    �      .idata$7�
      .idata$5P      .idata$4      .idata$6�      .text   �$      .data   @      .bss    �      .idata$7�
      .idata$5X      .idata$4      .idata$6�      .text   �$      .data   @      .bss    �      .idata$7�
      .idata$5`      .idata$4       .idata$6�      .text   �$      .data   @      .bss    �      .idata$7�
      .idata$5h      .idata$4(      .idata$6      .text    %      .data   @      .bss    �      .idata$7�
      .idata$5p      .idata$40      .idata$6      .text   %      .data   @      .bss    �      .idata$7       .idata$5x      .idata$48      .idata$62      .text   %      .data   @      .bss    �      .idata$7      .idata$5�      .idata$4@      .idata$6@      .text   %      .data   @      .bss    �      .idata$7      .idata$5�      .idata$4H      .idata$6H      .text    %      .data   @      .bss    �      .idata$7      .idata$5�      .idata$4P      .idata$6j      .text   (%      .data   @      .bss    �      .idata$7      .idata$5�      .idata$4X      .idata$6�      .text   0%      .data   @      .bss    �      .idata$7      .idata$5�      .idata$4`      .idata$6�      .text   8%      .data   @      .bss    �      .idata$7      .idata$5�      .idata$4h      .idata$6�      .text   @%      .data   @      .bss    �      .idata$7      .idata$5�      .idata$4p      .idata$6�      .text   H%      .data   @      .bss    �      .idata$7       .idata$5�      .idata$4x      .idata$6�      .text   P%      .data   @      .bss    �      .idata$7$      .idata$5�      .idata$4�      .idata$6�      .file   4  ��  gfake              hname          fthunk  @      .text   `%                       .data   @                       .bss    �                       .idata$2x                      .idata$4       .idata$5@      .file   ^  ��  gfake              .text   `%                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7(     "                 .text   `%      .data   @      .bss    �      .idata$7�
      .idata$5      .idata$4�      .idata$6�      .text   h%      .data   @      .bss    �      .idata$7�
      .idata$5       .idata$4�      .idata$6�      .text   p%      .data   @      .bss    �      .idata$7�
      .idata$5(      .idata$4�      .idata$6�      .text   x%      .data   @      .bss    �      .idata$7�
      .idata$50      .idata$4�      .idata$6�      .file   l  ��  gfake              hname   �      fthunk        .text   �%                       .data   @                       .bss    �                       .idata$2d                      .idata$4�      .idata$5      .file   �  ��  gfake              .text   �%                       .data   @                       .bss    �                       .idata$4�                      .idata$58                      .idata$7�
     "                 .text   �%      .data   @      .bss    �      .idata$7�
      .idata$5      .idata$4�      .idata$6l      .file   �  ��  gfake              hname   �      fthunk        .text   �%                       .data   @                       .bss    �                       .idata$2P                      .idata$4�      .idata$5      .file   �  ��  gfake              .text   �%                       .data   @                       .bss    �                       .idata$4�                      .idata$5                      .idata$7�
                      .text   �%      .data   @      .bss    �      .idata$7\
      .idata$5�      .idata$4�      .idata$6@      .text   �%      .data   @      .bss    �      .idata$7`
      .idata$5�      .idata$4�      .idata$6P      .text   �%      .data   @      .bss    �      .idata$7d
      .idata$5�      .idata$4�      .idata$6Z      .text   �%      .data   @      .bss    �      .idata$7h
      .idata$5�      .idata$4�      .idata$6b      .file   �  ��  gfake              hname   �      fthunk  �      .text   �%                       .data   @                       .bss    �                       .idata$2<                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   �%                       .data   @                       .bss    �                       .idata$4�                      .idata$5                       .idata$7l
                      .text   �%      .data   @      .bss    �      .idata$7,
      .idata$5�      .idata$4�      .idata$6       .text   �%      .data   @      .bss    �      .idata$70
      .idata$5�      .idata$4�      .idata$60      .file   �  ��  gfake              hname   �      fthunk  �      .text   �%                       .data   @                       .bss    �                       .idata$2(                      .idata$4�      .idata$5�      .file   L  ��  gfake              .text   �%                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$74
     &                 .text   �%      .data   @      .bss    �      .idata$7
      .idata$5�      .idata$4x      .idata$6      .text   �%      .data   @      .bss    �      .idata$7
      .idata$5�      .idata$4p      .idata$6�      .text   �%      .data   @      .bss    �      .idata$7
      .idata$5�      .idata$4h      .idata$6�      .text   �%      .data   @      .bss    �      .idata$7
      .idata$5�      .idata$4`      .idata$6�      .text   �%      .data   @      .bss    �      .idata$7
      .idata$5�      .idata$4X      .idata$6�      .text   �%      .data   @      .bss    �      .idata$7
      .idata$5�      .idata$4P      .idata$6�      .text   �%      .data   @      .bss    �      .idata$7 
      .idata$5�      .idata$4H      .idata$6�      .text   �%      .data   @      .bss    �      .idata$7�	      .idata$5�      .idata$4@      .idata$6�      .text    &      .data   @      .bss    �      .idata$7�	      .idata$5x      .idata$48      .idata$6l      .text   &      .data   @      .bss    �      .idata$7�	      .idata$5p      .idata$40      .idata$6T      .text   &      .data   @      .bss    �      .idata$7�	      .idata$5h      .idata$4(      .idata$6<      .file   Z  ��  gfake              hname   (      fthunk  h      .text    &                       .data   @                       .bss    �                       .idata$2                      .idata$4(      .idata$5h      .file   h  ��  gfake              .text    &                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7
     
                 .file   �  ��  gcygming-crtend        �	  `*                       .text    &                       .data   @                       .bss    �                           �  `*                         �  X                          �  �                         �	  x*                         )  �     +                 .idata$2        .idata$5       .idata$4�       .idata$4       .idata$5`      .idata$7�	      .rsrc       
    __xc_z         putchar �$          �	  �          �	  P$          �	  �$          �	  �          �	  
          
  x          2
  �          E
              T
  �*          c
  �           r
  8          �
  d           �
  �%          �
             �
  �            �            @          -             :      	        I  `          h  8%          �  �       __xl_a  0           �  �%          �  �          �  �      _cexit  �$          �  `  ��       �     ��         �          6              L  (          _      ��       y     ��       �  0           �  h      __xl_d  @           �  �      _tls_end   	        �  �      __tzname`$          �  �          

  	          
  �%          &
  H          7
             I
  �           Z
  0           j
  0          �
  �          �
      	    memcpy  h%          �
  H          �
  �      puts    �$          �
  �          �
  �       malloc  �%      _CRT_MT 0       optarg  p            �%          #              1            L            Z  �          }  H          �     ��       �  �          �  0          �  �          �  �          �  �            h           6  �      opterr  `           A  h          \  (          p  �
          �  �%          �  �          �  `           �  �%          �  (             P          ,  P           >  �          Y  `%          n  x            @      abort   @%          �  �          �  �           �  P       __dll__     ��       �      ��         �$            �	          -  �          ]  &          r  p           �            �  �%          �  �          �  `          �  P           �  �          �     ��                strrchr x%          -  �
      calloc  �%          ^  �          h  P          �  `          �  �           �             �  �      Sleep   �%          �   	      _commodep           �  @                         p*            �          3  <           _  �           s         optind  \           �  �      __xi_z  (           �            �             �             �             �  �$            P          #  p          >  l       signal  P%          I  �$          Y  H           p              �         strncmp x$          �  p*          �  �          �  �          �  �                       #      ��       6  �          J  (          {  �          �  P          �             �  �          �  �          �  �           �  8               ��         �          4  �%          B   &          Z  %          y   %          �  �%          �  �          �  �           �              �  �                ��       !              0  �           @  X          `             n  X      __xl_z  H       __end__              {  �          �  �          �  0          �  �*      __xi_a             �  0%          �  �          �  �%      __xc_a                               ��       8  P           J     ��   _fmode  �           X  �          j  �          ~            �  �%          �  @          �  �          �  �           �  �$          �             �  �            x          #  4
          X  �$          c  �          x         fputc   �$      __xl_c  8           �     	    optopt  X           �  �          �  �          �  �          �  d           �              �  p            �          6  X$          A  l
      _newmodeP           o  (%      fwrite  �$          y            �  (          �  �          �  �          �      ��       �      ��       �  (            �            @           &  �$          3  �      exit    H%          E             m     ��       �      ��       �  �          �  p      _exit   %          �  �           �  p          �  �$      strlen  p$            �            %            p          B  0      strchr  p%          i  &             %          �  @          �  `          �  x             �          (  �           9  �          S  (          u  �          �  �           �  P           �  �$          �  �           �  �$      free    �%          �  �       �  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame print_usage.isra.0 .rdata$.refptr.optind .text.startup .xdata.startup .pdata.startup __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names permute_args parse_long_options illoptstring recargstring getopt_internal posixly_correct.0 nonopt_end nonopt_start illoptchar recargchar getopt_long getopt_long_only local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp_GetEnvironmentVariableW __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone __imp_idevice_new_with_options _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode __imp___p___argc __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __rt_psrelocs_start __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ __imp_fputc .refptr.optind VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler TlsGetValue __bss_start__ __imp___C_specific_handler __imp_putchar ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection __C_specific_handler __mingw_optreset .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf ___crt_xp_end__ __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname _tls_used __imp_lockdownd_client_new __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ .refptr._newmode __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ idevice_set_debug_level __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func __native_startup_state ___crt_xc_start__ lockdownd_client_free ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a .refptr.__mingw_initltsdyn_force __rt_psrelocs_size .refptr.__ImageBase __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname lockdownd_client_new __imp___p___wargv __imp_strlen __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __p__wenviron GetEnvironmentVariableW _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_lockdownd_get_device_name __IAT_start__ __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit __DTOR_LIST__ __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_VirtualProtect idevice_free ___tls_end__ .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm idevice_get_device_list_extended __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_strchr ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __imp_idevice_device_list_extended_free __minor_subsystem_version__ __minor_image_version__ __imp___set_app_type __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR __imp_idevice_get_device_list_extended DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv lockdownd_get_device_name idevice_device_list_extended_free .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_daylight __p___wargv __mingw_app_type 