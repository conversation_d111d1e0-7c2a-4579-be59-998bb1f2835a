MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� �<�g  F  � & ( 0   f     �        @                       �    �l  `                                             �  �   �  �   `  �           �  �                           @S  (                   Г  �                          .text   X.      0                 `  `.data   P   @      6              @  �.rdata  �   P      8              @  @.pdata  �   `      H              @  @.xdata  x   p      L              @  @.bss    �   �                      �  �.idata  �   �      P              @  �.CRT    `    �      `              @  �.tls        �      b              @  �.rsrc   �   �      d              @  �.reloc  �    �      j              @  B/4      �   �      l              @  B/19     ��   �   �   r              @  B/31     $)   �  *   \             @  B/45     ".     0   �             @  B/57     �   @     �             @  B/70     t   P     �             @  B/81     �   `     �             @  B/97     F   �      �             @  B/113    �   �                  @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H�eH  1��    H�fH  �    H�iH  �    H��G  f�8MZuHcP<HЁ8PE  tfH�H  �
�o  � ��tC�   �&  �t%  H��H  ���\%  H��H  ���t  H�MG  �8tP1�H��(Ð�   ��%  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
qH  �|
  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H�EH  L��n  H��n  H�
�n  � ��n  H��G  D�H��n  H�D$ �-"  �H��8��    ATUWVSH�� H�?G  H�-Ȃ  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5G  1�����V  �����  �n     ����L  ���e  H�1F  H� H��tE1��   1����  H�
1G  �#�  H��F  H�
����H��M$  �  ��m  �{Hc�H��H���$  L�%�m  H�Ņ��F  H��1�I��G#  H�pH���s$  I��H�D I�H��H���$  H9�u�H�H�    H�-]m  �(  H��E  L�Bm  �
Lm  H� L� H�7m  �$  �
m  �m  ����   � m  ��ttH�� [^_]A\�f�     H�5�E  �   ���������   �!  ��������H��E  H�
�E  �)#  �   �������1�H�����f�     ��"  ��l  H�� [^_]A\�f.�     H�yE  H�
bE  �   ��"  �7���f�H�����������"  �H��(H��D  �    ������H��(� H��(H�uD  �     �z�����H��(� H��(�g   H���H��(Ð�����������H�
	   �����@ Ð��������������VSH��(�ֺ/   H���s"  H�PH��HEڅ�tGH�5�  �   ��I��H�t;  H���  �   ��I��A�w  �   H�
p;  H��([^�]!  H�5΀  �   ��I��H�-;  H���u  �   ��I���f�     WVSH���   L����L����   H��t;H�
�<  ���  tH�
�<  �   H�
k  H��I��H���   [^_��   @ H�|$ L�KI��H��H�:<  �M  H�
6<  ���  tH�
1<  �   H�
�j  H��I���   �H���   [^_Ð����%Ҁ  ���%  ���%��  ���%��  ���%��  ���%��  ���%r�  ���%b�  ���%"~  ���%~  ���%~  ���%�}  ���%�}  ���%�}  ���%�}  ���%�}  ���%�}  ���%�  ���%�  ���%�  ��H��(H��)  H� H��t"D  ��H��)  H�PH�@H��)  H��u�H��(�fD  VSH��(H�sA  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^�C��� 1�fD  D�@��J�<� L��u��fD  �Zi  ��t�D  �Fi     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H��@  �8t�    ��t��tN�   H��([^�f�H�ш  H�5ʈ  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H��<  Hc�H����    H�p;  �DA �y�qH�q�   �  �DD$0I��H�<  �|$(H��I���t$ ��  �t$@|$P1�DD$`H��x[^ÐH��:  ��    H�9;  ��    H�	;  �s���@ H�i;  �c���@ H�1;  �S���H��;  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�  A�   �   H�
�;  I����  H�t$(�   �  H��H��I���-  �8  ��    WVSH��PHc56g  H�˅��  H�(g  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H��f  H��H��H�H�x �     �#  �WA�0   H�H��f  H�T$ H�L�Wz  H���}   �D$D�P����t�P���u�of  H��P[^_� ��H�L$ H�T$8A�@   �   DD�HEf  H�KI��H�S��y  ��u���y  H�
�:  ���d���@ 1��!���H�
f  �WH�
H:  L�D�>���H��H�
:  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%�e  E��tH�e[^_A\A]A^A_]�fD  ��e     �9	  H�H��H��   H����  L�-�<  H��<  �^e      H)�H�D$0H�Se  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5^<  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
T9  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  ��c  ������H�5kw  1�H�}�D  H��c  H�D� E��t
H�PH�HI����A��H��(D;%vc  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5:  �s�;H��L�>H���Z����>L9�r��������H�
m7  �����H�
)7  ���������H��XH�ub  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
9b  �T  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H�L7  Hc�H��� 1ҹ   ��  H���>  H���  H��a  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �i  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �  H���@����   �   ��  �f�     1ҹ   ��  H��t*H�������   ���i���f�     �   ���T����   �   �  �@����   �   �  �,����   �   �m  �����������ATUWVSH�� L�%�`  L����s  H��`  H��t6H�-�s  H�=�s  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%�s  WVSH�� �;`  ��H�օ�u
1�H�� [^_ú   �   �	  H��H��t3H�pH�5`  �8H���#s  H��_  H��H��_  H�C�(s  묃��멐VSH��(��_  �˅�u1�H��([^�D  H�5�_  H����r  H�
�_  H��t'1��H��H��tH���9�H�Au�H��tH�B�m  H����r  1�H��([^� H�Q_  ��ff.�     @ SH�� ����   w0��tL�._  ����   �_     �   H�� [�f�     ��u��^  ��t��<�����f.�     ��^  ��uf��^  ��u�H��^  H��t�    H��H�[�  H��u�H�
�^  H��^      ��^      ��q  �l����k����   H�� [������f�     H�
y^  ��q  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���  H��w{H��4  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���  ��u�H��H�� [^_��    1�H��H�� [^_� H�)4  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H��3  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L�i3  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H��2  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H��2  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L�2  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������AUATUWVS��D�Ɖ�L��)�)։�������   �� A�ՙA��D���u�D��)șA��E��~lHc�A��A�L�׉څ�~Yf�     M�1��f�     M��A��D�2A)�9�D��AL҃�Lc�N��M�M�M�9�u�A��I��E9�tD��뱐[^_]A\A]�A���u���ff.�     @ WVSH��0H�t$XL�L$hH��H�T$XL�D$`H�t$(�@  �   H� H�8��
  H�Q.  I��H���6  �   �
  I��H��H���^
  �   �
  �
   H����
  �H��0[^_�ff.�      AWAVAUATUWVSH��H��  H�=�  H��$�   H��M��H��$�   �=   �D$<��L��$�   �D$8��  �  I��H���  H��I��H)�M�4$M����   1�L�D$0I�\$ E1�D��$�   �L$(�����A���*Hc�H��L��P9S�tV�D$(   L�3H�� A��M��tfI��L��H���  ��u�L���  H9���   H��uE��u��u�D���fD  H�PH9S�u��@9C��   DD$(�D$(��     �L$(L�D$0��uKA����ux��$�   ���  ��  ���.  ��      �?   H��H[^_]A\A]A^A_�fD  D�
q  E��t�H��$�   �8:t�I����H�
-  �����@ L�D$0Ic�H��L�k��uRM��teD�&  E��tH��$�   �8:��   H�{ �l  H��$�   �-�  �8:�Z����:   �U���@ �U���wM����   L��V  H��$�    tH��$�   D�8H�S�CH�������1��
���f�     H��H�D$(��
  L�D$(H�������fD  H��$�   �8:�����H��H�
�+  ���������I����H�
�+  �����%������d���HcT$8H��$�   D�t$<H��A��D�5  H�4V  H���3����
�  ��t)H��$�   �8:tH��H�
�+  �E�����  ���D$81�H�{ tB��  �D$8��  H��$�   �8:�
��������k�����D$<��  �����������C��     AWAVAUATUWVSH��HD��$�   A��H��M��M��M���y  D�
8  �  E����  �����  �VU  ����  A�E <-�  D��  E����  <+��  H�U      ����   ��  ������  ����f.�     ��T      ��  D9���  Hc�H�t� �>-��  H��)  H�x  A����  A����  �5V  ����5  �=C  ���t%��I��A�؉������)����!  ����)��  ���(  H�=  ����Z���M��tkHc
  H9|� t]<-�r  A���w  <:�k  ��L���R	  H�������D$ M��L��H��L��$�   �����Ã����  H�=�  �L���L�=�  <:�,  ��-��   ��L����  H���  �PM��t��Wu	��;��  ��:��   � ��  �C  �}  f��nS     �(     1�E1�H�
((  ��e  �LS  ��������  A�E <-�����A��I�������@ �F����  �-   L���?  H���0����=�  �t
�=�  ���  L�~H���-   L�=�  � �8  �-   L����  H����   �x:�%���H��R      � �Z  ��  L�=�R  H�r'  ��H�0  �6  �s�     �  �����D  A���<+�#��������    �5�  H�#'  �=�  H��  ���uP���t�=�  ��  ������  �����������H��H[^_]A\A]A^A_�f���H�5�Q  ��  �   �ԉ�)�A��I���)��&����|  �L���L�=`  <:������ u�W  �
U  ��tA�} :��   �8  �?   �q���fD  A����2����
	  ���t
�=�  ��  H�~H�=�  <-������~ ������5�  ��H�
&  ��  H��  ��������A�؉�I��L$<�[����L$<)�)��  ������     ��H�
�%  �����G���H��H�=r  1������ ��   �g  ���^  D9�|e�W  H�x%  H�9  ��tA�} :t�W   H�
R&  �����  W   A�} :������:   �I�����  �S�����  �����H�H�D� H��  �D$     M��L��H��L��$�   �������H��$  H��  ������x:�\�������  A9�~Hc�H�D� H��O  �:���H��$  H�m  �w  ��tA�} :t��H�
�%  ������P  A�} :�
����0����     H��8E1��D$(    H�D$     ����H��8�ff.�      H��8H�D$`�D$(   H�D$ ����H��8�H��8H�D$`�D$(   H�D$ �e���H��8�H��8E1�L�D$ I��H��1��O  H��8Ð�H��HH�D$`L�D$`I������H�D$(H�D$     L�L$hI��H�ʹ   H�D$8�  H��HÐ�������������VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8�  H�t$ E1�I��H��1��  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�{  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H���  ���   ����  �[  � ��Z  H� H��'  H� H�M��t	A�$��  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���P  ���   ����  ��  � ���  H� H��  H� H�M��t	A�$�  1�H�� [^_]A\�fD  SH�� H����  ���    HD�H�� [�f�H�	$  �8 t1�Ð�  ff.�     SH�� �˹   �  A��H��"  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H�%#  ��~   H��  �j   H��  �V   H��  H��(�f.�     H��(H��"  ��>   H��  �*   H�s  �   H�_  H��(Ð����������%B`  ���%B`  ���%B`  ���     �%
`  ���%
`  ���%�_  ���%�_  ���%�_  ���%�_  ���%�_  ���%�_  ���%�_  ���%�_  ���%�_  ���     �%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���%�^  ���     �%^  ���%^  ���%^  ���%^  ���%�]  ���     �%�]  ���%�]  ���%�]  ���%�]  ���%r]  ���%r]  ���%R]  ���%B]  ���%2]  ���%"]  ���%]  ���%]  ���%�\  ���%�\  ���%�\  ���%�\  ���%�\  ���     UAWAVAUATWVSH��h  H��$�   E1�   L�5   ��H��L�}@�8����s���H�E�    H�dH  H��  H�E@H��  H�E`H��  H���   H��  H�E�    H�E�    H�E�    �EH    H�EP    �EXd   �Eh    H�Ep    �Exh   ǅ�       Hǅ�       ǅ�   l   H���   ǅ�       Hǅ�       ǅ�   n   Hǅ�       ǅ�       Hǅ�       ǅ�       �E�    M��M��H�ډ�H�D$     ����A�Ń��tmA��ltH&A��dtPA��h�>  H�1��\���1�����D  A��n�  �E�   �   ��     A�   �   � �   �����r���H�J   HcH��H��)ƃ�t	����  H�1�H�E��:�������  1�H�U��7���H���  H�E�1�I��U�H�H����   fD  I��L��H�ĀH��H�E�    H�t$0���������  f�}���   �E
������f��vf= �t
f-�f��wuH�}� ��  H�M��} H��tS�2   �fD  ����  H�M��UA��   I���q���H������H��
v��U
H�M I������H�E�H��t
H���j���f��E�H�U�L����E�H�H��H����������  H�U�H�M��c�������  H�U�1��]�H�5D  �E�H���H����   �  ��t	����   �x�  H�
@E  ��   H��t.H�
�  �!���HcU�H�
E  I��H�E�H��H�����H�U�HcE�D�e�tH�x��   H�������E�H�U���E�H�H��H����   �H���d���E���h����E���H��t�H�
]  �r����   ��Y  A�   �   H�
k  I���c�������H�U�H�������H�}� ������0���H�
  �����E�H�U��b����   E1���Y  H�
-D  H������H�M�����D��H���   [^_A\A]A^A_]�H�U�H�M�A�   �����H�M�H����  H�U�L�  �������d  H�M�H�U��p���A�Ņ�u]H�}� tVH�Mк   �<���1��=���H�M��T���H�M��k���H�M��r���H�M�H���Y����@����O���H��
������y����   A�������X  A�"   �   H�
�  I������H�Mк   ����1�����H�M������H�M�������H��   �����   �i����   �FX  A�   �   H�
s  I������A����������   �X  A�'   �   H�
4  I�������{���1��0����   ��W  A�#   �   H�
3  I���S����H�M��@����   ��W  A�$   �   H�
�  I���#����j����   ��W  L�E�H�(  H���8����G����������������������������� > @           ��������                                                                                                                                                                                                                                                                                                                                                                                                                                                P> @           ��������        ����                           ������������    �V @   ?                     ����            �2 @           �2 @            3 @           P3 @           �� @   �� @   �4 @   05 @   �3 @   �4 @   4 @   �3 @   A @   A @    A @      �p  4A @   0A @   PDT PST �4 @   �4 @                                                                                                                                                                                           Usage: %s [OPTIONS] [UDID]
     Prints device name or a list of attached devices.

  If UDID is given, the name of the connected device with that UDID  will be retrieved.

  -l, --list      list UDIDs of all devices attached via USB
  -n, --network   list UDIDs of all devices available via network
  -d, --debug     enable communication debugging
  -h, --help      prints usage information

PhoneCheck 2.0
 %.8s-%s USB:WATCH USB:IOS debug help list network dhln ERROR: Libusb init failed!
      ERROR: Libusb device count failed!
     failed to get device descriptor ERROR: No device with UDID %s attached.
 idevice_id     ERROR: Connecting to device failed!
    ERROR: Could not get device name!
      ERROR: Unable to retrieve device list!
 MUX:WATCH MUX:IOS  (WiFi)  (USB)                        P @                            � @   � @   �� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  8�����������������,�������Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      ��������������������@�����������@������                        %s:     P O S I X L Y _ C O R R E C T           unknown option -- %s            unknown option -- %c                            option doesn't take an argument -- %.*s         ambiguous option -- %.*s                        option requires an argument -- %s                               option requires an argument -- %c                               runtime error %d
               0@ @           p@ @           0> @              @           �_ @           �_ @            S @           �@ @           � @           �� @           �� @           �� @           �� @            � @           �� @           `� @           h� @            � @           � @           � @           (� @           �� @            @ @           Ѐ @           � @           � @           p� @           \@ @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                            p    .  p  0  y  p  �  �  p  �  �  $p  �  
  Dp    $  dp  0  <  lp  @  A  pp  P  �  tp  �  �  �p  @  z  �p  �  �  �p  �    �p      �p     O  �p  P  �  �p  �  �  �p  �  �  �p  �  �  �p     i  �p  p  �  q  �  =  q  @  ~  ,q  �  �  4q  �  M   8q  P   �   @q  �   /!  Pq  0!  �!  \q  �!  �"  hq  �"  �"  pq  �"  @#  tq  @#  �#  xq  �#  `$  �q  `$  �$  �q  �$  %  �q   %  V%  �q  `%  �%  �q  �%  �&  �q   '  �'  �q  �'  R(  �q  `(  �+  �q  �+  2  �q   2  B2  �q  P2  p2  �q  p2  �2  �q  �2  �2   r  �2  �2  r   3  H3  r  P3  �3  r  �3  �3  $r  �3  
4  (r  4  z4  8r  �4  �4  Hr  �4  �4  Pr  �4  �4  Tr  �4  %5  \r  05  f5  dr  p5  �5  lr  �7  >  �p   >  %>  tr                                                                                                                                                                                                                                                                                                          B   b  
 
20`pP�	 B  �6     �  �  �  �  	 B  �6     �    �     B         B0`  
 
 0`p  �- 0`
p	����P   B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   0`pP�� R0`p	 �0`
p	P����  	 �0`
p	P����   b   b   b   b   �   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                                             �          �  Г  `�          8�  �  p�          ��  (�  Б          ��  ��  �          Ԝ  ��  �          ��  Ȕ   �          (�  ؔ  H�          ��   �  ؒ          ؝  ��  (�           �  ��  @�          4�  ��  h�          `�   �  ��          ��  @�                      ��      ��      ��      ��      ��      �      0�      H�              d�              |�      ��      ��      Ɨ      ֗      �      
�      (�      0�      >�      P�              `�      p�              ��      ��      ��      ��              ��              ��      ؘ      �      �              ��      �      �       �      *�      D�      \�      r�      ��      ��      ��      ʙ      ֙      �      �      �      �              "�      4�      D�      R�      l�      ��      ��      ��      ��              ��      ƚ              К      ޚ      �      ��              �      �      0�              D�      T�      b�      |�      ��      ��      ܛ      �              ��      ��      ��      ��      ��      �      0�      H�              d�              |�      ��      ��      Ɨ      ֗      �      
�      (�      0�      >�      P�              `�      p�              ��      ��      ��      ��              ��              ��      ؘ      �      �              ��      �      �       �      *�      D�      \�      r�      ��      ��      ��      ʙ      ֙      �      �      �      �              "�      4�      D�      R�      l�      ��      ��      ��      ��              ��      ƚ              К      ޚ      �      ��              �      �      0�              D�      T�      b�      |�      ��      ��      ܛ      �              _ idevice_device_list_extended_free f idevice_free  h idevice_get_device_list_extended  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new  � lockdownd_get_device_name  plist_print_to_stream DeleteCriticalSection =EnterCriticalSection  KGetEnvironmentVariableW tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy  |strchr  }strrchr  __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf   __stdio_common_vsprintf � fputc � fwrite  � puts  � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset     plist_dict_set_item   8 plist_new_dict    ; plist_new_string   libusb_close   libusb_exit ' libusb_free_device_list G libusb_get_device_descriptor  I libusb_get_device_list  a libusb_get_string_descriptor_ascii  y libusb_init � libusb_open  �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  libimobiledevice-glue-1.0.dll   (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  KERNEL32.dll    <�  <�  api-ms-win-crt-environment-l1-1-0.dll   P�  P�  P�  P�  api-ms-win-crt-heap-l1-1-0.dll  d�  api-ms-win-crt-math-l1-1-0.dll  x�  x�  x�  x�  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  api-ms-win-crt-string-l1-1-0.dll    Ȑ  Ȑ  Ȑ  Ȑ  api-ms-win-crt-time-l1-1-0.dll  ܐ  ܐ  ܐ  libplist-2.0.dll    �  �  �  �  �  �  �  �  libusb-1.0.dll                                                                                                                                                                                                                                                                                                                                                                      0 @                    @                   P @     @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          0     8�   @  4    �P�����������ȠРؠ����� ��� �(�8�@�   P  L    �@�H�P�X��� �� �0�@�P�`�p�����������Ш�� �� �0�@�P�`�p�����   �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                    ,               @   $                      <    �&       P @   L      �7 @   �                      ,    bF       @ @   �                           �L                           �R                       ,    RS        @                              �T                       ,    ]U         @   �                           $]                           �]                       ,    )_       � @   �                       ,    _b       � @                              �b                       ,    �c         @   =                      ,    �z       @ @   L                           �}                       ,    P~       � @   �                      ,    1�       P  @   b                          �                           ��                       ,    ]�       �" @   �                      ,    /�        ' @   �                          k�                       ,    	�       �2 @                          ,    ��       �2 @   B                       ,    -�        3 @   H                       ,    ��       P3 @   2                           ��                       ,    $�       �3 @                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   �   �  -GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden   �  �           9  char {   
size_t #,�   long long unsigned int �   
ssize_t -#�   long long int short unsigned int int �   long int unsigned int _iobuf !
<  _Placeholder #<    .
FILE /  unsigned char double float long double �   /optind �   {   �  option  >�  name @�   has_arg A�   flag B~  val C�    �  �   $�    G:  no_argument  required_argument optional_argument  signed char 
uint8_t $K  short int 
uint16_t &�   
uint32_t (  long unsigned int 
plist_t Y<  �   'u  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y 
idevice_error_t 0�  %~  2 �  ~  
idevice_t 3�  �   idevice_options   9#  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK   idevice_connection_type   @r  CONNECTION_USBMUXD CONNECTION_NETWORK  idevice_info F�  udid G�   product_id Hw  conn_type I#  conn_data J<   
idevice_info_t L�  r  �   $�	  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ 
lockdownd_error_t P�  %  R)�	    
lockdownd_client_t S#
  �	  K  _Float16 __bf16 &JOB_OBJECT_NET_RATE_CONTROL_FLAGS   	�
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  {     �     tagCOINITBASE   
�U  COINITBASE_MULTITHREADED   &VARENUM   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 0libusb_device_descriptor $  	bLength I   	bDescriptorType I  	bcdUSB f  	bDeviceClass !I  	bDeviceSubClass %I  	bDeviceProtocol )I  	bMaxPacketSize0 ,I  	idVendor /f  	idProduct 2f  
	bcdDevice 5f  	iManufacturer 8I  	iProduct ;I  	iSerialNumber >I  	bNumConfigurations AI   !o  �0  o  !a  �A  a  !L  �%R  L  F  $  5  verbose !�   	D� @   reset "�   	@� @   device_serial #
�  	8� @   dict_usb $	�  	0� @   libusb_close ��  W   
libusb_get_string_descriptor_ascii C�   0  W  I  
  �    
libusb_open ��   T  a  T   W  
libusb_get_device_descriptor c�   �  a  �   �
  strrchr 
]�  �  �  �    idevice_device_list_extended_free �u  �  �   �  'plist_print_to_stream ;  �     >  $  
printf ��   8  �  " idevice_get_device_list_extended �u  p  p  ~   �  free �  <   lockdownd_client_free ��	  �  �	   libusb_free_device_list \�  �  �    a  
lockdownd_get_device_name �	    �	  
   idevice_free �u  3  �   lockdownd_client_new ��	  d  �  d  �   �	  
fprintf ��   �    �  " idevice_new_with_options �u  �  �  �  �   �  libusb_exit P�  \   
libusb_get_device_list Z�     \     �  __acrt_iob_func ]  5     
libusb_init O�   T  T   \  getopt_long M�   �  �   �  �  �  ~   �  �  1exit � �  �    'idevice_set_debug_level l�  �    2plist_new_dict �
�  plist_dict_set_item �
  �  �  �   plist_new_string �
�  5  �   
sprintf ��   V  �  �  " strlen 
@�   o  �   3main ��   �7 @   �      ��  #argc ��   �  �  #argv �
      devs ��  ��}usb_devcount �
�   >  :  r ��   X  T  i �	�   ��}device ��  ��}client ��	  ��}dev_list ��  ��}device_name ��  ��}ret ��   x  n  mode ��   �  �  include_usb ��   �  �  include_network ��   �  �  udid ��  �  �  c ��   B  ,  longopts ��  ��~4�  �9 @    h   b    �  �  �  �  �  5h   (  ��}  �  �  6'  z  �  �  (5  ��}?  P  Z  k      v  @  <  �  �9 @   Y  �  Rs Q  ?: @   �  �  Xt Y� G: @   V  �  Rt  ]: @   �  �  R��~Xt  n: @   �  �; @       R2 �; @   �  E  R	R @   Q1XO �; @   0  Rs Q��~   7�   \  V  �7 @   �  �7 @   �  �8 @   Y  �  Rt Qs X~ Y w 0 �8 @   ?  �  Q0
  t   s  �8 @   �  �  R0 :9 @   �    R1 n9 @   5  %  R0 �9 @   �  C  R0Q��} �: @   8  c  R��~Q��} ; @     %; @   �  I; @     �  Rt  �; @     �  R	�R @    �; @     �  R1 < @   �  < @   �  >< @   �  
  R��}Qv X6 [< @   3  0  Q��~X	aR @    p< @   �  I  Q��~ �< @   �  `  Q1 �< @   �  w  R0 �< @   �  �< @     �< @   �  �< @   u  �< @     �  R2 �< @   �  �  R	�R @   Q1X" 
= @   �    Q1 = @   �    R0 = @   �  &= @     5= @   ?  \  Q1
  �R  s  ?= @   �  s  R1 J= @     �  R2 d= @   �  �  R	�Q @   Q1XK z= @     �  R2 �= @   �  �  R	�R @   Q1X' �= @   �    R0 �= @     "  R2 �= @   �  L  R	�Q @   Q1X# �= @     �= @     p  R2 �= @   �  �  R	pR @   Q1X$ > @     �  R2 > @   i  Q	8R @   Xv   �  �  �    )print_device `�  dev `)a  handle `DW  desc b"�
  len c�   serial d�  *8  f�  port_nums g
�  *C  h�  port_path j  pos k�   ret m�   i n
I   K  �  8�    K  �  �    I  �  �    K  �  �    9insertdevice_dict <� @   �       ��  +8  �  �  �  +C  .�   �  �  #serial <=�  �  �  :H @   I       �  newserial H�  ��~c @   5  �  Ru Q	�Q @   Xs Ys ~ @     � @   �  Qu    @   V  �  Rs  ( @     ;D @   �  Q�X  �  �  �    )print_usage )?  argc )�   argv )*
  is_error )4�   name +�   <�  P @   �       �z  "      2  I  C    e  c  
  z  x  e @   �  �  Rs Q/ � @     �  R2 � @   i  �  Q	 P @   Xs  � @     �  R2 =� @   �  *  R	 P @   Q1X
w � @     A  R1 � @   i  f  Q	 P @   Xs  � @     R1  K  �  >�   b   ,fwrite __builtin_fwrite ?__main __main ,puts __builtin_puts  ]   -	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @ @   �       C
  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	P� @   atexit ��   �  Y   __main 5� @          ��   @   �   	__do_global_ctors  � @   j       �  
nptrs "�   �  �  
i #�   �  �  � @   j  R	@ @     	__do_global_dtors @ @   :       �[  p [  	 @ @    	   �   l
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  z  j  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
�  �   �,  __uninitialized  __initializing __initialized  �  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	@ @   �  	@ @   =  
"	h� @   [  	`� @    �    "  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _dowildcard  �   	 @ @   int  }   P  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ?  '   @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �    @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  P  _newmode �   	p� @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 z  b    @   �       �  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	�� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	@S @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	 S @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	�� @   __mingw_initltsdyn_force ��   	�� @   __mingw_initltssuo_force ��   	�� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@    @   /       �}  
�  �  �  �  
�  *M  �  �  
�  ;d  �  �  E @   �   __tlregdtor m�   � @          ��  func m  R __dyn_tls_init L@  	  �  �  �  *M  �  ;d  pfunc N
$  ps O
�    �  P @   �       ��      �  4  ,  �  \  T  �  �  �  � @    � @   +       L�  �  �  |  �  �  �  �  �  �  �  �  �  �  �  �   � @   �    �    �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 h  P  �
  _commode �   	�� @   int  w   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  !	  �
  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   .  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  � @   �         double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   � @   �       �0  pexcept 0  �  �  type 
�  
    M @   b  �  R2 v @   7  Q	xT @   Xs Yt w �ww(�ww0�w  5   �    3  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 c
  K
  � @          �  _fpreset 	� @          � �    `  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �
  *  __mingw_app_type �   	�� @   int  G   �  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �    @   =      d  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �� @   ]      ��  4was_init �%  	�� @   5mSecs �%  r  p  !�  e @   �   �4  �  �  �  6�   
�  �  z  
�  �  �  
�  
	  �  
�  �	  �	  

  �	  �	  
  �	  �	  "E  �   �  
F  9
  1
  
[  j
  b
  y @   `  R	�U @   Xu w t      @    @          �;  �  �
  �
  �  �
  �
  �  �
  �
     @    @          �  �
  �
  �  �
  �
  �  �
  �
   @   �  Ru    !  � @   �   ��  �  �
  �
  �  �
  �
  �  �
  �
  7  � @   �   �      �      �  !    � @   �  Ru      � @   � @   
       �w  �  +  )  �  6  4  �  E  C    � @   � @   
       �  O  M  �  Z  X  �  i  g  � @   �  Ru      � @   � @          �   �  s  q  �  ~  |  �  �  �    � @   � @          �  �  �  �  �  �  �  �  �  � @   �  Ru    "$  �   �  
)  �  �  83  �   
4  �  �     @    @   
       s�  �  �  �  �  �  �  �  �     @    @   
       �      �      �  !     @   �  Rt      
0 @   `    R	�U @    = @   `  R	`U @      9�  � @   X       �|  
�  -  )  :�  ��/ @   
  Yu   ' @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �p @   b      �`  &addr ��  I  =  b �:  ��h �g  �  y  i �%  �  �  >P @   P       �  new_protect �
u  �  �  
� @   
  �  Ys  � @    
  � @   `  R	8U @     
� @   �
  �  Rs  � @   n
  
! @   E
    Q��X0 
� @   `  >  R	 U @    � @   `  R	�T @   Qs   ?__report_error T  @   i       �/  &msg T  �  �  @argp ��   �X
, @     �  R2 
F @   /  �  R	�T @   Q1XK 
U @       R2 
c @   �
  !  Qs Xt  i @   �
   Afwrite __builtin_fwrite   �   f  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @ @   L       �  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr �� @          �P  f ,�  �  �  � @   �  R�R  __mingw_raise_matherr �@ @   >       �typ !�   

  
  name 2�  "
  
  a1 ?w   4
  0
  a2 Jw   H
  D
  rslt 
w   � ex 0  �@y @   R�@   �    x  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  �  _fmode �   	Ѐ @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 h  P  � @   �      �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	�� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   � @   �      ��  'exception_data �-�  r
  d
  old_handler �
	  �
  �
  action ��     �
  reset_fpu ��   �  t  
� @   �
  �  R8Q0 ( @   �  R�R 
G @   �
  �  R4Q0 ] @   �  R4 
� @   �
    R8Q0 
� @   �
  7  R8Q1 
� @   �
  S  R;Q0 � @   f  R;   @   y  R8 
  @   �
  �  R;Q1 
/  @   �
  �  R4Q1 
C  @   �
  �  R8Q1 )H  @   �
   �	   �
   $  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 y  a  P  @   b      Y  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	 � @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �! @   �       �n  	hDllHandle z�  �  �  	reason {<  Z  B  	reserved |S  �  �   5" @   K       �  
keyp �&�  F  @  
t �-�  ^  \  T" @   �  
{" @   C  R	 � @     !n  " @   " @          �  �  " @   )
   "n   " @   �   �E  #�   �  �" @   )
    �" @   6  
�" @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   0! @   �       �d	  	key A(<  n  f  
prev_key C�  �  �  
cur_key D�  �  �  `! @   �  B	  Rt  �! @   �  
�! @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �  @   o       �$
  	key *%<  �  �  	dtor *1.       
new_key ,$
  N  F  �  @   �  �	  R1QH 
! @   �  
  Rt  
(! @   �  Rt   �  &n  P  @   p       ��  m  k  '�  �  @          �
  �  w  s  �  @     �  @     (�  @   Rt   j  @   �  �
  R|  )�  @   �  R	 � @      �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _CRT_MT �   	0@ @   int  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 :  "    __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	a� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	`� @    �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �" @   �      E  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  �  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��    �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	    �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   �  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �% @   �       �7
  i �(�   �  �    �	`  �  �	  �  �  importDesc �@  �  �  �  �^
  importsStartRVA �	I  �  �  �  �% @   	�  ��  �  �  �  �  �  	�  & @    �  �  �  �  �      �  #  !      M  4& @   4& @   J       �q  /  -  f  }  ;  7  �  ]  W  �  w  u    
_IsNonwritableInCurrentImage �  `% @   �       ��  pTarget �%`  �  �    �	`  rvaTarget �
�  �  �  �  �^
  �  �  �  `% @   �  �/  �  �  �  �  �  	�  p% @    �  �  �  �  �  �  �  �  �  �      M  �% @   �% @   I       �q  �  �  f  }  �  �  �  �  �  �  �  �    
_GetPEImageBase �`   % @   6       �0    �	`  	�   % @   �  �	�  �  �  �  �  	�  0% @    �  �  �  �  �      �           
_FindPESectionExec y^
  �$ @   s       �%  eNo y�   *  &    {	`  �  |	  ;  9  �  }^
  E  C  )  ~�   O  M  	�  �$ @   i  �	�  i  �  �  �  	�  �$ @    y  �  y  �  �  \  X  �  m  k       
__mingw_GetSectionCount g�   `$ @   7       ��    i	`  �  j	  y  w  	�  `$ @   N  m	�  N  �  �  �  	�  p$ @    ^  �  ^  �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
  �# @   �       �  p Y&s  �  �    [	`  rva \
�  �  �  �  �# @   (  _�  �  (  �  �  �  	�  �# @    8  �  8  �  �  �  �  �  �  �      	M  $ @   C  c
q  �  �  f  C  }    �  �       �  *  (     
_FindPESectionByName :^
  @# @   �       �M  pName :#�  =  3    <	`  �  =	  i  g  �  >^
  s  q  )  ?�   }  {  �  U# @     F  �    �  �  �  �  e# @    e# @          �  �  �  �  �  �  �  �     &O# @   �  -  Rt  '�# @   z  Rs Qt X8  _FindPESection $^
  �    $`  (rva $-�  �  &	  �  '^
  )  (�    _ValidateImageBase   �    `  pDOSHeader �  �  	  pOptHeader v   )�  �" @   ,       �~  �  �  �  �  �  �  �  �  	�  �" @      �  �  �    �  �  �  �  �  �  �     *M  �" @   P       �f  �  �  +q  Q}      �  +  )  �  5  1    8   m  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   ' @   �      �  
__gnuc_va_list �   #__builtin_va_list �   char �   
va_list w   
size_t #,�   long long unsigned int long long int 
wchar_t b
  short unsigned int int long int 	�   6  	#  unsigned int long unsigned int unsigned char double float long double 	�  	6  optind #  optopt #  opterr #  optarg 6  option  >*  name @/   has_arg A#  flag B@  val C#   �  	�   /  $E  G~  no_argument  required_argument optional_argument  _iobuf 0!
  _ptr %6   _cnt &	#  _base '6  _flag (	#  _file )	#  _charbuf *	#   _bufsiz +	#  $_tmpfname ,6  ( 
FILE /~  
DWORD �U  signed char short int WCHAR 1�   E  	E  	S  LPWSTR 5X  LPCWSTR 9]  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS E  �i  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  %tagCOINITBASE E  ��  COINITBASE_MULTITHREADED   VARENUM E  		+  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � �  	`@ @   �  	\@ @   �  	X@ @   &__mingw_optreset D#  	�� @   �  	�� @   
place f6  	P@ @   
nonopt_start i#  	H@ @   
nonopt_end j#  	D@ @   �   �  �   ! �  
recargchar m�  	�W @   
recargstring n�  	@W @   �   :  �    *  
ambig o:  	W @   �   f  �   ' V  
noarg pf  	�V @   �   �  �    �  
illoptchar q�  	�V @   
illoptstring r�  	�V @   vfprintf )#  �  �  4  �    	  �  fprintf "#  	  �  4   __acrt_iob_func ]�  @	  E   '__p___argv ��  strncmp 
V#  w	  /  /  �    strlen 
@�   �	  /   strchr 
D6  �	  /  #   GetEnvironmentVariableW ?  �	  q  b     getopt_long_only O#  p2 @           ��
  J  ,#  �  �  D  ,+�
  �  �  2  ,>/  �  �  P  -�
  �  �  idx --@  � �2 @     R�RQ�QX�XY�Yw � w(5  	;  	*  getopt_long M#  P2 @           �q  J  #  �  �  D  &�
  �  �  2  9/    
  P   �
  $     idx  -@  � k2 @     R�RQ�QX�XY�Yw � w(1  getopt #   2 @   "       �  J  #  :  6  D  !�
  P  L  2  4/  f  b  =2 @     R�RQ�QX�XY0w 0w(0  (getopt_internal C#  �+ @   (      ��  J  C#  �  x  D  C*�
  �  �  2  C=/  �  �  P  D�
  "    idx D*@  � )flags D3#  P  B   oli F6  �  �   optchar G#  �  �  *:  G#  �  �  +posixly_correct H
#  	@@ @   ,start g- @      C
  Rt Qu Xs Yv  ~- @   �	  [
  R}  �- @   �  �
  Rv Q} X~ Y�  �- @   �	  �
  R} Qs  F. @   �	  �
  R	hV @   Q0X0 �. @   �	  �
  R} Q- �. @   �	  	  R} Q- �/ @      '  Ru Yv  �0 @      T  R���Qt Xs Yv  �0 @   �  y  R	�V @   Qs  31 @   �  �  R	�W @   QW �1 @   �  �  Rv Q} X~ Y� w 0 2 @   �  R	�W @   Qs   -parse_long_options �#  `( @   �      �   D  �"�
      2  �5/  '  #  P  ��
  ?  7  idx �*@  c  _  .:  �3#  � current_argv �6  w  s  has_equal �6  �  �  current_argv_len �	�   �  �  i �#    �  ambiguous �	#  -    match �#  �  x  �( @   �	  /  Ru Q= 8) @   S	  S  Ru Q~ Xt  D) @   w	  k  R~  * @   �  �  R	W @   Qt Xu  �* @   w	  �  Ru  + @   �  �  R	�V @   Qu  &+ @   �  �  R	�V @   Qt Xu  �+ @   �  R	@W @   Qu   !permute_args � ' @   �       ��  panonopt_start �#  �  �  panonopt_end �&#  �  �  opt_end �8#  �  �  D  ��
      cstart �#  ?  5  cyclelen �#  b  `  i �#  k  i  j �#  |  r  ncycle �#  �  �  nnonopts �&#  �  �  nopts �0#  �  �  pos �7#  �  �  swap �6      /�  ' @    �  ��  *     �  L  F  0�  1�  f  ^     2gcd �#  �  a �	#  b �#  3c �#   !warnx ~�' @   �       ��  fmt ~/  �  }  
ap ��   �X4�  �' @    �' @   W       �  �  �    �  �  5�' @   @	  ( @   	  u  R2 ( @   �  �  Q	`V @   Xu  $( @   	  �  R2 2( @   �  �  Qs Xt  <( @   	  �  R2 J( @   "  R:   6_vwarnx u"  fmt u/  ap u!�    7fputc __builtin_fputc 
  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  �(  _MINGW_INSTALL_DEBUG_MATHERR �   	p@ @   int  �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �2 @          +)  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	�@ @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  �2 @          �_File *�  �  �  _Format J�  �  �  _ArgList Z�   �  �  �2 @   �  R0Q�RX�QY0w �X   �   U!  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �2 @   B       �)  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   size_t #,�   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(B  G  	threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   e  j  t  
 �   e  �   o  __imp_sprintf �  	�@ @   P  __stdio_common_vsprintf �  �  �   e  �   o  $  �    sprintf �  �2 @   B       �_Dest )j  #    _Format It  <  6  
ap 
�   �hret   Q  O  �2 @   �  R2Q�RX	�Y�Qw 0w(�   �   �"  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  l   3 @   H       (*  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	�@ @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	   3 @   H       �_Format .�  i  c  
ap 
�   �Xret 	  ~  |  ,3 @   �  �  R1 A3 @   �  R0Xs Y0w t    �   �#  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 f  N  P3 @   2       �*  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�@ @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  P3 @   2       �_File )�  �  �  _Format I�  �  �  
ap 
�   �hret 	  �  �  }3 @   �  R0Q�RX�QY0w �   �   \%  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 K  3  G+  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	�� @   local__winitenv 
`  	�� @   '  
	�@ @     
	�@ @    �   &  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 h  P  �3 @         �+  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	@A @   �      �   __imp_at_quick_exit g)  	8A @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	4A @   
initial_tzname1 }
V  	0A @   
initial_tznames ~�  	 A @   
initial_timezone 
%  	A @   
initial_daylight �  	A @   __imp_tzname ��  	A @   __imp_timezone ��  	A @   __imp_daylight ��  	 A @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�@ @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�@ @   �	  __imp__amsg_exit �V  	�@ @   F  __imp__get_output_format �\
  	�@ @   -
  __imp_tzset ��  	�@ @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�@ @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �4 @   5       ��
  file �!�
  �  �  fmt �6�  �  �  
ap ��   �h+ret �       5 @   �
  R4Q�RX�QY0w �  ,tzset "05 @   6       �  -  45 @   45 @   -       �B5 @   +  N5 @     Z5 @       ._tzset �/_get_output_format nF  �3 @          �0_amsg_exit i�4 @   .       ��  ret i  "    �4 @   z  �  R2 �4 @   Q  �  Q	�W @   Xs  �4 @   <  R�  at_quick_exit �  �4 @          �   func ]*�  5  1  1�4 @   �   _onexit ��  �4 @          �p  func V%�  M  G  �4 @   �  Rs   __wgetmainargs J  4 @   j       �[  _Argc J"�  l  f  _Argv J5I  �  �  _Env JGI  �  �   ]  JQ  �  �  !i  Jl�	  � 04 @   0  @4 @   	  &  R	v  $0.# E4 @   �  N4 @   �  Y4 @   �  m4 @   U   __getmainargs >  �3 @   j       �E  _Argc >!�  �  �  _Argv >1S    �  _Env >@S       ]  >J  3  -  !i  >e�	  � �3 @   �  �3 @   �    R	v  $0.# �3 @   �  �3 @   �  �3 @   u  �3 @   U   2  p5 @   6       ��5 @   +  �5 @     �5 @                                                                                              
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  (    I  ( 
  H}   !I  $ >  H }  	
 :!;9!I8  
 :;9I  .?:;9'I<  
 :;9I8  
.?:;9'I<  4 :!;9I  I  (   4 :!;9I  4 :!;9I�B  ! I/  & I   <  .?:;9'<   1�B   :!;9I  4 :!;9I?  4 1�B  4 1  H}  I �~  :;9  >!I:;9   >!!I:;9  ! :!;9I  "   # :!;9I�B  $7 I  % :;9I  &>!!I:;9  '.?:;9!'<  (4 1  ).:!;9!
' !  *4 :!;9!I  + :!;!<9I�B  ,. ?<n:!;!   -%U  .   /4 :;9I?<  0:;9  1.?:;9'�<  2. ?:;9'I<  3.?:;9'I@z  41R�BUXYW  5U  64 1I�B  74 4I�B  8! I  9.?:;9'@z  :  ;H}�  <.1@z  =H}�  >! I/  ?. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   I ~  (   H}  $ >   I   :!;9I�B  4 :!;9I�B  
 :;9I8  	 !I  
4 :!;9I  & I  (   
 :;9I  H}  .?:;9'I<   :!;9I�B  4 :!;9I?<  4 G  I  ! I/   :!;9I�B   1�B   :!;9I   :!;9I  .?:!;9!'I@z   :!;9I  :;9  7 I  >!!I:;9  .?:!;9!'I<      4 :!;9I�B  !.:!;9!'@z  "%  # I  $>I:;9  %>I:;9  &4 :;9I?  '. ?:;9'I<  (.:;9'I@z  ) :;9I�B  *4 :;9I�B  +4 :;9I  ,
 :;9  -.:;9'I@z  . :;9I  /1R�BUXYW  0U  14 1�B  2.:;9'I   34 :;9I  41R�BXYW  5H }  6.:;9'   7. ?<n:;   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}    I  $ >   !I  I ~  
 :!;9I8   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                                                                                               5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg     �   �
        T  m  �  �  �  �   �   �           (  ;  F  N  [  d  m  v  ~  �   
 	P @   (h
1
U?Y  � J t t � <Y  �  uf! W t t! � <Y! J�x<` Xfs� ��D�  x s��s� �� <f�   	�7 @   ��;Xdt�Y �uu+����t?t? �Yy�z�	�	ez��XtgL1<�@*NY��� J �  <�
�~<t<Jr�ZY��
2Jf'�5Il�6JJ6JJ<t � �! �f�JY" � � < �~J <" � f  f�&� �� 
J5 1 J
]t < 5 ? 9�5 
�vr	fZ* � Jt* <- J J
�IgJ�% ]� f  f�<5 ��Z�~� �  <YX���|�~X�< ft <Y�X� /J� �� �O Jz�w���J�	�~�  ��Ye f  <Y�w��L.� � ��f7X �  <YMXu �  <Y.� �  <YyX �� <Y #    K   �
      �  �    5  ?  I  Q  ^  g  q   	@ @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �  �      I  T  \  i  r  }  6     .   �
      �  �      R     .   �
      p  �  �  �   	 @    6     .   �
      
  %  L  W      K   �
      �  �  �    
       ,  6  >   	  @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �  �  �  �  6     .   �
      9	  Q	  x	  �	  �     <   �
      �	  �	  
  .
  5
  <
  C
   	� @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �
  �
  �
  �
   	� @   		3 6     .   �
      9  Q  x  �  |    s   �
      �  �  %  @  O  ^  g  q  }  �  �  �  �  �  �  �  �  �    	  @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	� @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      4
  L
  s
  �
  �
  �
   	@ @   
L�Z
�KTYZ
g=yuX 6     .   �
      �
    :  E  �    U   �
      �  �  �  	�        '  1  B  O  X   	� @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      �  �  �        "  .  8  @  M  X  a  u   	P  @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      �  �      6     .   �
      u  �  �  �  G    K   �
      $  <  c  ~  �  �  �  �  �  �   	�" @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< ]	    n   �
      
  "  J  e  n  w  �  �  �  �  �  �  �  �  �  �  �    	 ' @   ��/x<_//a</�< 9zXL  X�  J"�
y<	�8	�M 
A	t>> w<  G  J�
= <    TXpfuV"Ys	 X � � <Y �f <Y	 �X < f	X� �bur�p<�Vrt��X <� = I=   Ju�.	p�<d�Xk
��% j�  <% J JZ �N �
��
Y
 ��<�J�</X�J�
�
� Xf <�� ��f^�<KY ��� ��8<Z�	t��2K;�/
���X� � �P<	X�X�XJ�w� ��	��+̟^X!&��w�!!��
A�
tvf
�( � f�X�
 ��	������f���
���f	�fY�-0��
�N  �� �, X t5 t�� � ��
�0�t<J<t, �
�. ��3 J- �����~�
�	�Xt	fxfK  �gX�	M
J
�' I� �� ��'tXu
�. �!��
�nJfgt	u	;u��P�	K
<
ew
cu	^]h��
Q�X�...�X<ugit0,�,Z� �J<t
�gu ��
g �~�	K;Xf X � J+ ��
zf=sg
y�f*Je.	
�!t�f<�
e� Ju� ����k� X
.�
�	�
	X�<��z��	 �u�g
t
J
�
.L
h	XL
h 6     .   �
      ?  W    �  n     A   �
      �    1  L  \  l  u     	�2 @   K
�<.Y �     A   �
      �  �    3  B  Q  Z  d   	�2 @   KU	\F	\fWY	Y �     A   �
      �  �  �    %  3  <  F   	 3 @   g?	YT�Y	 X� <uX �     A   �
      �  �  �  �  	    !  +   	P3 @   KU	\fp	\;Y	Y W     O   �
        �  �  �        &  /  :  F  |    h   �
      �  �  �  �  &  8  J  Q  Z  d  m  u  �  �  �  �   	�3 @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	�4 @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �      4   P  P @   �       A�A�D@^
A�A�E    L   P  � @   �       A�A�A �G�B
 A�A�A�IP A�A�A�  d   P  �7 @   �      A�B�B �B(�B0�A8�A@�AH�	G�H��
�A�A�B�B�B�B�A�%A          ���� x �         X  @ @   :       D0u  4   X  � @   j       A�A�D@@
A�A�H       X  � @             ���� x �         �   @             ���� x �      $       @   /       D0R
JN    L     P @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�         � @             ���� x �      <   �  � @   �       A�A�D�P�
���
���A�A�B    ���� x �           � @             ���� x �      $   @    @   i       A�A�DP   <   @  p @   b      A�A�A �Dp�
 A�A�A�D   \   @  � @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �            @ @   >       D`y        � @             ���� x �      4   p  � @   �      A�D0}
A�Mf
A�I     ���� x �      L   �  P  @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �  �  @   o       A�A�A �D@U
 A�A�A�A    D   �  0! @   �       A�A�D@R
A�A�FR
A�A�D      4   �  �! @   �       A�D0p
A�J�
A�A      ���� x �         �  �" @   ,          �  �" @   P       L   �  @# @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       �  �# @   �          �  `$ @   7          �  �$ @   s          �   % @   6          �  `% @   �          �  �% @   �          ���� x �      T      ' @   �       B�B�A �A(�A0�A8��
�0A�(A� A�B�B�A      <     �' @   �       A�A�A �DPw A�A�A�      l     `( @   �      B�B�B �B(�A0�A8�A@�AH�	D�e
HA�@A�8A�0A�(B� B�B�B�G    l     �+ @   (      B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�C          2 @   "       D@]       P2 @           D@[       p2 @           D@[     ���� x �          
  �2 @          D@Y     ���� x �         8
  �2 @   B       DP}     ���� x �      ,   p
   3 @   H       A�A�D`A�A�   ���� x �         �
  P3 @   2       DPm     ���� x �         �
  �3 @          L   �
  �3 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �
  4 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �
  �4 @          A�D0WA�    �
  �4 @             �
  �4 @   .       A�D0   �
  �4 @   5       DPp     �
  05 @   6       D0q     �
  p5 @   6       D0q                                                                                                                                                                                                                                                                                                                                                                                                  Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion lockdownd_client_private locationid prod_ven libusb_device_handle libusb_device libusb_context idevice_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection options short_too nargv nargc long_options _DoWildCard _StartInfo                                                                                                                                             C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools idevice_usb.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/plist ../include/libimobiledevice ./libusb C:/msys64/ucrt64/include/libimobiledevice-glue idevice_usb.c idevice_usb.c corecrt.h stdio.h getopt.h stdint.h plist.h libimobiledevice.h lockdown.h winnt.h combaseapi.h wtypes.h libusb.h string.h utils.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:/M/B/src/mingw-w64/mingw-w64-crt/misc/getopt.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include getopt.c getopt.c vadefs.h corecrt.h getopt.h stdio.h minwindef.h winnt.h combaseapi.h wtypes.h string.h processenv.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_sprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_sprintf.c ucrt_sprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                                                                                                                                                                                                                                 �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� �                  �7 @    7R7�T��t q ���t p ����Rp �       �7 @    7Q7�S��Q     �9 @    
P��P     n9 @    P��P       8 @    �0���	0��	�	0��	�
	���
�0�  8 @   �1�  8 @   �0�  8 @   �0�              8 @    �0���P��v ����}��
v �
�
0��
�v                       8 @    �0���P��]��P��]��P��]��]�	�	]�
�
]�
�]   �9 @   	��~    �9 @    pS��S      �9 @    �����������       �9 @    �t ��t ��t       �9 @    �0���0���0�     �9 @    P��P      �9 @    ��������     � @    
R
��R�           � @    QNTNT�Q�T�T���Q�             � @    XMSMSQST�X�T�S���X�           P @    
Q
+T+k�Q�krTr��Q�      V @    0�+PerP   V @   ��  �   V @   ��
  � 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
�S @   ���
�S @   ���
0T @   ���
T @   ���
fT @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ����r     ��U  ��2�  ����r     ��U  ��1�  ����r     ��U  ��1�  ����r     ��U  �	�	4�  �	�	��r     �	�	U  �	�	4�  �	�	��r     �	�	U  �	�	8�  �	�	��r     �	�	U  �	�	8�  �	�	��r     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
��u     �
�
T  �
�
4�  �
�
��u     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � ;            ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�             �	�
R�
�\��R��\���R���\           �	�
Q�
�V��Q��V���Q���V           �	�
X�
�]��X��]��]         �	�
Y�
�^���Y���^               �	�
�(���(��_���(��_���(��_       �
�
P�
�p���P��p���P             �
�
P�
�S��-���S��S��S     ��
0��
�
P��0�     ��R��	�      ��Q��	�         ��X��\���X���	\     ��Y��	�     ��U��	U               ��P��X����������P������	���	�	��         ��T��T��P��	T      ��0���_��_                  ��0�����������R��1�������0���	0��	�	0�            ��	����_��_��	����	_�	�	_        WRW��R���R          Q�S���Q���S        WXW��X���X          QYQ�U���Y���U          MW\WgQg�\��|���\   ?�P  ?W0�      Mg0�g�R��r���R��0�  6M]       �V���Q�R���V       �T���X�Q���T       MW\W�Q��Q��\     gsZ��Z       %T%%P%,Q,6]��T     %V%/P��V        ,Q,/]/6Q��Q       ��R��S���R�  ��T    ��R��S T                RQ�R�        QX�Q�        X�`�X� B                3R3<Q<B�R�        .Q.<Y<B�Q�   =BP )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                                                                                                                                                                                                           X         Z���� ���� ���� ���� ���� ���� ���� <         �9 @    ������� P @   ��7 @   �
 S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����          6��                       .file   a   ��  gcrtexe.c              �                                �              �   �                        �   �                        �   �                        %                          @  p                        `             k  P	                        �  0	                        �  �                        �  p	                        �  0          �  �	                    envp           argv            argc    (                          @	                        '  �          9  �                        ^  �                        �             �  @                        �  `	                        �  �                          P                    mainret            "   	                        8  �                        N   	                        d  	                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �	     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  �	     +                 .file   �   ��  gidevice_usb.c         m  P                           �  �      main    �'          �  �	                    .text   P     L               .data                            .bss    0                       .rdata         	                .xdata  t                       .pdata  l                          �  �'     �  K                 �  �                           �  �                          �  �&  
   �  �                 �  �     �                    �  �     �                   �  0      @                    �  \      @                      9     
                          o                       �     �                    )   
     +                     4  P                    .text   �      .data           .bss    P       .idata$7�      .idata$5x      .idata$4�      .idata$6�      .text   �      .data           .bss    P       .idata$7�      .idata$5p      .idata$4�      .idata$6�      .text   �      .data           .bss    P       .idata$7�      .idata$5h      .idata$4�      .idata$6�      .text   �      .data           .bss    P       .idata$7�      .idata$5`      .idata$4�      .idata$6�      .text   �      .data           .bss    P       .idata$7�      .idata$5X      .idata$4�      .idata$6|      .text   �      .data           .bss    P       .idata$7|      .idata$5P      .idata$4�      .idata$6b      .text   �      .data           .bss    P       .idata$7x      .idata$5H      .idata$4�      .idata$6T      .text   �      .data           .bss    P       .idata$7t      .idata$5@      .idata$4�      .idata$6D      .file   �   ��  gfake              hname   �      fthunk  @      .text   �                       .data                            .bss    P                        .idata$2�                      .idata$4�      .idata$5@      .file   0  ��  gfake              .text   �                       .data                            .bss    P                        .idata$4�                      .idata$5�                      .idata$7�                      .text   �      .idata$7      .idata$5      .idata$4P      .idata$6H      .text   �      .idata$7      .idata$5       .idata$4H      .idata$60      .text   �      .idata$7      .idata$5�      .idata$4@      .idata$6      .text   �      .idata$7      .idata$5�      .idata$48      .idata$6�      .text          .idata$7      .idata$5�      .idata$40      .idata$6�      .text         .idata$7       .idata$5�      .idata$4(      .idata$6�      .text         .idata$7�      .idata$5�      .idata$4       .idata$6�      .text         .idata$7�      .idata$5�      .idata$4      .idata$6�      .text          .idata$74      .idata$5      .idata$4`      .idata$6d      .text   (      .idata$7\      .idata$50      .idata$4x      .idata$60      .text   0      .idata$7X      .idata$5(      .idata$4p      .idata$6      .text   8      .idata$7T      .idata$5       .idata$4h      .idata$6      .file   T  ��  ggccmain.c             �  @                       p.0                 �  �          �                       __main  �            P       .text   @     �                .data                          .bss    P                       .xdata  �                       .pdata  �      $   	                 �  bF  
   a                   �  -	     ?                    �  �     5                     �  p      0                      C
     '                     �     �                     )  0
     +                     4  X     �                .file   j  ��  gnatstart.c        .text                          .data                          .bss    `                           �  �L  
     
                 �  l
     �                     �  �                             j     V   
                   �                            z                         )  `
     +                 .file   ~  ��  gwildcard.c        .text                          .data                           .bss    p                            �  �R  
   �                    �  "     .                     �  �                             �     :                      �     �                     )  �
     +                 .file   �  ��  gdllargv.c         _setargv                       .text                         .data   0                        .bss    p                        .xdata  �                       .pdata  �                          �  RS  
   �                   �  P     :                     �  �      0                      �     V                      '     �                     )  �
     +                     4  �     0                .file   �  ��  g_newmode.c        .text                           .data   0                        .bss    p                           �  �T  
   �                    �  �     .                     �                              P     :                      �     �                     )  �
     +                 .file   �  ��  gtlssup.c              !                              0  P          ?  �                    __xd_a  P       __xd_z  X           V  �      .text         �                .data   0                        .bss    �                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata        H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  ]U  
   �  6                 �  �     �                    �  �                        �  0     0                      �                          �                            b     �                     )        +                     4       �                .file   �  ��  gxncommod.c        .text   �                       .data   0                        .bss    �                           �  $]  
   �                    �  �
     .                     �  `                            �
     :                      P     �                     )  P     +                 .file     ��  gcinitexe.c        .text   �                       .data   0                        .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �]  
   {                   �  �
     a                     �  �                            �
     :                      �     �                     )  �     +                 .file   6  ��  gmerr.c            _matherr�                       .text   �     �                .data   0                        .bss    �                        .rdata  �     @               .xdata  �                       .pdata  �                          �  )_  
   6  
                 �  .                         �  �     �                    �  �     0                           �                      �	     �                     )  �     +                     4  �     X                .file   S  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   0                        .bss    �                        .xdata  �                       .pdata  �                          �  _b  
   �                    �  3     -                     �  �     0                      �     X                      K
     �                     )  �     +                     4       0                .file   g  ��  gmingw_helpers.    .text    	                       .data   0                        .bss    �                           �  �b  
   �                    �  `     .                     �                               *     :                      �
     �                     )       +                 .file   �  ��  gpseudo-reloc.c        b   	                           q  p	          �  �       the_secs�           �  �
          �  �           �                           �  0                    .text    	     =  &             .data   0                        .bss    �                       .rdata  �     [                .xdata  �      0                 .pdata  �      $   	                 �  �c  
   K  �                 �  �     �                    �  d     �  
                 �        0                    �  �      W                       d     �                     �     	                       �     O                    )  @     +                     4  @     �                .file   �  ��  gusermatherr.c           @                           ,  �           :  �      .text   @     L                .data   0                        .bss    �                       .xdata  ,                      .pdata                            �  �z  
   �                   �  f                         �  �     r                     �  P     0                      �     �                      �     �                     )  p     +                     4        P                .file   �  ��  gxtxtmode.c        .text   �                       .data   0                        .bss    �                           �  �}  
   �                    �  x     .                     �  �                            �     :                      �
     �                     )  �     +                 .file   �  ��  gcrt_handler.c         Q  �                       .text   �     �               .data   0                        .bss    �                       .xdata  8                      .rdata        (   
             .pdata  8                         �  P~  
   �                   �  �     ~                    �  X
     _                    �  �     0                      �     �  
                   �                            P                         )  �     +                     4  p     P                .file     ��  gtlsthrd.c             h  P                           �             �             �  �          �            �  0          �  �      .text   P     b  "             .data   0                        .bss          H                 .xdata  @     0                 .pdata  D     0                    �  1�  
   �
  A                 �  $     a                    �  �     �                    �  �     0                    �  �                             Y     x                     a     %                    )   
     +                     4  �     (               .file   $  ��  gtlsmcrt.c         .text   �                       .data   0                       .bss    `                           �  �  
   �                    �  �     .                     �                               �     :                      �     �                     )  0
     +                 .file   8  ��  g                .text   �                       .data   @                        .bss    `                          �  ��  
   �                    �  �     0                     �                                    :                      "     �                     )  `
     +                 .file   p  ��  gpesect.c                �                           /  �          >  @          S  �          p  `          �  �          �             �  `          �  �      .text   �     �  	             .data   @                        .bss    p                       .xdata  p     ,                 .pdata  t     l                    �  ]�  
   �  �                 �  �     �                    �  �     �                    �  @     0                    �  
     �                       E     K                     �     T                       �     �                     )  �
     +                     4  �     (               .text   �     2                 .data   @                        .bss    p                       .text                           .data   @                        .bss    p                           )  �
     +                 .file   �  ��  ggetopt.c              �                          warnx   �          �  `      place   P       ambig             
  �      noarg   �            @          $  �          4  @           F  D           Q  H           ^  �          i  �      getopt   "          t  P"          �  p"      .text         �  t             .data   @      $                .bss    �                      .xdata  �     d                 .pdata  �     T                .rdata  `     B                    �  /�  
   <  �                 �  m     �                    �  z     ?                    �  p     0                    �  �                            �     a	                     2     +                       �     .                    )  �
     +                     4       �               .file   �  ��  gmingw_matherr.    .text   �"                       .data   p                       .bss    �                           �  k�  
   �                    �  �     .                     �  �                            �(     :                      �     �                     )        +                 .file   �  ��  gucrt_vfprintf.    vfprintf�"                       .text   �"                     .data   �                      .bss    �                       .xdata                         .pdata  4                         �  	�  
   �                   �        8                    �  �     X                     �  �     0                      +)     r   	                   �     �                     )  P     +                     4   
     8                .file   �  ��  gucrt_sprintf.c    sprintf �"                       .text   �"     B                .data   �                      .bss    �                       .xdata                        .pdata  @                         �  ��  
   �                   �  U!     9                    �       F                     �  �     0                      �)     �   	                   �     �                     )  �     +                     4  8
     8                .file     ��  gucrt_printf.c     printf   #                       .text    #     H                .data   �                      .bss    �                       .xdata                        .pdata  L                         �  -�  
   �  
                 �  �"     l                    �  W     -                     �        0                      (*     �   	                   l     �                     )  �     +                     4  p
     H                .file   0  ��  gucrt_fprintf.c    fprintf P#                       .text   P#     2                .data   �                      .bss    �                       .xdata                        .pdata  X                         �  ��  
   �                   �  �#     b                    �  �     F                     �  P     0                      �*     �   	                   N     �                     )  �     +                     4  �
     8                .file   F  ��  g__initenv.c           �  �          �  �      .text   �#                       .data   �                      .bss    �                          �  ��  
   �                   �  \%     �                     �  �                            G+     [                      3                         )       +                 .file   �  ��  gucrtbase_compa        �  �#                           �  �#          �  $      _onexit �$          �  �$          �  �                        	  �$          	  �$      tzset   0%          ,	  `                    _tzset  p%          H	            Y	            j	             z	  4          �	  0      .text   �#       "             .data   �      x   
             .bss    �                       .xdata  $     P                 .pdata  d     l                .rdata  �                          �  $�  
   �  Y                 �  &                          �  �     |                    �  �     0                      �+     �                     ]                            P     `                    )  @     +                     4  �
     �               .text   �%      .data   P      .bss    �      .idata$7$      .idata$5�      .idata$4@      .idata$6�
      .text   �%      .data   P      .bss    �      .idata$7(      .idata$5       .idata$4H      .idata$6�
      .text   �%      .data   P      .bss    �      .idata$7,      .idata$5      .idata$4P      .idata$6�
      .text   �%      .data   P      .bss    �      .idata$70      .idata$5      .idata$4X      .idata$6�
      .file   �  ��  gfake              hname   @      fthunk  �      .text   �%                       .data   P                       .bss    �                       .idata$2�                      .idata$4@      .idata$5�      .file   �  ��  gfake              .text   �%                       .data   P                       .bss    �                       .idata$4`                      .idata$5                      .idata$74                      .text   �%      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4(      .idata$6�
      .text   �%      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$40      .idata$6�
      .file   �  ��  gfake              hname   (      fthunk  �      .text   �%                       .data   P                       .bss    �                       .idata$2�                      .idata$4(      .idata$5�      .file     ��  gfake              .text   �%                       .data   P                       .bss    �                       .idata$48                      .idata$5�                      .idata$7      !                 .text   �%      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6"
      .text   �%      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$64
      .text   �%      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6D
      .text   �%      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6R
      .text    &      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6l
      .text   &      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4       .idata$6�
      .text   &      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4      .idata$6�
      .text   &      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4      .idata$6�
      .text    &      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4      .idata$6�
      .file   (  ��  gfake              hname   �      fthunk  �      .text   0&                       .data   P                       .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   0&                       .data   P                       .bss    �                       .idata$4                       .idata$5�                      .idata$7�
                       .text   0&      .data   P      .bss    �      .idata$7L
      .idata$5       .idata$4H      .idata$6�      .text   8&      .data   P      .bss    �      .idata$7P
      .idata$5      .idata$4P      .idata$6	      .text   @&      .data   P      .bss    �      .idata$7T
      .idata$5      .idata$4X      .idata$6	      .text   H&      .data   P      .bss    �      .idata$7X
      .idata$5      .idata$4`      .idata$6 	      .text   P&      .data   P      .bss    �      .idata$7\
      .idata$5       .idata$4h      .idata$6*	      .text   X&      .data   P      .bss    �      .idata$7`
      .idata$5(      .idata$4p      .idata$6D	      .text   `&      .data   P      .bss    �      .idata$7d
      .idata$50      .idata$4x      .idata$6\	      .text   h&      .data   P      .bss    �      .idata$7h
      .idata$58      .idata$4�      .idata$6r	      .text   p&      .data   P      .bss    �      .idata$7l
      .idata$5@      .idata$4�      .idata$6�	      .text   x&      .data   P      .bss    �      .idata$7p
      .idata$5H      .idata$4�      .idata$6�	      .text   �&      .data   P      .bss    �      .idata$7t
      .idata$5P      .idata$4�      .idata$6�	      .text   �&      .data   P      .bss    �      .idata$7x
      .idata$5X      .idata$4�      .idata$6�	      .text   �&      .data   P      .bss    �      .idata$7|
      .idata$5`      .idata$4�      .idata$6�	      .text   �&      .data   P      .bss    �      .idata$7�
      .idata$5h      .idata$4�      .idata$6�	      .text   �&      .data   P      .bss    �      .idata$7�
      .idata$5p      .idata$4�      .idata$6
      .text   �&      .data   P      .bss    �      .idata$7�
      .idata$5x      .idata$4�      .idata$6
      .text   �&      .data   P      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6
      .file   �  ��  gfake              hname   H      fthunk         .text   �&                       .data   P                       .bss    �                       .idata$2�                      .idata$4H      .idata$5       .file   �  ��  gfake              .text   �&                       .data   P                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�
     "                 .text   �&      .data   P      .bss    �      .idata$7
      .idata$5�      .idata$4       .idata$6�      .text   �&      .data   P      .bss    �      .idata$7
      .idata$5�      .idata$4(      .idata$6�      .text   �&      .data   P      .bss    �      .idata$7 
      .idata$5�      .idata$40      .idata$6�      .text   �&      .data   P      .bss    �      .idata$7$
      .idata$5�      .idata$48      .idata$6�      .file   �  ��  gfake              hname          fthunk  �      .text   �&                       .data   P                       .bss    �                       .idata$2x                      .idata$4       .idata$5�      .file     ��  gfake              .text   �&                       .data   P                       .bss    �                       .idata$4@                      .idata$5�                      .idata$7(
     "                 .text   �&      .data   P      .bss    �      .idata$7�      .idata$5�      .idata$4      .idata$6�      .file     ��  gfake              hname         fthunk  �      .text   �&                       .data   P                       .bss    �                       .idata$2d                      .idata$4      .idata$5�      .file   @  ��  gfake              .text   �&                       .data   P                       .bss    �                       .idata$4                      .idata$5�                      .idata$7�                      .text   �&      .data   P      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �&      .data   P      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text    '      .data   P      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   '      .data   P      .bss    �      .idata$7�      .idata$5�      .idata$4       .idata$6�      .file   N  ��  gfake              hname   �      fthunk  �      .text   '                       .data   P                       .bss    �                       .idata$2P                      .idata$4�      .idata$5�      .file   j  ��  gfake              .text   '                       .data   P                       .bss    �                       .idata$4                      .idata$5�                      .idata$7�                      .text   '      .data   P      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6`      .text   '      .data   P      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6p      .file   x  ��  gfake              hname   �      fthunk  �      .text    '                       .data   P                       .bss    �                       .idata$2<                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text    '                       .data   P                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�     &                 .text    '      .data   P      .bss    �      .idata$7�      .idata$5x      .idata$4�      .idata$6P      .text   ('      .data   P      .bss    �      .idata$7|      .idata$5p      .idata$4�      .idata$6>      .text   0'      .data   P      .bss    �      .idata$7x      .idata$5h      .idata$4�      .idata$60      .text   8'      .data   P      .bss    �      .idata$7t      .idata$5`      .idata$4�      .idata$6(      .text   @'      .data   P      .bss    �      .idata$7p      .idata$5X      .idata$4�      .idata$6
      .text   H'      .data   P      .bss    �      .idata$7l      .idata$5P      .idata$4�      .idata$6�      .text   P'      .data   P      .bss    �      .idata$7h      .idata$5H      .idata$4�      .idata$6�      .text   X'      .data   P      .bss    �      .idata$7d      .idata$5@      .idata$4�      .idata$6�      .text   `'      .data   P      .bss    �      .idata$7`      .idata$58      .idata$4�      .idata$6�      .text   h'      .data   P      .bss    �      .idata$7\      .idata$50      .idata$4x      .idata$6�      .text   p'      .data   P      .bss    �      .idata$7X      .idata$5(      .idata$4p      .idata$6|      .file   �  ��  gfake              hname   p      fthunk  (      .text   �'                       .data   P                       .bss    �                       .idata$2(                      .idata$4p      .idata$5(      .file   �  ��  gfake              .text   �'                       .data   P                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�     
                 .file     ��  gcygming-crtend        �	   .                       .text   �'                       .data   P                       .bss    �                           �   .                         �  t                          �  �                         �	  8.                         )  p     +                 .idata$2        .idata$5�      .idata$4      .idata$2       .idata$5      .idata$4`      .idata$2�       .idata$5       .idata$4h      .idata$4X      .idata$5      .idata$7      .idata$4h      .idata$5       .idata$78      .idata$4�      .idata$58      .idata$7`      .rsrc       
    __xc_z             �	  �          �	  �%          �	  0          �	  h          
   &          6
  p          B
  �          ^
  8          |
  �          �
              �
  H.          �
            �
  `          �
  �          �
  x           !  @'          =  �          Z  �          |  �          �  �          �             �            �      	        �  �          �  �&            �       __xl_a  0             X'            P          A  �      _cexit  H&          U  `  ��       m     ��       �  4          �  H          �              �  �          �      ��       	
     ��       %
  0           7
  (      __xl_d  @           S
  h      _tls_end   	        x
         __tzname�%          �
  �      reset   @           �
  �	          �
   '          �
            �
             �
  �           �
  0           �
  �            �           5  P          M      	    memcpy  �&      verbose D           X  �          t  p	      puts     &          �  0          �  (          �  �       malloc  '      _CRT_MT 0       optarg  �          �  0'          �              �  �          �  �            �          :            I     ��       a  �            �          �  @          �  @	          �  H          �  �           �  �      opterr  `           �  (            8          -  �          [  '          h  p          �  �           �  ('          �  <           �            �  P           �  P            �&          +  �          <  �      abort   �&          ]             �  �           �  P       __dll__     ��       �      ��       �  8&          �            �               h'          /  p           L  �          W  �&          e  	          t  (          �  �          �  d           �  @          �     ��       �         strrchr �&          �  (
      calloc  �&          0  @          :  &          R             m  �          y  �           �  �          �  �      Sleep   8'          �  �	      _commode�           �  P          �  p             �          
  0.            �          /  P           [  �           o  (       optind  \           �  �      __xi_z  (           �  �          �             �             �  �          �  8             �%            �          -  0          H  �       signal  �&          S  �%          c             �  h           �              �  �      strncmp �%          �  @          �  0.          �  @            �            �           C  �          d      ��       w  8          �            �  �          �  �
          �  �          �              �            8          @  �          M  `	          l  �           �  �          �     ��       �  H          �  '          �  `'          �  x&      dict_usb0           
  `&            P'          7  x          B  `          _  �           �              �  �           �     ��       �              �  �           �  0            �                       <  �          J  X          m        __xl_z  H       __end__              z  �          �  X          �  �          �  @          �  H.      __xi_a             �  �&          �  `          	  H'      __xc_a                �          5     ��       N  P           `     ��       n  �      _fmode  �           �  h          �  �          �             �  �&          �             �  0	          �  �             X&          !             2  �          E  8          W  �          �  0&          �  �          �  p          �        fputc   &      __xl_c  8           �     	    optopt  X           �  `          �  x          
  X            �           6              B  �
          q             �  �%          �             �  �          �  �      _newmodep           �  �&      fwrite  &          �              �          !  P	          0  �          F      ��       ^      ��       o  �          |  �          �  `           �  �%          �  �      exit    �&          �  �          �     ��             ��         `          /            K  �           Y  0      _exit   p&          r  �             �          �  P&      strlen  �%          �   	          �  P          �  h&          �  �            �          *  �          M  �      strchr  �&          Y  p'          o  �&          �  �          �             �  �           �  �             �           )   x          ;   �          U             w   �          �    	          �   �           �   P           �   �%          �              �   @&          �          free     '          !  �       !  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame print_usage.isra.0 insertdevice_dict .rdata$.refptr.optind .text.startup .xdata.startup .pdata.startup __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names permute_args parse_long_options illoptstring recargstring getopt_internal posixly_correct.0 nonopt_end nonopt_start illoptchar recargchar getopt_long getopt_long_only local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight plist_new_dict __imp_libusb_get_string_descriptor_ascii __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp_GetEnvironmentVariableW __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname __imp_idevice_new_with_options _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter libusb_get_device_descriptor .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode __imp___p___argc __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __rt_psrelocs_start __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __imp_libusb_exit __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ __imp_fputc .refptr.optind VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll _head__libs_libusb_1_0_dll_a .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ plist_new_string __mingw_oldexcpt_handler TlsGetValue __bss_start__ libusb_close __imp___C_specific_handler ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection __C_specific_handler __mingw_optreset .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf ___crt_xp_end__ __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_puts _set_new_mode .refptr.__xi_a __imp_plist_new_dict .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname _tls_used __stdio_common_vsprintf __imp_lockdownd_client_new __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ .refptr._newmode __data_end__ __imp_libusb_init __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ idevice_set_debug_level __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force device_serial __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func _head_libimobiledevice_glue_1_0_dll __native_startup_state ___crt_xc_start__ lockdownd_client_free __imp_libusb_close ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a .refptr.__mingw_initltsdyn_force __rt_psrelocs_size plist_dict_set_item .refptr.__ImageBase libusb_free_device_list __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname lockdownd_client_new __imp___p___wargv __imp_strlen libimobiledevice_glue_1_0_dll_iname __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __p__wenviron GetEnvironmentVariableW _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit __imp_libusb_get_device_list _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_plist_new_string _head_libplist_2_0_dll __imp_lockdownd_get_device_name __IAT_start__ __imp_libusb_get_device_descriptor __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter libusb_get_device_list __imp__onexit __DTOR_LIST__ __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp___stdio_common_vsprintf __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc ___libs_libusb_1_0_dll_a_iname __imp_VirtualProtect idevice_free ___tls_end__ .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __imp_plist_dict_set_item libusb_exit __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm idevice_get_device_list_extended __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_strchr ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __imp_idevice_device_list_extended_free __minor_subsystem_version__ __minor_image_version__ __imp___set_app_type __imp_plist_print_to_stream __imp_sprintf __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z __imp_libusb_free_device_list _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR __imp_idevice_get_device_list_extended libusb_get_string_descriptor_ascii libusb_init DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv __imp_libusb_open lockdownd_get_device_name idevice_device_list_extended_free libusb_open .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_daylight __p___wargv plist_print_to_stream __mingw_app_type 