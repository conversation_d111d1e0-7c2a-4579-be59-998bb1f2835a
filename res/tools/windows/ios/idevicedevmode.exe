MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� �=�g � �  � & ( "   V     �        @                       �    ��  `                                             �      �  �   `  �           �  �                            T  (                   ��  �                          .text   �       "                 `  `.data      @      (              @  �.rdata  �   P      *              @  @.pdata  �   `      :              @  @.xdata     p      >              @  @.bss    �   �                      �  �.idata      �      B              @  �.CRT    `    �      P              @  �.tls        �      R              @  �.rsrc   �   �      T              @  �.reloc  �    �      Z              @  B/4      p   �      \              @  B/19     ��   �   �   b              @  B/31     s#   �  $   .             @  B/45     �"   �  $   R             @  B/57     �
         v             @  B/70        0     �             @  B/81     D   @     �             @  B/97     %   `     �             @  B/113    �   �     �             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H��G  1��    H��G  �    H��G  �    H�G  f�8MZuHcP<HЁ8PE  tfH�_G  �
�o  � ��tC�   �  �$  H�H  ���  H��G  ����  H��F  �8tP1�H��(Ð�   �v  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�G  ��  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H��G  L��n  H��n  H�
�n  � ��n  H�1G  D�H��n  H�D$ ��  �H��8��    ATUWVSH�� H��F  H�-��  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5\F  1�����V  �����  �n     ����L  ���e  H��E  H� H��tE1��   1����8
  H�
�F  ��  H��E  H�
����H���  �   ��m  �{Hc�H��H���0  L�%�m  H�Ņ��F  H��1�I���  H�pH���  I��H�D I�H��H���  H9�u�H�H�    H�-]m  �  H��D  L�Bm  �
Lm  H� L� H�7m  �"  �
m  �m  ����   � m  ��ttH�� [^_]A\�f�     H�5	E  �   ���������   �  ��������H��D  H�
�D  ��  �   �������1�H�����f�     �c  ��l  H�� [^_]A\�f.�     H��D  H�
�D  �   �o  �7���f�H����������y  �H��(H��C  �    ������H��(� H��(H��C  �     �z�����H��(� H��(�  H���H��(Ð�����������H�
	   �����@ Ð��������������H��(H�
�;  �`  H�
	<  �T  H�
%<  �H  H�
Q<  �<  H�
�<  �0  H�
�<  �$  H�
�<  �  H�
�<  H��(�  �     H��8L��<  1�H�D$     L�L$ �h  f����uH�L$ H��tH�T$(�  H�L$(�  H��8�D  SH��0L��<  ��L�L$(�D$'H��<  H�D$(    �  ��u'H�L$(H��t�@  ��t$��tH�
�<  �\  @ �D$'H��0[�D  H�L$(H�T$'�  �D$'<t0��u��t�H�
Y<  �  �D$'��D  ��t��f.�     ��t�H�
*<  ��  �D$'�f�     VSH��8�\$pH��L�΃���   �z  Hc�H�D$(�e  H�L$(H�<  I���  H�T$(H����   H�L$(���h  H�D$(    ����   H�T$(H����   ��uVH�L$(H��;  �E  H��H����   1��
  �   H����~  I��H��;  H���R  �H��8[^�f.�     ��H�
�;  ��  �H��8[^�f.�     1��9���������H�
;  H��8[^��  fD  ��H�
';  �  �H��8[^�f�H�
A;  �  롐��%�|  ���%�|  ���%�|  ���%�|  ���%z|  ���%j|  ���%Z|  ���%J|  ���%:|  ���%*|  ���%�~  ���%�~  ���%�~  ���%r~  ���%b~  ���%R~  ���%B~  ���%2~  ���%"~  ���     H��(H�5(  H� H��t"D  ��H�(  H�PH�@H�(  H��u�H��(�fD  VSH��(H�C?  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^����� 1�fD  D�@��J�<� L��u��fD  ��g  ��t�D  ��g     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H�S>  �8t�    ��t��tN�   H��([^�f�H�Q�  H�5J�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H��;  Hc�H����    H��:  �DA �y�qH�q�   ��  �DD$0I��H�Z;  �|$(H��I���t$ ��  �t$@|$P1�DD$`H��x[^ÐH�):  ��    H�y:  ��    H�I:  �s���@ H��:  �c���@ H�q:  �S���H��:  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(��  A�   �   H�
�:  I����  H�t$(�   �  H��H��I���
  �X  ��    WVSH��PHc5�e  H�˅��  H��e  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H�5e  H��H��H�H�x �     �#  �WA�0   H�H�e  H�T$ H�L��x  H���}   �D$D�P����t�P���u��d  H��P[^_� ��H�L$ H�T$8A�@   �   DD�H�d  H�KI��H�S�,x  ��u���w  H�
�9  ���d���@ 1��!���H�jd  �WH�
�9  L�D�>���H��H�
T9  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%d  E��tH�e[^_A\A]A^A_]�fD  ��c     �9	  H�H��H��   H����  L�-�:  H��:  ��c      H)�H�D$0H��c  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5.:  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
�8  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  �b  ������H�5�u  1�H�}�D  H�b  H�D� E��t
H�PH�HI����A��H��(D;%�a  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5�7  �s�;H��L�>H���Z����>L9�r��������H�
�6  �����H�
i6  ���������H��XH��`  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
�`  �d  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H��6  Hc�H��� 1ҹ   ��  H���>  H���  H�:`  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �$  H���@����   �   �  �f�     1ҹ   ��
  H��t*H�������   ���i���f�     �   ���T����   �   �
  �@����   �   �
  �,����   �   �
  �����������ATUWVSH�� L�%_  L���r  H��^  H��t6H�-+r  H�=�q  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%�q  WVSH�� ��^  ��H�օ�u
1�H�� [^_ú   �   �
  H��H��t3H�pH�5~^  �8H���kq  H�L^  H��H�B^  H�C�hq  묃��멐VSH��(�,^  �˅�u1�H��([^�D  H�5)^  H���q  H�
�]  H��t'1��H��H��tH���9�H�Au�H��tH�B�}	  H����p  1�H��([^� H��]  ��ff.�     @ SH�� ����   w0��tL��]  ����   �|]     �   H�� [�f�     ��u�]]  ��t��<�����f.�     �B]  ��uf�8]  ��u�H�$]  H��t�    H��H�[�  H��u�H�
 ]  H��\      ��\      ��o  �l����k����   H�� [������f�     H�
�\  ��o  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���  H��w{H��2  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���F  ��u�H��H�� [^_��    1�H��H�� [^_� H��1  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H�y1  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L�91  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H��0  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H�y0  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L��/  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������H��8E1�L�D$ I��H��1��  H��8Ð�VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H���  ���   ����  �[  � ��Z  H� H��  H� H�M��t	A�$��  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���P  ���   ����  ��  � ���  H� H��  H� H�M��t	A�$�s  1�H�� [^_]A\�fD  SH�� H����  ���    HD�H�� [�f�H��-  �8 t1�Ð�  ff.�     SH�� �˹   �  A��H��,  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H��,  ��~   H��  �j   H��  �V   H��  H��(�f.�     H��(H��,  ��>   H��  �*   H��  �   H�  H��(Ð����������%Zj  ���%Zj  ���%Zj  ���     �%j  ���%j  ���%j  ���     �%�i  ���%�i  ���%�i  ���%�i  ���%�i  ���%�i  ���%�i  ���     �%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���%�h  ���     �%Bh  ���%Bh  ���%"h  ���     �%�g  ���%�g  ���%�g  ���%�g  ���%�g  ���%�g  ���%�g  ���     �%rg  ���%bg  ���%Rg  ���%Bg  ���%2g  ���%"g  ���%g  ���%g  ���%�f  ���%�f  ��AWAVAUATUWVSH��XA��H���E���H�D$0    H�D$8    H�D$@    A����   �   E1�E1�L�=#%  fD  H�\� �;��-u�{uu
�{ ��    L��H�����������   ��-t ���������H��X[^_]A\A]A^A_�D  �{stZ��-uՀ{eto��-uʀ{rtt��-u��{a�i  ��-u��{nu��{ u�H��A�   L9��Y���H�
v$  ����� �{ u�A�   H��L9��/�����D  �{ u�A�   ��f��{ u�A�   �΃�Hc�H�\� H��t�A��H�L$0H������D�@���������   H�L$0H�T$8L�+$  �����Å��"  H�L$8����f(���$  f/���   H�L$8L�D$@H�c$  �Z����Å��  1�H�L$0L�D$HH�T$HH�T$@�%������  A���  ��   A���  A��ui1��D$ L�L$HL�D$@H�T$8H�L$0�����H�{ �����A�   ������   ��e  I��H�*#  H���j�������fH~�H�
�#  ����H�L$HH��t����H�L$8H��t����H�L$0H�����������������   �se  A��H��"  H�������A��u��D$    �5����   �Ae  A��H�G#  H��������v�����H�
i#  �l����c����D$    �����H�L$8�   �����B���������������������������������`0 @           ��������                                                                                                                                                                                                                                                                                                                                                                                �0 @           ��������        ����                           ����            �( @           �( @           �( @           p� @   x� @   �* @   �* @   0) @   `* @   �) @   @) @   �@ @   �@ @   �@ @      �p  �@ @   �@ @   PDT PST @* @    * @                                                                                                                                                                                                                                                           
 Tool To Enable DeveloperMode on iOS 16+ Devices ((make sure no passcode is set)) Work Only Home Screen           -s Get DeveloperMode Current State      -r Reveal DeveloperMode Toggle in Device Settings       -e Enable DeveloperMode on Device (Auto Device Restart!)        -a Accept DeveloperMode Prompt on Device Screen To Turn On DevMode (First Time Only)            -n Connect to network device PhoneCheck 2.1 Developed By AaterAli  ProductVersion DeveloperModeStatus com.apple.security.mac.amfi ON OFF NA devmode is already enable! action        could not send plist, error %d Error Error: %s
 SUCCESS could not receive response, error %d --udid 
UDID Not Specified.        ERROR: No device found with udid %s idevicedevmode      ERROR: Could not connect to lockdownd, error code %d    Develop Mode Not Compatible On %.1f Devices com.apple.amfi.lockdown     ERROR: Could not start amfi service, error code %d      could not start client, error %d              0@                        � @                            � @   � @   l� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ��������D�������������������Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      �������������������� �������@��� �������        runtime error %d
               0@ @           @@ @           p0 @              @           �^ @           �^ @           �S @           �@ @           ȕ @           �� @           h� @           d� @           `� @            � @           �� @           @� @           H� @            � @           � @           � @           (� @           p� @            @ @           �� @             @           p @           P� @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                                                                                                                                            p    .  p  0  y  p  �  �  p  �  �  $p  �  
  Dp    $  dp  0  <  lp  @  A  pp  P  �  tp  �    |p    �  �p  �    �p  �  �  �p     j  �p  p  �  �p  �  �  �p  �  �  �p  �  Q  �p  `  c  �p  p  h  �p  p  s  �p  �  �   q  �  R  q  `  �  q  �  �  0q         8q     �!  <q  �!  @"  Dq  @"  �"  Tq  �"  1#  `q  @#  2$  lq  @$  l$  tq  p$  �$  xq  �$  ]%  |q  `%  �%  �q  �%  &  �q   &  �&  �q  �&  �&  �q  �&  i'  �q  p'  6(  �q  �(  �(  �q  �(  �(  �q  �(  ")  �q  0)  3)  �q  @)  �)  �q  �)  *  �q   *  >*  �q  @*  U*  �q  `*  �*  �q  �*  �*  �q  �*  +  �q  +  F+  r  -  Z0  �p  `0  e0  r                                                                                                                                                                                                                                                                                                                                                                                  B   b  
 
20`pP�	 B  `,     �  �     �  	 B  `,     �          B         B   b   R0 b0`  	 �0`
p	P����   B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   b   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     �          ��  ��  `�          ܛ  �  ��          �  H�  ȑ          �  X�  ��          T�  p�  �          x�  ��  �          ��  ��  0�          �  ��  ��          H�  P�   �          t�  ��   �          ��  ��  H�          �  ؕ                          (�      8�      T�      l�      ��      ��      Ȗ      �      �      8�              `�      x�      ��      ��      ��      ԗ      �      ��      �      �              *�              2�      B�              R�      b�      l�      t�              ~�              ��      ��              ��            И      ޘ      �      �      �      0�      >�      F�      h�      ��      ��      ��      ƙ      Ι      ֙              ��      �      �      �      *�      F�      P�              X�      b�      l�              v�      ��      ��      ��              ��      ��      ؚ      �       �      �      0�      H�      \�              (�      8�      T�      l�      ��      ��      Ȗ      �      �      8�              `�      x�      ��      ��      ��      ԗ      �      ��      �      �              *�              2�      B�              R�      b�      l�      t�              ~�              ��      ��              ��            И      ޘ      �      �      �      0�      >�      F�      h�      ��      ��      ��      ƙ      Ι      ֙              ��      �      �      �      *�      F�      P�              X�      b�      l�              v�      ��      ��      ��              ��      ��      ؚ      �       �      �      0�      H�      \�              f idevice_free  l idevice_new_with_options  � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_get_value   � lockdownd_start_service   property_list_service_client_free property_list_service_client_new  property_list_service_receive_plist   property_list_service_send_xml_plist  DeleteCriticalSection =EnterCriticalSection  tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery  V atof   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf  � fwrite  � puts  � strcmp  � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset   plist_dict_get_item    plist_dict_set_item    plist_free    # plist_get_bool_val    ( plist_get_node_type   + plist_get_string_ptr  , plist_get_string_val  8 plist_new_dict    = plist_new_uint     �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  �  �  �  �  �  �  �  �  KERNEL32.dll    (�  api-ms-win-crt-convert-l1-1-0.dll   <�  <�  api-ms-win-crt-environment-l1-1-0.dll   P�  P�  P�  P�  api-ms-win-crt-heap-l1-1-0.dll  d�  api-ms-win-crt-math-l1-1-0.dll  x�  x�  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  ��  api-ms-win-crt-string-l1-1-0.dll    Ȑ  Ȑ  Ȑ  Ȑ  api-ms-win-crt-time-l1-1-0.dll  ܐ  ܐ  ܐ  ܐ  ܐ  ܐ  ܐ  ܐ  ܐ  libplist-2.0.dll            0 @                    @                   � @   � @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          0     x�   @  0    �P�`�p�������������������ȠР���� �   P  H   � ����0�@�P�`�p�����������Ч�� �� �0�@�P�`�p�����������Ш �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                            ,               @   $                      <    �&       P @   �      - @   J                      ,    x@       � @   �                           �F                           �L                       ,    hM       � @                              �N                       ,    sO       � @   �                           :W                           �W                       ,    ?Y       p @   �                       ,    u\       p @                              ]                       ,    �]       � @   =                      ,    �t       � @   L                           �w                       ,    fx         @   �                      ,    G�       �! @   b                          �                           ��                       ,    s�       @$ @   �                          E�                       ,    �       �( @                          ,    ��       �( @   H                       ,    3�       �( @   2                           մ                       ,    w�       0) @                                                                                                                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   �   �  GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden   �  �           9  char {   long long unsigned int long long int short unsigned int int long int unsigned int _iobuf !
  _Placeholder #     FILE /�   unsigned char double float long double long unsigned int {   signed char uint8_t $"  short int uint16_t &�   uint64_t *0�   plist_t Y  �   i{  PLIST_BOOLEAN  PLIST_UINT PLIST_REAL PLIST_STRING PLIST_ARRAY PLIST_DICT PLIST_DATE PLIST_DATA PLIST_KEY PLIST_UID 	PLIST_NULL 
PLIST_NONE  plist_type v�  �   'V  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 0�    2 z    idevice_t 3�  n  idevice_options �   9  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  �     �   $�  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ lockdownd_error_t P
  D  R)	  D  lockdownd_client_t S#8	  	  lockdownd_service_descriptor `�	  port a�   ssl_enabled b
~  identifier cj   lockdownd_service_descriptor_t e.�	  =	  �   #  PROPERTY_LIST_SERVICE_E_SUCCESS  PROPERTY_LIST_SERVICE_E_INVALID_ARG PROPERTY_LIST_SERVICE_E_PLIST_ERROR ~PROPERTY_LIST_SERVICE_E_MUX_ERROR }PROPERTY_LIST_SERVICE_E_SSL_ERROR |PROPERTY_LIST_SERVICE_E_RECEIVE_TIMEOUT {PROPERTY_LIST_SERVICE_E_NOT_ENOUGH_DATA zPROPERTY_LIST_SERVICE_E_UNKNOWN_ERROR �~ property_list_service_error_t ,�	  property_list_service_private .5T  !property_list_service_client_private property_list_service_client_t /(�  .  cmd_mode �    �  CMD_NONE  CMD_INFO CMD_REVEAL CMD_ENABLE CMD_PROMPT  
idevice_free �V       
lockdownd_client_free ��  ?  	   
property_list_service_client_free J  s  z   
property_list_service_client_new ?  �    �	  �   z  
lockdownd_start_service ��  �  	    �   �	  
lockdownd_client_new_with_handshake ��  .
    .
     	  
idevice_new_with_options �V  h
  h
    �     
strcmp ?�   �
       fprintf ��   �
  �
       �
  
__acrt_iob_func ]�
  �
  �    plist_get_string_ptr �    �     �  plist_dict_get_item �
�  6  �     
property_list_service_receive_plist �  q  z  q   �  printf ��   �     "plist_free �
�  �   
property_list_service_send_xml_plist W  �  z  �   plist_dict_set_item �  �    �   
plist_new_uint �
�  2  �   #plist_new_dict �
�  plist_get_bool_val �n  �  n   ~  plist_get_node_type �{  �  �   atof 	�3  �     plist_get_string_val ��  �  �   j  
lockdownd_get_value ��    	      q   main ��   - @   J      ��  argc ��   �  �  argv ��    �  	use_network �	�   )  '  $result �	�   	cmd �	�   9  7  
device �  ��	ret �V  Q  G  %;  �	  ��	ldret ��  �  �  
service �$�	  ��	i �	�     �  	udid �j  e  [  &cleanup +�/ @   	ver �3  �  �  '/  (z  ��(res_client 	'  �  �  +- @   �  �- @   m
  �  Rs Q  �- @   �  . @   v  �  R	�R @    �. @   3
     R��Qs X	�0}  $0)( 	�# �. @   �  &  Q��X	�R @    �. @   �  �. @   �  Y  Q	DS @   X�� / @   s  r  X�� R/ @   �  t/ @   �
  �  R2 �/ @   �
  �  Q	�R @   Xs  �/ @   v  �  R	S @    �/ @   ?  �/ @     �/ @   �  �/ @   �
    R2 �/ @   �
  =  Q	�R @   Xs  0 @   �
  T  R2 !0 @   �
  y  Q	`S @   Xs  40 @   v  �  R	�S @    U0 @   g  Q1  )DeveloperModeAction r� @   >      �g  device r$  �  �  ;  r?	    �  service rh�	  =  5  /  r�z  z  f  *dev_action r��   � 
dict �
�  �X	res �#  �  �  �   �  	dm x	�       � @   g  �  Q0 � @   �  R	�Q @     |   �  	success �~      	val ��  2  .  w @   (       ^  	err �  L  H  ~ @   �
  %  Q0 � @   �
  <  R2 � @   �
  Q	MR @   Xs   k @   
  }  Q	GR @     @   �  R	XR @     � @   2   @     �  Rs  $ &  @   �  �  Q	R @    , @   �  �  Rt  8 @   �  V @   6  &  Rt Q�X � @   v  E  R	`R @     @   v  R	(R @   Qs   GetDeveloperModeState T�    @   �       ��  client T.	  d  `  printmode T9�   �  |  
node W
�  �h
bv X
~  �gh   �  	type ^{  �  �  P @   s  d @   �  .  R	�Q @    � @   I  F  Q�g � @   �  e  R	�Q @    � @   �  R	�Q @     = @   �  R�RQ	�Q @   X	�Q @   Y�h  GetProductVersionValue C3  � @   K       ��  client C2	  �  �  
node F
�  �`	prod_ver G3  �  �  � @          q  
res Lj  �h� @   �  c  Q�h  @   �   � @   �  R�RQ0X	�Q @   Y�`  +print_usage 5
P @   h       ��  ` @   �  �  R	 P @    l @   �    R	pP @    x @   �  "  R	�P @    � @   �  A  R	�P @    � @   �  `  R	Q @    � @   �    R	pQ @    � @   �  �  R	�Q @    � @   �  R	�Q @     ,__main __main -puts __builtin_puts 
  ]   7  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 Q  9  � @   �       �  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	0� @   atexit ��   �  Y   __main 5p @          ��  � @   �   	__do_global_ctors    @   j       �  
nptrs "�     �  
i #�       M @   j  R	� @     	__do_global_dtors � @   :       �[  p [  	 @ @    	   �   v	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 9  !  #
  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
]  �   �,  __uninitialized  __initializing __initialized  ]  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	@ @   �  	@ @   =  
"	H� @   [  	@� @    �    ,
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 G  /  y
  _dowildcard  �   	 @ @   int  }   Z
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  � @          �
  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   � @          � �    �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  j  	  _newmode �   	P� @   int  �   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 !  	  � @   �       C  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	l� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	 T @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�S @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	h� @   __mingw_initltsdyn_force ��   	d� @   __mingw_initltssuo_force ��   	`� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@  � @   /       �}  
u  �  6  2  
�  *M  H  D  
�  ;d  Z  V  � @   �   __tlregdtor m�   ` @          ��  func m  R __dyn_tls_init L@  	  u  �  �  *M  �  ;d  pfunc N
$  ps O
�    �  � @   �       ��  p  h  �  �  �  �  �  �  �  �  �    @      @   +       L�  �  �  �  �  �  �  �    �  �      �  (  $   E @   �    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  Y  _commode �   	p� @   int  w   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   8
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 M	  5	  p @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   p @   �       �0  pexcept 0  O  I  type 
�  q  e  � @   b  �  R2 � @   7  Q	8U @   Xs Yt w �ww(�ww0�w  5   �    =  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 

  �	  p @          �
  _fpreset 	p @          � �    j  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  �
  __mingw_app_type �   	�� @   int  G   �  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ?  u  � @   =        __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �` @   ]      ��  4was_init �%  	�� @   5mSecs �%  �  �  !�  � @   �   �4  �  �  �  6�   
�  �  �  
�  W  '  
�  n	  V	  
�  �	  �	  

  
  
  
  P
  @
  "E  �   �  
F  �
  �
  
[  �
  �
  � @   `  R	�V @   Xu w t     � @   � @          �;  �  �
  �
  �  
    �        � @   � @          �  #  !  �  .  ,  �  =  ;  � @   �  Ru    !  R @   �   ��  �  G  E  �  R  P  �  a  _  7  R @   �   �  k  i  �  v  t  �  �  �  ^ @   �  Ru       @    @   
       �w  �  �  �  �  �  �  �  �  �     @    @   
       �  �  �  �  �  �  �  �  �   @   �  Ru        @     @          �   �  �  �  �  �  �  �  �  �      @     @          �  �  �  �      �      ( @   �  Ru    "$  �   �  
)  #    83    
4  =  ;    � @   � @   
       s�  G  E  �  R  P  �  a  _    � @   � @   
       �  k  i  �  v  t  �  �  �  � @   �  Rt      
� @   `    R	XV @    � @   `  R	 V @      9�  p @   X       �|  
�  �  �  :�  ��� @   
  Yu   � @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �� @   b      �`  &addr ��  �  �  b �:  ��h �g  �  �  i �%  
  
  >� @   P       �  new_protect �
u  3
  1
  
 @   
  �  Ys   @    
   @   `  R	�U @     
P @   �
  �  Rs  } @   n
  
� @   E
    Q��X0 
B @   `  >  R	�U @    R @   `  R	�U @   Qs   ?__report_error T� @   i       �/  &msg T  ?
  ;
  @argp ��   �X
� @     �  R2 
� @   /  �  R	�U @   Q1XK 
� @       R2 
� @   �
  !  Qs Xt  � @   �
   Afwrite __builtin_fwrite   �   p  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  � @   L       �  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr �   @          �P  f ,�  Z
  V
    @   �  R�R  __mingw_raise_matherr �� @   >       �typ !�   n
  h
  name 2�  �
  �
  a1 ?w   �
  �
  a2 Jw   �
  �
  rslt 
w   � ex 0  �@� @   R�@   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 p
  X
  K  _fmode �   	�� @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �
    @   �      �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	�� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��     @   �      ��  'exception_data �-�  �
  �
  old_handler �
	      action ��   l  R  reset_fpu ��   �  �  
l  @   �
  �  R8Q0 (�  @   �  R�R 
�  @   �
  �  R4Q0 �  @   �  R4 
,! @   �
    R8Q0 
E! @   �
  7  R8Q1 
\! @   �
  S  R;Q0 r! @   f  R; �! @   y  R8 
�! @   �
  �  R;Q1 
�! @   �
  �  R4Q1 
�! @   �
  �  R8Q1 )�! @   �
   �	   �
   .  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99      �! @   b        char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	�� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  @# @   �       �n  	hDllHandle z�  ?  '  	reason {<  �  �  	reserved |S  =  %   �# @   K       �  
keyp �&�  �  �  
t �-�  �  �  �# @   �  
�# @   C  R	 � @     !n  �# @   �# @          �  �  �# @   )
   "n  �# @     �E  #  �  $ @   )
    $ @   6  
-$ @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   �" @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�      �" @   �  B	  Rt  # @   �  
# @   �  Rt   ___w64_mingwthr_add_key_dtor *�   @" @   o       �$
  	key *%<  >  4  	dtor *1.  r  d  
new_key ,$
  �  �  o" @   �  �	  R1QH �" @   �  
  Rt  
�" @   �  Rt   �  &n  �! @   p       ��  �  �  '�  " @          �
  �  �  �  " @     " @     (#" @   Rt   �! @   �  �
  R|  )@" @   �  R	 � @      �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 E  -  �  _CRT_MT �   	0@ @   int  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	A� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	@� @    �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @$ @   �      �  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  �  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  �  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  �  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   �  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  p' @   �       �7
  i �(�   �  �  �  �	`  �  �	  	    importDesc �@  '  %  �  �^
  importsStartRVA �	I  7  /  �  p' @   	�  ��  �  �  �  �  �  	�  �' @    �  �  �  �  �  v  r  �  �  �      M  �' @   �' @   J       �q  �  �  f  }  �  �  �  �  �  �  �  �    
_IsNonwritableInCurrentImage �  �& @   �       ��  pTarget �%`  �  �  �  �	`  rvaTarget �
�      �  �^
      �  �& @   �  �/  �  �  �  �  �  	�  �& @    �  �  �  �  �  '  #  �  8  6      M  ' @   ' @   I       �q  D  B  f  }  N  L  �  X  V  �  b  `    
_GetPEImageBase �`  �& @   6       �0  �  �	`  	�  �& @   �  �	�  �  �  �  �  	�  �& @    �  �  �  �  �  o  k  �  �  ~       
_FindPESectionExec y^
   & @   s       �%  eNo y�   �  �  �  {	`  �  |	  �  �  �  }^
  �  �  �  ~�   �  �  	�   & @   �  �	�  �  �  �  �  	�  1& @    �  �  �  �  �  �  �  �  �  �       
__mingw_GetSectionCount g�   �% @   7       ��  �  i	`  �  j	  �  �  	�  �% @   g  m	�  g  �  �  �  	�  �% @    w  �  w  �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
  `% @   �       �  p Y&s      �  [	`  rva \
�  1  /  �  `% @   A  _�  �  A  �  �  �  	�  p% @    Q  �  Q  �  �  =  9  �  N  L      	M  �% @   \  c
q  Z  X  f  \  }  f  b  �  �  �  �  �  �     
_FindPESectionByName :^
  �$ @   �       �M  pName :#�  �  �  �  <	`  �  =	  �  �  �  >^
  �  �  �  ?�   �  �  �  �$ @   6  F  �  6  �  �  �  �  �$ @    �$ @          �  �  �  �  �  �  �  �     &�$ @   �  -  Rt  ':% @   z  Rs Qt X8  _FindPESection $^
  �  �  $`  (rva $-�  �  &	  �  '^
  �  (�    _ValidateImageBase   �  �  `  pDOSHeader �  �  	  pOptHeader v   )�  @$ @   ,       �~  �       �      �  �  	�  I$ @    /  �  *  $  /  �  �  D  @  �  Q  O     *M  p$ @   P       �f  ]  Y  +q  Q}  p  l  �  �  �  �  �  �    �    w  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  h  I  _MINGW_INSTALL_DEBUG_MATHERR �   	@@ @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 0    �( @          �  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	P@ @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  �( @          �_File *�  �  �  _Format J�  	    _ArgList Z�   "    �( @   �  R0Q�RX�QY0w �X   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99      �( @   H       �  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	`@ @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �( @   H       �_Format .�  H  B  
ap 
�   �Xret 	  ]  [  �( @   �  �  R1 �( @   �  R0Xs Y0w t    �   I  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �( @   2       �  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	p@ @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  �( @   2       �_File )�  u  o  _Format I�  �  �  
ap 
�   �hret 	  �  �  ) @   �  R0Q�RX�QY0w �   �   �  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �     char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	x� @   local__winitenv 
`  	p� @   '  
	�@ @     
	�@ @    �   S   "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  0) @         o   __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	 A @   �      �   __imp_at_quick_exit g)  	�@ @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	�@ @   
initial_tzname1 }
V  	�@ @   
initial_tznames ~�  	�@ @   
initial_timezone 
%  	�@ @   
initial_daylight �  	�@ @   __imp_tzname ��  	�@ @   __imp_timezone ��  	�@ @   __imp_daylight ��  	�@ @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�@ @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�@ @   �	  __imp__amsg_exit �V  	�@ @   F  __imp__get_output_format �\
  	�@ @   -
  __imp_tzset ��  	�@ @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�@ @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �* @   5       ��
  file �!�
  �  �  fmt �6�  �  �  
ap ��   �h+ret �  �  �  �* @   �
  R4Q�RX�QY0w �  ,tzset "�* @   6       �  -  �* @   �* @   -       ��* @   +  �* @     �* @       ._tzset �/_get_output_format nF  0) @          �0_amsg_exit i`* @   .       ��  ret i    �  q* @   z  �  R2 �* @   Q  �  Q	W @   Xs  �* @   <  R�  at_quick_exit �  @* @          �   func ]*�      1U* @   �   _onexit ��   * @          �p  func V%�  ,  &  -* @   �  Rs   __wgetmainargs J  �) @   j       �[  _Argc J"�  K  E  _Argv J5I  j  d  _Env JGI  �  �     JQ  �  �  !
  Jl�	  � �) @   0  �) @   	  &  R	v  $0.# �) @   �  �) @   �  �) @   �  
* @   U   __getmainargs >  @) @   j       �E  _Argc >!�  �  �  _Argv >1S  �  �  _Env >@S  �  �     >J      !
  >e�	  � `) @   �  p) @   �    R	v  $0.# u) @   �  ~) @   �  �) @   u  �) @   U   2  + @   6       �"+ @   +  .+ @     :+ @           
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  ( 
   I  H}  (    !I  $ >   :;9I  	4 :!;9I�B  
.?:;9'I<  H }   :!;9I�B  
4 :!;9I  H}  .?:;9'I<  
 :;9I8  >!I:;9  .?:!;9!
'<  .?:!;9'I@z  U  :;9   :;9I   <  >!!I:;9!  7 I      :!;9I�B  H}�    %U  & I      ! <  ".?:;9'<  #. ?:;9'I<  $4 :;9I
  %4 :;9I  &
 :;9  '4 :;9I  (4 :;9I�B  ).?:;9'@z  * :;9I  +.:;9@z  ,. ?<n  -. ?<n:;   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �    c   �
        W  p  �  �   �   �  �  �  �  �      %  .    	P @   5K�������uI�L
��� K	L
X
Z���Zo3
VZ
q� X	L
X
Z
Z	J! J �XXrf�XK* J, : J
�w�$ �& 4 J��it?
�Y
 ;Y X� <Y*�*W/Y
���
L=�uW= f� <fX 	��fV�.Y	�'t	Yf��X s<�  	- @   �����$�	 	s�:, <
� ., �( ��	'XY� �f��k��� � cf!J  af .�	 Zh  uf  J �h �hp�$=<Y'_  J � X��(X .	��	K�	�,X .	�(5/(�	Y5	��
�.J� �.�	� �� <Y
X0X
YZ	X	YZ	X	��� �� <Y.f	\� �� <Y_�Xv�� #    K   �
      �  �  �  �  �  �  �         	� @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      k  �  �  �  �  �        $  6     .   �
      y  �  �  �  R     .   �
        /  V  `   	� @    6     .   �
      �  �  �  �      K   �
      Q  i  �  �  �  �  �  �  �  �   	� @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      A  Y  �  �  6     .   �
      �  �  	  *	  �     <   �
      {	  �	  �	  �	  �	  �	  �	   	p @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      <
  T
  {
  �
   	p @   		3 6     .   �
      �
  �
    /  |    s   �
      �  �  �  �  �        $  .  6  C  L  T  `  q  z  �    	� @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	` @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      �  �  
  5
  C
  Q
   	� @   
L�Z
�KTYZ
g=yuX 6     .   �
      �
  �
  �
  �
  �    U   �
      D  \  �  	�  �  �  �  �  �  �  �  �   	  @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      Q  i  �  �  �  �  �  �  �  �  �  �       	�! @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      v  �  �  �  6     .   �
        4  [  o  G    K   �
      �  �  
  %  .  7  A  M  W  _   	@$ @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< 6     .   �
      �  �  �    n     A   �
      i  �  �  �  �  �  �  �   	�( @   K
�<.Y �     A   �
      O  g  �  �  �  �  �  �   	�( @   g?	YT�Y	 X� <uX �     A   �
      2  J  s  �  �  �  �  �   	�( @   KU	\fp	\;Y	Y W     O   �
        +  S  n  �  �  �  �  �  �  �  |    h   �
      5  M  t  �  �  �  �  �  �  �    	      *  <   	0) @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	`* @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                     ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �         P  P @   h       D0_    P  � @   K       D@F ,   P   @   �       A�D@\
A�F     \   P  � @   >      A�A�DP�
A�A�KS
A�A�K[
A�A�KS
A�A�C  l   P  - @   J      B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�F        ���� x �         �  � @   :       D0u  4   �    @   j       A�A�D@@
A�A�H       �  p @             ���� x �         0  � @             ���� x �      $   `  � @   /       D0R
JN    L   `  � @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       `  ` @             ���� x �      <     p @   �       A�A�D�P�
���
���A�A�B    ���� x �         `  p @             ���� x �      $   �  � @   i       A�A�DP   <   �  � @   b      A�A�A �Dp�
 A�A�A�D   \   �  ` @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         p  � @   >       D`y     p     @             ���� x �      4   �    @   �      A�D0}
A�Mf
A�I     ���� x �      L     �! @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <     @" @   o       A�A�A �D@U
 A�A�A�A    D     �" @   �       A�A�D@R
A�A�FR
A�A�D      4     @# @   �       A�D0p
A�J�
A�A      ���� x �         8  @$ @   ,          8  p$ @   P       L   8  �$ @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       8  `% @   �          8  �% @   7          8   & @   s          8  �& @   6          8  �& @   �          8  p' @   �          ���� x �         `  �( @          D@Y     ���� x �      ,   �  �( @   H       A�A�D`A�A�   ���� x �         �  �( @   2       DPm     ���� x �         	  0) @          L   	  @) @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   	  �) @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   	   * @          A�D0WA�    	  @* @             	  `* @   .       A�D0   	  �* @   5       DPp     	  �* @   6       D0q     	  + @   6       D0q                                                                                                                                                                                                                                                                                                                                                          Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion idevice_private plistclient lockdown lockdownd_client_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection _DoWildCard _StartInfo                                                                                                                                                                                                                                         C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools idevicedevmode.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/plist ../include/libimobiledevice idevicedevmode.c idevicedevmode.c stdio.h stdint.h plist.h libimobiledevice.h lockdown.h property_list_service.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                                                                                                                                                                                                                                                                                                                                             �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� %              - @    R�^���R�         - @    Q�V���Q���V  +- @   70�  +- @   70�         4- @    q	����	����P��	����P                       =- @    h ��~� ����P��S��P��S�� ����S��P��S��P��S             F- @    1�ZTu�T��t���T��t���T��t���T         F- @    _0�u�0���S��0���S       �. @    a'b��b         / @    *P��P��P��P     � @    
R
��R�           � @    Q��Q���Q��R���Q�         � @    X��X���X���X�                     � @    Y�T���Y���T���Y���T���Y���T���Y���T           3 @    P#S#7Ps�P��S   � @   P    Z @    L0���0�     n @    P��P     � @    PS      @    ,R,��R�          @    QbSbc�Q�c�S     P @    P#6P     � @    RK�R�  � @   0
�         1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
�T @   ���
�T @   ���
�T @   ���
�T @   ���
&U @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ����l     ��U  ��2�  ����l     ��U  ��1�  ����l     ��U  ��1�  ����l     ��U  �	�	4�  �	�	��l     �	�	U  �	�	4�  �	�	��l     �	�	U  �	�	8�  �	�	��l     �	�	U  �	�	8�  �	�	��l     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
��o     �
�
T  �
�
4�  �
�
��o     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � T                RQ�R�        QX�Q�        X�`�X� )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            X         Z���� ���� ���� ���� ���� ���� ���� U         K @    -Ye� Z @    E�� � @    ( P @   �- @   � S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����                   .file   a   ��  gcrtexe.c              �                                �              �   �                        �   �                        �   �                        %  `                        @  �                        `             k  �                        �  �                        �  @                        �  �                        �  0          �  �                    envp           argv            argc    (                          �                        '  �          9                           ^  0                        �             �  �                        �  �                        �                            �                    mainret            "  P                        8  @                        N  p                        d  `                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  	     +                 .file   �   ��  gidevicedevmode        m  P                           y  �          �            �  �      main          .text   P     �  4             .data                            .bss    0                        .rdata         �                .xdata  t      $                 .pdata  l      0                    �       J                    �  �                           �  �                          �  �&  
   �  �                 �  �     �                    �  �     )                   �  0      @                    �  \      Y                      9     �                          >                       �     s                    )  @	     +                     4  P     X  
             .text          .idata$7�      .idata$5�      .idata$4P      .idata$68      .text   (      .idata$7�      .idata$5�      .idata$4H      .idata$6      .text   0      .idata$7�      .idata$5�      .idata$4@      .idata$6�      .text   8      .idata$7�      .idata$5�      .idata$48      .idata$6�      .text   @      .idata$7�      .idata$5�      .idata$40      .idata$6�      .text   H      .idata$7�      .idata$5�      .idata$4(      .idata$6�      .text   P      .idata$7|      .idata$5�      .idata$4       .idata$6l      .text   X      .idata$7x      .idata$5�      .idata$4      .idata$6T      .text   `      .idata$7t      .idata$5�      .idata$4      .idata$68      .text   h      .idata$7p      .idata$5�      .idata$4      .idata$6(      .text   p      .idata$7�
      .idata$5      .idata$4�      .idata$6\      .text   x      .idata$7�
      .idata$5      .idata$4�      .idata$6H      .text   �      .idata$7�
      .idata$5      .idata$4x      .idata$60      .text   �      .idata$7�
      .idata$5       .idata$4p      .idata$6      .text   �      .idata$7�
      .idata$5�      .idata$4h      .idata$6       .text   �      .idata$7�
      .idata$5�      .idata$4`      .idata$6�
      .text   �      .idata$7�
      .idata$5�      .idata$4X      .idata$6�
      .text   �      .idata$7�
      .idata$5�      .idata$4P      .idata$6�
      .text   �      .idata$7�
      .idata$5�      .idata$4H      .idata$6�
      .file   #  ��  ggccmain.c             �  �                       p.0                 �             
  P                    __main  p          '  0       .text   �     �                .data                          .bss    0                       .xdata  �                       .pdata  �      $   	                 �  x@  
   a                   �  7     ?                    �  �     5                     �  p      0                      �     '                     9     �                     )  p	     +                     4  �     �                .file   9  ��  gnatstart.c        .text   �                       .data                          .bss    @                           �  �F  
     
                 �  v	     �                     �  �                             #
     V   
                   ]                            !                         )  �	     +                 .file   M  ��  gwildcard.c        .text   �                       .data                           .bss    P                            �  �L  
   �                    �  ,
     .                     �  �                             y
     :                      /     �                     )  �	     +                 .file   i  ��  gdllargv.c         _setargv�                       .text   �                      .data   0                        .bss    P                        .xdata  �                       .pdata  �                          �  hM  
   �                   �  Z
     :                     �  �      0                      �
     V                      �     �                     )   
     +                     4  0     0                .file   }  ��  g_newmode.c        .text   �                       .data   0                        .bss    P                           �  �N  
   �                    �  �
     .                     �                              	     :                      j     �                     )  0
     +                 .file   �  ��  gtlssup.c              3  �                           B  �          Q  0                    __xd_a  P       __xd_z  X           h  `	      .text   �     �                .data   0                        .bss    `                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  sO  
   �  6                 �  �
     �                    �  &                        �  0     0                      C                          u                            	     �                     )  `
     +                     4  `     �                .file   �  ��  gxncommod.c        .text   p	                       .data   0                        .bss    p                           �  :W  
   �                    �  �     .                     �  `                            Y     :                      �     �                     )  �
     +                 .file   �  ��  gcinitexe.c        .text   p	                       .data   0                        .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �W  
   {                   �  �     a                     �  �                            �     :                      �     �                     )  �
     +                 .file     ��  gmerr.c            _matherrp	                       .text   p	     �                .data   0                        .bss    �                        .rdata  @     @               .xdata  �                       .pdata  �                          �  ?Y  
   6  
                 �  8
                         �  =     �                    �  �     0                      �     �                      5	     �                     )  �
     +                     4       X                .file   "  ��  gCRT_fp10.c        _fpresetp
                       fpreset p
      .text   p
                      .data   0                        .bss    �                        .xdata  �                       .pdata                           �  u\  
   �                    �  =     -                     �  �     0                      �
     X                      �	     �                     )        +                     4  `     0                .file   6  ��  gmingw_helpers.    .text   �
                       .data   0                        .bss    �                           �  ]  
   �                    �  j     .                     �                               �
     :                      �
     �                     )  P     +                 .file   c  ��  gpseudo-reloc.c        t  �
                           �  �
          �  �       the_secs�           �  `          �  �           �  p                        �  �                    .text   �
     =  &             .data   0                        .bss    �                       .rdata  �     [                .xdata        0                 .pdata       $   	                 �  �]  
   K  �                 �  �     �                    �  �     �  
                 �        0                    �  �      W                            �                     �     	                       ?     O                    )  �     +                     4  �     �                .file   �  ��  gusermatherr.c         (  �                           >  �           L         .text   �     L                .data   0                        .bss    �                       .xdata  0                      .pdata  8                         �  �t  
   �                   �  p                         �  J
     r                     �  P     0                      �     �                      �     �                     )  �     +                     4  p     P                .file   �  ��  gxtxtmode.c        .text                          .data   0                        .bss    �                           �  �w  
   �                    �  �     .                     �  �                            K     :                      X
     �                     )  �     +                 .file   �  ��  gcrt_handler.c         c                         .text        �               .data   0                        .bss    �                       .xdata  <                      .rdata  �     (   
             .pdata  P                         �  fx  
   �                   �  �     ~                    �  �
     _                    �  �     0                      �     �  
                   �                            �
                         )       +                     4  �     P                .file   �  ��  gtlsthrd.c             z  �                           �             �  �           �  @          �  �           �  �            @      .text   �     b  "             .data   0                        .bss    �      H                 .xdata  D     0                 .pdata  \     0                    �  G�  
   �
  A                 �  .     a                    �       �                    �  �     0                    �                                   x                          %                    )  @     +                     4       (               .file   �  ��  gtlsmcrt.c         .text   @                       .data   0                       .bss    @                           �  �  
   �                    �  �     .                     �                               �     :                      -     �                     )  p     +                 .file     ��  g                .text   @                       .data   @                        .bss    @                          �  ��  
   �                    �  �     0                     �                               �     :                      �     �                     )  �     +                 .file   ?  ��  gpesect.c              .  @                           A  p          P  �          e  `          �  �          �             �  �          �  �          �  p      .text   @     �  	             .data   @                        .bss    P                       .xdata  t     ,                 .pdata  �     l                    �  s�  
   �  �                 �  �     �                    �  �     �                    �  @     0                    �  #     �                       �     K                     �     T                       �     �                     )  �     +                     4  8     (               .text   @     2                 .data   @                        .bss    P                       .text   �                       .data   @                        .bss    P                           )   
     +                 .file   S  ��  gmingw_matherr.    .text   �                       .data   @                       .bss    p                           �  E�  
   �                    �  w     .                     �  p                            I     :                      h     �                     )  0
     +                 .file   q  ��  gucrt_vfprintf.    vfprintf�                       .text   �                     .data   P                      .bss    p                       .xdata  �                      .pdata  �                         �  �  
   �                   �  �     8                    �  �     X                     �  �     0                      �     r   	                        �                     )  `
     +                     4  `     8                .file   �  ��  gucrt_printf.c     printf  �                       .text   �     H                .data   `                      .bss    p                       .xdata  �                      .pdata                           �  ��  
   �  
                 �  �     l                    �  6     -                     �  �     0                      �     �   	                         �                     )  �
     +                     4  �     H                .file   �  ��  gucrt_fprintf.c    fprintf �                       .text   �     2                .data   p                      .bss    p                       .xdata  �                      .pdata                           �  3�  
   �                   �  I     b                    �  c     F                     �  �     0                      �     �   	                   �     �                     )  �
     +                     4  �     8                .file   �  ��  g__initenv.c           �  p            x      .text   0                       .data   �                      .bss    p                          �  մ  
   �                   �  �     �                     �                                     [                      �                         )  �
     +                 .file     ��  gucrtbase_compa          0                           .  @          <  �      _onexit            K  @          Y                           ~  `          �  �      tzset   �          �  �                    _tzset            �  �           �  �           �  �           �  �           �  �       .text   0       "             .data   �      x   
             .bss    �                       .xdata  �     P                 .pdata       l                .rdata                            �  w�  
   �  Y                 �  S                           �  �     |                    �  @     0                      o      �                                                 �     `                    )        +                     4  	     �               .text   P      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4       .idata$6v
      .text   X      .data         .bss    �      .idata$7�
      .idata$5�      .idata$4(      .idata$6�
      .text   `      .data         .bss    �      .idata$7�
      .idata$5�      .idata$40      .idata$6�
      .text   h      .data         .bss    �      .idata$7�
      .idata$5�      .idata$48      .idata$6�
      .file      ��  gfake              hname          fthunk  �      .text   p                       .data                          .bss    �                       .idata$2�                      .idata$4       .idata$5�      .file   C  ��  gfake              .text   p                       .data                          .bss    �                       .idata$4@                      .idata$5�                      .idata$7�
                      .text   p      .data         .bss    �      .idata$7h
      .idata$5�      .idata$4       .idata$6X
      .text   x      .data         .bss    �      .idata$7l
      .idata$5�      .idata$4      .idata$6b
      .text   �      .data         .bss    �      .idata$7p
      .idata$5�      .idata$4      .idata$6l
      .file   Q  ��  gfake              hname          fthunk  �      .text   �                       .data                          .bss    �                       .idata$2�                      .idata$4       .idata$5�      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4                      .idata$5�                      .idata$7t
     !                 .text   �      .data         .bss    �      .idata$7,
      .idata$5P      .idata$4�      .idata$6�	      .text   �      .data         .bss    �      .idata$70
      .idata$5X      .idata$4�      .idata$6�	      .text   �      .data         .bss    �      .idata$74
      .idata$5`      .idata$4�      .idata$6
      .text   �      .data         .bss    �      .idata$78
      .idata$5h      .idata$4�      .idata$6
      .text   �      .data         .bss    �      .idata$7<
      .idata$5p      .idata$4�      .idata$6*
      .text   �      .data         .bss    �      .idata$7@
      .idata$5x      .idata$4�      .idata$6F
      .text   �      .data         .bss    �      .idata$7D
      .idata$5�      .idata$4�      .idata$6P
      .file   �  ��  gfake              hname   �      fthunk  P      .text   �                       .data                          .bss    �                       .idata$2�                      .idata$4�      .idata$5P      .file   #  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4�                      .idata$5�                      .idata$7H
                       .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$40      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$48      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4@      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4H      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4P      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5�      .idata$4X      .idata$6	      .text          .data         .bss    �      .idata$7�      .idata$5�      .idata$4`      .idata$6	      .text         .data         .bss    �      .idata$7�      .idata$5�      .idata$4h      .idata$60	      .text         .data         .bss    �      .idata$7�      .idata$5       .idata$4p      .idata$6>	      .text         .data         .bss    �      .idata$7�      .idata$5      .idata$4x      .idata$6F	      .text          .data         .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6h	      .text   (      .data         .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�	      .text   0      .data         .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�	      .text   8      .data         .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6�	      .text   @      .data         .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6�	      .text   H      .data         .bss    �      .idata$7 
      .idata$58      .idata$4�      .idata$6�	      .text   P      .data         .bss    �      .idata$7
      .idata$5@      .idata$4�      .idata$6�	      .file   1  ��  gfake              hname   0      fthunk  �      .text   `                       .data                          .bss    �                       .idata$2�                      .idata$40      .idata$5�      .file   M  ��  gfake              .text   `                       .data                          .bss    �                       .idata$4�                      .idata$5H                      .idata$7
     "                 .text   `      .data         .bss    �      .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   h      .data         .bss    �      .idata$7�      .idata$5�      .idata$4       .idata$6�      .file   [  ��  gfake              hname         fthunk  �      .text   p                       .data                          .bss    �                       .idata$2x                      .idata$4      .idata$5�      .file   p  ��  gfake              .text   p                       .data                          .bss    �                       .idata$4(                      .idata$5�                      .idata$7�     "                 .text   p      .data         .bss    �      .idata$7t      .idata$5�      .idata$4      .idata$6~      .file   ~  ��  gfake              hname         fthunk  �      .text   �                       .data                          .bss    �                       .idata$2d                      .idata$4      .idata$5�      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4                      .idata$5�                      .idata$7x                      .text   �      .data         .bss    �      .idata$7D      .idata$5p      .idata$4�      .idata$6R      .text   �      .data         .bss    �      .idata$7H      .idata$5x      .idata$4�      .idata$6b      .text   �      .data         .bss    �      .idata$7L      .idata$5�      .idata$4�      .idata$6l      .text   �      .data         .bss    �      .idata$7P      .idata$5�      .idata$4�      .idata$6t      .file   �  ��  gfake              hname   �      fthunk  p      .text   �                       .data                          .bss    �                       .idata$2P                      .idata$4�      .idata$5p      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4                       .idata$5�                      .idata$7T                      .text   �      .data         .bss    �      .idata$7      .idata$5X      .idata$4�      .idata$62      .text   �      .data         .bss    �      .idata$7      .idata$5`      .idata$4�      .idata$6B      .file   �  ��  gfake              hname   �      fthunk  X      .text   �                       .data                          .bss    �                       .idata$2<                      .idata$4�      .idata$5X      .file   �  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4�                      .idata$5h                      .idata$7     &                 .text   �      .data         .bss    �      .idata$7�      .idata$5H      .idata$4�      .idata$6*      .file     ��  gfake              hname   �      fthunk  H      .text   �                       .data                          .bss    �                       .idata$2(                      .idata$4�      .idata$5H      .file   W  ��  gfake              .text   �                       .data                          .bss    �                       .idata$4�                      .idata$5P                      .idata$7�     "                 .text   �      .data         .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6      .text   �      .data         .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6      .text   �      .data         .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5      .idata$4x      .idata$6�      .text   �      .data         .bss    �      .idata$7�      .idata$5       .idata$4p      .idata$6�      .text          .data         .bss    �      .idata$7�      .idata$5�      .idata$4h      .idata$6x      .text         .data         .bss    �      .idata$7�      .idata$5�      .idata$4`      .idata$6`      .file   e  ��  gfake              hname   `      fthunk  �      .text                          .data                          .bss    �                       .idata$2                      .idata$4`      .idata$5�      .file   s  ��  gfake              .text                          .data                          .bss    �                       .idata$4�                      .idata$5@                      .idata$7�     
                 .file   �  ��  gcygming-crtend        	  `                        .text                          .data                          .bss    �                           �  `                          �                            �  �                         	  x                          )  P     +                 .idata$2        .idata$5�      .idata$4      .idata$2�       .idata$5�      .idata$4H      .idata$4X      .idata$5�      .idata$7�      .idata$4�      .idata$5       .idata$7�
      .rsrc       
    __xc_z             &	            A	  �          `	  P          k	  x          z	  �          �	  0          �	  �          �	  X          �	              �	  �           �	  �           �	  �
          
  �          1
            F
  x           u
  �          �
  �          �
  x          �
  `          �
  �          �
  �          �
  �                 	          0          1  8          P  �       __xl_a  0           \  �          i            �  �          �  0          �  �          �  �      _cexit  �            p            `  ��       ,     ��       E  �
          s              �  �          �      ��       �     ��       �  0           �  �      __xl_d  @            
  (      _tls_end   	        %
  P      __tzname`          ;
  �          H
  �          Y
             k
  �           |
  0           �
             �
  8          �
  �          �
      	    memcpy  h          �
  �            �      puts    �            (          @  �          f  �       malloc  �      _CRT_MT 0             �          �  �          �              �  �          �  �          �  �          �     ��         h          )             <  �          P            u  h           �  �          �  �          �  �           �  x          �  �            �            `           6  �          E  <           x  �          �  P           �            �  `          �  H          �        abort   @          �  p          &  �           :  �          E  P           U  �          �  �      __dll__     ��       �      ��       �  �          �  �          �  t
          
             "  @           ?  �          J  �          X  `          g            |  0          �  d           �             �     ��       �             �  �      calloc  �          #             -  (          9  �       atof    �          e  �          r  �          �  @      Sleep   �          �  �      _commodep           �  �          �            �  x          �  p           �  p          
  P           6  �           J             d  �      __xi_z  (           p             �             �  �          �  �          �             �  �          �  l       signal  P            �            H           /              A  X      strncmp �          W  �          j  p           y  �          �  @          �  �           �  �          �  �            (           2  �          S      ��       f  �          z  `          �  �          �  
          �  �          �  �          #  �          0  H          ;  �          H  �          g  �           |  P          �  �          �     ��       �            �  �          �                         $  �          >  8          I  �           v              �  P           �     ��       �              �  �           �  �          �  �           	  �            �      __xl_z  H       __end__              $  p          C            e         strcmp  p          s  �           �  �      __xi_a             �  0          �             �  �      __xc_a              �  �          �     ��       �  P                ��   _fmode  �             (          ,  `          @  `          Y  p          j  �          {  �          �  p           �  �          �             �  X          �  �          �              �          %             @  0          U  h      __xl_c  8           b     	        o  �          �  �          �  8          �            �  d           �              �  H
          ,             Q  �          i  X          t  �          �  T      _newmodeP           �  (      fwrite  �          �  �          �  �          �  P          �      ��             ��       "  @          /  @           E  �          R  �      exit    H          d     ��       �      ��       �             �  �      _exit             �  `           �  @          �  �      strlen  x          �  p                        @          9            O             l  �          �  �           �  �          �  �           �  �          �  �          &  �          7  P          F  �           _  P           o  �          �  �           �  �      free    �          �  �       �  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame print_usage GetProductVersionValue GetDeveloperModeState DeveloperModeAction .text.startup .xdata.startup .pdata.startup __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 __imp_plist_get_string_val ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight plist_new_dict __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname __imp_idevice_new_with_options __imp_plist_new_uint _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode __imp___p___argc plist_get_node_type __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __imp_property_list_service_receive_plist property_list_service_client_new __rt_psrelocs_start __imp_plist_get_node_type plist_new_uint __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll property_list_service_client_free .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free .refptr._matherr property_list_service_receive_plist .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler TlsGetValue __imp_strcmp __bss_start__ __imp___C_specific_handler ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection __C_specific_handler lockdownd_get_value .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf plist_free ___crt_xp_end__ __imp_property_list_service_send_xml_plist __imp_lockdownd_start_service __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_puts _set_new_mode .refptr.__xi_a __imp_plist_new_dict .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname _tls_used __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ lockdownd_start_service .refptr._newmode plist_get_string_ptr __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func __native_startup_state ___crt_xc_start__ lockdownd_client_free plist_get_bool_val ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_lockdownd_get_value plist_get_string_val _head_lib64_libapi_ms_win_crt_convert_l1_1_0_a .refptr.__mingw_initltsdyn_force __rt_psrelocs_size plist_dict_set_item .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp___p___wargv __imp_property_list_service_client_free __imp_strlen __imp_atof __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __p__wenviron _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_plist_dict_get_item _head_libplist_2_0_dll __IAT_start__ __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit __DTOR_LIST__ __imp_plist_get_bool_val __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_plist_get_string_ptr __imp_VirtualProtect idevice_free ___tls_end__ __lib64_libapi_ms_win_crt_convert_l1_1_0_a_iname .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname property_list_service_send_xml_plist __dyn_tls_init_callback __timezone __imp_plist_dict_set_item __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ __minor_image_version__ __imp___set_app_type __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR DeleteCriticalSection _initialize_wide_environment __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv plist_dict_get_item __imp_property_list_service_client_new __imp_plist_free .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_daylight __p___wargv __mingw_app_type 