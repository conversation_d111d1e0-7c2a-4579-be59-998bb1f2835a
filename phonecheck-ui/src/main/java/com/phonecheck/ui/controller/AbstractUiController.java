package com.phonecheck.ui.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jfoenix.controls.JFXButton;
import com.jfoenix.controls.JFXDialog;
import com.jfoenix.controls.JFXDialogLayout;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.mqtt.messages.PublishableMessage;
import com.phonecheck.model.store.UiInMemoryStore;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.ui.controller.print.PrintController;
import com.phonecheck.ui.controller.utility.DialogController;
import com.phonecheck.ui.controller.utility.FXMLLoaderUtil;
import com.phonecheck.ui.listener.IDialogListener;
import javafx.animation.FadeTransition;
import javafx.animation.Interpolator;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.effect.DropShadow;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.paint.Paint;
import javafx.scene.text.Font;
import javafx.scene.web.WebEngine;
import javafx.scene.web.WebView;
import javafx.stage.Modality;
import javafx.stage.Popup;
import javafx.stage.Stage;
import javafx.stage.Window;
import javafx.util.Duration;
import lombok.Getter;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.io.IOException;

public abstract class AbstractUiController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractUiController.class);
    protected static final String UI_FXML_PACKAGE = "/com/phonecheck/ui/";

    @Autowired
    private FXMLLoaderUtil fxmlLoaderUtil;
    @Autowired
    @Getter
    private ApplicationContext applicationContext;
    @Getter
    @Autowired
    private IMqttAsyncClient mqttClient;
    @Getter
    @Autowired
    private ObjectMapper mapper;

    @Getter
    @Autowired
    private LocalizationService localizationService;

    @FXML
    private HBox notificationHBox;
    @FXML
    private Popup portMapPopup;

    /**
     * Set the device serial in the logging context
     *
     * @param deviceId device id
     */
    protected void setDeviceIdMDC(final String deviceId) {
        MDC.clear();
        MDC.put("id", deviceId);
    }

    /**
     * Runs a runnable on javafx thread
     *
     * @param runnable Runnable to be executed on javafx thread
     */
    public void runOnFxThread(final Runnable runnable) {
        Runnable safeRunnable = () -> {
            try {
                runnable.run();
            } catch (Exception e) {
                LOGGER.error("Error occurred while executing the runnable", e);
            }
        };

        if (Platform.isFxApplicationThread()) {
            safeRunnable.run();
        } else {
            Platform.runLater(safeRunnable);
        }
    }

    /**
     * Publish a message to an MQTT topic.
     *
     * @param topic          mqtt topic
     * @param requestMessage mqtt message
     */
    protected void publishToMqttTopic(final String topic, final PublishableMessage requestMessage) {
        if (!mqttClient.isConnected()) {
            LOGGER.warn("MQTT client was disconnected from the server");
            LOGGER.info("Assigning a new client to the controller");
            mqttClient = applicationContext.getBean(IMqttAsyncClient.class);
        }
        try {
            final MqttMessage message = new MqttMessage();
            message.setPayload(mapper.writeValueAsBytes(requestMessage));
            mqttClient.publish(topic, message);
        } catch (MqttException e) {
            LOGGER.error("Could not publish to {}", topic, e);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {}", topic, e);
        }
    }

    /**
     * Opens print dialogue to manually select printer and label to print
     *
     * @param mainParent              root {@link Stage} or {@link StackPane} to host the dialog
     * @param isBulkPrint             is true if print command is requested from main UI
     *                                and is false if print command is coming from device UI
     * @param device                  target device
     * @param isFromTransactionDetail this field will determine if a device printing is requested outside of processing
     */
    protected void openPrintDialogue(final StackPane mainParent,
                                     final boolean isBulkPrint,
                                     final Device device, final boolean isFromTransactionDetail) {
        try {
            Stage stage = new Stage();
            FXMLLoader loader = fxmlLoaderUtil.getFxmlLoader("print");
            loader.setControllerFactory(applicationContext::getBean);
            final Pane parent = loader.load();
            Scene scene = new Scene(parent, parent.getPrefWidth(), parent.getPrefHeight());
            scene.getStylesheets().add("com/phonecheck/style/main.css");
            scene.getStylesheets().add("com/phonecheck/style/light-theme.css");
            stage.initOwner(mainParent.getScene().getWindow());
            stage.setScene(scene);
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.setResizable(false);
            stage.setTitle("Print dialogue");
            stage.setFullScreen(false);
            PrintController controller = applicationContext.getBean(PrintController.class);
            controller.setWindow(stage);
            controller.setBulkPrint(isBulkPrint);
            controller.setFromTransactionDetail(isFromTransactionDetail);
            controller.setDevice(device);
            stage.showAndWait();
        } catch (final Exception e) {
            LOGGER.error("Could not load the print.fxml", e);
        }
    }

    /**
     * Displays a modal dialog that does not return a result. The dialog is centered within the <code>parent</code>
     * and features a dark translucent glasspane behind the dialog.
     *
     * @param parent  root {@link Stage} or {@link StackPane} to host the dialog
     * @param heading text to be displayed at the top of the dialog
     * @param content component to be displayed as the main dialog body
     */
    public void showInfoDialog(final StackPane parent, final String heading, final Region content) {
        try {
            final FXMLLoader loader =
                    new FXMLLoader(getClass().getResource(UI_FXML_PACKAGE + "utility/dialog.fxml"),
                            localizationService.getResourceBundle());
            DialogController dialogController = getApplicationContext().getBean(DialogController.class);
            loader.setController(dialogController);
            Region dialogRoot = loader.load();

            dialogController.setHeading(heading);
            dialogController.setContent(content);

            final JFXDialog dialog = new JFXDialog(parent, dialogRoot, JFXDialog.DialogTransition.CENTER);
            dialogController.setCloseListener(new IDialogListener<Void, Void>() {
                @Override
                public void onNegativeOption(final Void v) {
                    dialog.close();
                }

                @Override
                public void onPositiveOption(final Void v, final Void data) {
                    dialog.close();
                }
            });

            // Translucent black glass pane behind the dialog content
            dialog.setBackground(new Background(new BackgroundFill(Paint.valueOf("rgba(0,0,0,0.6)"),
                    CornerRadii.EMPTY, Insets.EMPTY)));
            dialog.show();
        } catch (IOException e) {
            LOGGER.error("Exception occurred when tried to show Info Dialog box", e);
        }
    }

    /**
     * Use this method if there is a formatted text needs to be displayed on a dialog box
     *
     * @param root    stack pane
     * @param header  dialog heading
     * @param content dialog content text
     */
    public void showInfoDialog(final StackPane root, final String header, final String content) {
        JFXDialogLayout layout = new JFXDialogLayout();
        Label label = new Label(header);
        label.setFont(new Font("Arial", 15));
        label.setStyle("-fx-font-weight: bold");
        layout.setHeading(label);
        label.setWrapText(true);

        WebView webView = new WebView();
        WebEngine webEngine = webView.getEngine();
        webEngine.loadContent(content);
        webEngine.setUserStyleSheetLocation(getClass().getResource("/com/phonecheck/style/web-view.css")
                .toExternalForm());
        webView.setPrefSize(200, 450);

        layout.setBody(webView);
        JFXDialog dialog = new JFXDialog(root, layout, JFXDialog.DialogTransition.CENTER);
        dialog.setOverlayClose(false);
        JFXButton okButton = new JFXButton("OK");
        okButton.setOnMouseClicked(event -> dialog.close());
        layout.setActions(okButton);
        dialog.show();
    }

    /**
     * Shows a port mapping expiration notification anchored to the top-right of the given window.
     * <p>
     * The notification includes a message, a "Remap" action button, and a close icon. It supports fade-in and fade-out
     * animations. When the "Remap" button is clicked, the provided {@code onRemapPressed} callback is executed.
     *
     * @param ownerWindow  the JavaFX window where the notification should be displayed
     * @param onRemapClick the callback to execute when the "Remap" button is clicked
     */
    public void showPortMappingUpdateRequiredNotification(final Window ownerWindow, final Runnable onRemapClick) {
        if (portMapPopup == null) {
            notificationHBox = createNotificationUI(
                    "The device is connected to an unmapped port. Please remap the ports.",
                    "Remap", onRemapClick);

            portMapPopup = new Popup();
            portMapPopup.getContent().add(notificationHBox);
            portMapPopup.setAutoHide(false);
            portMapPopup.setHideOnEscape(true);
        }

        if (portMapPopup.isShowing()) {
            portMapPopup.hide();
        }

        // Position top-right of the owner window
        double x = ownerWindow.getX() + ownerWindow.getWidth() - 340;
        double y = ownerWindow.getY() + 116;

        portMapPopup.show(ownerWindow, x, y);

        // Play fade in animation
        if (notificationHBox != null) {
            playFadeAnimation(notificationHBox, Interpolator.EASE_IN, null);
        }

    }

    /**
     * Creates a customizable notification layout (HBox) with a message, action button, and close icon.
     * Includes styling, layout configuration, and fade-out behavior for both the action and close buttons.
     *
     * @param message        the message to display inside the notification
     * @param actionBtnLabel the text to show on the action button (e.g. "Remap", "Retry")
     * @param onActionClick  callback to invoke when the action button is clicked
     * @return configured HBox containing the notification UI
     */
    private HBox createNotificationUI(final String message, final String actionBtnLabel, final Runnable onActionClick) {
        HBox notificationHbox = new HBox(10);
        notificationHbox.setAlignment(Pos.CENTER_LEFT);
        notificationHbox.setPrefWidth(320);
        notificationHbox.setOpacity(0); // Start transparent for fade-in
        notificationHbox.setStyle("-fx-background-color: white; -fx-padding: 8; -fx-background-radius: 6;");

        // Add drop shadow for visual depth
        DropShadow dropShadow = new DropShadow();
        dropShadow.setRadius(6.0);
        dropShadow.setOffsetX(0);
        dropShadow.setOffsetY(2);
        dropShadow.setColor(Color.rgb(0, 0, 0, 0.2));
        notificationHbox.setEffect(dropShadow);

        Label messageLabel = new Label(message);
        messageLabel.setFont(Font.font("Helvetica", 12));
        messageLabel.setWrapText(true);
        messageLabel.setAlignment(Pos.CENTER_LEFT);
        HBox.setHgrow(messageLabel, Priority.ALWAYS);
        messageLabel.setMaxWidth(Double.MAX_VALUE);

        Button remapButton = new Button(actionBtnLabel);
        remapButton.setMinWidth(56);
        remapButton.setMaxHeight(24);
        remapButton.setStyle("-fx-background-color: #00DF8F; -fx-border-radius: 4; -fx-font-size: 12;");
        remapButton.setTextFill(Color.WHITE);
        remapButton.setOnAction(e -> {
            if (portMapPopup != null) {
                playFadeAnimation(notificationHbox, Interpolator.EASE_OUT, portMapPopup::hide);
            }
            onActionClick.run();
        });

        ImageView closeIcon = new ImageView();
        closeIcon.setFitHeight(12);
        closeIcon.setFitWidth(12);
        closeIcon.setPreserveRatio(true);
        closeIcon.setPickOnBounds(true);
        setImageIconToImageView(AbstractUiController.this, closeIcon, "black-cross.png");

        HBox closeButtonBox = new HBox(closeIcon);
        closeButtonBox.setAlignment(Pos.CENTER_LEFT);
        closeButtonBox.setOnMouseClicked(e -> {
            if (portMapPopup != null) {
                playFadeAnimation(notificationHbox, Interpolator.EASE_OUT, portMapPopup::hide);
            }
        });

        notificationHbox.getChildren().addAll(messageLabel, remapButton, closeButtonBox);

        return notificationHbox;
    }

    /**
     * Plays a fade-in or fade-out animation on the given node with a default duration of 300ms.
     *
     * @param node         the node to animate
     * @param interpolator EASE_IN to fade in, EASE_OUT to fade out
     * @param onFinished   optional callback to run after the animation completes (may be null)
     */
    public void playFadeAnimation(final Node node, final Interpolator interpolator, final Runnable onFinished) {
        FadeTransition fade = new FadeTransition(Duration.millis(300), node);
        boolean fadeIn = interpolator == Interpolator.EASE_IN;
        fade.setFromValue(fadeIn ? 0 : 1);
        fade.setToValue(fadeIn ? 1 : 0);
        fade.setInterpolator(interpolator);
        if (onFinished != null) {
            fade.setOnFinished(e -> onFinished.run());
        }
        fade.play();
    }

    /**
     * This method sets an image to an ImageView object using the specified loader and file name.
     *
     * @param instance  the loader object used to load the image resource
     * @param imageView the ImageView object to set the image to
     * @param fileName  the name of the image file to load
     */
    public void setImageIconToImageView(final Object instance, final ImageView imageView,
                                        final String fileName) {
        if (instance == null) {
            LOGGER.warn("Instance should not null while set image into imageview");
            return;
        }
        String url = instance.getClass().getClassLoader()
                .getResource("com/phonecheck/image/icon/" + fileName)
                .toExternalForm();
        final Image image = new Image(url);
        imageView.setImage(image);
    }

    /**
     * Sets up device fields on the label before printing. This method currently sets the
     * package name of the provided device to the name obtained from the assigned cloud customization.
     * Additional related fields can be added to this method in the future as needed.
     *
     * @param uiInMemoryStore UiInMemoryStore
     * @param device          Device
     */
    public void setDeviceFieldsForLabelPrint(final UiInMemoryStore uiInMemoryStore,
                                             final Device device) {
        device.setPackageName(uiInMemoryStore.getAssignedCloudCustomization().getName());
        device.setStationId(uiInMemoryStore.getLoggedInUserId());
        device.setTesterName(uiInMemoryStore.getLoggedInTesterName());
        if (uiInMemoryStore.getTransaction() != null) {
            device.setInvoiceNo(uiInMemoryStore.getTransaction().getInvoiceNo());
            device.setVendorName(uiInMemoryStore.getTransaction().getVendorName());
            device.setBoxNo(uiInMemoryStore.getTransaction().getBoxNo());
        }
        // TODO: Later we can add all ui related fields here
    }
}
