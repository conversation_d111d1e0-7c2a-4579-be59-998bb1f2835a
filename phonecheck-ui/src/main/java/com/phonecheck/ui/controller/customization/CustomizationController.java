package com.phonecheck.ui.controller.customization;

import com.jfoenix.controls.JFXCheckBox;
import com.jfoenix.controls.JFXRadioButton;
import com.phonecheck.model.constants.CustomizationConstants;
import com.phonecheck.model.customization.LabelWorkFlowPrintSettings;
import com.phonecheck.model.customization.LocalCustomizations;
import com.phonecheck.model.customization.PrintSettings;
import com.phonecheck.model.event.camera.CameraListResponseEvent;
import com.phonecheck.model.event.customization.CustomizationResponseEvent;
import com.phonecheck.model.event.print.PrintersListResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.CameraListRequestMessage;
import com.phonecheck.model.mqtt.messages.PrintersListRequestMessage;
import com.phonecheck.model.mqtt.messages.UpdateCustomizationsMessage;
import com.phonecheck.model.print.Printer;
import com.phonecheck.model.print.brother.BrotherPaperType;
import com.phonecheck.model.print.brother.BrotherRollType;
import com.phonecheck.model.print.dymo.DymoPaperTray;
import com.phonecheck.model.print.label.LabelVariant;
import com.phonecheck.model.store.UiInMemoryStore;
import com.phonecheck.ui.controller.AbstractUiController;
import com.phonecheck.ui.controller.firmware.FirmwareDownloaderController;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.layout.VBox;
import javafx.stage.DirectoryChooser;
import javafx.stage.Stage;
import lombok.Setter;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.controlsfx.control.CheckComboBox;
import org.controlsfx.control.ToggleSwitch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Controller for the customization page
 */
@Component
public class CustomizationController extends AbstractUiController implements Initializable {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomizationController.class);
    private static final String FIRMWARE_FXML = "/com/phonecheck/ui/firmwaredownloader.fxml";
    public static final String SELECT_EXPORT_DIRECTORY = "Select Export Directory";

    @FXML
    private JFXCheckBox enableDevModeSetting;
    @FXML
    private JFXCheckBox enableAutoOpenIosApp;
    @FXML
    private ComboBox<String> label1ComboBox;
    @FXML
    private ComboBox<String> label2ComboBox;
    @FXML
    private ComboBox<String> printer1ComboBox;
    @FXML
    private ComboBox<String> printer2ComboBox;
    @FXML
    private VBox loaderViewCustomization;
    @FXML
    private VBox mainViewCustomization;
    @FXML
    private Tab firmware;
    @FXML
    private Button saveCustomizationButton;
    @FXML
    private HBox saveCustomizationHbox;
    @FXML
    private TabPane mainTabPane;
    @FXML
    private HBox printer1RollTypeHBox;
    @FXML
    private JFXRadioButton printer1RollTapeRB;
    @FXML
    private JFXRadioButton printer1RollLabelRB;
    @FXML
    private ToggleGroup printer1RollTypeTG;
    @FXML
    private HBox printer2RollTypeHBox;
    @FXML
    private JFXRadioButton printer2RollTapeRB;
    @FXML
    private JFXRadioButton printer2RollLabelRB;
    @FXML
    private ToggleGroup printer2RollTypeTG;
    @FXML
    private HBox printer1TwinTurboHBox;
    @FXML
    private JFXCheckBox printer1DymoTwinTurboCB;
    @FXML
    private HBox printer1InputTrayHBox;
    @FXML
    private JFXRadioButton printer1InputTrayLB;
    @FXML
    private JFXRadioButton printer1InputTrayRB;
    @FXML
    private ToggleGroup printer1InputTrayTG;
    @FXML
    private HBox printer2TwinTurboHBox;
    @FXML
    private JFXCheckBox printer2DymoTwinTurboCB;
    @FXML
    private HBox printer2InputTrayHBox;
    @FXML
    private JFXRadioButton printer2InputTrayLB;
    @FXML
    private JFXRadioButton printer2InputTrayRB;
    @FXML
    private ToggleGroup printer2InputTrayTG;
    @FXML
    private TextField restoreThreadTxtField;
    @FXML
    private TextField restoreDelayTxtField;

    @FXML
    private ComboBox<String> paperType1ComboBox;
    @FXML
    private ComboBox<String> paperType2ComboBox;
    @FXML
    private HBox printer1PaperTypeHBox;
    @FXML
    private HBox printer2PaperTypeHBox;
    @FXML
    private JFXCheckBox autoExportCheckBox;
    @FXML
    private TextField filePathTextField;
    @FXML
    private Button browseButton;
    @FXML
    private CheckComboBox<String> shopfloorGradeCheckComboBox;
    @FXML
    private ComboBox<String> cameraListComboBox;
    @FXML
    private TextField imageGradeFolderPathTextField;
    @FXML
    private Button imageGradeBrowseLocationButton;
    @FXML
    private JFXCheckBox jsonCheckBox;
    @FXML
    private JFXCheckBox xmlCheckBox;
    @FXML
    private JFXCheckBox successfulEraseCheckBox;
    @FXML
    private JFXCheckBox onAppResultsCheckBox;
    @FXML
    private JFXCheckBox ignoreNaForOem;
    @FXML
    private ToggleSwitch labelWorkFlowToggle;
    @FXML
    private ComboBox<String> label1ComboBoxTRWorking;
    @FXML
    private ComboBox<String> label2ComboBoxTRWorking;
    @FXML
    private ComboBox<String> printer1ComboBoxTRWorking;
    @FXML
    private ComboBox<String> printer2ComboBoxTRWorking;
    @FXML
    private ComboBox<String> label1ComboBoxTRFailed;
    @FXML
    private ComboBox<String> label2ComboBoxTRFailed;
    @FXML
    private ComboBox<String> printer1ComboBoxTRFailed;
    @FXML
    private ComboBox<String> printer2ComboBoxTRFailed;
    @FXML
    private ComboBox<String> paperType1ComboBoxTRWorking;
    @FXML
    private ComboBox<String> paperType1ComboBoxTRFailed;
    @FXML
    private ComboBox<String> paperType2ComboBoxTRWorking;
    @FXML
    private ComboBox<String> paperType2ComboBoxTRFailed;
    @FXML
    private HBox printer1RollTypeHBoxTRWorking;
    @FXML
    private HBox printer1TwinTurboHBoxTRWorking;
    @FXML
    private HBox printer1PaperTypeHBoxTRWorking;
    @FXML
    private HBox printer1PaperTypeHBoxTRFailed;
    @FXML
    private HBox printer1TwinTurboHBoxTRFailed;
    @FXML
    private HBox printer1RollTypeHBoxTRFailed;
    @FXML
    private HBox printer2RollTypeHBoxTRWorking;
    @FXML
    private HBox printer2TwinTurboHBoxTRWorking;
    @FXML
    private HBox printer2PaperTypeHBoxTRWorking;
    @FXML
    private HBox printer2PaperTypeHBoxTRFailed;
    @FXML
    private HBox printer2TwinTurboHBoxTRFailed;
    @FXML
    private HBox printer2RollTypeHBoxTRFailed;
    @FXML
    private JFXCheckBox printer1DymoTwinTurboCBTRWorking;
    @FXML
    private JFXCheckBox printer2DymoTwinTurboCBTRWorking;
    @FXML
    private JFXCheckBox printer1DymoTwinTurboCBTRFailed;
    @FXML
    private JFXCheckBox printer2DymoTwinTurboCBTRFailed;
    @FXML
    private ToggleGroup printer1RollTypeTGTRWorking;
    @FXML
    private ToggleGroup printer2RollTypeTGTRWorking;
    @FXML
    private ToggleGroup printer1InputTrayTGTRWorking;
    @FXML
    private ToggleGroup printer2InputTrayTGTRWorking;
    @FXML
    private ToggleGroup printer1RollTypeTGTRFailed;
    @FXML
    private ToggleGroup printer2RollTypeTGTRFailed;
    @FXML
    private ToggleGroup printer1InputTrayTGTRFailed;
    @FXML
    private ToggleGroup printer2InputTrayTGTRFailed;
    @FXML
    private JFXRadioButton printer1RollTapeRBTRWorking;
    @FXML
    private JFXRadioButton printer1RollLabelRBTRWorking;
    @FXML
    private JFXRadioButton printer1RollTapeRBTRFailed;
    @FXML
    private JFXRadioButton printer1RollLabelRBTRFailed;
    @FXML
    private JFXRadioButton printer1InputTrayRBTRWorking;
    @FXML
    private JFXRadioButton printer1InputTrayRBTRFailed;
    @FXML
    private JFXRadioButton printer1InputTrayLBTRWorking;
    @FXML
    private JFXRadioButton printer1InputTrayLBTRFailed;
    @FXML
    private JFXRadioButton printer2RollLabelRBTRWorking;
    @FXML
    private JFXRadioButton printer2RollTapeRBTRWorking;
    @FXML
    private JFXRadioButton printer2RollTapeRBTRFailed;
    @FXML
    private JFXRadioButton printer2RollLabelRBTRFailed;
    @FXML
    private JFXRadioButton printer2InputTrayRBTRWorking;
    @FXML
    private JFXRadioButton printer2InputTrayRBTRFailed;
    @FXML
    private JFXRadioButton printer2InputTrayLBTRWorking;
    @FXML
    private JFXRadioButton printer2InputTrayLBTRFailed;
    @FXML
    private HBox printer1InputTrayHBoxTRWorking;
    @FXML
    private HBox printer2InputTrayHBoxTRWorking;
    @FXML
    private HBox printer1InputTrayHBoxTRFailed;
    @FXML
    private HBox printer2InputTrayHBoxTRFailed;
    @FXML
    private RadioButton oneFlowRadioBtn;
    @FXML
    private RadioButton separateFlowRadioBtn;
    @FXML
    private HBox labelForWorkingSelection;
    @FXML
    private HBox labelForFailedSelection;
    @FXML
    private VBox trFailedVBox;
    @FXML
    private VBox label1WorkingVBox;
    @FXML
    private JFXCheckBox fetchXiaomiAlternateSerial;

    @Setter
    private Stage window;
    @Setter
    private String loggedInUserId;
    @Autowired
    private UiInMemoryStore uiInMemoryStore;
    private static LabelWorkFlowPrintSettings labelWorkFlowPrintSettings;

    /**
     * Listener for customization data received from the backend
     *
     * @param event CustomizationResponseEvent
     */
    @Async
    @EventListener
    public void onReceiveCustomizationResponse(final CustomizationResponseEvent event) {

        if (StringUtils.isNotBlank(event.getUserId())) {
            LocalCustomizations.LocalCustomizationsBuilder customizationsBuilder = LocalCustomizations.builder()
                    .userId(event.getUserId())
                    .defaultLabel1(event.getLabel1Name())
                    .defaultLabel2(event.getLabel2Name())
                    .defaultPrinter1(event.getPrinter1Name())
                    .rollTypePrinter1(event.getRollTypePrinter1())
                    .selectedTrayPrinter1(event.getSelectedTrayPrinter1())
                    .isDymoTwinTurboPrinter1(event.isDymoTwinTurboPrinter1())
                    .defaultPrinter2(event.getPrinter2Name())
                    .rollTypePrinter2(event.getRollTypePrinter2())
                    .selectedTrayPrinter2(event.getSelectedTrayPrinter2())
                    .isDymoTwinTurboPrinter2(event.isDymoTwinTurboPrinter2())
                    .enableDevMode(event.isEnableDevMode())
                    .enableAutoOpenIosApp(event.isEnableAutoOpenIosApp())
                    .selectedPaperType1(event.getSelectedPaperType1())
                    .selectedPaperType2(event.getSelectedPaperType2())
                    .restoreThreads(event.getRestoreThreadCount())
                    .restoreDelay(event.getRestoreDelay())
                    .autoExport(event.isAutoExport())
                    .exportFormatJson(event.isExportFormatJson())
                    .exportFormatXml(event.isExportFormatXml())
                    .exportOnSuccessfulErase(event.isExportOnSuccessfulErase())
                    .exportOnAppResults(event.isExportOnAppResults())
                    .exportFilePath(event.getExportFilePath())
                    .labelWorkFlowPrintSettings(event.getLabelWorkFlowPrintSettings())
                    .fetchXiaomiAlternateSerial(event.isFetchXiaomiAlternateSerial())
                    .ignoreNaForOem(event.isIgnoreNaForOem())
                    .shopfloorSettings(event.getShopfloorSettings());
            uiInMemoryStore.setCustomizations(customizationsBuilder.build());
        }
    }

    @Async
    @EventListener
    public void onReceivePrinterListEvent(final PrintersListResponseEvent event) {
        if (printer1ComboBox != null) {
            LOGGER.debug("Received printers list from backend.");
            // Initialize printers combo box with list of printers
            ObservableList<String> printersList = FXCollections.observableArrayList(event.getPrintersList());
            printersList.add(0, CustomizationConstants.NONE);
            Runnable runnable = () -> {
                printer1ComboBox.setItems(printersList);
                printer1ComboBoxTRWorking.setItems(printersList);
                printer1ComboBoxTRFailed.setItems(printersList);
            };
            runOnFxThread(runnable);
        }

        if (printer2ComboBox != null) {
            LOGGER.debug("Received printers list from backend.");
            // Initialize printers combo box with list of printers
            ObservableList<String> printersList = FXCollections.observableArrayList(event.getPrintersList());
            printersList.add(0, CustomizationConstants.NONE);
            Runnable runnable = () -> {
                printer2ComboBox.setItems(printersList);
                printer2ComboBoxTRWorking.setItems(printersList);
                printer2ComboBoxTRFailed.setItems(printersList);
            };
            runOnFxThread(runnable);
        }
    }

    @Async
    @EventListener
    public void onReceiveCameraListEvent(final CameraListResponseEvent event) {
        if (event.getCameraList() != null && !event.getCameraList().isEmpty()) {
            runOnFxThread(() -> {
                LOGGER.info("List of cameras received from backend: {}", event.getCameraList());
                cameraListComboBox.setItems(FXCollections.observableArrayList(event.getCameraList()));
            });
        }
    }

    /**
     * Save customizations entered on the UI
     */
    @FXML
    public void saveCustomizations() {
        saveCustomizationInMemory();
        publishCustomizationToMqtt();
        window.close();
    }

    /**
     * Handle Toggle entered on the UI
     * i.e, Visibility
     */
    @FXML
    public void handleLabelWorkFlowToggle() {
        if (labelWorkFlowToggle.isSelected()) {

            oneFlowRadioBtn.setDisable(false);
            separateFlowRadioBtn.setDisable(false);

            //One flow / select for working
            label1WorkingVBox.setDisable(false);

            //separate select for failed
            trFailedVBox.setDisable(false);
        } else {

            oneFlowRadioBtn.setDisable(true);
            separateFlowRadioBtn.setDisable(true);

            //One flow / select for working
            label1WorkingVBox.setDisable(true);

            //separate select for failed
            trFailedVBox.setDisable(true);
        }
    }

    private void publishCustomizationToMqtt() {
        UpdateCustomizationsMessage message = new UpdateCustomizationsMessage();
        message.setUserName(loggedInUserId);
        message.setLabel1Name(label1ComboBox.getSelectionModel().getSelectedItem());
        message.setLabel2Name(label2ComboBox.getSelectionModel().getSelectedItem());
        message.setPrinter1Name(printer1ComboBox.getSelectionModel().getSelectedItem());
        message.setPrinter2Name(printer2ComboBox.getSelectionModel().getSelectedItem());
        message.setDymoTwinTurboPrinter1(printer1DymoTwinTurboCB.isSelected());
        message.setDymoTwinTurboPrinter2(printer2DymoTwinTurboCB.isSelected());
        message.setEnableDevMode(enableDevModeSetting.isSelected());
        message.setEnableAutoOpenIosApp(enableAutoOpenIosApp.isSelected());
        message.setRestoreThreadCount(Integer.parseInt(restoreThreadTxtField.getText()));
        message.setRestoreDelay(Integer.parseInt(restoreDelayTxtField.getText()));
        message.setFirmwarePath(uiInMemoryStore.getFirmwareDownloadPath());
        message.setSelectedPaperType1(paperType1ComboBox.getSelectionModel().getSelectedItem());
        message.setSelectedPaperType2(paperType2ComboBox.getSelectionModel().getSelectedItem());
        message.setAutoExport(autoExportCheckBox.isSelected());
        message.setExportFormatJson(jsonCheckBox.isSelected());
        message.setExportFormatXml(xmlCheckBox.isSelected());
        message.setExportOnSuccessfulErase(successfulEraseCheckBox.isSelected());
        message.setExportOnAppResults(onAppResultsCheckBox.isSelected());
        message.setExportFilePath(filePathTextField.getText());
        message.setLabelWorkFlowPrintSettings(uiInMemoryStore.getCustomizations().getLabelWorkFlowPrintSettings());
        message.setFetchXiaomiAlternateSerial(uiInMemoryStore.getCustomizations().isFetchXiaomiAlternateSerial());
        message.setIgnoreNaForOem(ignoreNaForOem.isSelected());
        message.setShopfloorSettings(LocalCustomizations.ShopfloorSettings
                .builder()
                .selectedImageGrades(shopfloorGradeCheckComboBox.getCheckModel().getCheckedItems())
                .imageGradeFolderPath(imageGradeFolderPathTextField.getText())
                .selectedCamera(cameraListComboBox.getSelectionModel().getSelectedItem())
                .build());

        if ((printer1RollTypeTG.getSelectedToggle() != null)) {
            message.setRollTypePrinter1(((JFXRadioButton) printer1RollTypeTG.getSelectedToggle())
                    .getId());
        }
        if ((printer2RollTypeTG.getSelectedToggle() != null)) {
            message.setRollTypePrinter2(((JFXRadioButton) printer2RollTypeTG.getSelectedToggle())
                    .getId());
        }
        if ((printer1InputTrayTG.getSelectedToggle() != null)) {
            message.setSelectedTrayPrinter1(((JFXRadioButton) printer1InputTrayTG.getSelectedToggle())
                    .getId());
        }
        if ((printer2InputTrayTG.getSelectedToggle() != null)) {
            message.setSelectedTrayPrinter2(((JFXRadioButton) printer2InputTrayTG.getSelectedToggle())
                    .getId());
        }

        LOGGER.info("Updated local customizations for user: {} {}", loggedInUserId, message);
        String topic = TopicBuilder.buildGenericTopic("customization", "save");

        publishToMqttTopic(topic, message);
    }

    /**
     * Save customization in InMemory object LocalCustomizationStore to use across UI module
     */
    private void saveCustomizationInMemory() {

        LOGGER.info("Saving the customization to memory");
        LabelWorkFlowPrintSettings.LabelWorkFlowPrintSettingsBuilder labelWorkFlowPrintSettingsBuilder =
                LabelWorkFlowPrintSettings.builder()
                        .labelWorkflowEnabled(labelWorkFlowToggle.isSelected())
                        .isOneFlowForAllTestResults(oneFlowRadioBtn.isSelected());

        labelWorkFlowPrintSettings = uiInMemoryStore.getCustomizations().getLabelWorkFlowPrintSettings();
        if (labelWorkFlowPrintSettings != null) {
            labelWorkFlowPrintSettingsBuilder.printerSettingsWhenWorking(uiInMemoryStore.getCustomizations()
                    .getLabelWorkFlowPrintSettings().getPrinterSettingsWhenWorking());
            labelWorkFlowPrintSettingsBuilder.printerSettingsWhenFailed(uiInMemoryStore.getCustomizations().
                    getLabelWorkFlowPrintSettings().getPrinterSettingsWhenFailed());
            labelWorkFlowPrintSettingsBuilder.oneFlowPrinterSettings(uiInMemoryStore.getCustomizations().
                    getLabelWorkFlowPrintSettings().getOneFlowPrinterSettings());
        }

        PrintSettings.PrintSettingsBuilder printSettingsBuilder1;
        PrintSettings.PrintSettingsBuilder printSettingsBuilder2 = PrintSettings.builder();
        if (labelWorkFlowToggle.isSelected()) {
            LOGGER.info("Label workflow toggle is ON");
            printSettingsBuilder1 = PrintSettings.builder()
                    .defaultLabel1(label1ComboBoxTRWorking.getSelectionModel().getSelectedItem())
                    .defaultPrinter1(printer1ComboBoxTRWorking.getSelectionModel().getSelectedItem())
                    .defaultLabel2(label2ComboBoxTRWorking.getSelectionModel().getSelectedItem())
                    .defaultPrinter2(printer2ComboBoxTRWorking.getSelectionModel().getSelectedItem())
                    .isDymoTwinTurboPrinter1(printer1DymoTwinTurboCBTRWorking.isSelected())
                    .isDymoTwinTurboPrinter2(printer2DymoTwinTurboCBTRWorking.isSelected())
                    .selectedPaperType1(paperType1ComboBoxTRWorking.getSelectionModel().getSelectedItem())
                    .selectedPaperType2(paperType2ComboBoxTRWorking.getSelectionModel().getSelectedItem());

            if ((printer1RollTypeTGTRWorking.getSelectedToggle() != null)) {
                printSettingsBuilder1.rollTypePrinter1(
                        ((JFXRadioButton) printer1RollTypeTGTRWorking.getSelectedToggle()).getId());
            }
            if ((printer2RollTypeTGTRWorking.getSelectedToggle() != null)) {
                printSettingsBuilder1.rollTypePrinter2(
                        ((JFXRadioButton) printer2RollTypeTGTRWorking.getSelectedToggle()).getId());
            }
            if ((printer1InputTrayTGTRWorking.getSelectedToggle() != null)) {
                printSettingsBuilder1.selectedTrayPrinter1(((JFXRadioButton) printer1InputTrayTGTRWorking.
                        getSelectedToggle()).getId());
            }
            if ((printer2InputTrayTGTRWorking.getSelectedToggle() != null)) {
                printSettingsBuilder1.selectedTrayPrinter2(((JFXRadioButton) printer2InputTrayTGTRWorking.
                        getSelectedToggle()).getId());
            }


            if (separateFlowRadioBtn.isSelected()) {
                LOGGER.info("separate flow radio button is currently selected");
                printSettingsBuilder2
                        .defaultLabel1(label1ComboBoxTRFailed.getSelectionModel().getSelectedItem())
                        .defaultPrinter1(printer1ComboBoxTRFailed.getSelectionModel().getSelectedItem())
                        .defaultLabel2(label2ComboBoxTRFailed.getSelectionModel().getSelectedItem())
                        .defaultPrinter2(printer2ComboBoxTRFailed.getSelectionModel().getSelectedItem())
                        .isDymoTwinTurboPrinter1(printer1DymoTwinTurboCBTRFailed.isSelected())
                        .isDymoTwinTurboPrinter2(printer2DymoTwinTurboCBTRFailed.isSelected())
                        .selectedPaperType1(paperType1ComboBoxTRFailed.getSelectionModel().getSelectedItem())
                        .selectedPaperType2(paperType2ComboBoxTRFailed.getSelectionModel().getSelectedItem());

                if ((printer1RollTypeTGTRFailed.getSelectedToggle() != null)) {
                    printSettingsBuilder2.rollTypePrinter1(((JFXRadioButton) printer1RollTypeTGTRFailed.
                            getSelectedToggle()).getId());
                }
                if ((printer2RollTypeTGTRFailed.getSelectedToggle() != null)) {
                    printSettingsBuilder2.rollTypePrinter2(((JFXRadioButton) printer2RollTypeTGTRFailed.
                            getSelectedToggle()).getId());
                }
                if ((printer1InputTrayTGTRFailed.getSelectedToggle() != null)) {
                    printSettingsBuilder2.selectedTrayPrinter1(((JFXRadioButton) printer1InputTrayTGTRFailed.
                            getSelectedToggle()).getId());
                }
                if ((printer2InputTrayTGTRFailed.getSelectedToggle() != null)) {
                    printSettingsBuilder2.selectedTrayPrinter2(((JFXRadioButton) printer2InputTrayTGTRFailed.
                            getSelectedToggle()).getId());
                }
                labelWorkFlowPrintSettingsBuilder.printerSettingsWhenWorking(printSettingsBuilder1.build());
                labelWorkFlowPrintSettingsBuilder.printerSettingsWhenFailed(printSettingsBuilder2.build());
                LOGGER.info("Passed and Failed print settings are saved");
            } else if (oneFlowRadioBtn.isSelected()) {
                LOGGER.info("one flow radio button is currently selected");
                labelWorkFlowPrintSettingsBuilder.oneFlowPrinterSettings(printSettingsBuilder1.build());
            }
        } else {
            labelWorkFlowPrintSettingsBuilder.labelWorkflowEnabled(false);
        }

        LocalCustomizations.LocalCustomizationsBuilder customizationsBuilder = LocalCustomizations.builder()
                .userId(loggedInUserId)
                .defaultLabel1(label1ComboBox.getSelectionModel().getSelectedItem())
                .defaultLabel2(label2ComboBox.getSelectionModel().getSelectedItem())
                .defaultPrinter1(printer1ComboBox.getSelectionModel().getSelectedItem())
                .defaultPrinter2(printer2ComboBox.getSelectionModel().getSelectedItem())
                .isDymoTwinTurboPrinter1(printer1DymoTwinTurboCB.isSelected())
                .isDymoTwinTurboPrinter2(printer2DymoTwinTurboCB.isSelected())
                .enableDevMode(enableDevModeSetting.isSelected())
                .selectedPaperType2(paperType2ComboBox.getSelectionModel().getSelectedItem())
                .selectedPaperType1(paperType1ComboBox.getSelectionModel().getSelectedItem())
                .enableAutoOpenIosApp(enableAutoOpenIosApp.isSelected())
                .restoreThreads(StringUtils.isNotBlank(restoreThreadTxtField.getText()) ?
                        Integer.parseInt(restoreThreadTxtField.getText()) : 0)
                // restore delay will be 10 seconds by default left empty
                .restoreDelay(StringUtils.isNotBlank(restoreDelayTxtField.getText()) ?
                        Integer.parseInt(restoreDelayTxtField.getText()) : 10)
                .autoExport(autoExportCheckBox.isSelected())
                .exportFormatJson(jsonCheckBox.isSelected())
                .exportFormatXml(xmlCheckBox.isSelected())
                .exportOnSuccessfulErase(successfulEraseCheckBox.isSelected())
                .exportOnAppResults(onAppResultsCheckBox.isSelected())
                .exportFilePath(filePathTextField.getText())
                .labelWorkFlowPrintSettings(labelWorkFlowPrintSettingsBuilder.build())
                .fetchXiaomiAlternateSerial(fetchXiaomiAlternateSerial.isSelected())
                .ignoreNaForOem(ignoreNaForOem.isSelected())
                .shopfloorSettings(LocalCustomizations.ShopfloorSettings
                        .builder()
                        .selectedImageGrades(shopfloorGradeCheckComboBox.getCheckModel().getCheckedItems())
                        .imageGradeFolderPath(imageGradeFolderPathTextField.getText())
                        .selectedCamera(cameraListComboBox.getSelectionModel().getSelectedItem())
                        .build());
        if ((printer1RollTypeTG.getSelectedToggle() != null)) {
            customizationsBuilder.rollTypePrinter1(((JFXRadioButton) printer1RollTypeTG.getSelectedToggle())
                    .getId());
        }
        if ((printer2RollTypeTG.getSelectedToggle() != null)) {
            customizationsBuilder.rollTypePrinter2(((JFXRadioButton) printer2RollTypeTG.getSelectedToggle())
                    .getId());
        }
        if ((printer1InputTrayTG.getSelectedToggle() != null)) {
            customizationsBuilder.selectedTrayPrinter1(((JFXRadioButton) printer1InputTrayTG.getSelectedToggle())
                    .getId());
        }
        if ((printer2InputTrayTG.getSelectedToggle() != null)) {
            customizationsBuilder.selectedTrayPrinter2(((JFXRadioButton) printer2InputTrayTG.getSelectedToggle())
                    .getId());
        }

        uiInMemoryStore.setCustomizations(customizationsBuilder.build());
        LOGGER.info("customization updated in ui in memory store");
    }

    /**
     * Initialize the UI elements
     *
     * @param location  The location used to resolve relative paths for the root object, or
     *                  {@code null} if the location is not known.
     * @param resources The resources used to localize the root object, or {@code null} if
     *                  the root object was not localized.
     */
    @Override
    public void initialize(final URL location, final ResourceBundle resources) {
        String printerListTopic = TopicBuilder.buildGenericTopic("printer", "list", "request");
        final PrintersListRequestMessage printersListRequestMessage = new PrintersListRequestMessage();
        publishToMqttTopic(printerListTopic, printersListRequestMessage);

        String cameraListTopic = TopicBuilder.buildGenericTopic("camera", "list", "request");
        final CameraListRequestMessage cameraListRequestMessage = new CameraListRequestMessage();
        publishToMqttTopic(cameraListTopic, cameraListRequestMessage);

        labelWorkFlowPrintSettings = uiInMemoryStore.getCustomizations().getLabelWorkFlowPrintSettings();
        // Initialize label list
        final List<String> labelsList = Arrays.stream(LabelVariant.values())
                .map(LabelVariant::getName).toList();
        List<String> allLabelsList = new ArrayList<>();
        allLabelsList.add(CustomizationConstants.NONE);
        allLabelsList.addAll(labelsList);

        //Add labels from cloud
        if (uiInMemoryStore.getLabelDataMap() != null) {
            List<String> cloudLabels = uiInMemoryStore.getLabelDataMap().keySet().stream().toList();
            allLabelsList.addAll(cloudLabels);
        }

        final ObservableList<String> observableLabelsList = FXCollections.observableArrayList(allLabelsList);
        final ObservableList<String> paperTypeList = FXCollections.observableArrayList(
                List.of(BrotherPaperType.MONO.getDisplayName(), BrotherPaperType.BLACKRED.getDisplayName()));

        LocalCustomizations localCustomizations = uiInMemoryStore.getCustomizations();

        enableAutoOpenIosApp.setSelected(localCustomizations.isEnableAutoOpenIosApp());
        if (StringUtils.isNotBlank(loggedInUserId)) {
            LOGGER.info("Received customizations for user: {}", loggedInUserId);
            Runnable runnable = () -> {
                setLabelFlowRadioButtonActions();
                paperType1ComboBox.setItems(paperTypeList);
                paperType2ComboBox.setItems(paperTypeList);
                paperType1ComboBoxTRWorking.setItems(paperTypeList);
                paperType2ComboBoxTRWorking.setItems(paperTypeList);
                paperType1ComboBoxTRFailed.setItems(paperTypeList);
                paperType2ComboBoxTRFailed.setItems(paperTypeList);

                paperType1ComboBox.setValue(localCustomizations.getSelectedPaperType1() != null ?
                        localCustomizations.getSelectedPaperType1() : BrotherPaperType.MONO.getDisplayName());
                paperType2ComboBox.setValue(localCustomizations.getSelectedPaperType2() != null ?
                        localCustomizations.getSelectedPaperType2() : BrotherPaperType.MONO.getDisplayName());

                setLabelWorkFlowPrintSettingsOnUI(true);
                handleLabelWorkFlowToggle();
                label1ComboBox.setItems(observableLabelsList);
                label2ComboBox.setItems(observableLabelsList);
                label1ComboBoxTRWorking.setItems(observableLabelsList);
                label2ComboBoxTRWorking.setItems(observableLabelsList);
                label1ComboBoxTRFailed.setItems(observableLabelsList);
                label2ComboBoxTRFailed.setItems(observableLabelsList);

                label1ComboBox.setValue(localCustomizations.getDefaultLabel1());
                label2ComboBox.setValue(localCustomizations.getDefaultLabel2());

                printer1ComboBox.setValue(localCustomizations.getDefaultPrinter1());
                if (localCustomizations.getDefaultPrinter1() != null) {
                    if (Printer.BROTHER.equals(Printer.getByName(localCustomizations.getDefaultPrinter1()))) {
                        managePrinterProperties(printer1RollTypeHBox, printer1TwinTurboHBox,
                                printer1PaperTypeHBox, true, false);
                    } else if (Printer.DYMO.equals(Printer.getByName(localCustomizations.getDefaultPrinter1()))) {
                        managePrinterProperties(printer1RollTypeHBox, printer1TwinTurboHBox,
                                printer1PaperTypeHBox, false, true);
                    }
                }

                if (StringUtils.isNotBlank(localCustomizations.getRollTypePrinter1())) {
                    if (StringUtils.containsIgnoreCase(BrotherRollType.LABEL_ID_P1.getValue(),
                            localCustomizations.getRollTypePrinter1())) {
                        printer1RollLabelRB.setSelected(true);
                    } else if (StringUtils.containsIgnoreCase(BrotherRollType.TAPE_ID_P1.getValue(),
                            localCustomizations.getRollTypePrinter1())) {
                        printer1RollTapeRB.setSelected(true);
                    }
                } else {
                    printer1RollLabelRB.setSelected(true);
                }

                if (StringUtils.isNotBlank(localCustomizations.getSelectedTrayPrinter1())) {
                    if (StringUtils.containsIgnoreCase(DymoPaperTray.LEFT_P1.getId(),
                            localCustomizations.getSelectedTrayPrinter1())) {
                        printer1InputTrayLB.setSelected(true);
                    } else if (StringUtils.containsIgnoreCase(DymoPaperTray.RIGHT_P1.getId(),
                            localCustomizations.getSelectedTrayPrinter1())) {
                        printer1InputTrayRB.setSelected(true);
                    }
                }

                printer2ComboBox.setValue(localCustomizations.getDefaultPrinter2());
                if (localCustomizations.getDefaultPrinter2() != null) {
                    if (Printer.BROTHER.equals(Printer.getByName(localCustomizations.getDefaultPrinter2()))) {
                        managePrinterProperties(printer2RollTypeHBox,
                                printer2TwinTurboHBox, printer2PaperTypeHBox, true, false);
                    } else if (Printer.DYMO.equals(Printer.getByName(localCustomizations.getDefaultPrinter2()))) {
                        managePrinterProperties(printer2RollTypeHBox, printer2TwinTurboHBox,
                                printer2PaperTypeHBox, false, true);
                    }
                }

                if (StringUtils.isNotBlank(localCustomizations.getRollTypePrinter2())) {
                    if (StringUtils.containsIgnoreCase(BrotherRollType.LABEL_ID_P2.getValue(),
                            localCustomizations.getRollTypePrinter2())) {
                        printer2RollLabelRB.setSelected(true);
                    } else if (StringUtils.containsIgnoreCase(BrotherRollType.TAPE_ID_P2.getValue(),
                            localCustomizations.getRollTypePrinter2())) {
                        printer2RollTapeRB.setSelected(true);
                    }
                } else {
                    printer2RollLabelRB.setSelected(true);
                }

                if (StringUtils.isNotBlank(localCustomizations.getSelectedTrayPrinter2())) {
                    if (StringUtils.containsIgnoreCase(DymoPaperTray.LEFT_P2.getId(),
                            localCustomizations.getSelectedTrayPrinter2())) {
                        printer2InputTrayLB.setSelected(true);
                    } else if (StringUtils.containsIgnoreCase(DymoPaperTray.RIGHT_P2.getId(),
                            localCustomizations.getSelectedTrayPrinter2())) {
                        printer2InputTrayRB.setSelected(true);
                    }
                }

                printer1ComboBox.getSelectionModel()
                        .selectedItemProperty().addListener(setPrinterComboBoxChangeListener(true,
                                printer1RollTypeHBox, printer1TwinTurboHBox, printer1PaperTypeHBox,
                                printer2RollTypeHBox, printer2TwinTurboHBox, printer2PaperTypeHBox));
                printer2ComboBox.getSelectionModel()
                        .selectedItemProperty().addListener(setPrinterComboBoxChangeListener(false,
                                printer1RollTypeHBox, printer1TwinTurboHBox, printer1PaperTypeHBox,
                                printer2RollTypeHBox, printer2TwinTurboHBox, printer2PaperTypeHBox));

                enableDevModeSetting.setSelected(localCustomizations.isEnableDevMode());
                enableAutoOpenIosApp.setSelected(localCustomizations.isEnableAutoOpenIosApp());
                fetchXiaomiAlternateSerial.setSelected(localCustomizations.isFetchXiaomiAlternateSerial());
                if (!localCustomizations.isAutoExport()) {
                    filePathTextField.setDisable(true);
                    browseButton.setDisable(true);
                }
                autoExportCheckBox.setSelected(localCustomizations.isAutoExport());
                jsonCheckBox.setSelected(localCustomizations.isExportFormatJson());
                xmlCheckBox.setSelected(localCustomizations.isExportFormatXml());
                successfulEraseCheckBox.setSelected(localCustomizations.isExportOnSuccessfulErase());
                onAppResultsCheckBox.setSelected(localCustomizations.isExportOnAppResults());
                filePathTextField.setText(localCustomizations.getExportFilePath());
                if (localCustomizations.getShopfloorSettings() != null) {
                    imageGradeFolderPathTextField.setText(
                            localCustomizations.getShopfloorSettings().getImageGradeFolderPath());
                }

                printer1DymoTwinTurboCB.selectedProperty().addListener((observable, oldValue, newValue) -> {
                    printer1InputTrayHBox.setVisible(newValue);
                    printer1InputTrayHBox.setManaged(newValue);
                });

                printer2DymoTwinTurboCB.selectedProperty().addListener((observable, oldValue, newValue) -> {
                    printer2InputTrayHBox.setVisible(newValue);
                    printer2InputTrayHBox.setManaged(newValue);
                });

                printer1DymoTwinTurboCB.setSelected(localCustomizations.isDymoTwinTurboPrinter1());
                printer2DymoTwinTurboCB.setSelected(localCustomizations.isDymoTwinTurboPrinter2());

                if (localCustomizations.getShopfloorSettings() != null &&
                        StringUtils.isNotEmpty(localCustomizations.getShopfloorSettings().getSelectedCamera())) {
                    cameraListComboBox.setValue(localCustomizations.getShopfloorSettings().getSelectedCamera());
                }

                hideLoaderView();
            };
            runOnFxThread(runnable);
        }
        if (mainTabPane != null) {
            // Add a listener to the tab selection model
            mainTabPane.getSelectionModel().selectedItemProperty().addListener((observable, oldTab, selectedTab)
                    -> {
                // Hide the saveCustomizationHbox for the "Firmware" tab
                final String firmwareTxt = getLocalizationService().getLanguageSpecificText("firmware");
                if (selectedTab != null && selectedTab.getText().equalsIgnoreCase(firmwareTxt)) {
                    boolean hideButton = selectedTab.getId().equals("firmware");
                    saveCustomizationHbox.setVisible(!hideButton);
                    saveCustomizationHbox.setManaged(!hideButton);
                    loadFirmwareTabContent();
                } else {
                    saveCustomizationHbox.setVisible(true);
                    saveCustomizationHbox.setManaged(true);
                }
            });
        }

        restoreThreadTxtField.setText(String.valueOf(localCustomizations.getRestoreThreads()));
        restoreThreadTxtField.setOnKeyTyped(event -> {
            if (!event.getCharacter().matches("[0-9]")) {
                event.consume();  // Ignore non-numeric input
            }
        });
        restoreDelayTxtField.setText(String.valueOf(localCustomizations.getRestoreDelay()));
        restoreDelayTxtField.setOnKeyTyped(event -> {
            if (!event.getCharacter().matches("[0-9]")) {
                event.consume();  // Ignore non-numeric input
            }
        });

        // Add listeners for enabling/disabling checkboxes
        ignoreNaForOem.setSelected(localCustomizations.isIgnoreNaForOem());
        autoExportCheckBox.selectedProperty().addListener((observable, oldValue, newValue) -> {
            jsonCheckBox.setDisable(!newValue);
            xmlCheckBox.setDisable(!newValue);
            browseButton.setDisable(!newValue);
            filePathTextField.setDisable(!newValue);
            successfulEraseCheckBox.setDisable(!newValue);
            onAppResultsCheckBox.setDisable(!newValue);
        });

        // Initialize the state based on the autoExportCheckBox
        boolean isAutoExportEnabled = autoExportCheckBox.isSelected();
        jsonCheckBox.setDisable(!isAutoExportEnabled);
        xmlCheckBox.setDisable(!isAutoExportEnabled);
        successfulEraseCheckBox.setDisable(!isAutoExportEnabled);
        onAppResultsCheckBox.setDisable(!isAutoExportEnabled);

        browseButton.setOnAction(event -> openDirectoryChooser());

        if (uiInMemoryStore.getShopfloorGrades() != null && !uiInMemoryStore.getShopfloorGrades().isEmpty()) {
            shopfloorGradeCheckComboBox.getItems().add(CustomizationConstants.ANY_GRADE);
            shopfloorGradeCheckComboBox.getItems().addAll(
                    FXCollections.observableArrayList(uiInMemoryStore.getShopfloorGrades()));

            // Check mark all shopfloor grades which were previously saved in localCustomization
            if (localCustomizations.getShopfloorSettings() != null &&
                    localCustomizations.getShopfloorSettings().getSelectedImageGrades() != null &&
                    !localCustomizations.getShopfloorSettings().getSelectedImageGrades().isEmpty()) {

                List<String> checkedShopfloorGrades = ListUtils.intersection(
                        shopfloorGradeCheckComboBox.getItems(),
                        localCustomizations.getShopfloorSettings().getSelectedImageGrades()
                );
                checkedShopfloorGrades.forEach(checkedGrade ->
                        shopfloorGradeCheckComboBox.getCheckModel().check(checkedGrade));
            }
        }
        imageGradeBrowseLocationButton.setOnAction(event -> openGradeImageDirectoryChooser());

        // Add listeners to make JSON and XML checkboxes behave like radio buttons
        jsonCheckBox.selectedProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue) {
                xmlCheckBox.setSelected(false);
            }
        });

        xmlCheckBox.selectedProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue) {
                jsonCheckBox.setSelected(false);
            }
        });
    }

    /**
     * This method handles the UI actions when radio buttons for one flow
     * and separate flow are switched.
     */
    private void setLabelFlowRadioButtonActions() {
        // Create a ToggleGroup and add each RadioButton to it
        ToggleGroup radioGroup = new ToggleGroup();
        oneFlowRadioBtn.setToggleGroup(radioGroup);
        separateFlowRadioBtn.setToggleGroup(radioGroup);
        // Add a listener to print the selected RadioButton's text
        radioGroup.selectedToggleProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue != null && newValue.toString().contains(CustomizationConstants.SEPARATE)) {
                labelForWorkingSelection.setVisible(true);
                labelForFailedSelection.setVisible(true);
                trFailedVBox.setVisible(true);
            } else if (newValue != null && newValue.toString().contains(CustomizationConstants.ONE_FLOW)) {
                labelForWorkingSelection.setVisible(false);
                labelForFailedSelection.setVisible(false);
                trFailedVBox.setVisible(false);
            }
            setLabelWorkFlowPrintSettingsOnUI(false);
        });
    }


    private void openDirectoryChooser() {
        DirectoryChooser directoryChooser = new DirectoryChooser();
        directoryChooser.setTitle(SELECT_EXPORT_DIRECTORY);
        // if path is pre-populated then reopen that folder
        if (StringUtils.isNotBlank(filePathTextField.getText())) {
            File directoryLocation = new File(filePathTextField.getText());
            if (directoryLocation.exists()) {
                directoryChooser.setInitialDirectory(new File(filePathTextField.getText()));
            }
        }
        File selectedDirectory = directoryChooser.showDialog(window);

        if (selectedDirectory != null) {
            filePathTextField.setText(selectedDirectory.getAbsolutePath());
        }
    }

    private void openGradeImageDirectoryChooser() {
        DirectoryChooser directoryChooser = new DirectoryChooser();
        directoryChooser.setTitle(SELECT_EXPORT_DIRECTORY);
        // if path is pre-populated then reopen that folder
        if (StringUtils.isNotBlank(imageGradeFolderPathTextField.getText())) {
            File directoryLocation = new File(imageGradeFolderPathTextField.getText());
            if (directoryLocation.exists()) {
                directoryChooser.setInitialDirectory(new File(imageGradeFolderPathTextField.getText()));
            }
        }
        File selectedDirectory = directoryChooser.showDialog(window);

        if (selectedDirectory != null) {
            imageGradeFolderPathTextField.setText(selectedDirectory.getAbsolutePath());
        }
    }

    /**
     * After successfully load content from backend,
     * hide loader view and show customizations view
     */
    private void hideLoaderView() {
        mainViewCustomization.setVisible(true);
        loaderViewCustomization.setVisible(false);
        loaderViewCustomization.setManaged(false);
    }

    /**
     * Loads and sets the content of the firmware tab from an FXML file
     */
    private void loadFirmwareTabContent() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource(FIRMWARE_FXML),
                    getLocalizationService().getResourceBundle());
            FirmwareDownloaderController controller =
                    getApplicationContext().getBean(FirmwareDownloaderController.class);
            loader.setController(controller);
            final Pane parent = loader.load();
            firmware.setContent(parent);
        } catch (Exception e) {
            LOGGER.error("Error while loading fxml file", e);
        }
    }

    /**
     * hide and show further customizations for printer
     *
     * @param printerRollTypeHBox
     * @param printerTwinTurboHBox
     * @param printerPaperTypeHBox
     * @param visibleRollType      show/hide roll type options
     * @param visibleTwinTurbo     show/hide twin turbo options
     */
    private void managePrinterProperties(final HBox printerRollTypeHBox, final HBox printerTwinTurboHBox,
                                         final HBox printerPaperTypeHBox,
                                         final boolean visibleRollType, final boolean visibleTwinTurbo) {
        printerRollTypeHBox.setVisible(visibleRollType);
        printerRollTypeHBox.setManaged(visibleRollType);
        printerTwinTurboHBox.setVisible(visibleTwinTurbo);
        printerTwinTurboHBox.setManaged(visibleTwinTurbo);
        printerPaperTypeHBox.setVisible(visibleRollType);
        printerPaperTypeHBox.setManaged(visibleRollType);
    }

    /**
     * Printer combo boxes change listeners
     *
     * @param isPrimaryPrinter      flag to check if listener is required for primary printer
     * @param printer1RollTypeHBox
     * @param printer1TwinTurboHBox
     * @param printer1PaperTypeHBox
     * @param printer2RollTypeHBox
     * @param printer2TwinTurboHBox
     * @param printer2PaperTypeHBox
     * @return change listener
     */
    private ChangeListener<String> setPrinterComboBoxChangeListener(final boolean isPrimaryPrinter,
                                                                    final HBox printer1RollTypeHBox,
                                                                    final HBox printer1TwinTurboHBox,
                                                                    final HBox printer1PaperTypeHBox,
                                                                    final HBox printer2RollTypeHBox,
                                                                    final HBox printer2TwinTurboHBox,
                                                                    final HBox printer2PaperTypeHBox) {
        return (observable, oldValue, newValue) -> {
            if (StringUtils.isNotBlank(newValue)) {
                if (!newValue.equals(oldValue)) {
                    if (Printer.BROTHER.equals(Printer.getByName(newValue))) {
                        if (isPrimaryPrinter) {
                            managePrinterProperties(printer1RollTypeHBox, printer1TwinTurboHBox,
                                    printer1PaperTypeHBox, true, false);
                        } else {
                            managePrinterProperties(printer2RollTypeHBox, printer2TwinTurboHBox,
                                    printer2PaperTypeHBox, true, false);
                        }
                    } else if (Printer.DYMO.equals(Printer.getByName(newValue))) {
                        if (isPrimaryPrinter) {
                            managePrinterProperties(printer1RollTypeHBox, printer1TwinTurboHBox,
                                    printer1PaperTypeHBox, false, true);
                        } else {
                            managePrinterProperties(printer2RollTypeHBox, printer2TwinTurboHBox,
                                    printer2PaperTypeHBox, false, true);
                        }
                    } else {
                        if (isPrimaryPrinter) {
                            managePrinterProperties(printer1RollTypeHBox, printer1TwinTurboHBox,
                                    printer1PaperTypeHBox, false, false);
                        } else {
                            managePrinterProperties(printer2RollTypeHBox, printer2TwinTurboHBox,
                                    printer2PaperTypeHBox, false, false);
                        }
                    }
                }
            }
        };
    }


    private void setPrintProperties(final HBox printerRollTypeHBox,
                                    final HBox printerTwinTurboHBox, final HBox printerPaperTypeHBox,
                                    final JFXRadioButton printerRollLabelRB, final JFXRadioButton printerRollTapeRB,
                                    final JFXRadioButton printerInputTrayLB, final JFXRadioButton printerInputTrayRB,
                                    final String defaultPrinter, final String rollTypePrinter,
                                    final String selectedTrayPrinter) {
        if (defaultPrinter != null) {
            if (Printer.BROTHER.equals(Printer.getByName(defaultPrinter))) {
                managePrinterProperties(printerRollTypeHBox,
                        printerTwinTurboHBox, printerPaperTypeHBox,
                        true, false);
            } else if (Printer.DYMO.equals(Printer.getByName(defaultPrinter))) {
                managePrinterProperties(printerRollTypeHBox,
                        printerTwinTurboHBox, printerPaperTypeHBox, false, true);
            }
        }

        if (StringUtils.isNotBlank(rollTypePrinter)) {
            if (StringUtils.containsIgnoreCase(printerRollLabelRB.getId(),
                    rollTypePrinter)) {
                printerRollLabelRB.setSelected(true);
            } else if (StringUtils.containsIgnoreCase(printerRollTapeRB.getId(),
                    rollTypePrinter)) {
                printerRollTapeRB.setSelected(true);
            }
        } else {
            printerRollLabelRB.setSelected(true);
        }

        if (StringUtils.isNotBlank(selectedTrayPrinter)) {
            if (StringUtils.containsIgnoreCase(printerInputTrayLB.getId(),
                    selectedTrayPrinter)) {
                printerInputTrayLB.setSelected(true);
            } else if (StringUtils.containsIgnoreCase(printerInputTrayRB.getId(),
                    selectedTrayPrinter)) {
                printerInputTrayRB.setSelected(true);
            }
        }
    }


    /**
     * This method with set label flow printer and label settings on UI
     *
     * @param isInitialize true when values to be set during initialize
     *                     false when values to be set while switching b/w
     *                     one flow and separate flow settings
     */
    private void setLabelWorkFlowPrintSettingsOnUI(final boolean isInitialize) {

        LocalCustomizations localCustomizations = uiInMemoryStore.getCustomizations();
        labelWorkFlowPrintSettings = localCustomizations.getLabelWorkFlowPrintSettings();

        if (labelWorkFlowPrintSettings != null) {
            //During initialize, set the state of label work flow toggle and workflow settings
            if (isInitialize) {
                labelWorkFlowToggle.setSelected(labelWorkFlowPrintSettings.isLabelWorkflowEnabled());
                oneFlowRadioBtn.setSelected(labelWorkFlowPrintSettings.isOneFlowForAllTestResults());
                separateFlowRadioBtn.setSelected(!labelWorkFlowPrintSettings.isOneFlowForAllTestResults());
            }

            //if user selection is one flow for all test results
            if (oneFlowRadioBtn.isSelected()) {
                PrintSettings oneFlowPrintSettings = labelWorkFlowPrintSettings.getOneFlowPrinterSettings();
                printer1ComboBoxTRWorking.getSelectionModel()
                        .selectedItemProperty().addListener(setPrinterComboBoxChangeListener(true,
                                printer1RollTypeHBoxTRWorking, printer1TwinTurboHBoxTRWorking,
                                printer1PaperTypeHBoxTRWorking, printer2RollTypeHBoxTRWorking,
                                printer2TwinTurboHBoxTRWorking, printer2PaperTypeHBoxTRWorking));
                printer2ComboBoxTRWorking.getSelectionModel()
                        .selectedItemProperty().addListener(setPrinterComboBoxChangeListener(false,
                                printer1RollTypeHBoxTRWorking, printer1TwinTurboHBoxTRWorking,
                                printer1PaperTypeHBoxTRWorking, printer2RollTypeHBoxTRWorking,
                                printer2TwinTurboHBoxTRWorking, printer2PaperTypeHBoxTRWorking));

                printer1DymoTwinTurboCBTRWorking.selectedProperty().addListener((observable, oldValue, newValue) -> {
                    printer1InputTrayHBoxTRWorking.setVisible(newValue);
                    printer1InputTrayHBoxTRWorking.setManaged(newValue);
                });

                printer2DymoTwinTurboCBTRWorking.selectedProperty().addListener((observable, oldValue, newValue) -> {
                    printer2InputTrayHBoxTRWorking.setVisible(newValue);
                    printer2InputTrayHBoxTRWorking.setManaged(newValue);
                });

                if (oneFlowPrintSettings != null) {
                    paperType1ComboBoxTRWorking.setValue(oneFlowPrintSettings.getSelectedPaperType1() != null ?
                            oneFlowPrintSettings.getSelectedPaperType1() : BrotherPaperType.MONO.getDisplayName());
                    paperType2ComboBoxTRWorking.setValue(oneFlowPrintSettings.getSelectedPaperType2() != null ?
                            oneFlowPrintSettings.getSelectedPaperType2() : BrotherPaperType.MONO.getDisplayName());

                    label1ComboBoxTRWorking.setValue(oneFlowPrintSettings.getDefaultLabel1());
                    label2ComboBoxTRWorking.setValue(oneFlowPrintSettings.getDefaultLabel2());

                    printer1ComboBoxTRWorking.setValue(oneFlowPrintSettings.getDefaultPrinter1());
                    setPrintProperties(printer1RollTypeHBoxTRWorking, printer1TwinTurboHBoxTRWorking,
                            printer1PaperTypeHBoxTRWorking, printer1RollLabelRBTRWorking, printer1RollTapeRBTRWorking,
                            printer1InputTrayLBTRWorking, printer1InputTrayRBTRWorking,
                            oneFlowPrintSettings.getDefaultPrinter1(), oneFlowPrintSettings.getRollTypePrinter1(),
                            oneFlowPrintSettings.getSelectedTrayPrinter1());

                    printer2ComboBoxTRWorking.setValue(oneFlowPrintSettings.getDefaultPrinter2());
                    setPrintProperties(printer2RollTypeHBoxTRWorking, printer2TwinTurboHBoxTRWorking,
                            printer2PaperTypeHBoxTRWorking, printer2RollLabelRBTRWorking, printer2RollTapeRBTRWorking,
                            printer2InputTrayLBTRWorking, printer2InputTrayRBTRWorking,
                            oneFlowPrintSettings.getDefaultPrinter2(), oneFlowPrintSettings.getDefaultLabel2(),
                            oneFlowPrintSettings.getSelectedTrayPrinter2());

                    printer1DymoTwinTurboCBTRWorking.setSelected(oneFlowPrintSettings.isDymoTwinTurboPrinter1());
                    printer2DymoTwinTurboCBTRWorking.setSelected(oneFlowPrintSettings.isDymoTwinTurboPrinter2());
                } else {
                    /*
                       If print settings for one flow were never set,
                       make sure they are displayed empty
                     */
                    paperType1ComboBoxTRWorking.setValue(StringUtils.EMPTY);
                    paperType2ComboBoxTRWorking.setValue(StringUtils.EMPTY);

                    label1ComboBoxTRWorking.setValue(StringUtils.EMPTY);
                    label2ComboBoxTRWorking.setValue(StringUtils.EMPTY);

                    printer1ComboBoxTRWorking.setValue(StringUtils.EMPTY);

                    printer2ComboBoxTRWorking.setValue(StringUtils.EMPTY);

                    printer1TwinTurboHBoxTRWorking.setVisible(false);
                    printer2TwinTurboHBoxTRWorking.setVisible(false);
                    printer1TwinTurboHBoxTRWorking.setManaged(false);
                    printer2TwinTurboHBoxTRWorking.setManaged(false);

                }

            }
            //if user selection is separate flow for all test results
            if (separateFlowRadioBtn.isSelected()) {

                PrintSettings printerSettingsWhenWorking = labelWorkFlowPrintSettings.getPrinterSettingsWhenWorking();
                PrintSettings printerSettingsWhenFailed = labelWorkFlowPrintSettings.getPrinterSettingsWhenFailed();

                /*
                     Listeners for when printer selection for test results working
                     is changed, dynamically show corresponding UI elements.
                 */
                printer1ComboBoxTRWorking.getSelectionModel()
                        .selectedItemProperty().addListener(setPrinterComboBoxChangeListener(true,
                                printer1RollTypeHBoxTRWorking, printer1TwinTurboHBoxTRWorking,
                                printer1PaperTypeHBoxTRWorking, printer2RollTypeHBoxTRWorking,
                                printer2TwinTurboHBoxTRWorking, printer2PaperTypeHBoxTRWorking));
                printer2ComboBoxTRWorking.getSelectionModel()
                        .selectedItemProperty().addListener(setPrinterComboBoxChangeListener(false,
                                printer1RollTypeHBoxTRWorking, printer1TwinTurboHBoxTRWorking,
                                printer1PaperTypeHBoxTRWorking, printer2RollTypeHBoxTRWorking,
                                printer2TwinTurboHBoxTRWorking, printer2PaperTypeHBoxTRWorking));

                printer1DymoTwinTurboCBTRWorking.selectedProperty().addListener((observable, oldValue, newValue) -> {
                    printer1InputTrayHBoxTRWorking.setVisible(newValue);
                    printer1InputTrayHBoxTRWorking.setManaged(newValue);
                });

                printer2DymoTwinTurboCBTRWorking.selectedProperty().addListener((observable, oldValue, newValue) -> {
                    printer2InputTrayHBoxTRWorking.setVisible(newValue);
                    printer2InputTrayHBoxTRWorking.setManaged(newValue);
                });


                if (printerSettingsWhenWorking != null) {
                    paperType1ComboBoxTRWorking.setValue(printerSettingsWhenWorking.getSelectedPaperType1() != null ?
                            printerSettingsWhenWorking.getSelectedPaperType1() :
                            BrotherPaperType.MONO.getDisplayName());
                    paperType2ComboBoxTRWorking.setValue(printerSettingsWhenWorking.getSelectedPaperType2() != null ?
                            printerSettingsWhenWorking.getSelectedPaperType2() :
                            BrotherPaperType.MONO.getDisplayName());

                    //set the values for label1, label2
                    label1ComboBoxTRWorking.setValue(printerSettingsWhenWorking.getDefaultLabel1());
                    label2ComboBoxTRWorking.setValue(printerSettingsWhenWorking.getDefaultLabel2());

                    //set the values for printer1, printer2
                    printer1ComboBoxTRWorking.setValue(printerSettingsWhenWorking.getDefaultPrinter1());
                    printer2ComboBoxTRWorking.setValue(printerSettingsWhenWorking.getDefaultPrinter2());

                    setPrintProperties(printer1RollTypeHBoxTRWorking, printer1TwinTurboHBoxTRWorking,
                            printer1PaperTypeHBoxTRWorking, printer1RollLabelRBTRWorking, printer1RollTapeRBTRWorking,
                            printer1InputTrayLBTRWorking, printer1InputTrayRBTRWorking,
                            printerSettingsWhenWorking.getDefaultPrinter1(),
                            printerSettingsWhenWorking.getRollTypePrinter1(),
                            printerSettingsWhenWorking.getSelectedTrayPrinter1());

                    setPrintProperties(printer2RollTypeHBoxTRWorking, printer2TwinTurboHBoxTRWorking,
                            printer2PaperTypeHBoxTRWorking, printer2RollLabelRBTRWorking, printer2RollTapeRBTRWorking,
                            printer2InputTrayLBTRWorking, printer2InputTrayRBTRWorking,
                            printerSettingsWhenWorking.getDefaultPrinter2(),
                            printerSettingsWhenWorking.getRollTypePrinter2(),
                            printerSettingsWhenWorking.getSelectedTrayPrinter2());

                    //when dymo printer is selected, checkbox is displayed and it's value is set here
                    printer1DymoTwinTurboCBTRWorking.setSelected(printerSettingsWhenWorking.isDymoTwinTurboPrinter1());
                    printer2DymoTwinTurboCBTRWorking.setSelected(printerSettingsWhenWorking.isDymoTwinTurboPrinter2());
                } else {
                    /*
                       If print settings for test results working flow were never set,
                       make sure they are displayed empty
                     */
                    paperType1ComboBoxTRWorking.setValue(StringUtils.EMPTY);
                    paperType2ComboBoxTRWorking.setValue(StringUtils.EMPTY);

                    label1ComboBoxTRWorking.setValue(StringUtils.EMPTY);
                    label2ComboBoxTRWorking.setValue(StringUtils.EMPTY);

                    printer1ComboBoxTRWorking.setValue(StringUtils.EMPTY);
                    printer2ComboBoxTRWorking.setValue(StringUtils.EMPTY);

                    printer1TwinTurboHBoxTRWorking.setVisible(false);
                    printer2TwinTurboHBoxTRWorking.setVisible(false);
                    printer1TwinTurboHBoxTRWorking.setManaged(false);
                    printer2TwinTurboHBoxTRWorking.setManaged(false);
                }


                /*
                     Listeners for when printer selection for test results failed
                     is changed, dynamically show corresponding UI elements.
                 */
                printer1ComboBoxTRFailed.getSelectionModel()
                        .selectedItemProperty().addListener(setPrinterComboBoxChangeListener(true,
                                printer1RollTypeHBoxTRFailed, printer1TwinTurboHBoxTRFailed,
                                printer1PaperTypeHBoxTRFailed, printer2RollTypeHBoxTRFailed,
                                printer2TwinTurboHBoxTRFailed, printer2PaperTypeHBoxTRFailed));
                printer2ComboBoxTRFailed.getSelectionModel()
                        .selectedItemProperty().addListener(setPrinterComboBoxChangeListener(false,
                                printer1RollTypeHBoxTRFailed, printer1TwinTurboHBoxTRFailed,
                                printer1PaperTypeHBoxTRFailed, printer2RollTypeHBoxTRFailed,
                                printer2TwinTurboHBoxTRFailed, printer2PaperTypeHBoxTRFailed));

                printer1DymoTwinTurboCBTRFailed.selectedProperty().addListener((observable, oldValue, newValue) -> {
                    printer1InputTrayHBoxTRFailed.setVisible(newValue);
                    printer1InputTrayHBoxTRFailed.setManaged(newValue);
                });

                printer2DymoTwinTurboCBTRFailed.selectedProperty().addListener((observable, oldValue, newValue) -> {
                    printer2InputTrayHBoxTRFailed.setVisible(newValue);
                    printer2InputTrayHBoxTRFailed.setManaged(newValue);
                });

                if (printerSettingsWhenFailed != null) {
                    paperType1ComboBoxTRFailed.setValue(printerSettingsWhenFailed.getSelectedPaperType1() != null ?
                            printerSettingsWhenFailed.getSelectedPaperType1() : BrotherPaperType.MONO.getDisplayName());
                    paperType2ComboBoxTRFailed.setValue(printerSettingsWhenFailed.getSelectedPaperType2() != null ?
                            printerSettingsWhenFailed.getSelectedPaperType2() : BrotherPaperType.MONO.getDisplayName());

                    label1ComboBoxTRFailed.setValue(printerSettingsWhenFailed.getDefaultLabel1());
                    label2ComboBoxTRFailed.setValue(printerSettingsWhenFailed.getDefaultLabel2());

                    printer1ComboBoxTRFailed.setValue(printerSettingsWhenFailed.getDefaultPrinter1());
                    printer2ComboBoxTRFailed.setValue(printerSettingsWhenFailed.getDefaultPrinter2());

                    setPrintProperties(printer1RollTypeHBoxTRFailed, printer1TwinTurboHBoxTRFailed,
                            printer1PaperTypeHBoxTRFailed, printer1RollLabelRBTRFailed,
                            printer1RollTapeRBTRFailed, printer1InputTrayLBTRFailed,
                            printer1InputTrayRBTRFailed, printerSettingsWhenFailed.getDefaultPrinter1(),
                            printerSettingsWhenFailed.getRollTypePrinter1(),
                            printerSettingsWhenFailed.getSelectedTrayPrinter1());

                    setPrintProperties(printer2RollTypeHBoxTRFailed,
                            printer2TwinTurboHBoxTRFailed, printer2PaperTypeHBoxTRFailed, printer2RollLabelRBTRFailed,
                            printer2RollTapeRBTRFailed, printer2InputTrayLBTRFailed, printer2InputTrayRBTRFailed,
                            printerSettingsWhenFailed.getDefaultPrinter2(),
                            printerSettingsWhenFailed.getRollTypePrinter2(),
                            printerSettingsWhenFailed.getSelectedTrayPrinter2());

                    printer1DymoTwinTurboCBTRFailed.setSelected(printerSettingsWhenFailed.isDymoTwinTurboPrinter1());
                    printer2DymoTwinTurboCBTRFailed.setSelected(printerSettingsWhenFailed.isDymoTwinTurboPrinter2());
                } else {
                    /*
                       If print settings for failed test results flow were never set,
                       make sure they are displayed empty
                     */
                    paperType1ComboBoxTRFailed.setValue(StringUtils.EMPTY);
                    paperType2ComboBoxTRFailed.setValue(StringUtils.EMPTY);

                    label1ComboBoxTRFailed.setValue(StringUtils.EMPTY);
                    label2ComboBoxTRFailed.setValue(StringUtils.EMPTY);

                    printer1ComboBoxTRFailed.setValue(StringUtils.EMPTY);
                    printer2ComboBoxTRFailed.setValue(StringUtils.EMPTY);

                    printer1TwinTurboHBoxTRFailed.setVisible(false);
                    printer2TwinTurboHBoxTRFailed.setVisible(false);
                    printer1TwinTurboHBoxTRFailed.setManaged(false);
                    printer2TwinTurboHBoxTRFailed.setManaged(false);
                }
            }
        }
    }
}
