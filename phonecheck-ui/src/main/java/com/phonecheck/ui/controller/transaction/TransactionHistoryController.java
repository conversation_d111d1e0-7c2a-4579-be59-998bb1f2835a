package com.phonecheck.ui.controller.transaction;

import com.phonecheck.model.event.transaction.TransactionHistoryResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.TransactionHistoryRequestMessage;
import com.phonecheck.model.store.UiInMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.ui.component.TransactionHistoryTableCell;
import com.phonecheck.ui.controller.AbstractUiController;
import com.phonecheck.ui.controller.MainController;
import com.phonecheck.ui.controller.utility.TransactionColumnMapperUtil;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.image.ImageView;
import javafx.scene.layout.*;
import javafx.stage.Stage;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;

@Component
public class TransactionHistoryController extends AbstractUiController implements Initializable {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionHistoryController.class);

    public static final String UI_STAGE_NAME = "TransactionHistory";

    @FXML
    private AnchorPane root;
    @FXML
    private StackPane loaderViewStackPane;
    @FXML
    private VBox mainContentVBox;
    @FXML
    private Label currentTransactions;
    @FXML
    private Label totalTransactions;
    @FXML
    private ImageView transactionMenu;
    @FXML
    private TextField searchView;
    @FXML
    private HBox tableHBox;
    @Setter
    private Stage stage;
    @Autowired
    private MainController mainController;
    @Autowired
    private UiInMemoryStore uiInMemoryStore;

    private TableView<Transaction> transactionHistoryTableView;
    private ObservableList<Transaction> observableTransactionList;

    private int dataOffset = 0;

    // ==================================================================
    //                       FXML Action Listeners
    // ==================================================================

    @Override
    public void initialize(final URL location, final ResourceBundle resources) {
        requestBackendForTransactionHistory();

        observableTransactionList = FXCollections.observableArrayList();

        Runnable runnable = () -> {
            //setting tableview columns
            setupTableView();
            setupTableViewColumns();
            setupListeners();
        };
        runOnFxThread(runnable);
    }

    // ==================================================================
    //                          UI related Methods
    // ==================================================================

    /**
     * To go to very first page of
     * all previous transaction list
     */
    @FXML
    public void onLeastPreviousClicked() {
    }

    /**
     * To go to previous transaction page
     */
    @FXML
    public void onPreviousClicked() {
    }

    /**
     * To go to last transactions page from
     * all previous transactions list
     */
    @FXML
    public void onLeastNextClicked() {
    }

    /**
     * To go to next transaction page
     */
    @FXML
    public void onNextClicked() {
    }

    /**
     * Sets up table view
     */
    private void setupTableView() {
        transactionHistoryTableView = new TableView<>();
        HBox.setHgrow(transactionHistoryTableView, Priority.ALWAYS);

        String tableViewCss = Objects
                .requireNonNull(getClass().getResource("/com/phonecheck/style/tableview.css")).toExternalForm();
        String scrollbarCss = Objects
                .requireNonNull(getClass().getResource("/com/phonecheck/style/scrollbar.css")).toExternalForm();

        transactionHistoryTableView.getStylesheets().addAll(tableViewCss, scrollbarCss);
        tableHBox.getChildren().add(transactionHistoryTableView);
    }

    /**
     * Sets up the columns of a TableView for displaying previous transaction data.
     * Iterates through the provided list of column names and creates
     * corresponding TableColumn instances with cell.
     */
    private void setupTableViewColumns() {
        Map<String, String> orderMap = TransactionColumnMapperUtil.getTransactionColumnOrderMap();

        for (Map.Entry<String, String> entry : orderMap.entrySet()) {
            TableColumn<Transaction, Object> column = new TableColumn<>(entry.getValue());

            column.setPrefWidth(column.getPrefWidth() * 2.0);
            column.setCellValueFactory(new PropertyValueFactory<>(entry.getKey()));
            column.setCellFactory(c -> new TransactionHistoryTableCell(uiInMemoryStore.getTransaction()));

            transactionHistoryTableView.getColumns().add(column);
        }
    }

    // ==================================================================
    //                       FXML Action Listeners
    // ==================================================================

    /**
     * Transaction history stage focused change listener
     */
    private final ChangeListener<Boolean> stageFocusedChangeListener = (obs, wasFocused, isNowFocused) -> {
        if (isNowFocused) {
            transactionHistoryTableView.refresh();
        }
    };

    /**
     * Search query text change listener
     */
    private final ChangeListener<String> searchQueryChangeListener = (observable, oldValue, searchAbleText)
            -> filterTableViewData(searchAbleText);

    /**
     * Sets up a listener for the search view input field.
     * The listener filters the data in the TableView based on the search input.
     */
    private void setupListeners() {
        stage.focusedProperty().addListener(stageFocusedChangeListener);

        searchView.textProperty().addListener(searchQueryChangeListener);

        transactionHistoryTableView.setRowFactory(tv -> {
            TableRow<Transaction> row = new TableRow<>();
            row.setOnMousePressed(event -> {
                if (!row.isEmpty()) {
                    Transaction selectedItem = row.getItem();
                    handleSelectedItemClick(selectedItem);
                }
            });
            return row;
        });

        stage.setOnHidden(event -> {
            stage.focusedProperty().removeListener(stageFocusedChangeListener);
            searchView.textProperty().removeListener(searchQueryChangeListener);

            transactionHistoryTableView.getItems().clear();
            transactionHistoryTableView.getColumns().clear();
            transactionHistoryTableView.setRowFactory(null);
            observableTransactionList.clear();

            tableHBox.getChildren().remove(transactionHistoryTableView);
            transactionHistoryTableView = null;

            if (mainController.getCurrentlyOpenedTransactionStages().isEmpty()) {
                mainController.getStackPane().getScene().getRoot().setEffect(null);
            }
        });
    }

    /**
     * Handles the selection of an item by displaying its details in a detail view.
     *
     * @param selectedRecord The Transaction object representing the selected item.
     *                       Its details will be displayed in the detail view.
     */
    private void handleSelectedItemClick(final Transaction selectedRecord) {
        mainController.loadTransactionDetails(selectedRecord, false, false);
        transactionHistoryTableView.getSelectionModel().clearSelection();
    }

    // ==================================================================
    //                    Methods related to data search
    // ==================================================================

    /**
     * Filters the data in the TableView based on the provided search text.
     *
     * @param searchQuery The search text
     */
    private void filterTableViewData(final String searchQuery) {
        ObservableList<Transaction> filteredList = FXCollections.observableArrayList();

        for (Transaction transaction : observableTransactionList) {
            if (searchInTransactionFields(transaction, searchQuery)) {
                filteredList.add(transaction);
            }
        }
        transactionHistoryTableView.setItems(filteredList);
    }

    /**
     * Checks if the provided search text is contained
     * in any String field of the given transaction model.
     *
     * @param transaction The transaction to be checked.
     * @param searchQuery The search text to be searched for in the tableview fields.
     * @return True if the search text is found in any column of the tableview, false otherwise.
     */
    private boolean searchInTransactionFields(final Transaction transaction, final String searchQuery) {
        for (Field field : Transaction.class.getDeclaredFields()) {
            try {
                field.setAccessible(true);
                String fieldValue = String.valueOf(field.get(transaction));
                if (fieldValue != null && fieldValue.toLowerCase()
                        .contains(searchQuery.toLowerCase())) {
                    return true;
                }
            } catch (IllegalAccessException e) {
                LOGGER.error("Error while searching data in transaction history records");
            }
        }

        return false;
    }

    // ==================================================================
    //                      MQTT messages requests / notify
    // ==================================================================

    private void requestBackendForTransactionHistory() {
        final String topic = TopicBuilder.buildGenericTopic("transaction-history", "request");

        // create MQTT message and publish
        final TransactionHistoryRequestMessage message = new TransactionHistoryRequestMessage();
        publishToMqttTopic(topic, message);
    }

    // ==================================================================
    //                         Events Listeners
    // ==================================================================

    /**
     * Transaction history response event listener.
     *
     * @param event TransactionHistoryResponseEvent
     */
    @EventListener
    public void onTransactionHistoryResponse(final TransactionHistoryResponseEvent event) {
        List<Transaction> transactionList = event.getTransactionList();
        Runnable runnable;
        runnable = () -> {
            if (transactionList != null && !transactionList.isEmpty()) {
                uiInMemoryStore.setTransactionList(transactionList);
                observableTransactionList.clear();
                observableTransactionList.addAll(transactionList);
                transactionHistoryTableView.setItems(observableTransactionList);
            }

            loaderViewStackPane.setVisible(false);
            loaderViewStackPane.setManaged(false);
            mainContentVBox.setVisible(true);
        };

        runOnFxThread(runnable);
    }
}
