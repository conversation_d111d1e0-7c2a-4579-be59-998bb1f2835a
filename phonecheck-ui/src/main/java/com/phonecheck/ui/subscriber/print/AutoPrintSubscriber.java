package com.phonecheck.ui.subscriber.print;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.AbstractEvent;
import com.phonecheck.model.event.print.AutoPrintLabelEvent;
import com.phonecheck.model.event.print.IWatchAutoPrintLabelEvent;
import com.phonecheck.model.mqtt.AutoPrintRequestMessage;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.print.brother.BrotherPaperType;
import com.phonecheck.model.print.brother.BrotherRollType;
import com.phonecheck.model.print.dymo.DymoPaperTray;
import com.phonecheck.model.store.UiInMemoryStore;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AutoPrintSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(AutoPrintSubscriber.class);
    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;
    private final UiInMemoryStore uiInMemoryStore;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "auto", "print", "request"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "auto", "print", "request")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        new Thread(() -> {
            try {
                final AutoPrintRequestMessage message = mapper.readValue(payload,
                        AutoPrintRequestMessage.class);
                setDeviceIdInMDC(message.getDevice().getId());
                LOGGER.info("Auto print request payload: {}", payload);

                // Create Print operation for default label 1
                if (!"None".equalsIgnoreCase(message.getLabel1Name()) &&
                        StringUtils.isNotBlank(message.getLabel1Name())) {
                    PrintOperation printOperation1 = new PrintOperation();
                    printOperation1.setLabelName(message.getLabel1Name());
                    printOperation1.setPrinterName(message.getPrinter1Name());
                    printOperation1.setDevice(message.getDevice());
                    printOperation1.setRollType(BrotherRollType.getByValue(message.getRollTypePrinter1()));
                    if (message.isDymoTwinTurboPrinter1()) {
                        printOperation1.setDymoPaperTray(DymoPaperTray.getById(message.getSelectedTrayPrinter1()));
                    }
                    printOperation1.setPrintToPdf(false);
                    printOperation1.setPrintToPreview(false);
                    printOperation1.setPaperType(BrotherPaperType.getByDisplayName(message.getSelectedPaperType1()));

                    // Publish auto print event for default label 1
                    AbstractEvent printLabel1Event =
                            (DeviceConnectionMode.IWATCH_HOST == uiInMemoryStore.getDeviceConnectionMode())
                                    ? new IWatchAutoPrintLabelEvent(this, printOperation1)
                                    : new AutoPrintLabelEvent(this, printOperation1);

                    eventPublisher.publishEvent(printLabel1Event);
                }

                // Create Print operation for default label 2
                if (!"None".equalsIgnoreCase(message.getLabel2Name()) &&
                        StringUtils.isNotBlank(message.getLabel2Name())) {
                    PrintOperation printOperation2 = new PrintOperation();
                    printOperation2.setLabelName(message.getLabel2Name());
                    printOperation2.setPrinterName(message.getPrinter2Name());
                    printOperation2.setDevice(message.getDevice());
                    printOperation2.setRollType(BrotherRollType.getByValue(message.getRollTypePrinter2()));
                    if (message.isDymoTwinTurboPrinter2()) {
                        printOperation2.setDymoPaperTray(DymoPaperTray.getById(message.getSelectedTrayPrinter2()));
                    }
                    printOperation2.setPrintToPdf(false);
                    printOperation2.setPrintToPreview(false);
                    printOperation2.setPaperType(BrotherPaperType.getByDisplayName(message.getSelectedPaperType2()));

                    // Publish auto print event for default label 2
                    AbstractEvent printLabel2Event =
                            (DeviceConnectionMode.IWATCH_HOST == uiInMemoryStore.getDeviceConnectionMode())
                                    ? new IWatchAutoPrintLabelEvent(this, printOperation2)
                                    : new AutoPrintLabelEvent(this, printOperation2);

                    eventPublisher.publishEvent(printLabel2Event);
                }
            } catch (JsonProcessingException e) {
                // There's nothing we can do at this point to fix the message
                LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
            }
        }).start();

    }
}
