package com.phonecheck.ui.subscriber.app;

import com.phonecheck.model.constants.FileConstants;
import com.phonecheck.model.event.login.BackendStartedEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Component
@AllArgsConstructor
public class BackendStartedSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(BackendStartedSubscriber.class);
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;
    private final SupportFilePath supportFilePath;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("backend", "started")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        new Thread(() -> {
            //From 3.10.4 we stopped writing syslog files into the logs folder. We no longer need them
            //Hence, delete any syslog files present in the system that were created from the previous builds.
            deleteSysLogFolderIfExists();
            final BackendStartedEvent newEvent = new BackendStartedEvent(this);
            eventPublisher.publishEvent(newEvent);
        }).start();
    }

    /**
     * Delete syslog(Device) folder if exists
     */
    private void deleteSysLogFolderIfExists() {
        Path sysLogFolderPath = Paths.get(
                supportFilePath.getPaths().getRootFolderPath() +
                        File.separator + FileConstants.LOGS_DIR + "/device/");

        LOGGER.info("Deleting the syslog folder from: {}", sysLogFolderPath);

        try {
            if (Files.exists(sysLogFolderPath)) {
                Files.walk(sysLogFolderPath) // Traverse the directory tree
                        .sorted((a, b) -> b.compareTo(a)) // Sort to delete files before directories
                        .forEach(path -> {
                            try {
                                Files.delete(path); // Delete each file or folder
                            } catch (IOException e) {
                                LOGGER.error("Failed to delete: {} due to {}", path, e.getMessage());
                            }
                        });
                LOGGER.info("Syslog(Device) folder deleted successfully.");
            } else {
                LOGGER.info("Syslog(Device) Folder does not exist.");
            }
        } catch (IOException e) {
            LOGGER.error("An error occurred while deleting the Syslog(Device): {}", e.getMessage());
        }
    }
}
