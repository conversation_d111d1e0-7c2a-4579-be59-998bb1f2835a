package com.phonecheck.ui.subscriber.port;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.port.PortMapTypeResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.PortMappingTypeResponseMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Reacts when get port mapping available response from backend is received
 */
@Component
@AllArgsConstructor
public class PortMappingTypeResponseSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(PortMappingTypeResponseSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("port-map", "type", "response")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Port mapping type response received: {}", payload);

        // Make sure it is a port mapping type response message
        try {
            final PortMappingTypeResponseMessage response = mapper.readValue(payload,
                    PortMappingTypeResponseMessage.class);

            final PortMapTypeResponseEvent event =
                    new PortMapTypeResponseEvent(this, response.isThunderSync());
            eventPublisher.publishEvent(event);
        } catch (JsonProcessingException e) {
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}

