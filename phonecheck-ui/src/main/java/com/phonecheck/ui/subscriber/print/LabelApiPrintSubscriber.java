package com.phonecheck.ui.subscriber.print;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.print.AutoPrintLabelEvent;
import com.phonecheck.model.event.print.IWatchAutoPrintLabelEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.LabelApiPrintRequestMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class LabelApiPrintSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(LabelApiPrintSubscriber.class);
    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "api-print", "request"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "api-print", "request")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("Label api print request payload: {}", payload);
        new Thread(() -> {
            try {
                final LabelApiPrintRequestMessage message = mapper.readValue(payload,
                        LabelApiPrintRequestMessage.class);
                setDeviceIdInMDC(message.getDevice().getId());

                if (message.isPrintForIWatch()) {
                    LOGGER.info("Raising print for iWatch");
                    IWatchAutoPrintLabelEvent event = new IWatchAutoPrintLabelEvent(this,
                            message.getPrintOperation());
                    eventPublisher.publishEvent(event);
                } else {
                    final AutoPrintLabelEvent printLabel1Event =
                            new AutoPrintLabelEvent(this, message.getPrintOperation());
                    eventPublisher.publishEvent(printLabel1Event);
                }
            } catch (JsonProcessingException e) {
                // There's nothing we can do at this point to fix the message
                LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
            }
        }).start();

    }
}
